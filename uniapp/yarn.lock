# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.1.0", "@ampproject/remapping@^2.1.2":
  version "2.2.0"
  resolved "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.2.0.tgz"
  integrity sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@babel/code-frame@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz"
  integrity sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/compat-data@^7.18.8":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.18.13.tgz"
  integrity sha512-5yUzC5LqyTFp2HLmDoxGQelcdYgSpP9xsnMWBphAscOdFrHSAVbLNzWiy32sVNDqJRDiJK6klfDnAgu6PAGSHw==

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.17.9":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/core/-/core-7.18.13.tgz"
  integrity sha512-ZisbOvRRusFktksHSG6pjj1CSvkPkcZq/KHD45LAkVP/oiHJkNBZWfpvlLmX8OtHDG8IuzsFlVRWo08w7Qxn0A==
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.18.13"
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-module-transforms" "^7.18.9"
    "@babel/helpers" "^7.18.9"
    "@babel/parser" "^7.18.13"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.18.13"
    "@babel/types" "^7.18.13"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.1"
    semver "^6.3.0"

"@babel/generator@^7.17.9", "@babel/generator@^7.18.13":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/generator/-/generator-7.18.13.tgz"
  integrity sha512-CkPg8ySSPuHTYPJYo7IRALdqyjM9HCbt/3uOBEFbzyGVP6Mn8bwFPB0jX6982JVNBlYzM1nnPkfjuXSOPtQeEQ==
  dependencies:
    "@babel/types" "^7.18.13"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/generator@^7.18.10", "@babel/generator@7.18.12":
  version "7.18.12"
  resolved "https://registry.npmmirror.com/@babel/generator/-/generator-7.18.12.tgz"
  integrity sha512-dfQ8ebCN98SvyL7IxNMCUtZQSq5R7kxgN+r8qYTGDmmSion1hX2C0zq2yo1bsCDhXixokv1SAWTZUMYbO/V5zg==
  dependencies:
    "@babel/types" "^7.18.10"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz"
  integrity sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-compilation-targets@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.18.9.tgz"
  integrity sha512-tzLCyVmqUiFlcFoAPLA/gL9TeYrF61VLNtb+hvkuVaB5SUjW7jcfrglBIX1vUIoT7CLP3bBlIMeyEsIl2eFQNg==
  dependencies:
    "@babel/compat-data" "^7.18.8"
    "@babel/helper-validator-option" "^7.18.6"
    browserslist "^4.20.2"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.9":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.18.13.tgz"
  integrity sha512-hDvXp+QYxSRL+23mpAlSGxHMDyIGChm0/AwTfTAAK5Ufe40nCsyNdaYCGuK91phn/fVu9kqayImRDkvNAgdrsA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.9"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-environment-visitor@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz"
  integrity sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==

"@babel/helper-function-name@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.18.9.tgz"
  integrity sha512-fJgWlZt7nxGksJS9a0XdSaI4XvpExnNIgRP+rVefWh5U7BL8pPuir6SJUmFKRfjWQ51OtWSzwOxhaH/EBWWc0A==
  dependencies:
    "@babel/template" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz"
  integrity sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.18.9.tgz"
  integrity sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==
  dependencies:
    "@babel/types" "^7.18.9"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz"
  integrity sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-transforms@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.18.9.tgz"
  integrity sha512-KYNqY0ICwfv19b31XzvmI/mfcylOzbLtowkw+mfvGPAQ3kfCnMLYbED3YecL5tPd8nAYFQFAd6JHp2LxZk/J1g==
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.18.6"
    "@babel/template" "^7.18.6"
    "@babel/traverse" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-optimise-call-expression@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz"
  integrity sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.18.9.tgz"
  integrity sha512-aBXPT3bmtLryXaoJLyYPXPlSD4p1ld9aYeR+sJNOZjJJGiOpb+fKfh3NkcCu7J54nUJwCERPBExCCpyCOHnu/w==

"@babel/helper-replace-supers@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.18.9.tgz"
  integrity sha512-dNsWibVI4lNT6HiuOIBr1oyxo40HvIVmbwPUm3XZ7wMh4k2WxrxTqZwSqw/eEmXDS9np0ey5M2bz9tBmO9c+YQ==
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/traverse" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-simple-access@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.18.6.tgz"
  integrity sha512-iNpIgTgyAvDQpDj76POqg+YEt8fPxx3yaNBg3S30dxNKm2SWfYhD0TGrK/Eu9wHpUW63VQU894TsTg+GLbUa1g==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz"
  integrity sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.18.10":
  version "7.18.10"
  resolved "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.18.10.tgz"
  integrity sha512-XtIfWmeNY3i4t7t4D2t02q50HvqHybPqW2ki1kosnvWCwuCMeo81Jf0gwr85jy/neUdg5XDdeFE/80DXiO+njw==

"@babel/helper-validator-identifier@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.18.6.tgz"
  integrity sha512-MmetCkz9ej86nJQV+sFCxoGGrUbU3q02kgLciwkrt9QqEB7cP39oKEY0PakknEO0Gu20SskMRi+AYZ3b1TpN9g==

"@babel/helper-validator-option@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz"
  integrity sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==

"@babel/helpers@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.18.9.tgz"
  integrity sha512-Jf5a+rbrLoR4eNdUmnFu8cN5eNJT6qdTdOg5IHIzq87WwyRw9PwguLFOWYgktN/60IP4fgDUawJvs7PjQIzELQ==
  dependencies:
    "@babel/template" "^7.18.6"
    "@babel/traverse" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz"
  integrity sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.16.4", "@babel/parser@^7.17.9", "@babel/parser@^7.18.10", "@babel/parser@^7.18.13":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/parser/-/parser-7.18.13.tgz"
  integrity sha512-dgXcIfMuQ0kgzLB2b9tRZs7TTFFaGM2AbtA4fJgUUYukzGH4jwsS7hzQHEGs67jdehpm22vkgKwvbU+aEflgwg==

"@babel/parser@^7.18.11", "@babel/parser@7.18.11":
  version "7.18.11"
  resolved "https://registry.npmmirror.com/@babel/parser/-/parser-7.18.11.tgz"
  integrity sha512-9JKn5vN+hDt0Hdqn1PiJ2guflwP+B6Ga8qbDuoF0PzzVhrzsKIJo8yGqVk6CmMHiMei9w1C1Bp9IMJSIK+HPIQ==

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-jsx@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz"
  integrity sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-typescript@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.18.6.tgz"
  integrity sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-typescript@^7.16.8":
  version "7.18.12"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.12.tgz"
  integrity sha512-2vjjam0cum0miPkenUbQswKowuxs/NjMwIKEq0zwegRxXk12C9YOF9STXnaUptITOtOJHKHpzvvWYOjbm6tc0w==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-typescript" "^7.18.6"

"@babel/runtime@^7.17.2":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.18.9.tgz"
  integrity sha512-lkqXDcvlFT5rvEjiu6+QYO+1GXrEHRo2LOtS7E4GtX5ESIZOgepqsZBVIj6Pv+a6zqsya9VCgiK1KAK4BvJDAw==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/standalone@^7.17.11":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/standalone/-/standalone-7.18.13.tgz"
  integrity sha512-5hjvvFkaXyfQri+s4CAZtx6FTKclfTNd2QN2RwgzCVJhnYYgKh4YFBCnNJSxurzvpSKD2NmpCkoWAkMc+j9y+g==

"@babel/template@^7.0.0", "@babel/template@^7.18.10", "@babel/template@^7.18.6":
  version "7.18.10"
  resolved "https://registry.npmmirror.com/@babel/template/-/template-7.18.10.tgz"
  integrity sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.10"
    "@babel/types" "^7.18.10"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.18.13", "@babel/traverse@^7.18.9":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.18.13.tgz"
  integrity sha512-N6kt9X1jRMLPxxxPYWi7tgvJRH/rtoU+dbKAPDM44RFHiMH8igdsaSBgFeskhSl/kLWLDUvIh1RXCrTmg0/zvA==
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.18.13"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.18.13"
    "@babel/types" "^7.18.13"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/traverse@7.18.11":
  version "7.18.11"
  resolved "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.18.11.tgz"
  integrity sha512-TG9PiM2R/cWCAy6BPJKeHzNbu4lPzOSZpeMfeNErskGpTJx6trEvFaVCbDvpcxwy49BKWmEPwiW8mrysNiDvIQ==
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.18.10"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.18.11"
    "@babel/types" "^7.18.10"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.17.0", "@babel/types@^7.18.10", "@babel/types@^7.18.13", "@babel/types@^7.18.6", "@babel/types@^7.18.9":
  version "7.18.13"
  resolved "https://registry.npmmirror.com/@babel/types/-/types-7.18.13.tgz"
  integrity sha512-ePqfTihzW0W6XAU+aMw2ykilisStJfDnsejDCXRchCcMJ4O0+8DhPXf2YUbZ6wjBlsEmZwLK/sPweWtu8hcJYQ==
  dependencies:
    "@babel/helper-string-parser" "^7.18.10"
    "@babel/helper-validator-identifier" "^7.18.6"
    to-fast-properties "^2.0.0"

"@dcloudio/types@^3.0.13":
  version "3.0.13"
  resolved "https://registry.npmmirror.com/@dcloudio/types/-/types-3.0.13.tgz"
  integrity sha512-K8z8DfylWnLjEHUnsc6DsxA6ugCzYZoAmyVIpT57K3cAfMTGx8E6GAW5x8Qy2lbFpw02N3D+sGHX63zO1zDVig==

"@dcloudio/uni-app-plus@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-app-plus/-/uni-app-plus-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-pqshzVWD6nWbnLZwQi2qJnGz2HCsC1YqxzhsZdHTIc55hvEVCIySQptDU1r8xqd7SLd9hM6njC23Uh6ZLP/giA==
  dependencies:
    "@dcloudio/uni-app-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-app-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-uts-vite" "3.0.0-alpha-3050520220824001"

"@dcloudio/uni-app-vite@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-app-vite/-/uni-app-vite-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-NP3Bq+MXnOGCXV1KKqU0UdZk6E7P9A+j6MH4X23+LDunzHuUf333ngtuhpSXbqHwtgzMX2jPGKF6m/R6GIKYjQ==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-i18n" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-nvue-styler" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uts" "3.0.0-alpha-3050520220824001"
    "@rollup/pluginutils" "^4.2.0"
    "@vitejs/plugin-vue" "^2.3.3"
    "@vue/compiler-dom" "3.2.37"
    "@vue/compiler-sfc" "3.2.37"
    debug "^4.3.3"
    fs-extra "^10.0.0"
    picocolors "^1.0.0"
    rollup "^2.59.0"

"@dcloudio/uni-app-vue@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-app-vue/-/uni-app-vue-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-LZ1BXm8tN7W7/5pms2CDb75sBz9SGhqGmzbIjfCXgpUXy4Vl2sToyIn3j1nZ0heGZwZA3A3BFWkuyxpZH95smA==

"@dcloudio/uni-app@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-app/-/uni-app-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-C5dABv0UY41Rrt0Ea3q3U0GMF7c4E1KPiiv9PfG2H8ZXNwLzoe6LVbnS/ZaKPAcG4yJcb6cEFecKXIoseA2Hrw==
  dependencies:
    "@dcloudio/uni-cloud" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-components" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-i18n" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-push" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-stat" "3.0.0-alpha-3050520220824001"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-automator@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-automator/-/uni-automator-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-o2xRx3w+ENZkKwhnQvdma69dpJwA+ho9JcL7ETIDZI6MrS3BENQcK0qRE7Xrks85IaqJUnPTFfD840K0LrcMeg==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    address "^1.1.2"
    cross-env "^7.0.3"
    debug "^4.3.3"
    default-gateway "^6.0.3"
    fs-extra "^10.0.0"
    licia "^1.29.0"
    postcss-selector-parser "^6.0.6"
    qrcode-reader "^1.0.4"
    qrcode-terminal "^0.12.0"
    ws "^8.4.2"

"@dcloudio/uni-cli-shared@^3.0.0-alpha-3050520220824001", "@dcloudio/uni-cli-shared@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-cli-shared/-/uni-cli-shared-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-MHqTQETm8CbyLZMq0+hhC0c3b1MgDu1yL4J8hjIq7i8fUeScOO1BdWofqwX/oDusgQV3735SZQH/29u7o7kcOA==
  dependencies:
    "@ampproject/remapping" "^2.1.2"
    "@babel/core" "^7.17.9"
    "@babel/parser" "^7.17.9"
    "@babel/types" "^7.17.0"
    "@dcloudio/uni-i18n" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@intlify/core-base" "9.1.9"
    "@intlify/shared" "9.1.9"
    "@intlify/vue-devtools" "9.1.9"
    "@rollup/pluginutils" "^4.2.0"
    "@vue/compiler-core" "3.2.37"
    "@vue/compiler-dom" "3.2.37"
    "@vue/compiler-sfc" "3.2.37"
    "@vue/server-renderer" "3.2.37"
    "@vue/shared" "3.2.37"
    autoprefixer "^10.4.8"
    base64url "^3.0.1"
    chokidar "^3.5.3"
    compare-versions "^3.6.0"
    debug "^4.3.3"
    es-module-lexer "^0.9.3"
    esbuild "^0.14.27"
    estree-walker "^2.0.2"
    fast-glob "^3.2.11"
    fs-extra "^10.0.0"
    hash-sum "^2.0.0"
    jsonc-parser "^3.0.0"
    magic-string "^0.26.1"
    merge "^2.1.1"
    mime "^3.0.0"
    module-alias "^2.2.2"
    os-locale-s-fix "^1.0.8-fix-1"
    picocolors "^1.0.0"
    postcss-import "^14.0.2"
    postcss-load-config "^3.1.1"
    postcss-modules "^4.3.0"
    postcss-selector-parser "^6.0.6"
    resolve "^1.22.0"
    tapable "^2.2.0"
    xregexp "3.1.0"

"@dcloudio/uni-cloud@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-cloud/-/uni-cloud-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-ZWHNS3X6huJ/01Nf4N6vkzvcPbAvslF46uyBwSptwEVrehjd7oQDGUxmcnkR1TyvETv2F3OkkbyBcR8bF9ctjQ==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-i18n" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/shared" "3.2.37"
    fast-glob "^3.2.11"

"@dcloudio/uni-components@^3.0.0-alpha-3050520220824001", "@dcloudio/uni-components@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-components/-/uni-components-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-76YlMDH6bT7LaGdx+XtGiLkCLOphxh3egS0MnmNpU9CfSAX0aGoK0buFn4RmcsIrQe8bMrEo+XQL0rDRI6axRA==

"@dcloudio/uni-h5-vite@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-h5-vite/-/uni-h5-vite-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-an/0CwsIsKxBMt2q4qnS+w81UpLznJMCGOYFfajddnrwuQvXdarrn+R+d+GHK4RLlp3DUZPwWlyDF2lYOPK26Q==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@rollup/pluginutils" "^4.2.0"
    "@vue/compiler-dom" "3.2.37"
    "@vue/compiler-sfc" "3.2.37"
    "@vue/server-renderer" "3.2.37"
    "@vue/shared" "3.2.37"
    debug "^4.3.3"
    fs-extra "^10.0.0"
    mime "^3.0.0"
    module-alias "^2.2.2"

"@dcloudio/uni-h5-vue@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-h5-vue/-/uni-h5-vue-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-+Q7FZH+D+2fT7BbuCBgY4OTIg1G32TfEGHreZjRnlCjlCFARZtLsbIFLRy1bAuwfwNOQtAcoCglGBV6084BSzA==
  dependencies:
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"

"@dcloudio/uni-h5@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-h5/-/uni-h5-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-WzpnA4JZgdZzBbgt/usnvaRkya4RtZV9UL8HqUlWBSacZ6k8yZlH/1RyqjjmdDweYmQyKD2XoyA3MJ7EFuTRTQ==
  dependencies:
    "@dcloudio/uni-h5-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-h5-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-i18n" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/server-renderer" "3.2.37"
    "@vue/shared" "3.2.37"
    localstorage-polyfill "^1.0.1"
    safe-area-insets "^1.4.1"
    vue-router "^4.1.3"
    xmlhttprequest "^1.8.0"

"@dcloudio/uni-i18n@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-i18n/-/uni-i18n-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-DWJiHnhW+8A0EnldewWuwUHr0EjdXxLmaTAV45lD17hrCnYXSNLHPmrSfiNwoMmsllBldwzzIeT+m+8mKfSkyA==

"@dcloudio/uni-mp-alipay@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-alipay/-/uni-mp-alipay-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-5oYIijmQ0U71IvmHNmpUdLUp8jvVRro1R8NagiGRA2U1wvYi2gLS28rocqo/9A8Duwqx3AX4LgAe/oCom9Q+KA==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/compiler-core" "3.2.37"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-mp-baidu@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-baidu/-/uni-mp-baidu-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-nQmK3MeoIEZCX2gZlpwHoI8ghn/15zjEYHtDtoR3i6hBsekFlZsXCtW4KHOY03tdVIadHbyzsUtGlIQxcLIIsA==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-compiler" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-weixin" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/compiler-core" "3.2.37"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-mp-compiler@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-compiler/-/uni-mp-compiler-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-AhjUkul+2pwwxz7I9CvrzW46UrFvuvQ+X6pn2xByqVQq7taWphDaeRRsKel5ZbDKQCARY0mDqzNfrYfZA4yUiA==
  dependencies:
    "@babel/generator" "^7.17.9"
    "@babel/parser" "^7.17.9"
    "@babel/types" "^7.17.0"
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/compiler-core" "3.2.37"
    "@vue/compiler-dom" "3.2.37"
    "@vue/shared" "3.2.37"
    estree-walker "^2.0.2"

"@dcloudio/uni-mp-kuaishou@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-kuaishou/-/uni-mp-kuaishou-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-iiKhu0MwbvzA34KrhTQFaBkAeTGxJ+q1rpPGxe6f/YCj75vC+d34JHim+rKsjjafKNwGerOlNtfg/XeX67lvTQ==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-compiler" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-weixin" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/compiler-core" "3.2.37"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-mp-lark@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-lark/-/uni-mp-lark-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-yWmO80+xx/31co2LaZOC/UQaRRHGGCqWgZawY6txFXAmtsqlA7UDrlQOBVSOnrx2Z9rzlcht1hTqmjMt++tyBw==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-compiler" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-toutiao" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/compiler-core" "3.2.37"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-mp-qq@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-qq/-/uni-mp-qq-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-ltC/Ml6cikLWjGPdrKbU3zgyY2Wi1wR8ZGkz2ZNB3zW0W7oZo9V18KY4Guy57V5vnJgdl3D4ZPUp/+JgOMJE4Q==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/shared" "3.2.37"
    fs-extra "^10.0.0"

"@dcloudio/uni-mp-toutiao@^3.0.0-alpha-3050520220824001", "@dcloudio/uni-mp-toutiao@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-toutiao/-/uni-mp-toutiao-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-6hFUCyDfpqmB6GxU3y6TDMZRYqg7SHUlcZEwKSo1I7+QHj7tWlLJGamiotG+tGfjaaxVNVliEmp1u29Wlg9Jwg==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-compiler" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/compiler-core" "3.2.37"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-mp-vite@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-vite/-/uni-mp-vite-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-iHod2byu8Tkzq+3BiANAO1DY5EBAs1H0OdKkcgPN9ucGB/tv9oyLtJD341SxRv2aaVHuEAmSgD0A5s+eIP/Dhw==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-i18n" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-compiler" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/compiler-sfc" "3.2.37"
    "@vue/shared" "3.2.37"
    debug "^4.3.3"
    magic-string "^0.26.1"

"@dcloudio/uni-mp-vue@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-vue/-/uni-mp-vue-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-PwfCcSRid7AN9ErCenyBZWTQIbI8Ll94K8dqJCGYWwkocZxK1kmgF8x8JWNWc37lYoqrLm6yzSMwqok0l45SiA==
  dependencies:
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-mp-weixin@^3.0.0-alpha-3050520220824001", "@dcloudio/uni-mp-weixin@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-mp-weixin/-/uni-mp-weixin-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-N7fYtnUuebl3zJVV9aAdvaagEy7RhI1EkYD6nyisLwhdbJtIcAGJt+ukJhr2P3F1lmKt4qenMDLaYqoyPYGWGQ==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-nvue-styler@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-nvue-styler/-/uni-nvue-styler-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-wpBJRmtZ2OPyc3qgRChoQywJRHt9tYJkLBrAVSOIPV9vrK9EdJU687rJHBhWfSRNK4eFO+VOsE3ArHijmMwy7Q==
  dependencies:
    "@vue/shared" "3.2.37"
    parse-css-font "^4.0.0"
    postcss "^8.4.13"

"@dcloudio/uni-push@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-push/-/uni-push-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-kRCk6sjbmnYjE7qb+ArwwO1NgXsA2OFVquo2c0l36f7DoTmtQvA5Q2/+LY8VG/kfjmks+VhJE+xTHJpXufwFmQ==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"

"@dcloudio/uni-quickapp-webview@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-quickapp-webview/-/uni-quickapp-webview-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-71ECquElsaz+mBMa/j2PeTccRIYH0/xpO5LdiI27ul75/U+hmwtG+e0+XkSQu2v1pXQzBx5Ll7M1Xfo3yUgAmQ==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vite" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-mp-vue" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@vue/shared" "3.2.37"

"@dcloudio/uni-shared@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-shared/-/uni-shared-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-nVzwsNzZ8qEFm/yxKfXtV0nxXp7MDT2rr4DhIXToPBnJPG6JFPbwgqWwn+HBVIEDZYyGk8dUu9C2ladZbrgyqw==
  dependencies:
    "@vue/shared" "3.2.37"

"@dcloudio/uni-stacktracey@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-stacktracey/-/uni-stacktracey-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-W628Ir/gh2ec4jvZr1aznsAe/SR92PsA4CwDhfNzVX+TYnCe+sphvBgveNcBSYNP9NkwQOY7nWC/bQB/lSgLHw==

"@dcloudio/uni-stat@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-stat/-/uni-stat-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-O+sMaLKhQrY4zXuNyMqeMNcrgOP+Hu/0vhSDx9zRIdewiRISxeiLRqYaFndRCY33fvK6K457vAuHWjN2sEQOfQ==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    debug "^4.3.3"

"@dcloudio/uni-uts-vite@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uni-uts-vite/-/uni-uts-vite-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-3PxyOEOgv4bxPuVKm3NMIFUdunnUWZvKFD2DqCpY8YR/XLr12sE9ZXVBmvcwth0ZQ1O/uIeUPFu/WH69ucQMew==
  dependencies:
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uts" "3.0.0-alpha-3050520220824001"
    adm-zip "^0.5.9"
    execa "^5.1.1"
    fast-glob "^3.2.11"
    fs-extra "^10.0.0"

"@dcloudio/uts-darwin-arm64@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uts-darwin-arm64/-/uts-darwin-arm64-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-wHLdtps0ER0nBIz84+CKK2F1lE+c7ezyQW78V+/vvR3I3Ted17XQdBpgoYj70GoAcB/JtPYIjaTcqR8zsOVuWA==

"@dcloudio/uts@3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/uts/-/uts-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-FV+586MRXcDEQvLVtsqrmiFWy7tDehxLM3dbGjYetR56oOxIz2ikBL86x8RKseAlTVfBA6OiSFPmvSBoqbMMeQ==
  dependencies:
    cac "6.7.9"
    chokidar "^3.5.3"
    fast-glob "^3.2.11"
    fs-extra "^10.0.0"
    picocolors "^1.0.0"
  optionalDependencies:
    "@dcloudio/uts-darwin-arm64" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uts-darwin-x64" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uts-win32-ia32-msvc" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uts-win32-x64-msvc" "3.0.0-alpha-3050520220824001"

"@dcloudio/vite-plugin-uni@^3.0.0-alpha-3050520220824001":
  version "3.0.0-alpha-3050520220824001"
  resolved "https://registry.npmmirror.com/@dcloudio/vite-plugin-uni/-/vite-plugin-uni-3.0.0-alpha-3050520220824001.tgz"
  integrity sha512-H9PuaDMsVULcw4mTnBavQlgYzA4H9VWDNkWRzcF8DuC1XNTZlgXLGg35+Rw447HBgRj7VRVcU5AVDGJH+8fO/A==
  dependencies:
    "@babel/core" "^7.17.9"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-transform-typescript" "^7.16.8"
    "@dcloudio/uni-cli-shared" "3.0.0-alpha-3050520220824001"
    "@dcloudio/uni-shared" "3.0.0-alpha-3050520220824001"
    "@rollup/pluginutils" "^4.2.0"
    "@vitejs/plugin-legacy" "^1.8.2"
    "@vitejs/plugin-vue" "^2.3.3"
    "@vitejs/plugin-vue-jsx" "^1.3.10"
    "@vue/compiler-core" "3.2.37"
    "@vue/compiler-dom" "3.2.37"
    "@vue/compiler-sfc" "3.2.37"
    "@vue/shared" "3.2.37"
    cac "6.7.9"
    debug "^4.3.3"
    estree-walker "^2.0.2"
    express "^4.17.1"
    fast-glob "^3.2.11"
    fs-extra "^10.0.0"
    hash-sum "^2.0.0"
    jsonc-parser "^3.0.0"
    picocolors "^1.0.0"

"@eslint/eslintrc@^1.3.0":
  version "1.3.0"
  resolved "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-1.3.0.tgz"
  integrity sha512-UWW0TMTmk2d7hLcWD1/e2g5HDM/HQ3csaLSqXCfqwh4uNDuNqlaKWXmEsL4Cs41Z0KnILNvwbHAah3C2yt06kw==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.3.2"
    globals "^13.15.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@humanwhocodes/config-array@^0.10.4":
  version "0.10.4"
  resolved "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.10.4.tgz"
  integrity sha512-mXAIHxZT3Vcpg83opl1wGlVZ9xydbfZO3r5YfRSH6Gpp2J/PfdBP0wbDa2sO6/qRbcalpoevVyW6A/fI6LfeMw==
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.1"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/gitignore-to-minimatch@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmmirror.com/@humanwhocodes/gitignore-to-minimatch/-/gitignore-to-minimatch-1.0.2.tgz"
  integrity sha512-rSqmMJDdLFUsyxR6FMtD00nfQKKLFb1kv+qBbOVKqErvloEIJLo5bDTJTQNTYgeyp78JsA7u/NPi5jT1GR/MuA==

"@humanwhocodes/object-schema@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
  integrity sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==

"@intlify/core-base@9.1.9":
  version "9.1.9"
  resolved "https://registry.npmmirror.com/@intlify/core-base/-/core-base-9.1.9.tgz"
  integrity sha512-x5T0p/Ja0S8hs5xs+ImKyYckVkL4CzcEXykVYYV6rcbXxJTe2o58IquSqX9bdncVKbRZP7GlBU1EcRaQEEJ+vw==
  dependencies:
    "@intlify/devtools-if" "9.1.9"
    "@intlify/message-compiler" "9.1.9"
    "@intlify/message-resolver" "9.1.9"
    "@intlify/runtime" "9.1.9"
    "@intlify/shared" "9.1.9"
    "@intlify/vue-devtools" "9.1.9"

"@intlify/core-base@9.2.2":
  version "9.2.2"
  resolved "https://registry.npmmirror.com/@intlify/core-base/-/core-base-9.2.2.tgz"
  integrity sha512-JjUpQtNfn+joMbrXvpR4hTF8iJQ2sEFzzK3KIESOx+f+uwIjgw20igOyaIdhfsVVBCds8ZM64MoeNSx+PHQMkA==
  dependencies:
    "@intlify/devtools-if" "9.2.2"
    "@intlify/message-compiler" "9.2.2"
    "@intlify/shared" "9.2.2"
    "@intlify/vue-devtools" "9.2.2"

"@intlify/devtools-if@9.1.9":
  version "9.1.9"
  resolved "https://registry.npmmirror.com/@intlify/devtools-if/-/devtools-if-9.1.9.tgz"
  integrity sha512-oKSMKjttG3Ut/1UGEZjSdghuP3fwA15zpDPcjkf/1FjlOIm6uIBGMNS5jXzsZy593u+P/YcnrZD6cD3IVFz9vQ==
  dependencies:
    "@intlify/shared" "9.1.9"

"@intlify/devtools-if@9.2.2":
  version "9.2.2"
  resolved "https://registry.npmmirror.com/@intlify/devtools-if/-/devtools-if-9.2.2.tgz"
  integrity sha512-4ttr/FNO29w+kBbU7HZ/U0Lzuh2cRDhP8UlWOtV9ERcjHzuyXVZmjyleESK6eVP60tGC9QtQW9yZE+JeRhDHkg==
  dependencies:
    "@intlify/shared" "9.2.2"

"@intlify/message-compiler@9.1.9":
  version "9.1.9"
  resolved "https://registry.npmmirror.com/@intlify/message-compiler/-/message-compiler-9.1.9.tgz"
  integrity sha512-6YgCMF46Xd0IH2hMRLCssZI3gFG4aywidoWQ3QP4RGYQXQYYfFC54DxhSgfIPpVoPLQ+4AD29eoYmhiHZ+qLFQ==
  dependencies:
    "@intlify/message-resolver" "9.1.9"
    "@intlify/shared" "9.1.9"
    source-map "0.6.1"

"@intlify/message-compiler@9.2.2":
  version "9.2.2"
  resolved "https://registry.npmmirror.com/@intlify/message-compiler/-/message-compiler-9.2.2.tgz"
  integrity sha512-IUrQW7byAKN2fMBe8z6sK6riG1pue95e5jfokn8hA5Q3Bqy4MBJ5lJAofUsawQJYHeoPJ7svMDyBaVJ4d0GTtA==
  dependencies:
    "@intlify/shared" "9.2.2"
    source-map "0.6.1"

"@intlify/message-resolver@9.1.9":
  version "9.1.9"
  resolved "https://registry.npmmirror.com/@intlify/message-resolver/-/message-resolver-9.1.9.tgz"
  integrity sha512-Lx/DBpigeK0sz2BBbzv5mu9/dAlt98HxwbG7xLawC3O2xMF9MNWU5FtOziwYG6TDIjNq0O/3ZbOJAxwITIWXEA==

"@intlify/runtime@9.1.9":
  version "9.1.9"
  resolved "https://registry.npmmirror.com/@intlify/runtime/-/runtime-9.1.9.tgz"
  integrity sha512-XgPw8+UlHCiie3fI41HPVa/VDJb3/aSH7bLhY1hJvlvNV713PFtb4p4Jo+rlE0gAoMsMCGcsiT982fImolSltg==
  dependencies:
    "@intlify/message-compiler" "9.1.9"
    "@intlify/message-resolver" "9.1.9"
    "@intlify/shared" "9.1.9"

"@intlify/shared@9.1.9":
  version "9.1.9"
  resolved "https://registry.npmmirror.com/@intlify/shared/-/shared-9.1.9.tgz"
  integrity sha512-xKGM1d0EAxdDFCWedcYXOm6V5Pfw/TMudd6/qCdEb4tv0hk9EKeg7lwQF1azE0dP2phvx0yXxrt7UQK+IZjNdw==

"@intlify/shared@9.2.2":
  version "9.2.2"
  resolved "https://registry.npmmirror.com/@intlify/shared/-/shared-9.2.2.tgz"
  integrity sha512-wRwTpsslgZS5HNyM7uDQYZtxnbI12aGiBZURX3BTR9RFIKKRWpllTsgzHWvj3HKm3Y2Sh5LPC1r0PDCKEhVn9Q==

"@intlify/vue-devtools@9.1.9":
  version "9.1.9"
  resolved "https://registry.npmmirror.com/@intlify/vue-devtools/-/vue-devtools-9.1.9.tgz"
  integrity sha512-YPehH9uL4vZcGXky4Ev5qQIITnHKIvsD2GKGXgqf+05osMUI6WSEQHaN9USRa318Rs8RyyPCiDfmA0hRu3k7og==
  dependencies:
    "@intlify/message-resolver" "9.1.9"
    "@intlify/runtime" "9.1.9"
    "@intlify/shared" "9.1.9"

"@intlify/vue-devtools@9.2.2":
  version "9.2.2"
  resolved "https://registry.npmmirror.com/@intlify/vue-devtools/-/vue-devtools-9.2.2.tgz"
  integrity sha512-+dUyqyCHWHb/UcvY1MlIpO87munedm3Gn6E9WWYdWrMuYLcoIoOEVDWSS8xSwtlPU+kA+MEQTP6Q1iI/ocusJg==
  dependencies:
    "@intlify/core-base" "9.2.2"
    "@intlify/shared" "9.2.2"

"@jridgewell/gen-mapping@^0.1.0":
  version "0.1.1"
  resolved "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
  integrity sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.2"
  resolved "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
  integrity sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.0.3":
  version "3.1.0"
  resolved "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
  integrity sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.14"
  resolved "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
  integrity sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==

"@jridgewell/trace-mapping@^0.3.9":
  version "0.3.15"
  resolved "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.15.tgz"
  integrity sha512-oWZNOULl+UbhsgB51uuZzglikfIKSUBO/M9W2OfEjn7cmqoAiCgmv9lyACTUacZwBz0ITnJ2NqjU8Tx0DHL88g==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@rollup/pluginutils@^4.2.0":
  version "4.2.1"
  resolved "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@rushstack/eslint-patch@^1.1.4":
  version "1.1.4"
  resolved "https://registry.npmmirror.com/@rushstack/eslint-patch/-/eslint-patch-1.1.4.tgz"
  integrity sha512-LwzQKA4vzIct1zNZzBmRKI9QuNpLgTQMEjsQLf3BXuGYb3QPTP4Yjf6mkdX+X1mYttZ808QpOwAzZjv28kq7DA==

"@types/json-schema@^7.0.9":
  version "7.0.11"
  resolved "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz"
  integrity sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==

"@types/lodash-es@^4.17.6":
  version "4.17.6"
  resolved "https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.6.tgz"
  integrity sha512-R+zTeVUKDdfoRxpAryaQNRKk3105Rrgx2CFRClIgRGaqDTdjsm8h6IYA8ir584W3ePzkZfst5xIgDwYrlh9HLg==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*":
  version "4.14.184"
  resolved "https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.184.tgz"
  integrity sha512-RoZphVtHbxPZizt4IcILciSWiC6dcn+eZ8oX9IWEYfDMcocdd42f7NPI6fQj+6zI8y4E0L7gu2pcZKLGTRaV9Q==

"@types/node@^18.7.16":
  version "18.7.16"
  resolved "https://registry.npmmirror.com/@types/node/-/node-18.7.16.tgz"
  integrity sha512-EQHhixfu+mkqHMZl1R2Ovuvn47PUw18azMJOTwSZr9/fhzHNGXAJ0ma0dayRVchprpCj0Kc1K1xKoWaATWF1qg==

"@typescript-eslint/eslint-plugin@^5.0.0":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.35.1.tgz"
  integrity sha512-RBZZXZlI4XCY4Wzgy64vB+0slT9+yAPQRjj/HSaRwUot33xbDjF1oN9BLwOLTewoOI0jothIltZRe9uJCHf8gg==
  dependencies:
    "@typescript-eslint/scope-manager" "5.35.1"
    "@typescript-eslint/type-utils" "5.35.1"
    "@typescript-eslint/utils" "5.35.1"
    debug "^4.3.4"
    functional-red-black-tree "^1.0.1"
    ignore "^5.2.0"
    regexpp "^3.2.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.0.0":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-5.35.1.tgz"
  integrity sha512-XL2TBTSrh3yWAsMYpKseBYTVpvudNf69rPOWXWVBI08My2JVT5jR66eTt4IgQFHA/giiKJW5dUD4x/ZviCKyGg==
  dependencies:
    "@typescript-eslint/scope-manager" "5.35.1"
    "@typescript-eslint/types" "5.35.1"
    "@typescript-eslint/typescript-estree" "5.35.1"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.35.1":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-5.35.1.tgz"
  integrity sha512-kCYRSAzIW9ByEIzmzGHE50NGAvAP3wFTaZevgWva7GpquDyFPFcmvVkFJGWJJktg/hLwmys/FZwqM9EKr2u24Q==
  dependencies:
    "@typescript-eslint/types" "5.35.1"
    "@typescript-eslint/visitor-keys" "5.35.1"

"@typescript-eslint/type-utils@5.35.1":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-5.35.1.tgz"
  integrity sha512-8xT8ljvo43Mp7BiTn1vxLXkjpw8wS4oAc00hMSB4L1/jIiYbjjnc3Qp2GAUOG/v8zsNCd1qwcqfCQ0BuishHkw==
  dependencies:
    "@typescript-eslint/utils" "5.35.1"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.35.1":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/types/-/types-5.35.1.tgz"
  integrity sha512-FDaujtsH07VHzG0gQ6NDkVVhi1+rhq0qEvzHdJAQjysN+LHDCKDKCBRlZFFE0ec0jKxiv0hN63SNfExy0KrbQQ==

"@typescript-eslint/typescript-estree@5.35.1":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-5.35.1.tgz"
  integrity sha512-JUqE1+VRTGyoXlDWWjm6MdfpBYVq+hixytrv1oyjYIBEOZhBCwtpp5ZSvBt4wIA1MKWlnaC2UXl2XmYGC3BoQA==
  dependencies:
    "@typescript-eslint/types" "5.35.1"
    "@typescript-eslint/visitor-keys" "5.35.1"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.35.1":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-5.35.1.tgz"
  integrity sha512-v6F8JNXgeBWI4pzZn36hT2HXXzoBBBJuOYvoQiaQaEEjdi5STzux3Yj8v7ODIpx36i/5s8TdzuQ54TPc5AITQQ==
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@typescript-eslint/scope-manager" "5.35.1"
    "@typescript-eslint/types" "5.35.1"
    "@typescript-eslint/typescript-estree" "5.35.1"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/visitor-keys@5.35.1":
  version "5.35.1"
  resolved "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-5.35.1.tgz"
  integrity sha512-cEB1DvBVo1bxbW/S5axbGPE6b7FIMAbo3w+AGq6zNDA7+NYJOIkKj/sInfTv4edxd4PxJSgdN4t6/pbvgA+n5g==
  dependencies:
    "@typescript-eslint/types" "5.35.1"
    eslint-visitor-keys "^3.3.0"

"@vitejs/plugin-legacy@^1.8.2":
  version "1.8.2"
  resolved "https://registry.npmmirror.com/@vitejs/plugin-legacy/-/plugin-legacy-1.8.2.tgz"
  integrity sha512-NCOKU+pU+cxLMR9P9RTolEuOK+h+zYBXlknj+zGcKSj/NXBZYgA1GAH1FnO4zijoWRiTaiOm2ha9LQrELE7XHg==
  dependencies:
    "@babel/standalone" "^7.17.11"
    core-js "^3.22.3"
    magic-string "^0.26.1"
    regenerator-runtime "^0.13.9"
    systemjs "^6.12.1"

"@vitejs/plugin-vue-jsx@^1.3.10":
  version "1.3.10"
  resolved "https://registry.npmmirror.com/@vitejs/plugin-vue-jsx/-/plugin-vue-jsx-1.3.10.tgz"
  integrity sha512-Cf5zznh4yNMiEMBfTOztaDVDmK1XXfgxClzOSUVUc8WAmHzogrCUeM8B05ABzuGtg0D1amfng+mUmSIOFGP3Pw==
  dependencies:
    "@babel/core" "^7.17.9"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-transform-typescript" "^7.16.8"
    "@rollup/pluginutils" "^4.2.0"
    "@vue/babel-plugin-jsx" "^1.1.1"
    hash-sum "^2.0.0"

"@vitejs/plugin-vue@^2.3.3":
  version "2.3.4"
  resolved "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-2.3.4.tgz"
  integrity sha512-IfFNbtkbIm36O9KB8QodlwwYvTEsJb4Lll4c2IwB3VHc2gie2mSPtSzL0eYay7X2jd/2WX02FjSGTWR6OPr/zg==

"@vue/babel-helper-vue-transform-on@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmmirror.com/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.0.2.tgz"
  integrity sha512-hz4R8tS5jMn8lDq6iD+yWL6XNB699pGIVLk7WSJnn1dbpjaazsjZQkieJoRX6gW5zpYSCFqQ7jUquPNY65tQYA==

"@vue/babel-plugin-jsx@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmmirror.com/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.1.1.tgz"
  integrity sha512-j2uVfZjnB5+zkcbc/zsOc0fSNGCMMjaEXP52wdwdIfn0qjFfEYpYZBFKFg+HHnQeJCVrjOeO0YxgaL7DMrym9w==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    camelcase "^6.0.0"
    html-tags "^3.1.0"
    svg-tags "^1.0.0"

"@vue/compiler-core@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.2.37.tgz"
  integrity sha512-81KhEjo7YAOh0vQJoSmAD68wLfYqJvoiD4ulyedzF+OEk/bk6/hx3fTNVfuzugIIaTrOx4PGx6pAiBRe5e9Zmg==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/shared" "3.2.37"
    estree-walker "^2.0.2"
    source-map "^0.6.1"

"@vue/compiler-dom@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.2.37.tgz"
  integrity sha512-yxJLH167fucHKxaqXpYk7x8z7mMEnXOw3G2q62FTkmsvNxu4FQSu5+3UMb+L7fjKa26DEzhrmCxAgFLLIzVfqQ==
  dependencies:
    "@vue/compiler-core" "3.2.37"
    "@vue/shared" "3.2.37"

"@vue/compiler-sfc@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.2.37.tgz"
  integrity sha512-+7i/2+9LYlpqDv+KTtWhOZH+pa8/HnX/905MdVmAcI/mPQOBwkHHIzrsEsucyOIZQYMkXUiTkmZq5am/NyXKkg==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.37"
    "@vue/compiler-dom" "3.2.37"
    "@vue/compiler-ssr" "3.2.37"
    "@vue/reactivity-transform" "3.2.37"
    "@vue/shared" "3.2.37"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"
    postcss "^8.1.10"
    source-map "^0.6.1"

"@vue/compiler-ssr@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.2.37.tgz"
  integrity sha512-7mQJD7HdXxQjktmsWp/J67lThEIcxLemz1Vb5I6rYJHR5vI+lON3nPGOH3ubmbvYGt8xEUaAr1j7/tIFWiEOqw==
  dependencies:
    "@vue/compiler-dom" "3.2.37"
    "@vue/shared" "3.2.37"

"@vue/devtools-api@^6.1.4", "@vue/devtools-api@^6.2.1":
  version "6.2.1"
  resolved "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.2.1.tgz"
  integrity sha512-OEgAMeQXvCoJ+1x8WyQuVZzFo0wcyCmUR3baRVLmKBo1LmYZWMlRiXlux5jd0fqVJu6PfDbOrZItVqUEzLobeQ==

"@vue/eslint-config-prettier@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmmirror.com/@vue/eslint-config-prettier/-/eslint-config-prettier-7.0.0.tgz"
  integrity sha512-/CTc6ML3Wta1tCe1gUeO0EYnVXfo3nJXsIhZ8WJr3sov+cGASr6yuiibJTL6lmIBm7GobopToOuB3B6AWyV0Iw==
  dependencies:
    eslint-config-prettier "^8.3.0"
    eslint-plugin-prettier "^4.0.0"

"@vue/eslint-config-typescript@^11.0.0":
  version "11.0.0"
  resolved "https://registry.npmmirror.com/@vue/eslint-config-typescript/-/eslint-config-typescript-11.0.0.tgz"
  integrity sha512-txuRzxnQVmtUvvy9UyWUy9sHWXNeRPGmSPqP53hRtaiUeCTAondI9Ho9GQYI/8/eWljYOST7iA4Aa8sANBkWaA==
  dependencies:
    "@typescript-eslint/eslint-plugin" "^5.0.0"
    "@typescript-eslint/parser" "^5.0.0"
    vue-eslint-parser "^9.0.0"

"@vue/reactivity-transform@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/reactivity-transform/-/reactivity-transform-3.2.37.tgz"
  integrity sha512-IWopkKEb+8qpu/1eMKVeXrK0NLw9HicGviJzhJDEyfxTR9e1WtpnnbYkJWurX6WwoFP0sz10xQg8yL8lgskAZg==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.37"
    "@vue/shared" "3.2.37"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"

"@vue/reactivity@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.2.37.tgz"
  integrity sha512-/7WRafBOshOc6m3F7plwzPeCu/RCVv9uMpOwa/5PiY1Zz+WLVRWiy0MYKwmg19KBdGtFWsmZ4cD+LOdVPcs52A==
  dependencies:
    "@vue/shared" "3.2.37"

"@vue/runtime-core@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.2.37.tgz"
  integrity sha512-JPcd9kFyEdXLl/i0ClS7lwgcs0QpUAWj+SKX2ZC3ANKi1U4DOtiEr6cRqFXsPwY5u1L9fAjkinIdB8Rz3FoYNQ==
  dependencies:
    "@vue/reactivity" "3.2.37"
    "@vue/shared" "3.2.37"

"@vue/runtime-dom@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.2.37.tgz"
  integrity sha512-HimKdh9BepShW6YozwRKAYjYQWg9mQn63RGEiSswMbW+ssIht1MILYlVGkAGGQbkhSh31PCdoUcfiu4apXJoPw==
  dependencies:
    "@vue/runtime-core" "3.2.37"
    "@vue/shared" "3.2.37"
    csstype "^2.6.8"

"@vue/server-renderer@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.2.37.tgz"
  integrity sha512-kLITEJvaYgZQ2h47hIzPh2K3jG8c1zCVbp/o/bzQOyvzaKiCquKS7AaioPI28GNxIsE/zSx+EwWYsNxDCX95MA==
  dependencies:
    "@vue/compiler-ssr" "3.2.37"
    "@vue/shared" "3.2.37"

"@vue/shared@3.2.37":
  version "3.2.37"
  resolved "https://registry.npmmirror.com/@vue/shared/-/shared-3.2.37.tgz"
  integrity sha512-4rSJemR2NQIo9Klm1vabqWjD8rs/ZaJSzMxkMNeJS6lHiUjjUeYFbooN19NgFjztubEKh3WlZUeOLVdbbUWHsw==

accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn-node@^1.8.2:
  version "1.8.2"
  resolved "https://registry.npmmirror.com/acorn-node/-/acorn-node-1.8.2.tgz"
  integrity sha512-8mt+fslDufLYntIoPAaIMUe/lrbrehIiwmR3t2k9LljIzoigEPF27eLk2hy8zSGzmR/ogr7zbRKINMo1u0yh5A==
  dependencies:
    acorn "^7.0.0"
    acorn-walk "^7.0.0"
    xtend "^4.0.2"

acorn-walk@^7.0.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-7.2.0.tgz"
  integrity sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA==

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.8.0:
  version "8.8.0"
  resolved "https://registry.npmmirror.com/acorn/-/acorn-8.8.0.tgz"
  integrity sha512-QOxyigPVrpZ2GXT+PFyZTl6TtOFc5egxHIP9IlQ+RbupQuX4RkT/Bee4/kQuC02Xkzg84JcT7oLYtDIQxp+v7w==

acorn@^7.0.0:
  version "7.4.1"
  resolved "https://registry.npmmirror.com/acorn/-/acorn-7.4.1.tgz"
  integrity sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==

address@^1.1.2:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/address/-/address-1.2.0.tgz"
  integrity sha512-tNEZYz5G/zYunxFm7sfhAxkXEuLj3K6BKwv6ZURlsF6yiUQ65z0Q2wZW9L5cPUl9ocofGvXOdFYbFHp0+6MOig==

adm-zip@^0.5.9:
  version "0.5.9"
  resolved "https://registry.npmmirror.com/adm-zip/-/adm-zip-0.5.9.tgz"
  integrity sha512-s+3fXLkeeLjZ2kLjCBwQufpI5fuN+kIGBxu6530nVQZGVol0d7Y/M88/xw9HGGUcJjKf8LutN3VPRUBq6N7Ajg==

ajv@^6.10.0, ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.2.tgz"
  integrity sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmmirror.com/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

autoprefixer@^10.4.8:
  version "10.4.8"
  resolved "https://registry.npmmirror.com/autoprefixer/-/autoprefixer-10.4.8.tgz"
  integrity sha512-75Jr6Q/XpTqEf6D2ltS5uMewJIx5irCU1oBYJrWjFenq/m12WRRrz6g15L1EIoYvPLXTbEry7rDOwrcYNj77xw==
  dependencies:
    browserslist "^4.21.3"
    caniuse-lite "^1.0.30001373"
    fraction.js "^4.2.0"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64url@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/base64url/-/base64url-3.0.1.tgz"
  integrity sha512-ir1UPr3dkwexU7FdV8qBBbNDRUhMmIekYMFZfi+C/sLNnRESKPl23nB9b2pltqfOQNnGzsDdId90AEtG5tCx4A==

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/big.js/-/big.js-5.2.2.tgz"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==

body-parser@1.20.0:
  version "1.20.0"
  resolved "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.0.tgz"
  integrity sha512-DfJ+q6EPcGKZD1QWUjSpqp+Q7bDQTsQIF4zfUAtZ6qk+H/3/QRhg9CEp39ss+/T2vw0+HaidC0ecJj/DRLIaKg==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.10.3"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz"
  integrity sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.20.2, browserslist@^4.21.3, "browserslist@>= 4.21.0":
  version "4.21.3"
  resolved "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.3.tgz"
  integrity sha512-898rgRXLAyRkM1GryrrBHGkqA5hlpkV5MhtZwg9QXeiyLUYs2k00Un05aX5l2/yJIOObYKOpS2JNo8nJDE7fWQ==
  dependencies:
    caniuse-lite "^1.0.30001370"
    electron-to-chromium "^1.4.202"
    node-releases "^2.0.6"
    update-browserslist-db "^1.0.5"

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cac@6.7.9:
  version "6.7.9"
  resolved "https://registry.npmmirror.com/cac/-/cac-6.7.9.tgz"
  integrity sha512-XN5qEpfNQCJ8jRaZgitSkkukjMRCGio+X3Ks5KUbGGlPbV+pSem1l9VuzooCBXOiMFshUZgyYqg6rgN8rjkb/w==

call-bind@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz"
  integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

camelcase@^6.0.0:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001370, caniuse-lite@^1.0.30001373:
  version "1.0.30001457"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001457.tgz"
  integrity sha512-SDIV6bgE1aVbK6XyxdURbUE89zY7+k1BBBaOwYwkNCglXlel/E7mELiHC64HQ+W0xSKlqWhV9Wh7iHxUjMs4fA==

chalk@^2.0.0:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.5.3, "chokidar@>=3.0.0 <4.0.0":
  version "3.5.3"
  resolved "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz"
  integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==

compare-versions@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/compare-versions/-/compare-versions-3.6.0.tgz"
  integrity sha512-W6Af2Iw1z4CB7q4uU4hv646dW9GQuBM+YpC0UvUCWSD8w90SJjp+ujJuXaEMtAXBtSqGfMPuFOVn4/+FlaqfBA==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/content-type/-/content-type-1.0.4.tgz"
  integrity sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==

convert-source-map@^1.7.0:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-1.8.0.tgz"
  integrity sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA==
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==

cookie@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmmirror.com/cookie/-/cookie-0.5.0.tgz"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

copy-text-to-clipboard@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/copy-text-to-clipboard/-/copy-text-to-clipboard-3.0.1.tgz"
  integrity sha512-rvVsHrpFcL4F2P8ihsoLdFHmd404+CMg71S756oRSeQgqk51U3kicGdnvfkrxva0xXH92SjGS62B0XIJsbh+9Q==

core-js@^3.11.0, core-js@^3.22.3:
  version "3.25.0"
  resolved "https://registry.npmmirror.com/core-js/-/core-js-3.25.0.tgz"
  integrity sha512-CVU1xvJEfJGhyCpBrzzzU1kjCfgsGUxhEvwUV2e/cOedYWHdmluamx+knDnmhqALddMG16fZvIqvs9aijsHHaA==

cross-env@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/cross-env/-/cross-env-7.0.3.tgz"
  integrity sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn@^7.0.1, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-font-size-keywords@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/css-font-size-keywords/-/css-font-size-keywords-1.0.0.tgz"
  integrity sha512-Q+svMDbMlelgCfH/RVDKtTDaf5021O486ZThQPIpahnIjUkMUslC+WuOQSWTgGSrNCH08Y7tYNEmmy0hkfMI8Q==

css-font-stretch-keywords@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/css-font-stretch-keywords/-/css-font-stretch-keywords-1.0.1.tgz"
  integrity sha512-KmugPO2BNqoyp9zmBIUGwt58UQSfyk1X5DbOlkb2pckDXFSAfjsD5wenb88fNrD6fvS+vu90a/tsPpb9vb0SLg==

css-font-style-keywords@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/css-font-style-keywords/-/css-font-style-keywords-1.0.1.tgz"
  integrity sha512-0Fn0aTpcDktnR1RzaBYorIxQily85M2KXRpzmxQPgh8pxUN9Fcn00I8u9I3grNr1QXVgCl9T5Imx0ZwKU973Vg==

css-font-weight-keywords@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/css-font-weight-keywords/-/css-font-weight-keywords-1.0.0.tgz"
  integrity sha512-5So8/NH+oDD+EzsnF4iaG4ZFHQ3vaViePkL1ZbZ5iC/KrsCY+WHq/lvOgrtmuOQ9pBBZ1ADGpaf+A4lj1Z9eYA==

css-list-helpers@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/css-list-helpers/-/css-list-helpers-2.0.0.tgz"
  integrity sha512-9Bj8tZ0jWbAM3u/U6m/boAzAwLPwtjzFvwivr2piSvyVa3K3rChJzQy4RIHkNkKiZCHrEMWDJWtTR8UyVhdDnQ==

css-system-font-keywords@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/css-system-font-keywords/-/css-system-font-keywords-1.0.0.tgz"
  integrity sha512-1umTtVd/fXS25ftfjB71eASCrYhilmEsvDEI6wG/QplnmlfmVM5HkZ/ZX46DT5K3eblFPgLUHt5BRCb0YXkSFA==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

csstype@^2.6.8:
  version "2.6.20"
  resolved "https://registry.npmmirror.com/csstype/-/csstype-2.6.20.tgz"
  integrity sha512-/WwNkdXfckNgw6S5R125rrW8ez139lBHWouiBvX8dfMFtcn6V81REDqnH7+CRpRipfYlyU1CmOnOxrmGcFOjeA==

debug@^4.1.0, debug@^4.1.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4:
  version "4.3.4"
  resolved "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmmirror.com/default-gateway/-/default-gateway-6.0.3.tgz"
  integrity sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==
  dependencies:
    execa "^5.0.0"

defined@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/defined/-/defined-1.0.0.tgz"
  integrity sha512-Y2caI5+ZwS5c3RiNDJ6u53VhQHv+hHKwhkI1iHvceKUHw9Df6EK2zRLfjejRgMuCuxK7PfSWIMwWecceVvThjQ==

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

destroy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detective@^5.2.1:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/detective/-/detective-5.2.1.tgz"
  integrity sha512-v9XE1zRnz1wRtgurGu0Bs8uHKFSTdteYZNbIPFVhUZ39L/S79ppMpdmVOZAnoz1jfEFodc48n6MX483Xo3t1yw==
  dependencies:
    acorn-node "^1.8.2"
    defined "^1.0.0"
    minimist "^1.2.6"

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==

electron-to-chromium@^1.4.202:
  version "1.4.230"
  resolved "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.230.tgz"
  integrity sha512-3pwjAK0qHSDN9+YAF4fJknsSruP7mpjdWzUSruIJD/JCH77pEh0SorEyb3xVaKkfwk2tzjOt2D8scJ0KAdfXLA==

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==

es-module-lexer@^0.9.3:
  version "0.9.3"
  resolved "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-0.9.3.tgz"
  integrity sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==

esbuild-darwin-arm64@0.14.54:
  version "0.14.54"
  resolved "https://registry.npmmirror.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.14.54.tgz"
  integrity sha512-OPafJHD2oUPyvJMrsCvDGkRrVCar5aVyHfWGQzY1dWnzErjrDuSETxwA2HSsyg2jORLY8yBfzc1MIpUkXlctmw==

esbuild@^0.14.27:
  version "0.14.54"
  resolved "https://registry.npmmirror.com/esbuild/-/esbuild-0.14.54.tgz"
  integrity sha512-Cy9llcy8DvET5uznocPyqL3BFRrFXSVqbgpMJ9Wz8oVjZlh/zUSNbPRbov0VX7VxN2JH1Oa0uNxZ7eLRb62pJA==
  optionalDependencies:
    "@esbuild/linux-loong64" "0.14.54"
    esbuild-android-64 "0.14.54"
    esbuild-android-arm64 "0.14.54"
    esbuild-darwin-64 "0.14.54"
    esbuild-darwin-arm64 "0.14.54"
    esbuild-freebsd-64 "0.14.54"
    esbuild-freebsd-arm64 "0.14.54"
    esbuild-linux-32 "0.14.54"
    esbuild-linux-64 "0.14.54"
    esbuild-linux-arm "0.14.54"
    esbuild-linux-arm64 "0.14.54"
    esbuild-linux-mips64le "0.14.54"
    esbuild-linux-ppc64le "0.14.54"
    esbuild-linux-riscv64 "0.14.54"
    esbuild-linux-s390x "0.14.54"
    esbuild-netbsd-64 "0.14.54"
    esbuild-openbsd-64 "0.14.54"
    esbuild-sunos-64 "0.14.54"
    esbuild-windows-32 "0.14.54"
    esbuild-windows-64 "0.14.54"
    esbuild-windows-arm64 "0.14.54"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz"
  integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-config-prettier@^8.3.0:
  version "8.5.0"
  resolved "https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.5.0.tgz"
  integrity sha512-obmWKLUNCnhtQRKc+tmnYuQl0pFU1ibYJQ5BGhTVB08bHe9wC8qUeG7c08dj9XX+AuPj1YSGSQIHl1pnDHZR0Q==

eslint-plugin-prettier@^4.0.0:
  version "4.2.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz"
  integrity sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-vue@^9.0.0, eslint-plugin-vue@^9.4.0:
  version "9.4.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-9.4.0.tgz"
  integrity sha512-Nzz2QIJ8FG+rtJaqT/7/ru5ie2XgT9KCudkbN0y3uFYhQ41nuHEaboLAiqwMcK006hZPQv/rVMRhUIwEGhIvfQ==
  dependencies:
    eslint-utils "^3.0.0"
    natural-compare "^1.4.0"
    nth-check "^2.0.1"
    postcss-selector-parser "^6.0.9"
    semver "^7.3.5"
    vue-eslint-parser "^9.0.1"
    xml-name-validator "^4.0.0"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.1.1.tgz"
  integrity sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-3.0.0.tgz"
  integrity sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==

eslint-visitor-keys@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz"
  integrity sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==

eslint@*, "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^6.2.0 || ^7.0.0 || ^8.0.0", eslint@^8.22.0, "eslint@>= 7.28.0", eslint@>=5, eslint@>=6.0.0, eslint@>=7.0.0, eslint@>=7.28.0:
  version "8.22.0"
  resolved "https://registry.npmmirror.com/eslint/-/eslint-8.22.0.tgz"
  integrity sha512-ci4t0sz6vSRKdmkOGmprBo6fmI4PrphDFMy5JEq/fNS0gQkJM3rLmrqcp8ipMcdobH3KtUP40KniAE9W19S4wA==
  dependencies:
    "@eslint/eslintrc" "^1.3.0"
    "@humanwhocodes/config-array" "^0.10.4"
    "@humanwhocodes/gitignore-to-minimatch" "^1.0.2"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.1.1"
    eslint-utils "^3.0.0"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.3"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    functional-red-black-tree "^1.0.1"
    glob-parent "^6.0.1"
    globals "^13.15.0"
    globby "^11.1.0"
    grapheme-splitter "^1.0.4"
    ignore "^5.2.0"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    regexpp "^3.2.0"
    strip-ansi "^6.0.1"
    strip-json-comments "^3.1.0"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^9.3.1, espree@^9.3.2, espree@^9.3.3:
  version "9.3.3"
  resolved "https://registry.npmmirror.com/espree/-/espree-9.3.3.tgz"
  integrity sha512-ORs1Rt/uQTqUKjDdGCyrtYxbazf5umATSf/K4qxjmZHORR6HJk+2s/2Pqe+Kk49HHINC/xNIrGfgh8sZcll0ng==
  dependencies:
    acorn "^8.8.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.3.0"

esquery@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/esquery/-/esquery-1.4.0.tgz"
  integrity sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^2.0.1, estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz"
  integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

execa@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

execa@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/execa/-/execa-6.1.0.tgz"
  integrity sha512-QVWlX2e50heYJcCPG0iWtf8r0xjEYfz/OYLGDYH+IyjWezzPNxz63qNFOu0l4YftGWuizFVZHHs8PrLU5p2IDA==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^3.0.1"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

express@^4.17.1:
  version "4.18.1"
  resolved "https://registry.npmmirror.com/express/-/express-4.18.1.tgz"
  integrity sha512-zZBcOX9TfehHQhtupq57OF8lFZ3UZi08Y97dwFCkD8p9d/d2Y3M+ykKcwaMDEL+4qyUolgBDX6AblpR3fL212Q==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.0"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.10.3"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-diff@^1.1.2:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.2.0.tgz"
  integrity sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w==

fast-glob@^3.2.11, fast-glob@^3.2.9:
  version "3.2.11"
  resolved "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.2.11.tgz"
  integrity sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.npmmirror.com/fastq/-/fastq-1.13.0.tgz"
  integrity sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==
  dependencies:
    reusify "^1.0.4"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/finalhandler/-/finalhandler-1.2.0.tgz"
  integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.0.4.tgz"
  integrity sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.7"
  resolved "https://registry.npmmirror.com/flatted/-/flatted-3.2.7.tgz"
  integrity sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

fraction.js@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/fraction.js/-/fraction.js-4.2.0.tgz"
  integrity sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmmirror.com/fresh/-/fresh-0.5.2.tgz"
  integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==

fs-extra@^10.0.0, fs-extra@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz"
  integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  integrity sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==

generic-names@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/generic-names/-/generic-names-4.0.0.tgz"
  integrity sha512-ySFolZQfw9FoDb3ed9d80Cm9f0+r7qj+HJkWjeD9RBfpxEVTlVhol+gvaQB/78WbwYfbnNh8nWHHBSlg072y6A==
  dependencies:
    loader-utils "^3.2.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-intrinsic@^1.0.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.2.tgz"
  integrity sha512-Jfm3OyCxHh9DJyc28qGk+JmfkpO41A4XkneDSujN9MDXrm4oDKdHvndhZ2dN94+ERNfkYJWDclW6k2L/ZGHjXA==
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-stream@^6.0.0, get-stream@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob@^7.1.3:
  version "7.2.3"
  resolved "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.15.0:
  version "13.17.0"
  resolved "https://registry.npmmirror.com/globals/-/globals-13.17.0.tgz"
  integrity sha512-1C+6nQRb1GwGMKm2dH/E7enFAMxGTmGI7/dEdhy/DNelv85w9B72t3uc5frtMNXIbzrarJJ/lTCjcaZwbLJmyw==
  dependencies:
    type-fest "^0.20.2"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.10"
  resolved "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.10.tgz"
  integrity sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==

grapheme-splitter@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz"
  integrity sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/has/-/has-1.0.3.tgz"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/hash-sum/-/hash-sum-2.0.0.tgz"
  integrity sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==

html-tags@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/html-tags/-/html-tags-3.2.0.tgz"
  integrity sha512-vy7ClnArOZwCnqZgvv+ddgHgJiAFXe3Ge9ML5/mBctVJoUoYPCdxVucOywjDARn6CVoh3dRSFdPHy2sX80L0Wg==

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

human-signals@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/human-signals/-/human-signals-3.0.1.tgz"
  integrity sha512-rQLskxnM/5OCldHo+wNXbpVgDn5A17CUoKX+7Sokwaknlq7CdSnphy0W39GU8dw59XiCXmFXDg4fRuckQRKewQ==

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz"
  integrity sha512-chIaY3Vh2mh2Q3RGXttaDIzeiPvaVXJ+C4DAh/w3c37SKZ/U6PGMmuicR2EQQp9bKG8zLMCl7I+PtIoOOPp8Gg==

icss-utils@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/icss-utils/-/icss-utils-5.1.0.tgz"
  integrity sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==

ignore@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/ignore/-/ignore-5.2.0.tgz"
  integrity sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==

immutable@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/immutable/-/immutable-4.1.0.tgz"
  integrity sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

invert-kv@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/invert-kv/-/invert-kv-3.0.1.tgz"
  integrity sha512-CYdFeFexxhv/Bcny+Q0BfOV+ltRlJcd4BBZBYFX/O0u4npJrgZtIcjokegtiSMAvlMTJ+Koq0GBCc//3bueQxw==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.9.0:
  version "2.10.0"
  resolved "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.10.0.tgz"
  integrity sha512-Erxj2n/LDAZ7H8WNJXd9tw38GYM3dv8rk8Zcs+jJuxYTW7sozH+SS8NtrSjVL1/vpLvWi1hxy96IzjJ3EHTJJg==
  dependencies:
    has "^1.0.3"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/is-stream/-/is-stream-3.0.0.tgz"
  integrity sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmmirror.com/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json5@^2.1.2, json5@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/json5/-/json5-2.2.1.tgz"
  integrity sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==

jsonc-parser@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.1.0.tgz"
  integrity sha512-DRf0QjnNeCUds3xTjKlQQ3DpJD51GvDjJfnxUVWg6PZTo2otSm+slzNAxU/35hF8/oJIKoG9slq30JYOsF2azg==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

lcid@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/lcid/-/lcid-3.1.1.tgz"
  integrity sha512-M6T051+5QCGLBQb8id3hdvIW8+zeFV2FyBGFS9IEK5H9Wt4MueD4bW1eWikpHgZp+5xR3l5c8pZUkQsIA0BFZg==
  dependencies:
    invert-kv "^3.0.0"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

licia@^1.29.0:
  version "1.37.0"
  resolved "https://registry.npmmirror.com/licia/-/licia-1.37.0.tgz"
  integrity sha512-jX49+WmzikOPGNrcy/giS23HCI8Pb7RF585Ei5d7oWF4WMelaZWv4odqQNdT0jtHkoUxqSvPz67Jvyq06xamUA==

lilconfig@^2.0.5, lilconfig@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/lilconfig/-/lilconfig-2.0.6.tgz"
  integrity sha512-9JROoBW7pobfsx+Sq2JsASvCo6Pfo6WWoUW79HuB1BCoBXD4PLWJPqDF6fNj67pqBYTbAHkE57M1kS/+L1neOg==

loader-utils@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.2.tgz"
  integrity sha512-TM57VeHptv569d/GKh6TAYdzKblwDNiumOdkFnejjD0XwTH87K90w3O7AiJRqdQoXygvi1VQTJTLGhJl7WqA7A==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

loader-utils@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/loader-utils/-/loader-utils-3.2.0.tgz"
  integrity sha512-HVl9ZqccQihZ7JM85dco1MvO9G+ONvxoGa9rkhzFsneGLKSUg1gJf9bWzhRhcvm2qChhWpebQhP44qxjKIUCaQ==

localstorage-polyfill@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/localstorage-polyfill/-/localstorage-polyfill-1.0.1.tgz"
  integrity sha512-m4iHVZxFH5734oQcPKU08025gIz2+4bjWR9lulP8ZYxEJR0BpA0w32oJmkzh8y3UI9ci7xCBehQDc3oA1X+VHw==

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  integrity sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

magic-string@^0.25.7:
  version "0.25.9"
  resolved "https://registry.npmmirror.com/magic-string/-/magic-string-0.25.9.tgz"
  integrity sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==
  dependencies:
    sourcemap-codec "^1.4.8"

magic-string@^0.26.1:
  version "0.26.2"
  resolved "https://registry.npmmirror.com/magic-string/-/magic-string-0.26.2.tgz"
  integrity sha512-NzzlXpclt5zAbmo6h6jNc8zl2gNRGHvmsZW4IvZhTC4W7k4OlLP+S5YLussa/r3ixNT66KOQfNORlXHSOy/X4A==
  dependencies:
    sourcemap-codec "^1.4.8"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/merge/-/merge-2.1.1.tgz"
  integrity sha512-jz+Cfrg9GWOZbQAnDQ4hlVnQky+341Yk5ru8bZSe6sIDTCIg8n9i/u7hSQGSVOF3C7lH6mGtqjkiT9G4wFLL0w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz"
  integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==

micromatch@^4.0.4:
  version "4.0.5"
  resolved "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/mime/-/mime-3.0.0.tgz"
  integrity sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-4.0.0.tgz"
  integrity sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.6:
  version "1.2.6"
  resolved "https://registry.npmmirror.com/minimist/-/minimist-1.2.6.tgz"
  integrity sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q==

module-alias@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmmirror.com/module-alias/-/module-alias-2.2.2.tgz"
  integrity sha512-A/78XjoX2EmNvppVWEhM2oGk3x4lLxnkEA4jTbaK97QKSDjkIoOsKQlfylt/d3kKKi596Qy3NP5XrXJ6fZIC9Q==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mutation-observer@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/mutation-observer/-/mutation-observer-1.0.3.tgz"
  integrity sha512-M/O/4rF2h776hV7qGMZUH3utZLO/jK7p8rnNgGkjKUw8zCGjRQPxB8z6+5l8+VjRUQ3dNYu4vjqXYLr+U8ZVNA==

nanoid@^3.3.4:
  version "3.3.4"
  resolved "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.4.tgz"
  integrity sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmmirror.com/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

node-releases@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.6.tgz"
  integrity sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

npm-run-path@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-5.1.0.tgz"
  integrity sha512-sJOdmRGrY2sjNTRMbSvluQqg+8X7ZK61yvzBEIDhz4f8z1TZFYABsqjjCBd/0PUNE9M6QDgHJXQkGUEm7Q+l9Q==
  dependencies:
    path-key "^4.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz"
  integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
  dependencies:
    boolbase "^1.0.0"

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-inspect@^1.9.0:
  version "1.12.2"
  resolved "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.2.tgz"
  integrity sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/onetime/-/onetime-6.0.0.tgz"
  integrity sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==
  dependencies:
    mimic-fn "^4.0.0"

optionator@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npmmirror.com/optionator/-/optionator-0.9.1.tgz"
  integrity sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

os-locale-s-fix@^1.0.8-fix-1:
  version "1.0.8-fix-1"
  resolved "https://registry.npmmirror.com/os-locale-s-fix/-/os-locale-s-fix-1.0.8-fix-1.tgz"
  integrity sha512-Sv0OvhPiMutICiwORAUefv02DCPb62IelBmo8ZsSrRHyI3FStqIWZvjqDkvtjU+lcujo7UNir+dCwKSqlEQ/5w==
  dependencies:
    lcid "^3.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-css-font@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/parse-css-font/-/parse-css-font-4.0.0.tgz"
  integrity sha512-lnY7dTUfjRXsSo5G5C639L8RaBBaVSgL+5hacIFKsNHzeCJQ5SFSZv1DZmc7+wZv/22PFGOq2YbaEHLdaCS/mQ==
  dependencies:
    css-font-size-keywords "^1.0.0"
    css-font-stretch-keywords "^1.0.1"
    css-font-style-keywords "^1.0.1"
    css-font-weight-keywords "^1.0.0"
    css-list-helpers "^2.0.0"
    css-system-font-keywords "^1.0.0"
    unquote "^1.1.1"

parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-key@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-key/-/path-key-4.0.0.tgz"
  integrity sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pinia@^2.0.20:
  version "2.0.20"
  resolved "https://registry.npmmirror.com/pinia/-/pinia-2.0.20.tgz"
  integrity sha512-fdHHumXW/0U5HhxmY1emo3I4z85p8NJPdbtFQSlmJXFe3ktuF0pYNVgVtk2q+j2zCtTufY763xzaEMx0t6T59g==
  dependencies:
    "@vue/devtools-api" "^6.2.1"
    vue-demi "*"

postcss-import@^14.0.2, postcss-import@^14.1.0:
  version "14.1.0"
  resolved "https://registry.npmmirror.com/postcss-import/-/postcss-import-14.1.0.tgz"
  integrity sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/postcss-js/-/postcss-js-4.0.0.tgz"
  integrity sha512-77QESFBwgX4irogGVPgQ5s07vLvFqWr228qZY+w6lW599cRlK/HmnlivnnVUxkjHnCu4J16PDMHcH+e+2HbvTQ==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^3.1.1, postcss-load-config@^3.1.4:
  version "3.1.4"
  resolved "https://registry.npmmirror.com/postcss-load-config/-/postcss-load-config-3.1.4.tgz"
  integrity sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg==
  dependencies:
    lilconfig "^2.0.5"
    yaml "^1.10.2"

postcss-modules-extract-imports@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz"
  integrity sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==

postcss-modules-local-by-default@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.0.tgz"
  integrity sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ==
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz"
  integrity sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg==
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  integrity sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==
  dependencies:
    icss-utils "^5.0.0"

postcss-modules@^4.3.0:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/postcss-modules/-/postcss-modules-4.3.1.tgz"
  integrity sha512-ItUhSUxBBdNamkT3KzIZwYNNRFKmkJrofvC2nWab3CPKhYBQ1f27XXh1PAPE27Psx58jeelPsxWB/+og+KEH0Q==
  dependencies:
    generic-names "^4.0.0"
    icss-replace-symbols "^1.1.0"
    lodash.camelcase "^4.3.0"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.0"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"
    string-hash "^1.1.1"

postcss-nested@5.0.6:
  version "5.0.6"
  resolved "https://registry.npmmirror.com/postcss-nested/-/postcss-nested-5.0.6.tgz"
  integrity sha512-rKqm2Fk0KbA8Vt3AdGN0FB9OBOMDVajMG6ZCf/GoHgdxUJ4sBFp0A/uMIRm+MJUdo33YXEtjqIz8u7DAp8B7DA==
  dependencies:
    postcss-selector-parser "^6.0.6"

postcss-rem-to-responsive-pixel@^5.1.3:
  version "5.1.3"
  resolved "https://registry.npmmirror.com/postcss-rem-to-responsive-pixel/-/postcss-rem-to-responsive-pixel-5.1.3.tgz"
  integrity sha512-qi17MnTWzJ/3PnPrlspG5njMgmUomLXdgRc4AGqjfyMzn896V6tB4u5jW3xjtkxMM5IHB1z8LHnS81cLMpQ2Kg==

postcss-selector-parser@^6.0.10, postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.6, postcss-selector-parser@^6.0.9:
  version "6.0.10"
  resolved "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz"
  integrity sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.0.0, postcss@^8.1.0, postcss@^8.1.10, postcss@^8.2.14, postcss@^8.3.3, postcss@^8.4.13, postcss@^8.4.14, postcss@^8.4.16, postcss@>=8.0.9:
  version "8.4.16"
  resolved "https://registry.npmmirror.com/postcss/-/postcss-8.4.16.tgz"
  integrity sha512-ipHE1XBvKzm5xI7hiHCZJCSugxvsdq2mPnsq5+UF+VHCjiBvtDrlxJfMBToWaP9D5XlgNmcFGqoHmUn0EYEaRQ==
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.7.1, "prettier@>= 2.0.0", prettier@>=2.0.0:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/prettier/-/prettier-2.7.1.tgz"
  integrity sha512-ujppO+MkdPqoVINuDFDRLClm7D78qbDt0/NR+wp5FqEZOoTNAjPHWj17QRhu7geIHJfcNhRk1XVQmF8Bp3ye+g==

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

punycode@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/punycode/-/punycode-2.1.1.tgz"
  integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==

qrcode-reader@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/qrcode-reader/-/qrcode-reader-1.0.4.tgz"
  integrity sha512-rRjALGNh9zVqvweg1j5OKIQKNsw3bLC+7qwlnead5K/9cb1cEIAGkwikt/09U0K+2IDWGD9CC6SP7tHAjUeqvQ==

qrcode-terminal@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmmirror.com/qrcode-terminal/-/qrcode-terminal-0.12.0.tgz"
  integrity sha512-EXtzRZmC+YGmGlDFbXKxQiMZNwCLEO6BANKXG4iCtSIM0yqc/pappSx3RIKr4r0uh5JsBckOXeKrB3Iz7mdQpQ==

qs@6.10.3:
  version "6.10.3"
  resolved "https://registry.npmmirror.com/qs/-/qs-6.10.3.tgz"
  integrity sha512-wr7M2E0OFRfIfJZjKGieI8lBKb7fRCH4Fv5KNPEs7gJ8jadvotdsS08PzOKR7opXhZ/Xkjtt3WF9g38drmyRqQ==
  dependencies:
    side-channel "^1.0.4"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/quick-lru/-/quick-lru-5.1.1.tgz"
  integrity sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.5.1:
  version "2.5.1"
  resolved "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.1.tgz"
  integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

regenerator-runtime@^0.13.4, regenerator-runtime@^0.13.9:
  version "0.13.9"
  resolved "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  integrity sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==

regexpp@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/regexpp/-/regexpp-3.2.0.tgz"
  integrity sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.1.7, resolve@^1.22.0, resolve@^1.22.1:
  version "1.22.1"
  resolved "https://registry.npmmirror.com/resolve/-/resolve-1.22.1.tgz"
  integrity sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rollup@^2.59.0:
  version "2.77.3"
  resolved "https://registry.npmmirror.com/rollup/-/rollup-2.77.3.tgz"
  integrity sha512-/qxNTG7FbmefJWoeeYJFbHehJ2HNWnjkAFRKzWN/45eNBBF/r8lo992CwcJXEzyVxs5FmfId+vTSTQDb+bxA+g==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-area-insets@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/safe-area-insets/-/safe-area-insets-1.4.1.tgz"
  integrity sha512-r/nRWTjFGhhm3w1Z6Kd/jY11srN+lHt2mNl1E/emQGW8ic7n3Avu4noibklfSM+Y34peNphHD/BSZecav0sXYQ==

safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sass@*, sass@^1.54.5:
  version "1.54.5"
  resolved "https://registry.npmmirror.com/sass/-/sass-1.54.5.tgz"
  integrity sha512-p7DTOzxkUPa/63FU0R3KApkRHwcVZYC0PLnLm5iyZACyp15qSi32x7zVUhRdABAATmkALqgGrjCJAcWvobmhHw==
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^7.3.5:
  version "7.3.7"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.3.7.tgz"
  integrity sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.6:
  version "7.3.7"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.3.7.tgz"
  integrity sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.7:
  version "7.3.7"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.3.7.tgz"
  integrity sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==
  dependencies:
    lru-cache "^6.0.0"

send@0.18.0:
  version "0.18.0"
  resolved "https://registry.npmmirror.com/send/-/send-0.18.0.tgz"
  integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://registry.npmmirror.com/serve-static/-/serve-static-1.15.0.tgz"
  integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz"
  integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

source-map-js@^1.0.2, "source-map-js@>=0.6.2 <2.0.0":
  version "1.0.2"
  resolved "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz"
  integrity sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==

source-map@^0.6.1, source-map@0.6.1:
  version "0.6.1"
  resolved "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  integrity sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

string-hash@^1.1.1:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/string-hash/-/string-hash-1.1.3.tgz"
  integrity sha512-kJUvRUFK49aub+a7T1nNE66EJbZBMnBgoC1UbCZ5n6bsZKBRga4KgBRTMn/pFkeCZSYtNeSyMxPDM0AXWELk2A==

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-3.0.0.tgz"
  integrity sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/svg-tags/-/svg-tags-1.0.0.tgz"
  integrity sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==

systemjs@^6.12.1:
  version "6.12.3"
  resolved "https://registry.npmmirror.com/systemjs/-/systemjs-6.12.3.tgz"
  integrity sha512-TtYUN86Hs8V1QGAoj9ad1xmJmZS9Lurfi8Iu8QWOKaUDDuTH0Bpfdxz9qZIdxsmvAg3WMQnZ5/pkQvloh2sr/Q==

tailwindcss@^3.1.8:
  version "3.1.8"
  resolved "https://registry.npmmirror.com/tailwindcss/-/tailwindcss-3.1.8.tgz"
  integrity sha512-YSneUCZSFDYMwk+TGq8qYFdCA3yfBRdBlS7txSq0LUmzyeqRe3a8fBQzbz9M3WS/iFT4BNf/nmw9mEzrnSaC0g==
  dependencies:
    arg "^5.0.2"
    chokidar "^3.5.3"
    color-name "^1.1.4"
    detective "^5.2.1"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.2.11"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    lilconfig "^2.0.6"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.14"
    postcss-import "^14.1.0"
    postcss-js "^4.0.0"
    postcss-load-config "^3.1.4"
    postcss-nested "5.0.6"
    postcss-selector-parser "^6.0.10"
    postcss-value-parser "^4.2.0"
    quick-lru "^5.1.1"
    resolve "^1.22.1"

tapable@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

tslib@^1.8.1:
  version "1.14.1"
  resolved "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.npmmirror.com/tsutils/-/tsutils-3.21.0.tgz"
  integrity sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typescript@*, typescript@^4.7.4, "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta", typescript@>=4.4.4:
  version "4.7.4"
  resolved "https://registry.npmmirror.com/typescript/-/typescript-4.7.4.tgz"
  integrity sha512-C0WQT0gezHuw6AdY1M2jxUO83Rjf0HP7Sk1DtXj6j1EwkQNZrHAg2XPWlq62oqEhYvONq5pkC2Y9oPljWToLmQ==

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz"
  integrity sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==

unquote@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/unquote/-/unquote-1.1.1.tgz"
  integrity sha512-vRCqFv6UhXpWxZPyGDh/F3ZpNv8/qo7w6iufLpQg9aKnQ71qM4B5KiI7Mia9COcjEhrO9LueHpMYjYzsWH3OIg==

update-browserslist-db@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.5.tgz"
  integrity sha512-dteFFpCyvuDdr9S/ff1ISkKt/9YZxKjI9WlRR99c180GaztJtRa/fn18FdxGVKVsnPY7/a/FDN68mcvUmP4U7Q==
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==

v8-compile-cache@^2.0.3:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  integrity sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz"
  integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==

vconsole@^3.14.6:
  version "3.14.6"
  resolved "https://registry.npmmirror.com/vconsole/-/vconsole-3.14.6.tgz"
  integrity sha512-8Ffk2SfNe6EzKqZ0aNnNjpAVBVT7zgJo81lYEJdKySYLVYBeSawdSkWi9fSjDg3WsQhgS1vNPmRqJDTuwdVbnQ==
  dependencies:
    "@babel/runtime" "^7.17.2"
    copy-text-to-clipboard "^3.0.1"
    core-js "^3.11.0"
    mutation-observer "^1.0.3"

vite@^2.5.10, vite@^2.8.0, vite@^2.9.14, vite@2.9.14:
  version "2.9.14"
  resolved "https://registry.npmmirror.com/vite/-/vite-2.9.14.tgz"
  integrity sha512-P/UCjSpSMcE54r4mPak55hWAZPlyfS369svib/gpmz8/01L822lMPOJ/RYW6tLCe1RPvMvOsJ17erf55bKp4Hw==
  dependencies:
    esbuild "^0.14.27"
    postcss "^8.4.13"
    resolve "^1.22.0"
    rollup "^2.59.0"
  optionalDependencies:
    fsevents "~2.3.2"

vue-demi@*:
  version "0.13.10"
  resolved "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.10.tgz"
  integrity sha512-/R4QhdqGyGqSysOfhkxmYHKwdETZq2z6HAf/fjeGErdJX9cJifX5ijHJS+VjNblGIhjXz/yQTwe/t7Cip+/aJw==

vue-eslint-parser@^9.0.0, vue-eslint-parser@^9.0.1:
  version "9.0.3"
  resolved "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.0.3.tgz"
  integrity sha512-yL+ZDb+9T0ELG4VIFo/2anAOz8SvBdlqEnQnvJ3M7Scq56DvtjY0VY88bByRZB0D4J0u8olBcfrXTVONXsh4og==
  dependencies:
    debug "^4.3.4"
    eslint-scope "^7.1.1"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.6"

vue-i18n@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmmirror.com/vue-i18n/-/vue-i18n-9.2.2.tgz"
  integrity sha512-yswpwtj89rTBhegUAv9Mu37LNznyu3NpyLQmozF3i1hYOhwpG8RjcjIFIIfnu+2MDZJGSZPXaKWvnQA71Yv9TQ==
  dependencies:
    "@intlify/core-base" "9.2.2"
    "@intlify/shared" "9.2.2"
    "@intlify/vue-devtools" "9.2.2"
    "@vue/devtools-api" "^6.2.1"

vue-router@^4.1.3:
  version "4.1.4"
  resolved "https://registry.npmmirror.com/vue-router/-/vue-router-4.1.4.tgz"
  integrity sha512-UgYen33gOtwT3cOG1+yRen+Brk9py8CSlC9LEa3UjvKZ4EAoSo8NjZPDeDnmNerfazorHIJG1NC7qdi1SuQJnQ==
  dependencies:
    "@vue/devtools-api" "^6.1.4"

"vue@^2.6.14 || ^3.2.0", vue@^3.0.0, "vue@^3.0.0-0 || ^2.6.0", vue@^3.2.0, vue@^3.2.25, vue@^3.2.37, vue@3.2.37:
  version "3.2.37"
  resolved "https://registry.npmmirror.com/vue/-/vue-3.2.37.tgz"
  integrity sha512-bOKEZxrm8Eh+fveCqS1/NkG/n6aMidsI6hahas7pa0w/l7jkbssJVsRhVDs07IdDq7h9KHswZOgItnwJAgtVtQ==
  dependencies:
    "@vue/compiler-dom" "3.2.37"
    "@vue/compiler-sfc" "3.2.37"
    "@vue/runtime-dom" "3.2.37"
    "@vue/server-renderer" "3.2.37"
    "@vue/shared" "3.2.37"

weapp-tailwindcss-webpack-plugin@^1.7.0:
  version "1.8.2"
  resolved "https://registry.npmmirror.com/weapp-tailwindcss-webpack-plugin/-/weapp-tailwindcss-webpack-plugin-1.8.2.tgz"
  integrity sha512-L+RsGqRKa00a8tOxajdNVZGVxtQ5dGiopzxOLU/K8bvYRYE1M5dfvUWIpX9Gntq27o3uG9OJ8j2Ab6lJOnCZNg==
  dependencies:
    "@babel/generator" "7.18.12"
    "@babel/parser" "7.18.11"
    "@babel/traverse" "7.18.11"
    loader-utils "^2.0.2"
    postcss "^8.4.16"
    postcss-selector-parser "^6.0.10"
    webpack-sources "^3.2.3"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

weixin-js-sdk@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/weixin-js-sdk/-/weixin-js-sdk-1.6.0.tgz"
  integrity sha512-3IYQH7aalJGFJrwdT3epvTdR1MboMiH7vIZ5BRL2eYOJ12BNah7csoMkmSZzkq1+l92sSq29XdTCVjCJoK2sBQ==

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.3.tgz"
  integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@^8.4.2:
  version "8.8.1"
  resolved "https://registry.npmmirror.com/ws/-/ws-8.8.1.tgz"
  integrity sha512-bGy2JzvzkPowEJV++hF07hAD6niYSr0JzBNo/J29WsB57A2r7Wlc1UFcTR9IzrPvuNVO4B8LGqF8qcpsVOhJCA==

xml-name-validator@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
  integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==

xmlhttprequest@^1.8.0:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/xmlhttprequest/-/xmlhttprequest-1.8.0.tgz"
  integrity sha512-58Im/U0mlVBLM38NdZjHyhuMtCqa61469k2YP/AaPbvCoV9aQGUpbJBj1QRm2ytRiVQBD/fsw7L2bJGDVQswBA==

xregexp@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/xregexp/-/xregexp-3.1.0.tgz"
  integrity sha512-4Y1x6DyB8xRoxosooa6PlGWqmmSKatbzhrftZ7Purmm4B8R4qIEJG1A2hZsdz5DhmIqS0msC0I7KEq93GphEVg==

xtend@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/xtend/-/xtend-4.0.2.tgz"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@^1.10.2:
  version "1.10.2"
  resolved "https://registry.npmmirror.com/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

z-paging@^2.3.8:
  version "2.3.8"
  resolved "https://registry.npmmirror.com/z-paging/-/z-paging-2.3.8.tgz"
  integrity sha512-sW83Tlw5Qm4g/Fe1JPDleOU8eKRZZRk/ARtNW56/FqTsz+wU809rvWSkYAFEjkOJ4onavgd+AcFwSIujCnb3Hg==
