<script setup lang="ts">
import { reactive, onActivated } from 'vue'
import {onReachBottom, onShow} from "@dcloudio/uni-app";
import {reqBdBrandList, reqBdBrandApply} from '@/api/shop'

const state = reactive<any>({
  list: [],
  listStatus: 'loadmore', // loading | nomore

  // pop: false,
  // popTitle: '',
  // popContent: '',
})
const form = reactive({
  keyword: '',
  is_me: 0,
  page_no: 1,
  page_size: 20
})
const getList = async () => {
  if (state.listStatus === 'loading' || state.listStatus === 'nomore') return;
  state.listStatus = 'loading'
  try {
    const f: any = {...form}
    const res = await reqBdBrandList(f)
    if (f.is_me !== form.is_me || f.keyword !== form.keyword) return
    const l = res.lists || []
    state.list = f.page_no === 1 ? l : state.list.concat(l)
    state.listStatus = l.length ? 'loadmore' : 'nomore'
    form.page_no += 1
  } catch (e) {
    console.error(e)
    state.listStatus = 'loadmore'
  }
}
const reGetList = () => {
  form.page_no = 1
  state.listStatus = 'loadmore'
  state.list = []
  getList()
}

onShow(() => {
  if (!state.list.length) getList()
})

onActivated(() => {
  if (!state.list.length) getList()
})

onReachBottom(() => {
  getList()
})
const onTabClick = (isMe: number) => {
  if (form.is_me === isMe) return
  form.is_me = isMe
  reGetList()
}
const showContent = (item: any, key: string) => {
  // state.popTitle = key === 'policy' ? '认领政策' : '简介'
  // state.popContent = item[key]
  // state.pop = true
  uni.showModal({
    title: key === 'policy' ? '政策' : '简介',
    content: item[key],
    showCancel: false,
    confirmText: '知道了'
  })
}

const handleApply = async (item: any) => {
  uni.showLoading({mask: true, title: '正在认领...'})
  try {
    await reqBdBrandApply({brand_id: item.id})
    uni.hideLoading()
    const i = state.list.find((a: any) => a.id === item.id)
    if (i !== -1) {
      // state.list[i] = { ...state.list[i], status: 1, status_text: '已认领' }
      state.list.splice(i, 1)
    }
    uni.showToast({title: '认领成功，可以去“我的”里面查看'})
  } catch (e) {
    console.error(e)
  }
}
</script>

<template>
  <div class="bdbrands">
    <div class="bdbrands__tp"></div>
    <div class="fixed top-0 left-0 right-0 bdbrands__t">
      <div class="flex items-center bdbrands__sw">
        <u-input v-model="form.keyword" style="padding: 0 20rpx;" class="flex-1 bdbrands__sinp"
                 placeholder="输入品牌名搜索" confirm-type="search" @confirm="reGetList"></u-input>
        <div class="bdbrands__sbtn" @click="reGetList">搜索</div>
      </div>
      <div class="flex items-center relative bdbrands__tabw">
        <div class="flex-1 flex items-center justify-center relative bdbrands__tab"
             :class="{active: form.is_me === 0}"
             @click="onTabClick(0)"
        >公海
        </div>
        <div class="flex-1 flex items-center justify-center relative bdbrands__tab"
             :class="{active: form.is_me === 1}"
             @click="onTabClick(1)"
        >我的
        </div>
      </div>
    </div>

    <div v-for="(item, index) in state.list" :key="index" class="relative bdbrands__it">
      <div class="flex items-start">
        <image class="bdbrands__iti" mode="aspectFill" :src="item.brand_logo"/>
        <div class="flex-1 bdbrands__itc">
          <div style="margin-bottom: 6rpx;" class="flex items-center">
            <div class="flex-1 truncate bdbrands__itt">{{ item.brand_name }}</div>
            <div :style="{color: item.status === 1 ? 'var(--color)' : (item.status === 2 ? '#2ba471' : '')}"
                 class="absolute bdbrands__its"
            >{{ item.status_text }}
            </div>
          </div>
          <div v-if="item.contact_name || item.contact_phone" style="margin-bottom: 6rpx;" class="flex items-center">
            <div>联系人：<span v-if="item.contact_name">{{ item.contact_name }}</span>
              <a style="margin-left: 8rpx;color: inherit;" class="no-underline" :href="'tel://' + item.contact_phone"
                 v-if="item.contact_phone">{{ item.contact_phone }}</a>
            </div>
          </div>
          <div v-if="item.company" style="margin-bottom: 6rpx;" class="flex items-center">
            <div>公司：{{ item.company }}</div>
          </div>
          <div v-if="Number(item.average)" style="margin-bottom: 6rpx;" class="flex items-center">
            <div class="">均价：¥{{ item.average }}</div>
          </div>
          <div v-if="item.desc || item.policy" style="margin-bottom: 6rpx;" class="flex items-center">
            <div class="bdbrands__tag" @click="showContent(item, 'desc')" v-if="item.desc">简介</div>
            <div class="bdbrands__tag" @click="showContent(item, 'policy')" v-if="item.policy">政策</div>
          </div>
          <div v-if="item.store_num && item.succ_num" class="flex items-center">
            <div class="bdbrands__itlvw">
              <div :style="{width: `${100 * item.succ_num / item.store_num}%`}" class="bdbrands__itlv"></div>
            </div>
            <div>{{ item.succ_num }}/{{ item.store_num }}</div>
          </div>
        </div>
        <div v-if="item.can_claim" class="bdbrands__itbtn" @click="handleApply(item)">认领</div>
      </div>
      <div v-if="item.claim_time || item.succ_time" class="flex justify-between bdbrands__itf">
        <div v-if="item.claim_time">认领于：{{ item.claim_time.substring(0, 16).replace(/-/g, '.') }}</div>
        <div v-if="item.succ_time">谈成于：{{ item.succ_time.substring(0, 16).replace(/-/g, '.') }}</div>
      </div>
    </div>

    <div style="padding: 16rpx 0 80rpx;">
      <u-loadmore :status="state.listStatus"
                  :load-text="{loadmore: '加载更多',loading: '正在加载...',nomore: '暂无数据'}" @loadmore="getList"/>
    </div>

    <!-- <u-popup v-model="state.pop" mode="center">
        <div>{{ state.popTitle }}</div>
        <div v-html="state.popContent"></div>
    </u-popup> -->
  </div>
</template>

<style lang="scss">
.bdbrands {
  --color: #0089FA;
  font-size: 28rpx;
  color: #222;

  &__tp {
    height: 160rpx;
  }

  &__t {
    z-index: 80;
    background: #fff;
    border-bottom: 2rpx solid #f3f4f6;
  }

  &__sw {
    height: 80rpx;
    padding: 0 0 0 24rpx;
  }

  &__sinp {
    font-size: inherit;
    height: 68rpx;
    line-height: 68rpx;
    padding: 0 20rpx;
    background: #f3f4f6;
    border-radius: 8rpx;
  }

  &__sbtn {
    color: #666;
    padding: 0 24rpx;
  }

  &__tabw {
    padding: 0 60rpx;
    height: 80rpx;
    color: #666;
  }

  &__tab {
    z-index: 1;
    height: 100%;

    &.active {
      color: var(--color);
      font-weight: 600;
    }
  }

  &__it {
    margin: 20rpx 20rpx 0;
    padding: 20rpx;
    font-size: 24rpx;
    color: #666;
    background: #fff;
    border-radius: 16rpx;
  }

  &__iti {
    width: 120rpx;
    height: 120rpx;
    border-radius: 16rpx;
    margin-right: 16rpx;
  }

  &__itc {
    margin: -2rpx 0 0;
    overflow: hidden;
  }

  &__itt {
    font-size: 28rpx;
    color: #111;
    font-weight: 500;
    max-width: 400rpx;
  }

  &__its {
    right: 20rpx;
    top: 22rpx;
    font-size: 22rpx;
    color: #999;
  }

  &__tag {
    font-size: 22rpx;
    padding: 2rpx 8rpx;
    margin-right: 8rpx;
    border: 2rpx solid #999;
    border-radius: 8rpx;
  }

  &__itlvw {
    height: 20rpx;
    width: 60%;
    background: #f3f4f6;
    border-radius: 10rpx;
    overflow: hidden;
    margin-right: 8rpx;
  }

  &__itlv {
    height: 100%;
    background: #93cdff;
    border-radius: 10rpx;
  }

  &__itbtn {
    color: #fff;
    width: 128rpx;
    line-height: 60rpx;
    text-align: center;
    background: var(--color);
    border-radius: 8rpx;
    margin: auto 0 0 16rpx;
  }

  &__itf {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
  }
}
</style>
