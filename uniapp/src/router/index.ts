import { ClientEnum } from '@/enums/appEnums'
import { client } from '@/utils/client'
import { BACK_URL } from '@/enums/constantEnums'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
import cache from '@/utils/cache'
import { routes } from './routes'
// #ifdef H5
import wechatOa from '@/utils/wechat'
// #endif
const whiteList = ['register', 'login', 'forget_pwd']
const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
list.forEach((item) => {
    uni.addInterceptor(item, {
        invoke(e) {
            // 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
            const url = e.url.split('?')[0]
            const currentRoute = routes.find((item) => {
                return url === item.path
            })
            // 需要登录并且没有token
            if (currentRoute?.auth && !getToken()) {
                uni.navigateTo({
                    url: '/pages/login/login'
                })
                return false
            }
            return e
        },
        fail(err) {
            // 失败回调拦截
            console.log(err)
        }
    })
})

export function setupRouter() {
    // #ifdef H5
    const app = getApp()
    app.$router.beforeEach((to: any, from: any) => {
        const currentRoute = routes.find((item) => {
            return to.path === item.path
        })
        // 需要登录并且没有token
        if (currentRoute?.auth && !getToken()) {
            // 好蠢哦，为了触发 afterEach 中的 BACK_URL
            setTimeout(() => {
                uni.navigateTo({
                    url: '/pages/login/login'
                })
            }, 0)
            // return false
        }
    })
    app.$router.afterEach((to: any, from: any) => {
        const index = whiteList.findIndex((item) => from.path.includes(item) || from.path === '/')
        const userStore = useUserStore()
        if (index == -1 && !userStore.isLogin) {
            //保存登录前的路径
            cache.set(BACK_URL, from.fullPath)
        }
    })
    setTimeout(async () => {
        if (client == ClientEnum.OA_WEIXIN) {
            // jssdk配置
            await wechatOa.config()
        }
    })
    // #endif
}
