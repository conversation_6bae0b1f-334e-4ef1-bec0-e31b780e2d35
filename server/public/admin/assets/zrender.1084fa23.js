import{_ as B}from"./tslib.60310f1a.js";var ms=function(){function e(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return e}(),ws=function(){function e(){this.browser=new ms,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return e}(),hr=new ws;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(hr.wxa=!0,hr.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?hr.worker=!0:typeof navigator>"u"?(hr.node=!0,hr.svgSupported=!0):Ts(navigator.userAgent,hr);function Ts(e,t){var r=t.browser,i=e.match(/Firefox\/([\d.]+)/),a=e.match(/MSIE\s([\d.]+)/)||e.match(/Trident\/.+?rv:(([\d.]+))/),n=e.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(e);i&&(r.firefox=!0,r.version=i[1]),a&&(r.ie=!0,r.version=a[1]),n&&(r.edge=!0,r.version=n[1],r.newEdge=+n[1].split(".")[0]>18),o&&(r.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,t.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&+r.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||r.ie&&+r.version>=9}const tt=hr;var ba=12,bs="sans-serif",cr=ba+"px "+bs,Cs=20,Ls=100,Ms="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function Ss(e){var t={};if(typeof JSON>"u")return t;for(var r=0;r<e.length;r++){var i=String.fromCharCode(r+32),a=(e.charCodeAt(r)-Cs)/Ls;t[i]=a}return t}var Ps=Ss(Ms),le={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var e,t;return function(r,i){if(!e){var a=le.createCanvas();e=a&&a.getContext("2d")}if(e)return t!==i&&(t=e.font=i||cr),e.measureText(r);r=r||"",i=i||cr;var n=/^([0-9]*?)px$/.exec(i),o=+(n&&n[1])||ba,s=0;if(i.indexOf("mono")>=0)s=o*r.length;else for(var f=0;f<r.length;f++){var h=Ps[r[f]];s+=h==null?o:h*o}return{width:s}}}(),loadImage:function(e,t,r){var i=new Image;return i.onload=t,i.onerror=r,i.src=e,i}},io=oi(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(e,t){return e["[object "+t+"]"]=!0,e},{}),ao=oi(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(e,t){return e["[object "+t+"Array]"]=!0,e},{}),ve=Object.prototype.toString,ni=Array.prototype,Rs=ni.forEach,Ds=ni.filter,Ca=ni.slice,As=ni.map,Ia=function(){}.constructor,pe=Ia?Ia.prototype:null,La="__proto__",xs=2311;function no(){return xs++}function Ma(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];typeof console<"u"&&console.error.apply(console,e)}function Br(e){if(e==null||typeof e!="object")return e;var t=e,r=ve.call(e);if(r==="[object Array]"){if(!te(e)){t=[];for(var i=0,a=e.length;i<a;i++)t[i]=Br(e[i])}}else if(ao[r]){if(!te(e)){var n=e.constructor;if(n.from)t=n.from(e);else{t=new n(e.length);for(var i=0,a=e.length;i<a;i++)t[i]=e[i]}}}else if(!io[r]&&!te(e)&&!Vi(e)){t={};for(var o in e)e.hasOwnProperty(o)&&o!==La&&(t[o]=Br(e[o]))}return t}function Sr(e,t,r){if(!zt(t)||!zt(e))return r?Br(t):e;for(var i in t)if(t.hasOwnProperty(i)&&i!==La){var a=e[i],n=t[i];zt(n)&&zt(a)&&!ne(n)&&!ne(a)&&!Vi(n)&&!Vi(a)&&!Ha(n)&&!Ha(a)&&!te(n)&&!te(a)?Sr(a,n,r):(r||!(i in e))&&(e[i]=Br(t[i]))}return e}function Ol(e,t){for(var r=e[0],i=1,a=e.length;i<a;i++)r=Sr(r,e[i],t);return r}function $(e,t){if(Object.assign)Object.assign(e,t);else for(var r in t)t.hasOwnProperty(r)&&r!==La&&(e[r]=t[r]);return e}function _t(e,t,r){for(var i=j(t),a=0;a<i.length;a++){var n=i[a];(r?t[n]!=null:e[n]==null)&&(e[n]=t[n])}return e}le.createCanvas;function Pt(e,t){if(e){if(e.indexOf)return e.indexOf(t);for(var r=0,i=e.length;r<i;r++)if(e[r]===t)return r}return-1}function Hl(e,t){var r=e.prototype;function i(){}i.prototype=t.prototype,e.prototype=new i;for(var a in r)r.hasOwnProperty(a)&&(e.prototype[a]=r[a]);e.prototype.constructor=e,e.superClass=t}function oo(e,t,r){if(e="prototype"in e?e.prototype:e,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),a=0;a<i.length;a++){var n=i[a];n!=="constructor"&&(r?t[n]!=null:e[n]==null)&&(e[n]=t[n])}else _t(e,t,r)}function At(e){return!e||typeof e=="string"?!1:typeof e.length=="number"}function J(e,t,r){if(!!(e&&t))if(e.forEach&&e.forEach===Rs)e.forEach(t,r);else if(e.length===+e.length)for(var i=0,a=e.length;i<a;i++)t.call(r,e[i],i,e);else for(var n in e)e.hasOwnProperty(n)&&t.call(r,e[n],n,e)}function xt(e,t,r){if(!e)return[];if(!t)return so(e);if(e.map&&e.map===As)return e.map(t,r);for(var i=[],a=0,n=e.length;a<n;a++)i.push(t.call(r,e[a],a,e));return i}function oi(e,t,r,i){if(!!(e&&t)){for(var a=0,n=e.length;a<n;a++)r=t.call(i,r,e[a],a,e);return r}}function Oa(e,t,r){if(!e)return[];if(!t)return so(e);if(e.filter&&e.filter===Ds)return e.filter(t,r);for(var i=[],a=0,n=e.length;a<n;a++)t.call(r,e[a],a,e)&&i.push(e[a]);return i}function Bl(e,t,r){if(!!(e&&t)){for(var i=0,a=e.length;i<a;i++)if(t.call(r,e[i],i,e))return e[i]}}function j(e){if(!e)return[];if(Object.keys)return Object.keys(e);var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}function Es(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return e.apply(t,r.concat(Ca.call(arguments)))}}var kl=pe&&si(pe.bind)?pe.call.bind(pe.bind):Es;function zl(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return function(){return e.apply(this,t.concat(Ca.call(arguments)))}}function ne(e){return Array.isArray?Array.isArray(e):ve.call(e)==="[object Array]"}function si(e){return typeof e=="function"}function Ge(e){return typeof e=="string"}function $l(e){return ve.call(e)==="[object String]"}function He(e){return typeof e=="number"}function zt(e){var t=typeof e;return t==="function"||!!e&&t==="object"}function Ha(e){return!!io[ve.call(e)]}function Fs(e){return!!ao[ve.call(e)]}function Vi(e){return typeof e=="object"&&typeof e.nodeType=="number"&&typeof e.ownerDocument=="object"}function Sa(e){return e.colorStops!=null}function Is(e){return e.image!=null}function Os(e){return e!==e}function Yl(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=0,i=e.length;r<i;r++)if(e[r]!=null)return e[r]}function pt(e,t){return e!=null?e:t}function Be(e,t,r){return e!=null?e:t!=null?t:r}function so(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return Ca.apply(e,t)}function Hs(e){if(typeof e=="number")return[e,e,e,e];var t=e.length;return t===2?[e[0],e[1],e[0],e[1]]:t===3?[e[0],e[1],e[2],e[1]]:e}function Wl(e,t){if(!e)throw new Error(t)}function Pr(e){return e==null?null:typeof e.trim=="function"?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var fo="__ec_primitive__";function Xl(e){e[fo]=!0}function te(e){return e[fo]}var Bs=function(){function e(t){this.data={};var r=ne(t);this.data={};var i=this;t instanceof e?t.each(a):t&&J(t,a);function a(n,o){r?i.set(n,o):i.set(o,n)}}return e.prototype.get=function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},e.prototype.set=function(t,r){return this.data[t]=r},e.prototype.each=function(t,r){for(var i in this.data)this.data.hasOwnProperty(i)&&t.call(r,this.data[i],i)},e.prototype.keys=function(){return j(this.data)},e.prototype.removeKey=function(t){delete this.data[t]},e}();function Gl(e){return new Bs(e)}function ql(e,t){for(var r=new e.constructor(e.length+t.length),i=0;i<e.length;i++)r[i]=e[i];for(var a=e.length,i=0;i<t.length;i++)r[i+a]=t[i];return r}function fi(e,t){var r;if(Object.create)r=Object.create(e);else{var i=function(){};i.prototype=e,r=new i}return t&&$(r,t),r}function ho(e){var t=e.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function qe(e,t){return e.hasOwnProperty(t)}function Ir(){}var ks=180/Math.PI;function zr(e,t){return e==null&&(e=0),t==null&&(t=0),[e,t]}function Ul(e,t){return e[0]=t[0],e[1]=t[1],e}function zs(e){return[e[0],e[1]]}function Ba(e,t,r){return e[0]=t[0]+r[0],e[1]=t[1]+r[1],e}function $s(e,t,r){return e[0]=t[0]-r[0],e[1]=t[1]-r[1],e}function Ys(e){return Math.sqrt(Ws(e))}function Ws(e){return e[0]*e[0]+e[1]*e[1]}function vi(e,t,r){return e[0]=t[0]*r,e[1]=t[1]*r,e}function Xs(e,t){var r=Ys(t);return r===0?(e[0]=0,e[1]=0):(e[0]=t[0]/r,e[1]=t[1]/r),e}function Qi(e,t){return Math.sqrt((e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1]))}var Gs=Qi;function qs(e,t){return(e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1])}var Or=qs;function Us(e,t,r,i){return e[0]=t[0]+i*(r[0]-t[0]),e[1]=t[1]+i*(r[1]-t[1]),e}function re(e,t,r){var i=t[0],a=t[1];return e[0]=r[0]*i+r[2]*a+r[4],e[1]=r[1]*i+r[3]*a+r[5],e}function Rr(e,t,r){return e[0]=Math.min(t[0],r[0]),e[1]=Math.min(t[1],r[1]),e}function Dr(e,t,r){return e[0]=Math.max(t[0],r[0]),e[1]=Math.max(t[1],r[1]),e}var pr=function(){function e(t,r){this.target=t,this.topTarget=r&&r.topTarget}return e}(),Zs=function(){function e(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return e.prototype._dragStart=function(t){for(var r=t.target;r&&!r.draggable;)r=r.parent||r.__hostTarget;r&&(this._draggingTarget=r,r.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new pr(r,t),"dragstart",t.event))},e.prototype._drag=function(t){var r=this._draggingTarget;if(r){var i=t.offsetX,a=t.offsetY,n=i-this._x,o=a-this._y;this._x=i,this._y=a,r.drift(n,o,t),this.handler.dispatchToElement(new pr(r,t),"drag",t.event);var s=this.handler.findHover(i,a,r).target,f=this._dropTarget;this._dropTarget=s,r!==s&&(f&&s!==f&&this.handler.dispatchToElement(new pr(f,t),"dragleave",t.event),s&&s!==f&&this.handler.dispatchToElement(new pr(s,t),"dragenter",t.event))}},e.prototype._dragEnd=function(t){var r=this._draggingTarget;r&&(r.dragging=!1),this.handler.dispatchToElement(new pr(r,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new pr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},e}();const Ns=Zs;var Vs=function(){function e(t){t&&(this._$eventProcessor=t)}return e.prototype.on=function(t,r,i,a){this._$handlers||(this._$handlers={});var n=this._$handlers;if(typeof r=="function"&&(a=i,i=r,r=null),!i||!t)return this;var o=this._$eventProcessor;r!=null&&o&&o.normalizeQuery&&(r=o.normalizeQuery(r)),n[t]||(n[t]=[]);for(var s=0;s<n[t].length;s++)if(n[t][s].h===i)return this;var f={h:i,query:r,ctx:a||this,callAtLast:i.zrEventfulCallAtLast},h=n[t].length-1,u=n[t][h];return u&&u.callAtLast?n[t].splice(h,0,f):n[t].push(f),this},e.prototype.isSilent=function(t){var r=this._$handlers;return!r||!r[t]||!r[t].length},e.prototype.off=function(t,r){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(r){if(i[t]){for(var a=[],n=0,o=i[t].length;n<o;n++)i[t][n].h!==r&&a.push(i[t][n]);i[t]=a}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},e.prototype.trigger=function(t){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];if(!this._$handlers)return this;var a=this._$handlers[t],n=this._$eventProcessor;if(a)for(var o=r.length,s=a.length,f=0;f<s;f++){var h=a[f];if(!(n&&n.filter&&h.query!=null&&!n.filter(t,h.query)))switch(o){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,r[0]);break;case 2:h.h.call(h.ctx,r[0],r[1]);break;default:h.h.apply(h.ctx,r);break}}return n&&n.afterTrigger&&n.afterTrigger(t),this},e.prototype.triggerWithContext=function(t){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];if(!this._$handlers)return this;var a=this._$handlers[t],n=this._$eventProcessor;if(a)for(var o=r.length,s=r[o-1],f=a.length,h=0;h<f;h++){var u=a[h];if(!(n&&n.filter&&u.query!=null&&!n.filter(t,u.query)))switch(o){case 0:u.h.call(s);break;case 1:u.h.call(s,r[0]);break;case 2:u.h.call(s,r[0],r[1]);break;default:u.h.apply(s,r.slice(1,o-1));break}}return n&&n.afterTrigger&&n.afterTrigger(t),this},e}();const $r=Vs;var Qs=Math.log(2);function Ki(e,t,r,i,a,n){var o=i+"-"+a,s=e.length;if(n.hasOwnProperty(o))return n[o];if(t===1){var f=Math.round(Math.log((1<<s)-1&~a)/Qs);return e[r][f]}for(var h=i|1<<r,u=r+1;i&1<<u;)u++;for(var l=0,v=0,c=0;v<s;v++){var _=1<<v;_&a||(l+=(c%2?-1:1)*e[r][v]*Ki(e,t-1,u,h,a|_,n),c++)}return n[o]=l,l}function ka(e,t){var r=[[e[0],e[1],1,0,0,0,-t[0]*e[0],-t[0]*e[1]],[0,0,0,e[0],e[1],1,-t[1]*e[0],-t[1]*e[1]],[e[2],e[3],1,0,0,0,-t[2]*e[2],-t[2]*e[3]],[0,0,0,e[2],e[3],1,-t[3]*e[2],-t[3]*e[3]],[e[4],e[5],1,0,0,0,-t[4]*e[4],-t[4]*e[5]],[0,0,0,e[4],e[5],1,-t[5]*e[4],-t[5]*e[5]],[e[6],e[7],1,0,0,0,-t[6]*e[6],-t[6]*e[7]],[0,0,0,e[6],e[7],1,-t[7]*e[6],-t[7]*e[7]]],i={},a=Ki(r,8,0,0,0,i);if(a!==0){for(var n=[],o=0;o<8;o++)for(var s=0;s<8;s++)n[s]==null&&(n[s]=0),n[s]+=((o+s)%2?-1:1)*Ki(r,7,o===0?1:0,1<<o,1<<s,i)/a*t[o];return function(f,h,u){var l=h*n[6]+u*n[7]+1;f[0]=(h*n[0]+u*n[1]+n[2])/l,f[1]=(h*n[3]+u*n[4]+n[5])/l}}}var za="___zrEVENTSAVED",ci=[];function Zl(e,t,r,i,a){return Ji(ci,t,i,a,!0)&&Ji(e,r,ci[0],ci[1])}function Ji(e,t,r,i,a){if(t.getBoundingClientRect&&tt.domSupported&&!uo(t)){var n=t[za]||(t[za]={}),o=Ks(t,n),s=Js(o,n,a);if(s)return s(e,r,i),!0}return!1}function Ks(e,t){var r=t.markers;if(r)return r;r=t.markers=[];for(var i=["left","right"],a=["top","bottom"],n=0;n<4;n++){var o=document.createElement("div"),s=o.style,f=n%2,h=(n>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[f]+":0",a[h]+":0",i[1-f]+":auto",a[1-h]+":auto",""].join("!important;"),e.appendChild(o),r.push(o)}return r}function Js(e,t,r){for(var i=r?"invTrans":"trans",a=t[i],n=t.srcCoords,o=[],s=[],f=!0,h=0;h<4;h++){var u=e[h].getBoundingClientRect(),l=2*h,v=u.left,c=u.top;o.push(v,c),f=f&&n&&v===n[l]&&c===n[l+1],s.push(e[h].offsetLeft,e[h].offsetTop)}return f&&a?a:(t.srcCoords=o,t[i]=r?ka(s,o):ka(o,s))}function uo(e){return e.nodeName.toUpperCase()==="CANVAS"}var js=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,di=[],tf=tt.browser.firefox&&+tt.browser.version.split(".")[0]<39;function ji(e,t,r,i){return r=r||{},i?$a(e,t,r):tf&&t.layerX!=null&&t.layerX!==t.offsetX?(r.zrX=t.layerX,r.zrY=t.layerY):t.offsetX!=null?(r.zrX=t.offsetX,r.zrY=t.offsetY):$a(e,t,r),r}function $a(e,t,r){if(tt.domSupported&&e.getBoundingClientRect){var i=t.clientX,a=t.clientY;if(uo(e)){var n=e.getBoundingClientRect();r.zrX=i-n.left,r.zrY=a-n.top;return}else if(Ji(di,e,i,a)){r.zrX=di[0],r.zrY=di[1];return}}r.zrX=r.zrY=0}function Pa(e){return e||window.event}function yt(e,t,r){if(t=Pa(t),t.zrX!=null)return t;var i=t.type,a=i&&i.indexOf("touch")>=0;if(a){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&ji(e,o,t,r)}else{ji(e,t,t,r);var n=rf(t);t.zrDelta=n?n/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&js.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function rf(e){var t=e.wheelDelta;if(t)return t;var r=e.deltaX,i=e.deltaY;if(r==null||i==null)return t;var a=Math.abs(i!==0?i:r),n=i>0?-1:i<0?1:r>0?-1:1;return 3*a*n}function ef(e,t,r,i){e.addEventListener(t,r,i)}function af(e,t,r,i){e.removeEventListener(t,r,i)}var nf=function(e){e.preventDefault(),e.stopPropagation(),e.cancelBubble=!0};function Nl(e){return e.which===2||e.which===3}var of=function(){function e(){this._track=[]}return e.prototype.recognize=function(t,r,i){return this._doTrack(t,r,i),this._recognize(t)},e.prototype.clear=function(){return this._track.length=0,this},e.prototype._doTrack=function(t,r,i){var a=t.touches;if(!!a){for(var n={points:[],touches:[],target:r,event:t},o=0,s=a.length;o<s;o++){var f=a[o],h=ji(i,f,{});n.points.push([h.zrX,h.zrY]),n.touches.push(f)}this._track.push(n)}},e.prototype._recognize=function(t){for(var r in pi)if(pi.hasOwnProperty(r)){var i=pi[r](this._track,t);if(i)return i}},e}();function Ya(e){var t=e[1][0]-e[0][0],r=e[1][1]-e[0][1];return Math.sqrt(t*t+r*r)}function sf(e){return[(e[0][0]+e[1][0])/2,(e[0][1]+e[1][1])/2]}var pi={pinch:function(e,t){var r=e.length;if(!!r){var i=(e[r-1]||{}).points,a=(e[r-2]||{}).points||i;if(a&&a.length>1&&i&&i.length>1){var n=Ya(i)/Ya(a);!isFinite(n)&&(n=1),t.pinchScale=n;var o=sf(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:e[0].target,event:t}}}}},lo="silent";function ff(e,t,r){return{type:e,event:r,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:hf}}function hf(){nf(this.event)}var uf=function(e){B(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.handler=null,r}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}($r),Wr=function(){function e(t,r){this.x=t,this.y=r}return e}(),lf=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],vo=function(e){B(t,e);function t(r,i,a,n){var o=e.call(this)||this;return o._hovered=new Wr(0,0),o.storage=r,o.painter=i,o.painterRoot=n,a=a||new uf,o.proxy=null,o.setHandlerProxy(a),o._draggingMgr=new Ns(o),o}return t.prototype.setHandlerProxy=function(r){this.proxy&&this.proxy.dispose(),r&&(J(lf,function(i){r.on&&r.on(i,this[i],this)},this),r.handler=this),this.proxy=r},t.prototype.mousemove=function(r){var i=r.zrX,a=r.zrY,n=co(this,i,a),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var f=this._hovered=n?new Wr(i,a):this.findHover(i,a),h=f.target,u=this.proxy;u.setCursor&&u.setCursor(h?h.cursor:"default"),s&&h!==s&&this.dispatchToElement(o,"mouseout",r),this.dispatchToElement(f,"mousemove",r),h&&h!==s&&this.dispatchToElement(f,"mouseover",r)},t.prototype.mouseout=function(r){var i=r.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",r),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:r})},t.prototype.resize=function(){this._hovered=new Wr(0,0)},t.prototype.dispatch=function(r,i){var a=this[r];a&&a.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(r){var i=this.proxy;i.setCursor&&i.setCursor(r)},t.prototype.dispatchToElement=function(r,i,a){r=r||{};var n=r.target;if(!(n&&n.silent)){for(var o="on"+i,s=ff(i,r,a);n&&(n[o]&&(s.cancelBubble=!!n[o].call(n,s)),n.trigger(i,s),n=n.__hostTarget?n.__hostTarget:n.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(f){typeof f[o]=="function"&&f[o].call(f,s),f.trigger&&f.trigger(i,s)}))}},t.prototype.findHover=function(r,i,a){for(var n=this.storage.getDisplayList(),o=new Wr(r,i),s=n.length-1;s>=0;s--){var f=void 0;if(n[s]!==a&&!n[s].ignore&&(f=vf(n[s],r,i))&&(!o.topTarget&&(o.topTarget=n[s]),f!==lo)){o.target=n[s];break}}return o},t.prototype.processGesture=function(r,i){this._gestureMgr||(this._gestureMgr=new of);var a=this._gestureMgr;i==="start"&&a.clear();var n=a.recognize(r,this.findHover(r.zrX,r.zrY,null).target,this.proxy.dom);if(i==="end"&&a.clear(),n){var o=n.type;r.gestureEvent=o;var s=new Wr;s.target=n.target,this.dispatchToElement(s,o,n.event)}},t}($r);J(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(e){vo.prototype[e]=function(t){var r=t.zrX,i=t.zrY,a=co(this,r,i),n,o;if((e!=="mouseup"||!a)&&(n=this.findHover(r,i),o=n.target),e==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(e==="mouseup")this._upEl=o;else if(e==="click"){if(this._downEl!==this._upEl||!this._downPoint||Gs(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,e,t)}});function vf(e,t,r){if(e[e.rectHover?"rectContain":"contain"](t,r)){for(var i=e,a=void 0,n=!1;i;){if(i.ignoreClip&&(n=!0),!n){var o=i.getClipPath();if(o&&!o.contain(t,r))return!1;i.silent&&(a=!0)}var s=i.__hostTarget;i=s||i.parent}return a?lo:!0}return!1}function co(e,t,r){var i=e.painter;return t<0||t>i.getWidth()||r<0||r>i.getHeight()}const cf=vo;var po=32,Xr=7;function df(e){for(var t=0;e>=po;)t|=e&1,e>>=1;return e+t}function Wa(e,t,r,i){var a=t+1;if(a===r)return 1;if(i(e[a++],e[t])<0){for(;a<r&&i(e[a],e[a-1])<0;)a++;pf(e,t,a)}else for(;a<r&&i(e[a],e[a-1])>=0;)a++;return a-t}function pf(e,t,r){for(r--;t<r;){var i=e[t];e[t++]=e[r],e[r--]=i}}function Xa(e,t,r,i,a){for(i===t&&i++;i<r;i++){for(var n=e[i],o=t,s=i,f;o<s;)f=o+s>>>1,a(n,e[f])<0?s=f:o=f+1;var h=i-o;switch(h){case 3:e[o+3]=e[o+2];case 2:e[o+2]=e[o+1];case 1:e[o+1]=e[o];break;default:for(;h>0;)e[o+h]=e[o+h-1],h--}e[o]=n}}function _i(e,t,r,i,a,n){var o=0,s=0,f=1;if(n(e,t[r+a])>0){for(s=i-a;f<s&&n(e,t[r+a+f])>0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s),o+=a,f+=a}else{for(s=a+1;f<s&&n(e,t[r+a-f])<=0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s);var h=o;o=a-f,f=a-h}for(o++;o<f;){var u=o+(f-o>>>1);n(e,t[r+u])>0?o=u+1:f=u}return f}function gi(e,t,r,i,a,n){var o=0,s=0,f=1;if(n(e,t[r+a])<0){for(s=a+1;f<s&&n(e,t[r+a-f])<0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s);var h=o;o=a-f,f=a-h}else{for(s=i-a;f<s&&n(e,t[r+a+f])>=0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s),o+=a,f+=a}for(o++;o<f;){var u=o+(f-o>>>1);n(e,t[r+u])<0?f=u:o=u+1}return f}function _f(e,t){var r=Xr,i,a,n=0;e.length;var o=[];i=[],a=[];function s(c,_){i[n]=c,a[n]=_,n+=1}function f(){for(;n>1;){var c=n-2;if(c>=1&&a[c-1]<=a[c]+a[c+1]||c>=2&&a[c-2]<=a[c]+a[c-1])a[c-1]<a[c+1]&&c--;else if(a[c]>a[c+1])break;u(c)}}function h(){for(;n>1;){var c=n-2;c>0&&a[c-1]<a[c+1]&&c--,u(c)}}function u(c){var _=i[c],g=a[c],d=i[c+1],p=a[c+1];a[c]=g+p,c===n-3&&(i[c+1]=i[c+2],a[c+1]=a[c+2]),n--;var y=gi(e[d],e,_,g,0,t);_+=y,g-=y,g!==0&&(p=_i(e[_+g-1],e,d,p,p-1,t),p!==0&&(g<=p?l(_,g,d,p):v(_,g,d,p)))}function l(c,_,g,d){var p=0;for(p=0;p<_;p++)o[p]=e[c+p];var y=0,m=g,T=c;if(e[T++]=e[m++],--d===0){for(p=0;p<_;p++)e[T+p]=o[y+p];return}if(_===1){for(p=0;p<d;p++)e[T+p]=e[m+p];e[T+d]=o[y];return}for(var b=r,w,L,C;;){w=0,L=0,C=!1;do if(t(e[m],o[y])<0){if(e[T++]=e[m++],L++,w=0,--d===0){C=!0;break}}else if(e[T++]=o[y++],w++,L=0,--_===1){C=!0;break}while((w|L)<b);if(C)break;do{if(w=gi(e[m],o,y,_,0,t),w!==0){for(p=0;p<w;p++)e[T+p]=o[y+p];if(T+=w,y+=w,_-=w,_<=1){C=!0;break}}if(e[T++]=e[m++],--d===0){C=!0;break}if(L=_i(o[y],e,m,d,0,t),L!==0){for(p=0;p<L;p++)e[T+p]=e[m+p];if(T+=L,m+=L,d-=L,d===0){C=!0;break}}if(e[T++]=o[y++],--_===1){C=!0;break}b--}while(w>=Xr||L>=Xr);if(C)break;b<0&&(b=0),b+=2}if(r=b,r<1&&(r=1),_===1){for(p=0;p<d;p++)e[T+p]=e[m+p];e[T+d]=o[y]}else{if(_===0)throw new Error;for(p=0;p<_;p++)e[T+p]=o[y+p]}}function v(c,_,g,d){var p=0;for(p=0;p<d;p++)o[p]=e[g+p];var y=c+_-1,m=d-1,T=g+d-1,b=0,w=0;if(e[T--]=e[y--],--_===0){for(b=T-(d-1),p=0;p<d;p++)e[b+p]=o[p];return}if(d===1){for(T-=_,y-=_,w=T+1,b=y+1,p=_-1;p>=0;p--)e[w+p]=e[b+p];e[T]=o[m];return}for(var L=r;;){var C=0,M=0,S=!1;do if(t(o[m],e[y])<0){if(e[T--]=e[y--],C++,M=0,--_===0){S=!0;break}}else if(e[T--]=o[m--],M++,C=0,--d===1){S=!0;break}while((C|M)<L);if(S)break;do{if(C=_-gi(o[m],e,c,_,_-1,t),C!==0){for(T-=C,y-=C,_-=C,w=T+1,b=y+1,p=C-1;p>=0;p--)e[w+p]=e[b+p];if(_===0){S=!0;break}}if(e[T--]=o[m--],--d===1){S=!0;break}if(M=d-_i(e[y],o,0,d,d-1,t),M!==0){for(T-=M,m-=M,d-=M,w=T+1,b=m+1,p=0;p<M;p++)e[w+p]=o[b+p];if(d<=1){S=!0;break}}if(e[T--]=e[y--],--_===0){S=!0;break}L--}while(C>=Xr||M>=Xr);if(S)break;L<0&&(L=0),L+=2}if(r=L,r<1&&(r=1),d===1){for(T-=_,y-=_,w=T+1,b=y+1,p=_-1;p>=0;p--)e[w+p]=e[b+p];e[T]=o[m]}else{if(d===0)throw new Error;for(b=T-(d-1),p=0;p<d;p++)e[b+p]=o[p]}}return{mergeRuns:f,forceMergeRuns:h,pushRun:s}}function gf(e,t,r,i){r||(r=0),i||(i=e.length);var a=i-r;if(!(a<2)){var n=0;if(a<po){n=Wa(e,r,i,t),Xa(e,r,i,r+n,t);return}var o=_f(e,t),s=df(a);do{if(n=Wa(e,r,i,t),n<s){var f=a;f>s&&(f=s),Xa(e,r,r+f,r+n,t),n=f}o.pushRun(r,n),o.mergeRuns(),a-=n,r+=n}while(a!==0);o.forceMergeRuns()}}var ht=1,Qr=2,Cr=4,Ga=!1;function yi(){Ga||(Ga=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function qa(e,t){return e.zlevel===t.zlevel?e.z===t.z?e.z2-t.z2:e.z-t.z:e.zlevel-t.zlevel}var yf=function(){function e(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=qa}return e.prototype.traverse=function(t,r){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,r)},e.prototype.getDisplayList=function(t,r){r=r||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(r),i},e.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var r=this._roots,i=this._displayList,a=0,n=r.length;a<n;a++)this._updateAndAddDisplayable(r[a],null,t);i.length=this._displayListLen,gf(i,qa)},e.prototype._updateAndAddDisplayable=function(t,r,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var a=t.getClipPath();if(t.ignoreClip)r=null;else if(a){r?r=r.slice():r=[];for(var n=a,o=t;n;)n.parent=o,n.updateTransform(),r.push(n),o=n,n=n.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),f=0;f<s.length;f++){var h=s[f];t.__dirty&&(h.__dirty|=ht),this._updateAndAddDisplayable(h,r,i)}t.__dirty=0}else{var u=t;r&&r.length?u.__clipPaths=r:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(yi(),u.z=0),isNaN(u.z2)&&(yi(),u.z2=0),isNaN(u.zlevel)&&(yi(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var l=t.getDecalElement&&t.getDecalElement();l&&this._updateAndAddDisplayable(l,r,i);var v=t.getTextGuideLine();v&&this._updateAndAddDisplayable(v,r,i);var c=t.getTextContent();c&&this._updateAndAddDisplayable(c,r,i)}},e.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},e.prototype.delRoot=function(t){if(t instanceof Array){for(var r=0,i=t.length;r<i;r++)this.delRoot(t[r]);return}var a=Pt(this._roots,t);a>=0&&this._roots.splice(a,1)},e.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},e.prototype.getRoots=function(){return this._roots},e.prototype.dispose=function(){this._displayList=null,this._roots=null},e}();const mf=yf;var _o;_o=tt.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(e){return setTimeout(e,16)};const ta=_o;var ke={linear:function(e){return e},quadraticIn:function(e){return e*e},quadraticOut:function(e){return e*(2-e)},quadraticInOut:function(e){return(e*=2)<1?.5*e*e:-.5*(--e*(e-2)-1)},cubicIn:function(e){return e*e*e},cubicOut:function(e){return--e*e*e+1},cubicInOut:function(e){return(e*=2)<1?.5*e*e*e:.5*((e-=2)*e*e+2)},quarticIn:function(e){return e*e*e*e},quarticOut:function(e){return 1- --e*e*e*e},quarticInOut:function(e){return(e*=2)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2)},quinticIn:function(e){return e*e*e*e*e},quinticOut:function(e){return--e*e*e*e*e+1},quinticInOut:function(e){return(e*=2)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2)},sinusoidalIn:function(e){return 1-Math.cos(e*Math.PI/2)},sinusoidalOut:function(e){return Math.sin(e*Math.PI/2)},sinusoidalInOut:function(e){return .5*(1-Math.cos(Math.PI*e))},exponentialIn:function(e){return e===0?0:Math.pow(1024,e-1)},exponentialOut:function(e){return e===1?1:1-Math.pow(2,-10*e)},exponentialInOut:function(e){return e===0?0:e===1?1:(e*=2)<1?.5*Math.pow(1024,e-1):.5*(-Math.pow(2,-10*(e-1))+2)},circularIn:function(e){return 1-Math.sqrt(1-e*e)},circularOut:function(e){return Math.sqrt(1- --e*e)},circularInOut:function(e){return(e*=2)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1)},elasticIn:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),-(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)))},elasticOut:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*e)*Math.sin((e-t)*(2*Math.PI)/i)+1)},elasticInOut:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),(e*=2)<1?-.5*(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)):r*Math.pow(2,-10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)*.5+1)},backIn:function(e){var t=1.70158;return e*e*((t+1)*e-t)},backOut:function(e){var t=1.70158;return--e*e*((t+1)*e+t)+1},backInOut:function(e){var t=2.5949095;return(e*=2)<1?.5*(e*e*((t+1)*e-t)):.5*((e-=2)*e*((t+1)*e+t)+2)},bounceIn:function(e){return 1-ke.bounceOut(1-e)},bounceOut:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},bounceInOut:function(e){return e<.5?ke.bounceIn(e*2)*.5:ke.bounceOut(e*2-1)*.5+.5}};const go=ke;var _e=Math.pow,Wt=Math.sqrt,Ue=1e-8,yo=1e-4,Ua=Wt(3),ge=1/3,Rt=zr(),dt=zr(),Hr=zr();function $t(e){return e>-Ue&&e<Ue}function mo(e){return e>Ue||e<-Ue}function N(e,t,r,i,a){var n=1-a;return n*n*(n*e+3*a*t)+a*a*(a*i+3*n*r)}function Za(e,t,r,i,a){var n=1-a;return 3*(((t-e)*n+2*(r-t)*a)*n+(i-r)*a*a)}function wo(e,t,r,i,a,n){var o=i+3*(t-r)-e,s=3*(r-t*2+e),f=3*(t-e),h=e-a,u=s*s-3*o*f,l=s*f-9*o*h,v=f*f-3*s*h,c=0;if($t(u)&&$t(l))if($t(s))n[0]=0;else{var _=-f/s;_>=0&&_<=1&&(n[c++]=_)}else{var g=l*l-4*u*v;if($t(g)){var d=l/u,_=-s/o+d,p=-d/2;_>=0&&_<=1&&(n[c++]=_),p>=0&&p<=1&&(n[c++]=p)}else if(g>0){var y=Wt(g),m=u*s+1.5*o*(-l+y),T=u*s+1.5*o*(-l-y);m<0?m=-_e(-m,ge):m=_e(m,ge),T<0?T=-_e(-T,ge):T=_e(T,ge);var _=(-s-(m+T))/(3*o);_>=0&&_<=1&&(n[c++]=_)}else{var b=(2*u*s-3*o*l)/(2*Wt(u*u*u)),w=Math.acos(b)/3,L=Wt(u),C=Math.cos(w),_=(-s-2*L*C)/(3*o),p=(-s+L*(C+Ua*Math.sin(w)))/(3*o),M=(-s+L*(C-Ua*Math.sin(w)))/(3*o);_>=0&&_<=1&&(n[c++]=_),p>=0&&p<=1&&(n[c++]=p),M>=0&&M<=1&&(n[c++]=M)}}return c}function To(e,t,r,i,a){var n=6*r-12*t+6*e,o=9*t+3*i-3*e-9*r,s=3*t-3*e,f=0;if($t(o)){if(mo(n)){var h=-s/n;h>=0&&h<=1&&(a[f++]=h)}}else{var u=n*n-4*o*s;if($t(u))a[0]=-n/(2*o);else if(u>0){var l=Wt(u),h=(-n+l)/(2*o),v=(-n-l)/(2*o);h>=0&&h<=1&&(a[f++]=h),v>=0&&v<=1&&(a[f++]=v)}}return f}function Xt(e,t,r,i,a,n){var o=(t-e)*a+e,s=(r-t)*a+t,f=(i-r)*a+r,h=(s-o)*a+o,u=(f-s)*a+s,l=(u-h)*a+h;n[0]=e,n[1]=o,n[2]=h,n[3]=l,n[4]=l,n[5]=u,n[6]=f,n[7]=i}function wf(e,t,r,i,a,n,o,s,f,h,u){var l,v=.005,c=1/0,_,g,d,p;Rt[0]=f,Rt[1]=h;for(var y=0;y<1;y+=.05)dt[0]=N(e,r,a,o,y),dt[1]=N(t,i,n,s,y),d=Or(Rt,dt),d<c&&(l=y,c=d);c=1/0;for(var m=0;m<32&&!(v<yo);m++)_=l-v,g=l+v,dt[0]=N(e,r,a,o,_),dt[1]=N(t,i,n,s,_),d=Or(dt,Rt),_>=0&&d<c?(l=_,c=d):(Hr[0]=N(e,r,a,o,g),Hr[1]=N(t,i,n,s,g),p=Or(Hr,Rt),g<=1&&p<c?(l=g,c=p):v*=.5);return u&&(u[0]=N(e,r,a,o,l),u[1]=N(t,i,n,s,l)),Wt(c)}function Tf(e,t,r,i,a,n,o,s,f){for(var h=e,u=t,l=0,v=1/f,c=1;c<=f;c++){var _=c*v,g=N(e,r,a,o,_),d=N(t,i,n,s,_),p=g-h,y=d-u;l+=Math.sqrt(p*p+y*y),h=g,u=d}return l}function Q(e,t,r,i){var a=1-i;return a*(a*e+2*i*t)+i*i*r}function Na(e,t,r,i){return 2*((1-i)*(t-e)+i*(r-t))}function bf(e,t,r,i,a){var n=e-2*t+r,o=2*(t-e),s=e-i,f=0;if($t(n)){if(mo(o)){var h=-s/o;h>=0&&h<=1&&(a[f++]=h)}}else{var u=o*o-4*n*s;if($t(u)){var h=-o/(2*n);h>=0&&h<=1&&(a[f++]=h)}else if(u>0){var l=Wt(u),h=(-o+l)/(2*n),v=(-o-l)/(2*n);h>=0&&h<=1&&(a[f++]=h),v>=0&&v<=1&&(a[f++]=v)}}return f}function bo(e,t,r){var i=e+r-2*t;return i===0?.5:(e-t)/i}function Ze(e,t,r,i,a){var n=(t-e)*i+e,o=(r-t)*i+t,s=(o-n)*i+n;a[0]=e,a[1]=n,a[2]=s,a[3]=s,a[4]=o,a[5]=r}function Cf(e,t,r,i,a,n,o,s,f){var h,u=.005,l=1/0;Rt[0]=o,Rt[1]=s;for(var v=0;v<1;v+=.05){dt[0]=Q(e,r,a,v),dt[1]=Q(t,i,n,v);var c=Or(Rt,dt);c<l&&(h=v,l=c)}l=1/0;for(var _=0;_<32&&!(u<yo);_++){var g=h-u,d=h+u;dt[0]=Q(e,r,a,g),dt[1]=Q(t,i,n,g);var c=Or(dt,Rt);if(g>=0&&c<l)h=g,l=c;else{Hr[0]=Q(e,r,a,d),Hr[1]=Q(t,i,n,d);var p=Or(Hr,Rt);d<=1&&p<l?(h=d,l=p):u*=.5}}return f&&(f[0]=Q(e,r,a,h),f[1]=Q(t,i,n,h)),Wt(l)}function Lf(e,t,r,i,a,n,o){for(var s=e,f=t,h=0,u=1/o,l=1;l<=o;l++){var v=l*u,c=Q(e,r,a,v),_=Q(t,i,n,v),g=c-s,d=_-f;h+=Math.sqrt(g*g+d*d),s=c,f=_}return h}var Mf=/cubic-bezier\(([0-9,\.e ]+)\)/;function Co(e){var t=e&&Mf.exec(e);if(t){var r=t[1].split(","),i=+Pr(r[0]),a=+Pr(r[1]),n=+Pr(r[2]),o=+Pr(r[3]);if(isNaN(i+a+n+o))return;var s=[];return function(f){return f<=0?0:f>=1?1:wo(0,i,n,1,f,s)&&N(0,a,o,1,s[0])}}}var Sf=function(){function e(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ir,this.ondestroy=t.ondestroy||Ir,this.onrestart=t.onrestart||Ir,t.easing&&this.setEasing(t.easing)}return e.prototype.step=function(t,r){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=r;return}var i=this._life,a=t-this._startTime-this._pausedTime,n=a/i;n<0&&(n=0),n=Math.min(n,1);var o=this.easingFunc,s=o?o(n):n;if(this.onframe(s),n===1)if(this.loop){var f=a%i;this._startTime=t-f,this._pausedTime=0,this.onrestart()}else return!0;return!1},e.prototype.pause=function(){this._paused=!0},e.prototype.resume=function(){this._paused=!1},e.prototype.setEasing=function(t){this.easing=t,this.easingFunc=si(t)?t:go[t]||Co(t)},e}();const Pf=Sf;var Lo=function(){function e(t){this.value=t}return e}(),Rf=function(){function e(){this._len=0}return e.prototype.insert=function(t){var r=new Lo(t);return this.insertEntry(r),r},e.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},e.prototype.remove=function(t){var r=t.prev,i=t.next;r?r.next=i:this.head=i,i?i.prev=r:this.tail=r,t.next=t.prev=null,this._len--},e.prototype.len=function(){return this._len},e.prototype.clear=function(){this.head=this.tail=null,this._len=0},e}(),Df=function(){function e(t){this._list=new Rf,this._maxSize=10,this._map={},this._maxSize=t}return e.prototype.put=function(t,r){var i=this._list,a=this._map,n=null;if(a[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var f=i.head;i.remove(f),delete a[f.key],n=f.value,this._lastRemovedEntry=f}s?s.value=r:s=new Lo(r),s.key=t,i.insertEntry(s),a[t]=s}return n},e.prototype.get=function(t){var r=this._map[t],i=this._list;if(r!=null)return r!==i.tail&&(i.remove(r),i.insertEntry(r)),r.value},e.prototype.clear=function(){this._list.clear(),this._map={}},e.prototype.len=function(){return this._list.len()},e}();const Ra=Df;var Va={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function wt(e){return e=Math.round(e),e<0?0:e>255?255:e}function Af(e){return e=Math.round(e),e<0?0:e>360?360:e}function oe(e){return e<0?0:e>1?1:e}function mi(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?wt(parseFloat(t)/100*255):wt(parseInt(t,10))}function kr(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?oe(parseFloat(t)/100):oe(parseFloat(t))}function wi(e,t,r){return r<0?r+=1:r>1&&(r-=1),r*6<1?e+(t-e)*r*6:r*2<1?t:r*3<2?e+(t-e)*(2/3-r)*6:e}function Yt(e,t,r){return e+(t-e)*r}function vt(e,t,r,i,a){return e[0]=t,e[1]=r,e[2]=i,e[3]=a,e}function ra(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e}var Mo=new Ra(20),ye=null;function _r(e,t){ye&&ra(ye,t),ye=Mo.put(e,ye||t.slice())}function Dt(e,t){if(!!e){t=t||[];var r=Mo.get(e);if(r)return ra(t,r);e=e+"";var i=e.replace(/ /g,"").toLowerCase();if(i in Va)return ra(t,Va[i]),_r(e,t),t;var a=i.length;if(i.charAt(0)==="#"){if(a===4||a===5){var n=parseInt(i.slice(1,4),16);if(!(n>=0&&n<=4095)){vt(t,0,0,0,1);return}return vt(t,(n&3840)>>4|(n&3840)>>8,n&240|(n&240)>>4,n&15|(n&15)<<4,a===5?parseInt(i.slice(4),16)/15:1),_r(e,t),t}else if(a===7||a===9){var n=parseInt(i.slice(1,7),16);if(!(n>=0&&n<=16777215)){vt(t,0,0,0,1);return}return vt(t,(n&16711680)>>16,(n&65280)>>8,n&255,a===9?parseInt(i.slice(7),16)/255:1),_r(e,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===a){var f=i.substr(0,o),h=i.substr(o+1,s-(o+1)).split(","),u=1;switch(f){case"rgba":if(h.length!==4)return h.length===3?vt(t,+h[0],+h[1],+h[2],1):vt(t,0,0,0,1);u=kr(h.pop());case"rgb":if(h.length!==3){vt(t,0,0,0,1);return}return vt(t,mi(h[0]),mi(h[1]),mi(h[2]),u),_r(e,t),t;case"hsla":if(h.length!==4){vt(t,0,0,0,1);return}return h[3]=kr(h[3]),ea(h,t),_r(e,t),t;case"hsl":if(h.length!==3){vt(t,0,0,0,1);return}return ea(h,t),_r(e,t),t;default:return}}vt(t,0,0,0,1)}}function ea(e,t){var r=(parseFloat(e[0])%360+360)%360/360,i=kr(e[1]),a=kr(e[2]),n=a<=.5?a*(i+1):a+i-a*i,o=a*2-n;return t=t||[],vt(t,wt(wi(o,n,r+1/3)*255),wt(wi(o,n,r)*255),wt(wi(o,n,r-1/3)*255),1),e.length===4&&(t[3]=e[3]),t}function xf(e){if(!!e){var t=e[0]/255,r=e[1]/255,i=e[2]/255,a=Math.min(t,r,i),n=Math.max(t,r,i),o=n-a,s=(n+a)/2,f,h;if(o===0)f=0,h=0;else{s<.5?h=o/(n+a):h=o/(2-n-a);var u=((n-t)/6+o/2)/o,l=((n-r)/6+o/2)/o,v=((n-i)/6+o/2)/o;t===n?f=v-l:r===n?f=1/3+u-v:i===n&&(f=2/3+l-u),f<0&&(f+=1),f>1&&(f-=1)}var c=[f*360,h,s];return e[3]!=null&&c.push(e[3]),c}}function Vl(e,t){var r=Dt(e);if(r){for(var i=0;i<3;i++)t<0?r[i]=r[i]*(1-t)|0:r[i]=(255-r[i])*t+r[i]|0,r[i]>255?r[i]=255:r[i]<0&&(r[i]=0);return ce(r,r.length===4?"rgba":"rgb")}}function Ql(e,t,r){if(!(!(t&&t.length)||!(e>=0&&e<=1))){r=r||[];var i=e*(t.length-1),a=Math.floor(i),n=Math.ceil(i),o=t[a],s=t[n],f=i-a;return r[0]=wt(Yt(o[0],s[0],f)),r[1]=wt(Yt(o[1],s[1],f)),r[2]=wt(Yt(o[2],s[2],f)),r[3]=oe(Yt(o[3],s[3],f)),r}}function Kl(e,t,r){if(!(!(t&&t.length)||!(e>=0&&e<=1))){var i=e*(t.length-1),a=Math.floor(i),n=Math.ceil(i),o=Dt(t[a]),s=Dt(t[n]),f=i-a,h=ce([wt(Yt(o[0],s[0],f)),wt(Yt(o[1],s[1],f)),wt(Yt(o[2],s[2],f)),oe(Yt(o[3],s[3],f))],"rgba");return r?{color:h,leftIndex:a,rightIndex:n,value:i}:h}}function Jl(e,t,r,i){var a=Dt(e);if(e)return a=xf(a),t!=null&&(a[0]=Af(t)),r!=null&&(a[1]=kr(r)),i!=null&&(a[2]=kr(i)),ce(ea(a),"rgba")}function jl(e,t){var r=Dt(e);if(r&&t!=null)return r[3]=oe(t),ce(r,"rgba")}function ce(e,t){if(!(!e||!e.length)){var r=e[0]+","+e[1]+","+e[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(r+=","+e[3]),t+"("+r+")"}}function Ne(e,t){var r=Dt(e);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*t:0}function Ef(e){return e.type==="linear"}function Ff(e){return e.type==="radial"}(function(){return tt.hasGlobalWindow&&si(window.btoa)?function(e){return window.btoa(unescape(e))}:typeof Buffer<"u"?function(e){return Buffer.from(e).toString("base64")}:function(e){return null}})();var ia=Array.prototype.slice;function It(e,t,r){return(t-e)*r+e}function Ti(e,t,r,i){for(var a=t.length,n=0;n<a;n++)e[n]=It(t[n],r[n],i);return e}function If(e,t,r,i){for(var a=t.length,n=a&&t[0].length,o=0;o<a;o++){e[o]||(e[o]=[]);for(var s=0;s<n;s++)e[o][s]=It(t[o][s],r[o][s],i)}return e}function me(e,t,r,i){for(var a=t.length,n=0;n<a;n++)e[n]=t[n]+r[n]*i;return e}function Qa(e,t,r,i){for(var a=t.length,n=a&&t[0].length,o=0;o<a;o++){e[o]||(e[o]=[]);for(var s=0;s<n;s++)e[o][s]=t[o][s]+r[o][s]*i}return e}function Of(e,t){for(var r=e.length,i=t.length,a=r>i?t:e,n=Math.min(r,i),o=a[n-1]||{color:[0,0,0,0],offset:0},s=n;s<Math.max(r,i);s++)a.push({offset:o.offset,color:o.color.slice()})}function Hf(e,t,r){var i=e,a=t;if(!(!i.push||!a.push)){var n=i.length,o=a.length;if(n!==o){var s=n>o;if(s)i.length=o;else for(var f=n;f<o;f++)i.push(r===1?a[f]:ia.call(a[f]))}for(var h=i[0]&&i[0].length,f=0;f<i.length;f++)if(r===1)isNaN(i[f])&&(i[f]=a[f]);else for(var u=0;u<h;u++)isNaN(i[f][u])&&(i[f][u]=a[f][u])}}function ze(e){if(At(e)){var t=e.length;if(At(e[0])){for(var r=[],i=0;i<t;i++)r.push(ia.call(e[i]));return r}return ia.call(e)}return e}function $e(e){return e[0]=Math.floor(e[0])||0,e[1]=Math.floor(e[1])||0,e[2]=Math.floor(e[2])||0,e[3]=e[3]==null?1:e[3],"rgba("+e.join(",")+")"}function Bf(e){return At(e&&e[0])?2:1}var we=0,Ye=1,So=2,Kr=3,aa=4,na=5,Ka=6;function Ja(e){return e===aa||e===na}function Te(e){return e===Ye||e===So}var Gr=[0,0,0,0],kf=function(){function e(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return e.prototype.isFinished=function(){return this._finished},e.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},e.prototype.needsAnimate=function(){return this.keyframes.length>=1},e.prototype.getAdditiveTrack=function(){return this._additiveTrack},e.prototype.addKeyframe=function(t,r,i){this._needsSort=!0;var a=this.keyframes,n=a.length,o=!1,s=Ka,f=r;if(At(r)){var h=Bf(r);s=h,(h===1&&!He(r[0])||h===2&&!He(r[0][0]))&&(o=!0)}else if(He(r)&&!Os(r))s=we;else if(Ge(r))if(!isNaN(+r))s=we;else{var u=Dt(r);u&&(f=u,s=Kr)}else if(Sa(r)){var l=$({},f);l.colorStops=xt(r.colorStops,function(c){return{offset:c.offset,color:Dt(c.color)}}),Ef(r)?s=aa:Ff(r)&&(s=na),f=l}n===0?this.valType=s:(s!==this.valType||s===Ka)&&(o=!0),this.discrete=this.discrete||o;var v={time:t,value:f,rawValue:r,percent:0};return i&&(v.easing=i,v.easingFunc=si(i)?i:go[i]||Co(i)),a.push(v),v},e.prototype.prepare=function(t,r){var i=this.keyframes;this._needsSort&&i.sort(function(g,d){return g.time-d.time});for(var a=this.valType,n=i.length,o=i[n-1],s=this.discrete,f=Te(a),h=Ja(a),u=0;u<n;u++){var l=i[u],v=l.value,c=o.value;l.percent=l.time/t,s||(f&&u!==n-1?Hf(v,c,a):h&&Of(v.colorStops,c.colorStops))}if(!s&&a!==na&&r&&this.needsAnimate()&&r.needsAnimate()&&a===r.valType&&!r._finished){this._additiveTrack=r;for(var _=i[0].value,u=0;u<n;u++)a===we?i[u].additiveValue=i[u].value-_:a===Kr?i[u].additiveValue=me([],i[u].value,_,-1):Te(a)&&(i[u].additiveValue=a===Ye?me([],i[u].value,_,-1):Qa([],i[u].value,_,-1))}},e.prototype.step=function(t,r){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,a=i?"additiveValue":"value",n=this.valType,o=this.keyframes,s=o.length,f=this.propName,h=n===Kr,u,l=this._lastFr,v=Math.min,c,_;if(s===1)c=_=o[0];else{if(r<0)u=0;else if(r<this._lastFrP){var g=v(l+1,s-1);for(u=g;u>=0&&!(o[u].percent<=r);u--);u=v(u,s-2)}else{for(u=l;u<s&&!(o[u].percent>r);u++);u=v(u-1,s-2)}_=o[u+1],c=o[u]}if(!!(c&&_)){this._lastFr=u,this._lastFrP=r;var d=_.percent-c.percent,p=d===0?1:v((r-c.percent)/d,1);_.easingFunc&&(p=_.easingFunc(p));var y=i?this._additiveValue:h?Gr:t[f];if((Te(n)||h)&&!y&&(y=this._additiveValue=[]),this.discrete)t[f]=p<1?c.rawValue:_.rawValue;else if(Te(n))n===Ye?Ti(y,c[a],_[a],p):If(y,c[a],_[a],p);else if(Ja(n)){var m=c[a],T=_[a],b=n===aa;t[f]={type:b?"linear":"radial",x:It(m.x,T.x,p),y:It(m.y,T.y,p),colorStops:xt(m.colorStops,function(L,C){var M=T.colorStops[C];return{offset:It(L.offset,M.offset,p),color:$e(Ti([],L.color,M.color,p))}}),global:T.global},b?(t[f].x2=It(m.x2,T.x2,p),t[f].y2=It(m.y2,T.y2,p)):t[f].r=It(m.r,T.r,p)}else if(h)Ti(y,c[a],_[a],p),i||(t[f]=$e(y));else{var w=It(c[a],_[a],p);i?this._additiveValue=w:t[f]=w}i&&this._addToTarget(t)}}},e.prototype._addToTarget=function(t){var r=this.valType,i=this.propName,a=this._additiveValue;r===we?t[i]=t[i]+a:r===Kr?(Dt(t[i],Gr),me(Gr,Gr,a,1),t[i]=$e(Gr)):r===Ye?me(t[i],t[i],a,1):r===So&&Qa(t[i],t[i],a,1)},e}(),Da=function(){function e(t,r,i,a){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=r,r&&a){Ma("Can' use additive animation on looped animation.");return}this._additiveAnimators=a,this._allowDiscrete=i}return e.prototype.getMaxTime=function(){return this._maxTime},e.prototype.getDelay=function(){return this._delay},e.prototype.getLoop=function(){return this._loop},e.prototype.getTarget=function(){return this._target},e.prototype.changeTarget=function(t){this._target=t},e.prototype.when=function(t,r,i){return this.whenWithKeys(t,r,j(r),i)},e.prototype.whenWithKeys=function(t,r,i,a){for(var n=this._tracks,o=0;o<i.length;o++){var s=i[o],f=n[s];if(!f){f=n[s]=new kf(s);var h=void 0,u=this._getAdditiveTrack(s);if(u){var l=u.keyframes,v=l[l.length-1];h=v&&v.value,u.valType===Kr&&h&&(h=$e(h))}else h=this._target[s];if(h==null)continue;t>0&&f.addKeyframe(0,ze(h),a),this._trackKeys.push(s)}f.addKeyframe(t,ze(r[s]),a)}return this._maxTime=Math.max(this._maxTime,t),this},e.prototype.pause=function(){this._clip.pause(),this._paused=!0},e.prototype.resume=function(){this._clip.resume(),this._paused=!1},e.prototype.isPaused=function(){return!!this._paused},e.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},e.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var r=t.length,i=0;i<r;i++)t[i].call(this)},e.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,r=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,r)for(var i=0;i<r.length;i++)r[i].call(this)},e.prototype._setTracksFinished=function(){for(var t=this._tracks,r=this._trackKeys,i=0;i<r.length;i++)t[r[i]].setFinished()},e.prototype._getAdditiveTrack=function(t){var r,i=this._additiveAnimators;if(i)for(var a=0;a<i.length;a++){var n=i[a].getTrack(t);n&&(r=n)}return r},e.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var r=this,i=[],a=this._maxTime||0,n=0;n<this._trackKeys.length;n++){var o=this._trackKeys[n],s=this._tracks[o],f=this._getAdditiveTrack(o),h=s.keyframes,u=h.length;if(s.prepare(a,f),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var l=h[u-1];l&&(r._target[s.propName]=l.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var v=new Pf({life:a,loop:this._loop,delay:this._delay||0,onframe:function(c){r._started=2;var _=r._additiveAnimators;if(_){for(var g=!1,d=0;d<_.length;d++)if(_[d]._clip){g=!0;break}g||(r._additiveAnimators=null)}for(var d=0;d<i.length;d++)i[d].step(r._target,c);var p=r._onframeCbs;if(p)for(var d=0;d<p.length;d++)p[d](r._target,c)},ondestroy:function(){r._doneCallback()}});this._clip=v,this.animation&&this.animation.addClip(v),t&&v.setEasing(t)}else this._doneCallback();return this}},e.prototype.stop=function(t){if(!!this._clip){var r=this._clip;t&&r.onframe(1),this._abortedCallback()}},e.prototype.delay=function(t){return this._delay=t,this},e.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},e.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},e.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},e.prototype.getClip=function(){return this._clip},e.prototype.getTrack=function(t){return this._tracks[t]},e.prototype.getTracks=function(){var t=this;return xt(this._trackKeys,function(r){return t._tracks[r]})},e.prototype.stopTracks=function(t,r){if(!t.length||!this._clip)return!0;for(var i=this._tracks,a=this._trackKeys,n=0;n<t.length;n++){var o=i[t[n]];o&&!o.isFinished()&&(r?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,n=0;n<a.length;n++)if(!i[a[n]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},e.prototype.saveTo=function(t,r,i){if(!!t){r=r||this._trackKeys;for(var a=0;a<r.length;a++){var n=r[a],o=this._tracks[n];if(!(!o||o.isFinished())){var s=o.keyframes,f=s[i?0:s.length-1];f&&(t[n]=ze(f.rawValue))}}}},e.prototype.__changeFinalValue=function(t,r){r=r||j(t);for(var i=0;i<r.length;i++){var a=r[i],n=this._tracks[a];if(!!n){var o=n.keyframes;if(o.length>1){var s=o.pop();n.addKeyframe(s.time,t[a]),n.prepare(this._maxTime,n.getAdditiveTrack())}}}},e}();function Ar(){return new Date().getTime()}var zf=function(e){B(t,e);function t(r){var i=e.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,r=r||{},i.stage=r.stage||{},i}return t.prototype.addClip=function(r){r.animation&&this.removeClip(r),this._head?(this._tail.next=r,r.prev=this._tail,r.next=null,this._tail=r):this._head=this._tail=r,r.animation=this},t.prototype.addAnimator=function(r){r.animation=this;var i=r.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(r){if(!!r.animation){var i=r.prev,a=r.next;i?i.next=a:this._head=a,a?a.prev=i:this._tail=i,r.next=r.prev=r.animation=null}},t.prototype.removeAnimator=function(r){var i=r.getClip();i&&this.removeClip(i),r.animation=null},t.prototype.update=function(r){for(var i=Ar()-this._pausedTime,a=i-this._time,n=this._head;n;){var o=n.next,s=n.step(i,a);s&&(n.ondestroy(),this.removeClip(n)),n=o}this._time=i,r||(this.trigger("frame",a),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var r=this;this._running=!0;function i(){r._running&&(ta(i),!r._paused&&r.update())}ta(i)},t.prototype.start=function(){this._running||(this._time=Ar(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Ar(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Ar()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var r=this._head;r;){var i=r.next;r.prev=r.next=r.animation=null,r=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(r,i){i=i||{},this.start();var a=new Da(r,i.loop);return this.addAnimator(a),a},t}($r);const $f=zf;var Yf=300,bi=tt.domSupported,Ci=function(){var e=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],r={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=xt(e,function(a){var n=a.replace("mouse","pointer");return r.hasOwnProperty(n)?n:a});return{mouse:e,touch:t,pointer:i}}(),ja={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},tn=!1;function oa(e){var t=e.pointerType;return t==="pen"||t==="touch"}function Wf(e){e.touching=!0,e.touchTimer!=null&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)}function Li(e){e&&(e.zrByTouch=!0)}function Xf(e,t){return yt(e.dom,new Gf(e,t),!0)}function Po(e,t){for(var r=t,i=!1;r&&r.nodeType!==9&&!(i=r.domBelongToZr||r!==t&&r===e.painterRoot);)r=r.parentNode;return i}var Gf=function(){function e(t,r){this.stopPropagation=Ir,this.stopImmediatePropagation=Ir,this.preventDefault=Ir,this.type=r.type,this.target=this.currentTarget=t.dom,this.pointerType=r.pointerType,this.clientX=r.clientX,this.clientY=r.clientY}return e}(),mt={mousedown:function(e){e=yt(this.dom,e),this.__mayPointerCapture=[e.zrX,e.zrY],this.trigger("mousedown",e)},mousemove:function(e){e=yt(this.dom,e);var t=this.__mayPointerCapture;t&&(e.zrX!==t[0]||e.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",e)},mouseup:function(e){e=yt(this.dom,e),this.__togglePointerCapture(!1),this.trigger("mouseup",e)},mouseout:function(e){e=yt(this.dom,e);var t=e.toElement||e.relatedTarget;Po(this,t)||(this.__pointerCapturing&&(e.zrEventControl="no_globalout"),this.trigger("mouseout",e))},wheel:function(e){tn=!0,e=yt(this.dom,e),this.trigger("mousewheel",e)},mousewheel:function(e){tn||(e=yt(this.dom,e),this.trigger("mousewheel",e))},touchstart:function(e){e=yt(this.dom,e),Li(e),this.__lastTouchMoment=new Date,this.handler.processGesture(e,"start"),mt.mousemove.call(this,e),mt.mousedown.call(this,e)},touchmove:function(e){e=yt(this.dom,e),Li(e),this.handler.processGesture(e,"change"),mt.mousemove.call(this,e)},touchend:function(e){e=yt(this.dom,e),Li(e),this.handler.processGesture(e,"end"),mt.mouseup.call(this,e),+new Date-+this.__lastTouchMoment<Yf&&mt.click.call(this,e)},pointerdown:function(e){mt.mousedown.call(this,e)},pointermove:function(e){oa(e)||mt.mousemove.call(this,e)},pointerup:function(e){mt.mouseup.call(this,e)},pointerout:function(e){oa(e)||mt.mouseout.call(this,e)}};J(["click","dblclick","contextmenu"],function(e){mt[e]=function(t){t=yt(this.dom,t),this.trigger(e,t)}});var sa={pointermove:function(e){oa(e)||sa.mousemove.call(this,e)},pointerup:function(e){sa.mouseup.call(this,e)},mousemove:function(e){this.trigger("mousemove",e)},mouseup:function(e){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",e),t&&(e.zrEventControl="only_globalout",this.trigger("mouseout",e))}};function qf(e,t){var r=t.domHandlers;tt.pointerEventsSupported?J(Ci.pointer,function(i){We(t,i,function(a){r[i].call(e,a)})}):(tt.touchEventsSupported&&J(Ci.touch,function(i){We(t,i,function(a){r[i].call(e,a),Wf(t)})}),J(Ci.mouse,function(i){We(t,i,function(a){a=Pa(a),t.touching||r[i].call(e,a)})}))}function Uf(e,t){tt.pointerEventsSupported?J(ja.pointer,r):tt.touchEventsSupported||J(ja.mouse,r);function r(i){function a(n){n=Pa(n),Po(e,n.target)||(n=Xf(e,n),t.domHandlers[i].call(e,n))}We(t,i,a,{capture:!0})}}function We(e,t,r,i){e.mounted[t]=r,e.listenerOpts[t]=i,ef(e.domTarget,t,r,i)}function Mi(e){var t=e.mounted;for(var r in t)t.hasOwnProperty(r)&&af(e.domTarget,r,t[r],e.listenerOpts[r]);e.mounted={}}var rn=function(){function e(t,r){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=r}return e}(),Zf=function(e){B(t,e);function t(r,i){var a=e.call(this)||this;return a.__pointerCapturing=!1,a.dom=r,a.painterRoot=i,a._localHandlerScope=new rn(r,mt),bi&&(a._globalHandlerScope=new rn(document,sa)),qf(a,a._localHandlerScope),a}return t.prototype.dispose=function(){Mi(this._localHandlerScope),bi&&Mi(this._globalHandlerScope)},t.prototype.setCursor=function(r){this.dom.style&&(this.dom.style.cursor=r||"default")},t.prototype.__togglePointerCapture=function(r){if(this.__mayPointerCapture=null,bi&&+this.__pointerCapturing^+r){this.__pointerCapturing=r;var i=this._globalHandlerScope;r?Uf(this,i):Mi(i)}},t}($r);const Nf=Zf;var Ro=1;tt.hasGlobalWindow&&(Ro=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Ve=Ro,fa=.4,ha="#333",ua="#ccc",Vf="#eee";function se(){return[1,0,0,1,0,0]}function Qf(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=1,e[4]=0,e[5]=0,e}function Kf(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e}function ee(e,t,r){var i=t[0]*r[0]+t[2]*r[1],a=t[1]*r[0]+t[3]*r[1],n=t[0]*r[2]+t[2]*r[3],o=t[1]*r[2]+t[3]*r[3],s=t[0]*r[4]+t[2]*r[5]+t[4],f=t[1]*r[4]+t[3]*r[5]+t[5];return e[0]=i,e[1]=a,e[2]=n,e[3]=o,e[4]=s,e[5]=f,e}function la(e,t,r){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4]+r[0],e[5]=t[5]+r[1],e}function Do(e,t,r){var i=t[0],a=t[2],n=t[4],o=t[1],s=t[3],f=t[5],h=Math.sin(r),u=Math.cos(r);return e[0]=i*u+o*h,e[1]=-i*h+o*u,e[2]=a*u+s*h,e[3]=-a*h+u*s,e[4]=u*n+h*f,e[5]=u*f-h*n,e}function Ao(e,t,r){var i=r[0],a=r[1];return e[0]=t[0]*i,e[1]=t[1]*a,e[2]=t[2]*i,e[3]=t[3]*a,e[4]=t[4]*i,e[5]=t[5]*a,e}function Jf(e,t){var r=t[0],i=t[2],a=t[4],n=t[1],o=t[3],s=t[5],f=r*o-n*i;return f?(f=1/f,e[0]=o*f,e[1]=-n*f,e[2]=-i*f,e[3]=r*f,e[4]=(i*s-o*a)*f,e[5]=(n*a-r*s)*f,e):null}var en=Qf,an=5e-5;function Zt(e){return e>an||e<-an}var Nt=[],gr=[],Si=se(),Pi=Math.abs,jf=function(){function e(){}return e.prototype.getLocalTransform=function(t){return e.getLocalTransform(this,t)},e.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},e.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},e.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},e.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},e.prototype.needLocalTransform=function(){return Zt(this.rotation)||Zt(this.x)||Zt(this.y)||Zt(this.scaleX-1)||Zt(this.scaleY-1)||Zt(this.skewX)||Zt(this.skewY)},e.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,r=this.needLocalTransform(),i=this.transform;if(!(r||t)){i&&en(i);return}i=i||se(),r?this.getLocalTransform(i):en(i),t&&(r?ee(i,t,i):Kf(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},e.prototype._resolveGlobalScaleRatio=function(t){var r=this.globalScaleRatio;if(r!=null&&r!==1){this.getGlobalScale(Nt);var i=Nt[0]<0?-1:1,a=Nt[1]<0?-1:1,n=((Nt[0]-i)*r+i)/Nt[0]||0,o=((Nt[1]-a)*r+a)/Nt[1]||0;t[0]*=n,t[1]*=n,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||se(),Jf(this.invTransform,t)},e.prototype.getComputedTransform=function(){for(var t=this,r=[];t;)r.push(t),t=t.parent;for(;t=r.pop();)t.updateTransform();return this.transform},e.prototype.setLocalTransform=function(t){if(!!t){var r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],a=Math.atan2(t[1],t[0]),n=Math.PI/2+a-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-a,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0}},e.prototype.decomposeTransform=function(){if(!!this.transform){var t=this.parent,r=this.transform;t&&t.transform&&(ee(gr,t.invTransform,r),r=gr);var i=this.originX,a=this.originY;(i||a)&&(Si[4]=i,Si[5]=a,ee(gr,r,Si),gr[4]-=i,gr[5]-=a,r=gr),this.setLocalTransform(r)}},e.prototype.getGlobalScale=function(t){var r=this.transform;return t=t||[],r?(t[0]=Math.sqrt(r[0]*r[0]+r[1]*r[1]),t[1]=Math.sqrt(r[2]*r[2]+r[3]*r[3]),r[0]<0&&(t[0]=-t[0]),r[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},e.prototype.transformCoordToLocal=function(t,r){var i=[t,r],a=this.invTransform;return a&&re(i,i,a),i},e.prototype.transformCoordToGlobal=function(t,r){var i=[t,r],a=this.transform;return a&&re(i,i,a),i},e.prototype.getLineScale=function(){var t=this.transform;return t&&Pi(t[0]-1)>1e-10&&Pi(t[3]-1)>1e-10?Math.sqrt(Pi(t[0]*t[3]-t[2]*t[1])):1},e.prototype.copyTransform=function(t){th(this,t)},e.getLocalTransform=function(t,r){r=r||[];var i=t.originX||0,a=t.originY||0,n=t.scaleX,o=t.scaleY,s=t.anchorX,f=t.anchorY,h=t.rotation||0,u=t.x,l=t.y,v=t.skewX?Math.tan(t.skewX):0,c=t.skewY?Math.tan(-t.skewY):0;if(i||a||s||f){var _=i+s,g=a+f;r[4]=-_*n-v*g*o,r[5]=-g*o-c*_*n}else r[4]=r[5]=0;return r[0]=n,r[3]=o,r[1]=c*n,r[2]=v*o,h&&Do(r,r,h),r[4]+=i+u,r[5]+=a+l,r},e.initDefaultProps=function(){var t=e.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),e}(),fe=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function th(e,t){for(var r=0;r<fe.length;r++){var i=fe[r];e[i]=t[i]}}const Aa=jf;var rh=function(){function e(t,r){this.x=t||0,this.y=r||0}return e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,r){return this.x=t,this.y=r,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,r){this.x+=t.x*r,this.y+=t.y*r},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var r=this.x-t.x,i=this.y-t.y;return Math.sqrt(r*r+i*i)},e.prototype.distanceSquare=function(t){var r=this.x-t.x,i=this.y-t.y;return r*r+i*i},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){if(!!t){var r=this.x,i=this.y;return this.x=t[0]*r+t[2]*i+t[4],this.y=t[1]*r+t[3]*i+t[5],this}},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,r,i){t.x=r,t.y=i},e.copy=function(t,r){t.x=r.x,t.y=r.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,r){return t.x*r.x+t.y*r.y},e.add=function(t,r,i){t.x=r.x+i.x,t.y=r.y+i.y},e.sub=function(t,r,i){t.x=r.x-i.x,t.y=r.y-i.y},e.scale=function(t,r,i){t.x=r.x*i,t.y=r.y*i},e.scaleAndAdd=function(t,r,i,a){t.x=r.x+i.x*a,t.y=r.y+i.y*a},e.lerp=function(t,r,i,a){var n=1-a;t.x=n*r.x+a*i.x,t.y=n*r.y+a*i.y},e}();const I=rh;var be=Math.min,Ce=Math.max,Vt=new I,Qt=new I,Kt=new I,Jt=new I,qr=new I,Ur=new I,eh=function(){function e(t,r,i,a){i<0&&(t=t+i,i=-i),a<0&&(r=r+a,a=-a),this.x=t,this.y=r,this.width=i,this.height=a}return e.prototype.union=function(t){var r=be(t.x,this.x),i=be(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Ce(t.x+t.width,this.x+this.width)-r:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Ce(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=r,this.y=i},e.prototype.applyTransform=function(t){e.applyTransform(this,this,t)},e.prototype.calculateTransform=function(t){var r=this,i=t.width/r.width,a=t.height/r.height,n=se();return la(n,n,[-r.x,-r.y]),Ao(n,n,[i,a]),la(n,n,[t.x,t.y]),n},e.prototype.intersect=function(t,r){if(!t)return!1;t instanceof e||(t=e.create(t));var i=this,a=i.x,n=i.x+i.width,o=i.y,s=i.y+i.height,f=t.x,h=t.x+t.width,u=t.y,l=t.y+t.height,v=!(n<f||h<a||s<u||l<o);if(r){var c=1/0,_=0,g=Math.abs(n-f),d=Math.abs(h-a),p=Math.abs(s-u),y=Math.abs(l-o),m=Math.min(g,d),T=Math.min(p,y);n<f||h<a?m>_&&(_=m,g<d?I.set(Ur,-g,0):I.set(Ur,d,0)):m<c&&(c=m,g<d?I.set(qr,g,0):I.set(qr,-d,0)),s<u||l<o?T>_&&(_=T,p<y?I.set(Ur,0,-p):I.set(Ur,0,y)):m<c&&(c=m,p<y?I.set(qr,0,p):I.set(qr,0,-y))}return r&&I.copy(r,v?qr:Ur),v},e.prototype.contain=function(t,r){var i=this;return t>=i.x&&t<=i.x+i.width&&r>=i.y&&r<=i.y+i.height},e.prototype.clone=function(){return new e(this.x,this.y,this.width,this.height)},e.prototype.copy=function(t){e.copy(this,t)},e.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},e.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},e.prototype.isZero=function(){return this.width===0||this.height===0},e.create=function(t){return new e(t.x,t.y,t.width,t.height)},e.copy=function(t,r){t.x=r.x,t.y=r.y,t.width=r.width,t.height=r.height},e.applyTransform=function(t,r,i){if(!i){t!==r&&e.copy(t,r);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var a=i[0],n=i[3],o=i[4],s=i[5];t.x=r.x*a+o,t.y=r.y*n+s,t.width=r.width*a,t.height=r.height*n,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}Vt.x=Kt.x=r.x,Vt.y=Jt.y=r.y,Qt.x=Jt.x=r.x+r.width,Qt.y=Kt.y=r.y+r.height,Vt.transform(i),Jt.transform(i),Qt.transform(i),Kt.transform(i),t.x=be(Vt.x,Qt.x,Kt.x,Jt.x),t.y=be(Vt.y,Qt.y,Kt.y,Jt.y);var f=Ce(Vt.x,Qt.x,Kt.x,Jt.x),h=Ce(Vt.y,Qt.y,Kt.y,Jt.y);t.width=f-t.x,t.height=h-t.y},e}();const W=eh;var nn={};function ut(e,t){t=t||cr;var r=nn[t];r||(r=nn[t]=new Ra(500));var i=r.get(e);return i==null&&(i=le.measureText(e,t).width,r.put(e,i)),i}function on(e,t,r,i){var a=ut(e,t),n=xa(t),o=Jr(0,a,r),s=Lr(0,n,i),f=new W(o,s,a,n);return f}function ih(e,t,r,i){var a=((e||"")+"").split(`
`),n=a.length;if(n===1)return on(a[0],t,r,i);for(var o=new W(0,0,0,0),s=0;s<a.length;s++){var f=on(a[s],t,r,i);s===0?o.copy(f):o.union(f)}return o}function Jr(e,t,r){return r==="right"?e-=t:r==="center"&&(e-=t/2),e}function Lr(e,t,r){return r==="middle"?e-=t/2:r==="bottom"&&(e-=t),e}function xa(e){return ut("\u56FD",e)}function he(e,t){return typeof e=="string"?e.lastIndexOf("%")>=0?parseFloat(e)/100*t:parseFloat(e):e}function ah(e,t,r){var i=t.position||"inside",a=t.distance!=null?t.distance:5,n=r.height,o=r.width,s=n/2,f=r.x,h=r.y,u="left",l="top";if(i instanceof Array)f+=he(i[0],r.width),h+=he(i[1],r.height),u=null,l=null;else switch(i){case"left":f-=a,h+=s,u="right",l="middle";break;case"right":f+=a+o,h+=s,l="middle";break;case"top":f+=o/2,h-=a,u="center",l="bottom";break;case"bottom":f+=o/2,h+=n+a,u="center";break;case"inside":f+=o/2,h+=s,u="center",l="middle";break;case"insideLeft":f+=a,h+=s,l="middle";break;case"insideRight":f+=o-a,h+=s,u="right",l="middle";break;case"insideTop":f+=o/2,h+=a,u="center";break;case"insideBottom":f+=o/2,h+=n-a,u="center",l="bottom";break;case"insideTopLeft":f+=a,h+=a;break;case"insideTopRight":f+=o-a,h+=a,u="right";break;case"insideBottomLeft":f+=a,h+=n-a,l="bottom";break;case"insideBottomRight":f+=o-a,h+=n-a,u="right",l="bottom";break}return e=e||{},e.x=f,e.y=h,e.align=u,e.verticalAlign=l,e}var Ri="__zr_normal__",Di=fe.concat(["ignore"]),nh=oi(fe,function(e,t){return e[t]=!0,e},{ignore:!1}),yr={},oh=new W(0,0,0,0),Ea=function(){function e(t){this.id=no(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return e.prototype._init=function(t){this.attr(t)},e.prototype.drift=function(t,r,i){switch(this.draggable){case"horizontal":r=0;break;case"vertical":t=0;break}var a=this.transform;a||(a=this.transform=[1,0,0,1,0,0]),a[4]+=t,a[5]+=r,this.decomposeTransform(),this.markRedraw()},e.prototype.beforeUpdate=function(){},e.prototype.afterUpdate=function(){},e.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},e.prototype.updateInnerText=function(t){var r=this._textContent;if(r&&(!r.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,a=i.local,n=r.innerTransformable,o=void 0,s=void 0,f=!1;n.parent=a?this:null;var h=!1;if(n.copyTransform(r),i.position!=null){var u=oh;i.layoutRect?u.copy(i.layoutRect):u.copy(this.getBoundingRect()),a||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(yr,i,u):ah(yr,i,u),n.x=yr.x,n.y=yr.y,o=yr.align,s=yr.verticalAlign;var l=i.origin;if(l&&i.rotation!=null){var v=void 0,c=void 0;l==="center"?(v=u.width*.5,c=u.height*.5):(v=he(l[0],u.width),c=he(l[1],u.height)),h=!0,n.originX=-n.x+v+(a?0:u.x),n.originY=-n.y+c+(a?0:u.y)}}i.rotation!=null&&(n.rotation=i.rotation);var _=i.offset;_&&(n.x+=_[0],n.y+=_[1],h||(n.originX=-_[0],n.originY=-_[1]));var g=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,d=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),p=void 0,y=void 0,m=void 0;g&&this.canBeInsideText()?(p=i.insideFill,y=i.insideStroke,(p==null||p==="auto")&&(p=this.getInsideTextFill()),(y==null||y==="auto")&&(y=this.getInsideTextStroke(p),m=!0)):(p=i.outsideFill,y=i.outsideStroke,(p==null||p==="auto")&&(p=this.getOutsideFill()),(y==null||y==="auto")&&(y=this.getOutsideStroke(p),m=!0)),p=p||"#000",(p!==d.fill||y!==d.stroke||m!==d.autoStroke||o!==d.align||s!==d.verticalAlign)&&(f=!0,d.fill=p,d.stroke=y,d.autoStroke=m,d.align=o,d.verticalAlign=s,r.setDefaultTextStyle(d)),r.__dirty|=ht,f&&r.dirtyStyle(!0)}},e.prototype.canBeInsideText=function(){return!0},e.prototype.getInsideTextFill=function(){return"#fff"},e.prototype.getInsideTextStroke=function(t){return"#000"},e.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?ua:ha},e.prototype.getOutsideStroke=function(t){var r=this.__zr&&this.__zr.getBackgroundColor(),i=typeof r=="string"&&Dt(r);i||(i=[255,255,255,1]);for(var a=i[3],n=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*a+(n?0:255)*(1-a);return i[3]=1,ce(i,"rgba")},e.prototype.traverse=function(t,r){},e.prototype.attrKV=function(t,r){t==="textConfig"?this.setTextConfig(r):t==="textContent"?this.setTextContent(r):t==="clipPath"?this.setClipPath(r):t==="extra"?(this.extra=this.extra||{},$(this.extra,r)):this[t]=r},e.prototype.hide=function(){this.ignore=!0,this.markRedraw()},e.prototype.show=function(){this.ignore=!1,this.markRedraw()},e.prototype.attr=function(t,r){if(typeof t=="string")this.attrKV(t,r);else if(zt(t))for(var i=t,a=j(i),n=0;n<a.length;n++){var o=a[n];this.attrKV(o,t[o])}return this.markRedraw(),this},e.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var r=this._normalState,i=0;i<this.animators.length;i++){var a=this.animators[i],n=a.__fromStateTransition;if(!(a.getLoop()||n&&n!==Ri)){var o=a.targetName,s=o?r[o]:r;a.saveTo(s)}}},e.prototype._innerSaveToNormal=function(t){var r=this._normalState;r||(r=this._normalState={}),t.textConfig&&!r.textConfig&&(r.textConfig=this.textConfig),this._savePrimaryToNormal(t,r,Di)},e.prototype._savePrimaryToNormal=function(t,r,i){for(var a=0;a<i.length;a++){var n=i[a];t[n]!=null&&!(n in r)&&(r[n]=this[n])}},e.prototype.hasState=function(){return this.currentStates.length>0},e.prototype.getState=function(t){return this.states[t]},e.prototype.ensureState=function(t){var r=this.states;return r[t]||(r[t]={}),r[t]},e.prototype.clearStates=function(t){this.useState(Ri,!1,t)},e.prototype.useState=function(t,r,i,a){var n=t===Ri,o=this.hasState();if(!(!o&&n)){var s=this.currentStates,f=this.stateTransition;if(!(Pt(s,t)>=0&&(r||s.length===1))){var h;if(this.stateProxy&&!n&&(h=this.stateProxy(t)),h||(h=this.states&&this.states[t]),!h&&!n){Ma("State "+t+" not exists.");return}n||this.saveCurrentToNormalState(h);var u=!!(h&&h.hoverLayer||a);u&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,h,this._normalState,r,!i&&!this.__inHover&&f&&f.duration>0,f);var l=this._textContent,v=this._textGuide;return l&&l.useState(t,r,i,u),v&&v.useState(t,r,i,u),n?(this.currentStates=[],this._normalState={}):r?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!u&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ht),h}}},e.prototype.useStates=function(t,r,i){if(!t.length)this.clearStates();else{var a=[],n=this.currentStates,o=t.length,s=o===n.length;if(s){for(var f=0;f<o;f++)if(t[f]!==n[f]){s=!1;break}}if(s)return;for(var f=0;f<o;f++){var h=t[f],u=void 0;this.stateProxy&&(u=this.stateProxy(h,t)),u||(u=this.states[h]),u&&a.push(u)}var l=a[o-1],v=!!(l&&l.hoverLayer||i);v&&this._toggleHoverLayerFlag(!0);var c=this._mergeStates(a),_=this.stateTransition;this.saveCurrentToNormalState(c),this._applyStateObj(t.join(","),c,this._normalState,!1,!r&&!this.__inHover&&_&&_.duration>0,_);var g=this._textContent,d=this._textGuide;g&&g.useStates(t,r,v),d&&d.useStates(t,r,v),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!v&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ht)}},e.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var r=this.animators[t];r.targetName&&r.changeTarget(this[r.targetName])}},e.prototype.removeState=function(t){var r=Pt(this.currentStates,t);if(r>=0){var i=this.currentStates.slice();i.splice(r,1),this.useStates(i)}},e.prototype.replaceState=function(t,r,i){var a=this.currentStates.slice(),n=Pt(a,t),o=Pt(a,r)>=0;n>=0?o?a.splice(n,1):a[n]=r:i&&!o&&a.push(r),this.useStates(a)},e.prototype.toggleState=function(t,r){r?this.useState(t,!0):this.removeState(t)},e.prototype._mergeStates=function(t){for(var r={},i,a=0;a<t.length;a++){var n=t[a];$(r,n),n.textConfig&&(i=i||{},$(i,n.textConfig))}return i&&(r.textConfig=i),r},e.prototype._applyStateObj=function(t,r,i,a,n,o){var s=!(r&&a);r&&r.textConfig?(this.textConfig=$({},a?this.textConfig:i.textConfig),$(this.textConfig,r.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var f={},h=!1,u=0;u<Di.length;u++){var l=Di[u],v=n&&nh[l];r&&r[l]!=null?v?(h=!0,f[l]=r[l]):this[l]=r[l]:s&&i[l]!=null&&(v?(h=!0,f[l]=i[l]):this[l]=i[l])}if(!n)for(var u=0;u<this.animators.length;u++){var c=this.animators[u],_=c.targetName;c.getLoop()||c.__changeFinalValue(_?(r||i)[_]:r||i)}h&&this._transitionState(t,f,o)},e.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var r=this.__zr;r&&t.addSelfToZr(r),t.__zr=r,t.__hostTarget=this}},e.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},e.prototype.getClipPath=function(){return this._clipPath},e.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},e.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},e.prototype.getTextContent=function(){return this._textContent},e.prototype.setTextContent=function(t){var r=this._textContent;r!==t&&(r&&r!==t&&this.removeTextContent(),t.innerTransformable=new Aa,this._attachComponent(t),this._textContent=t,this.markRedraw())},e.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),$(this.textConfig,t),this.markRedraw()},e.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},e.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},e.prototype.getTextGuideLine=function(){return this._textGuide},e.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},e.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},e.prototype.markRedraw=function(){this.__dirty|=ht;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},e.prototype.dirty=function(){this.markRedraw()},e.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var r=this._textContent,i=this._textGuide;r&&(r.__inHover=t),i&&(i.__inHover=t)},e.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var r=this.animators;if(r)for(var i=0;i<r.length;i++)t.animation.addAnimator(r[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},e.prototype.removeSelfFromZr=function(t){if(!!this.__zr){this.__zr=null;var r=this.animators;if(r)for(var i=0;i<r.length;i++)t.animation.removeAnimator(r[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},e.prototype.animate=function(t,r,i){var a=t?this[t]:this,n=new Da(a,r,i);return t&&(n.targetName=t),this.addAnimator(n,t),n},e.prototype.addAnimator=function(t,r){var i=this.__zr,a=this;t.during(function(){a.updateDuringAnimation(r)}).done(function(){var n=a.animators,o=Pt(n,t);o>=0&&n.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},e.prototype.updateDuringAnimation=function(t){this.markRedraw()},e.prototype.stopAnimation=function(t,r){for(var i=this.animators,a=i.length,n=[],o=0;o<a;o++){var s=i[o];!t||t===s.scope?s.stop(r):n.push(s)}return this.animators=n,this},e.prototype.animateTo=function(t,r,i){Ai(this,t,r,i)},e.prototype.animateFrom=function(t,r,i){Ai(this,t,r,i,!0)},e.prototype._transitionState=function(t,r,i,a){for(var n=Ai(this,r,i,a),o=0;o<n.length;o++)n[o].__fromStateTransition=t},e.prototype.getBoundingRect=function(){return null},e.prototype.getPaintRect=function(){return null},e.initDefaultProps=function(){var t=e.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=ht;function r(i,a,n,o){Object.defineProperty(t,i,{get:function(){if(!this[a]){var f=this[a]=[];s(this,f)}return this[a]},set:function(f){this[n]=f[0],this[o]=f[1],this[a]=f,s(this,f)}});function s(f,h){Object.defineProperty(h,0,{get:function(){return f[n]},set:function(u){f[n]=u}}),Object.defineProperty(h,1,{get:function(){return f[o]},set:function(u){f[o]=u}})}}Object.defineProperty&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),e}();oo(Ea,$r);oo(Ea,Aa);function Ai(e,t,r,i,a){r=r||{};var n=[];xo(e,"",e,t,r,i,n,a);var o=n.length,s=!1,f=r.done,h=r.aborted,u=function(){s=!0,o--,o<=0&&(s?f&&f():h&&h())},l=function(){o--,o<=0&&(s?f&&f():h&&h())};o||f&&f(),n.length>0&&r.during&&n[0].during(function(_,g){r.during(g)});for(var v=0;v<n.length;v++){var c=n[v];u&&c.done(u),l&&c.aborted(l),r.force&&c.duration(r.duration),c.start(r.easing)}return n}function xi(e,t,r){for(var i=0;i<r;i++)e[i]=t[i]}function sh(e){return At(e[0])}function fh(e,t,r){if(At(t[r]))if(At(e[r])||(e[r]=[]),Fs(t[r])){var i=t[r].length;e[r].length!==i&&(e[r]=new t[r].constructor(i),xi(e[r],t[r],i))}else{var a=t[r],n=e[r],o=a.length;if(sh(a))for(var s=a[0].length,f=0;f<o;f++)n[f]?xi(n[f],a[f],s):n[f]=Array.prototype.slice.call(a[f]);else xi(n,a,o);n.length=a.length}else e[r]=t[r]}function hh(e,t){return e===t||At(e)&&At(t)&&uh(e,t)}function uh(e,t){var r=e.length;if(r!==t.length)return!1;for(var i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function xo(e,t,r,i,a,n,o,s){for(var f=j(i),h=a.duration,u=a.delay,l=a.additive,v=a.setToFinal,c=!zt(n),_=e.animators,g=[],d=0;d<f.length;d++){var p=f[d],y=i[p];if(y!=null&&r[p]!=null&&(c||n[p]))if(zt(y)&&!At(y)&&!Sa(y)){if(t){s||(r[p]=y,e.updateDuringAnimation(t));continue}xo(e,p,r[p],y,a,n&&n[p],o,s)}else g.push(p);else s||(r[p]=y,e.updateDuringAnimation(t),g.push(p))}var m=g.length;if(!l&&m)for(var T=0;T<_.length;T++){var b=_[T];if(b.targetName===t){var w=b.stopTracks(g);if(w){var L=Pt(_,b);_.splice(L,1)}}}if(a.force||(g=Oa(g,function(P){return!hh(i[P],r[P])}),m=g.length),m>0||a.force&&!o.length){var C=void 0,M=void 0,S=void 0;if(s){M={},v&&(C={});for(var T=0;T<m;T++){var p=g[T];M[p]=r[p],v?C[p]=i[p]:r[p]=i[p]}}else if(v){S={};for(var T=0;T<m;T++){var p=g[T];S[p]=ze(r[p]),fh(r,i,p)}}var b=new Da(r,!1,!1,l?Oa(_,function(R){return R.targetName===t}):null);b.targetName=t,a.scope&&(b.scope=a.scope),v&&C&&b.whenWithKeys(0,C,g),S&&b.whenWithKeys(0,S,g),b.whenWithKeys(h==null?500:h,s?M:i,g).delay(u||0),e.addAnimator(b,t),o.push(b)}}const Eo=Ea;var Fo=function(e){B(t,e);function t(r){var i=e.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(r),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(r){return this._children[r]},t.prototype.childOfName=function(r){for(var i=this._children,a=0;a<i.length;a++)if(i[a].name===r)return i[a]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(r){return r&&r!==this&&r.parent!==this&&(this._children.push(r),this._doAdd(r)),this},t.prototype.addBefore=function(r,i){if(r&&r!==this&&r.parent!==this&&i&&i.parent===this){var a=this._children,n=a.indexOf(i);n>=0&&(a.splice(n,0,r),this._doAdd(r))}return this},t.prototype.replace=function(r,i){var a=Pt(this._children,r);return a>=0&&this.replaceAt(i,a),this},t.prototype.replaceAt=function(r,i){var a=this._children,n=a[i];if(r&&r!==this&&r.parent!==this&&r!==n){a[i]=r,n.parent=null;var o=this.__zr;o&&n.removeSelfFromZr(o),this._doAdd(r)}return this},t.prototype._doAdd=function(r){r.parent&&r.parent.remove(r),r.parent=this;var i=this.__zr;i&&i!==r.__zr&&r.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(r){var i=this.__zr,a=this._children,n=Pt(a,r);return n<0?this:(a.splice(n,1),r.parent=null,i&&r.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var r=this._children,i=this.__zr,a=0;a<r.length;a++){var n=r[a];i&&n.removeSelfFromZr(i),n.parent=null}return r.length=0,this},t.prototype.eachChild=function(r,i){for(var a=this._children,n=0;n<a.length;n++){var o=a[n];r.call(i,o,n)}return this},t.prototype.traverse=function(r,i){for(var a=0;a<this._children.length;a++){var n=this._children[a],o=r.call(i,n);n.isGroup&&!o&&n.traverse(r,i)}return this},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var i=0;i<this._children.length;i++){var a=this._children[i];a.addSelfToZr(r)}},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var i=0;i<this._children.length;i++){var a=this._children[i];a.removeSelfFromZr(r)}},t.prototype.getBoundingRect=function(r){for(var i=new W(0,0,0,0),a=r||this._children,n=[],o=null,s=0;s<a.length;s++){var f=a[s];if(!(f.ignore||f.invisible)){var h=f.getBoundingRect(),u=f.getLocalTransform(n);u?(W.applyTransform(i,h,u),o=o||i.clone(),o.union(i)):(o=o||h.clone(),o.union(h))}}return o||i},t}(Eo);Fo.prototype.type="group";const Mr=Fo;/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Xe={},Io={};function lh(e){delete Io[e]}function vh(e){if(!e)return!1;if(typeof e=="string")return Ne(e,1)<fa;if(e.colorStops){for(var t=e.colorStops,r=0,i=t.length,a=0;a<i;a++)r+=Ne(t[a].color,1);return r/=i,r<fa}return!1}var ch=function(){function e(t,r,i){var a=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=r,this.id=t;var n=new mf,o=i.renderer||"canvas";Xe[o]||(o=j(Xe)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Xe[o](r,n,i,t),f=i.ssr||s.ssrOnly;this.storage=n,this.painter=s;var h=!tt.node&&!tt.worker&&!f?new Nf(s.getViewportRoot(),s.root):null;this.handler=new cf(n,s,h,s.root),this.animation=new $f({stage:{update:f?null:function(){return a._flush(!0)}}}),f||this.animation.start()}return e.prototype.add=function(t){!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},e.prototype.remove=function(t){!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},e.prototype.configLayer=function(t,r){this.painter.configLayer&&this.painter.configLayer(t,r),this.refresh()},e.prototype.setBackgroundColor=function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=vh(t)},e.prototype.getBackgroundColor=function(){return this._backgroundColor},e.prototype.setDarkMode=function(t){this._darkMode=t},e.prototype.isDarkMode=function(){return this._darkMode},e.prototype.refreshImmediately=function(t){t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},e.prototype.refresh=function(){this._needsRefresh=!0,this.animation.start()},e.prototype.flush=function(){this._flush(!1)},e.prototype._flush=function(t){var r,i=Ar();this._needsRefresh&&(r=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(r=!0,this.refreshHoverImmediately());var a=Ar();r?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:a-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},e.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},e.prototype.wakeUp=function(){this.animation.start(),this._stillFrameAccum=0},e.prototype.refreshHover=function(){this._needsRefreshHover=!0},e.prototype.refreshHoverImmediately=function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover()},e.prototype.resize=function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},e.prototype.clearAnimation=function(){this.animation.clear()},e.prototype.getWidth=function(){return this.painter.getWidth()},e.prototype.getHeight=function(){return this.painter.getHeight()},e.prototype.setCursorStyle=function(t){this.handler.setCursorStyle(t)},e.prototype.findHover=function(t,r){return this.handler.findHover(t,r)},e.prototype.on=function(t,r,i){return this.handler.on(t,r,i),this},e.prototype.off=function(t,r){this.handler.off(t,r)},e.prototype.trigger=function(t,r){this.handler.trigger(t,r)},e.prototype.clear=function(){for(var t=this.storage.getRoots(),r=0;r<t.length;r++)t[r]instanceof Mr&&t[r].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()},e.prototype.dispose=function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,lh(this.id)},e}();function tv(e,t){var r=new ch(no(),e,t);return Io[r.id]=r,r}function rv(e,t){Xe[e]=t}var va=new Ra(50);function dh(e){if(typeof e=="string"){var t=va.get(e);return t&&t.image}else return e}function Oo(e,t,r,i,a){if(e)if(typeof e=="string"){if(t&&t.__zrImageSrc===e||!r)return t;var n=va.get(e),o={hostEl:r,cb:i,cbPayload:a};return n?(t=n.image,!hi(t)&&n.pending.push(o)):(t=le.loadImage(e,sn,sn),t.__zrImageSrc=e,va.put(e,t.__cachedImgObj={image:t,pending:[o]})),t}else return e;else return t}function sn(){var e=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<e.pending.length;t++){var r=e.pending[t],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}e.pending.length=0}function hi(e){return e&&e.width&&e.height}var Ei=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function ph(e,t,r,i,a){if(!t)return"";var n=(e+"").split(`
`);a=Ho(t,r,i,a);for(var o=0,s=n.length;o<s;o++)n[o]=Bo(n[o],a);return n.join(`
`)}function Ho(e,t,r,i){i=i||{};var a=$({},i);a.font=t,r=pt(r,"..."),a.maxIterations=pt(i.maxIterations,2);var n=a.minChar=pt(i.minChar,0);a.cnCharWidth=ut("\u56FD",t);var o=a.ascCharWidth=ut("a",t);a.placeholder=pt(i.placeholder,"");for(var s=e=Math.max(0,e-1),f=0;f<n&&s>=o;f++)s-=o;var h=ut(r,t);return h>s&&(r="",h=0),s=e-h,a.ellipsis=r,a.ellipsisWidth=h,a.contentWidth=s,a.containerWidth=e,a}function Bo(e,t){var r=t.containerWidth,i=t.font,a=t.contentWidth;if(!r)return"";var n=ut(e,i);if(n<=r)return e;for(var o=0;;o++){if(n<=a||o>=t.maxIterations){e+=t.ellipsis;break}var s=o===0?_h(e,a,t.ascCharWidth,t.cnCharWidth):n>0?Math.floor(e.length*a/n):0;e=e.substr(0,s),n=ut(e,i)}return e===""&&(e=t.placeholder),e}function _h(e,t,r,i){for(var a=0,n=0,o=e.length;n<o&&a<t;n++){var s=e.charCodeAt(n);a+=0<=s&&s<=127?r:i}return n}function gh(e,t){e!=null&&(e+="");var r=t.overflow,i=t.padding,a=t.font,n=r==="truncate",o=xa(a),s=pt(t.lineHeight,o),f=!!t.backgroundColor,h=t.lineOverflow==="truncate",u=t.width,l;u!=null&&(r==="break"||r==="breakAll")?l=e?ko(e,t.font,u,r==="breakAll",0).lines:[]:l=e?e.split(`
`):[];var v=l.length*s,c=pt(t.height,v);if(v>c&&h){var _=Math.floor(c/s);l=l.slice(0,_)}if(e&&n&&u!=null)for(var g=Ho(u,a,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),d=0;d<l.length;d++)l[d]=Bo(l[d],g);for(var p=c,y=0,d=0;d<l.length;d++)y=Math.max(ut(l[d],a),y);u==null&&(u=y);var m=y;return i&&(p+=i[0]+i[2],m+=i[1]+i[3],u+=i[1]+i[3]),f&&(m=u),{lines:l,height:c,outerWidth:m,outerHeight:p,lineHeight:s,calculatedLineHeight:o,contentWidth:y,contentHeight:v,width:u}}var yh=function(){function e(){}return e}(),fn=function(){function e(t){this.tokens=[],t&&(this.tokens=t)}return e}(),mh=function(){function e(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]}return e}();function wh(e,t){var r=new mh;if(e!=null&&(e+=""),!e)return r;for(var i=t.width,a=t.height,n=t.overflow,o=(n==="break"||n==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:n==="breakAll"}:null,s=Ei.lastIndex=0,f;(f=Ei.exec(e))!=null;){var h=f.index;h>s&&Fi(r,e.substring(s,h),t,o),Fi(r,f[2],t,o,f[1]),s=Ei.lastIndex}s<e.length&&Fi(r,e.substring(s,e.length),t,o);var u=[],l=0,v=0,c=t.padding,_=n==="truncate",g=t.lineOverflow==="truncate";function d(z,Y,q){z.width=Y,z.lineHeight=q,l+=q,v=Math.max(v,Y)}t:for(var p=0;p<r.lines.length;p++){for(var y=r.lines[p],m=0,T=0,b=0;b<y.tokens.length;b++){var w=y.tokens[b],L=w.styleName&&t.rich[w.styleName]||{},C=w.textPadding=L.padding,M=C?C[1]+C[3]:0,S=w.font=L.font||t.font;w.contentHeight=xa(S);var P=pt(L.height,w.contentHeight);if(w.innerHeight=P,C&&(P+=C[0]+C[2]),w.height=P,w.lineHeight=Be(L.lineHeight,t.lineHeight,P),w.align=L&&L.align||t.align,w.verticalAlign=L&&L.verticalAlign||"middle",g&&a!=null&&l+w.lineHeight>a){b>0?(y.tokens=y.tokens.slice(0,b),d(y,T,m),r.lines=r.lines.slice(0,p+1)):r.lines=r.lines.slice(0,p);break t}var R=L.width,x=R==null||R==="auto";if(typeof R=="string"&&R.charAt(R.length-1)==="%")w.percentWidth=R,u.push(w),w.contentWidth=ut(w.text,S);else{if(x){var A=L.backgroundColor,E=A&&A.image;E&&(E=dh(E),hi(E)&&(w.width=Math.max(w.width,E.width*P/E.height)))}var D=_&&i!=null?i-T:null;D!=null&&D<w.width?!x||D<M?(w.text="",w.width=w.contentWidth=0):(w.text=ph(w.text,D-M,S,t.ellipsis,{minChar:t.truncateMinChar}),w.width=w.contentWidth=ut(w.text,S)):w.contentWidth=ut(w.text,S)}w.width+=M,T+=w.width,L&&(m=Math.max(m,w.lineHeight))}d(y,T,m)}r.outerWidth=r.width=pt(i,v),r.outerHeight=r.height=pt(a,l),r.contentHeight=l,r.contentWidth=v,c&&(r.outerWidth+=c[1]+c[3],r.outerHeight+=c[0]+c[2]);for(var p=0;p<u.length;p++){var w=u[p],k=w.percentWidth;w.width=parseInt(k,10)/100*r.width}return r}function Fi(e,t,r,i,a){var n=t==="",o=a&&r.rich[a]||{},s=e.lines,f=o.font||r.font,h=!1,u,l;if(i){var v=o.padding,c=v?v[1]+v[3]:0;if(o.width!=null&&o.width!=="auto"){var _=he(o.width,i.width)+c;s.length>0&&_+i.accumWidth>i.width&&(u=t.split(`
`),h=!0),i.accumWidth=_}else{var g=ko(t,f,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+c,l=g.linesWidths,u=g.lines}}else u=t.split(`
`);for(var d=0;d<u.length;d++){var p=u[d],y=new yh;if(y.styleName=a,y.text=p,y.isLineHolder=!p&&!n,typeof o.width=="number"?y.width=o.width:y.width=l?l[d]:ut(p,f),!d&&!h){var m=(s[s.length-1]||(s[0]=new fn)).tokens,T=m.length;T===1&&m[0].isLineHolder?m[0]=y:(p||!T||n)&&m.push(y)}else s.push(new fn([y]))}}function Th(e){var t=e.charCodeAt(0);return t>=33&&t<=383}var bh=oi(",&?/;] ".split(""),function(e,t){return e[t]=!0,e},{});function Ch(e){return Th(e)?!!bh[e]:!0}function ko(e,t,r,i,a){for(var n=[],o=[],s="",f="",h=0,u=0,l=0;l<e.length;l++){var v=e.charAt(l);if(v===`
`){f&&(s+=f,u+=h),n.push(s),o.push(u),s="",f="",h=0,u=0;continue}var c=ut(v,t),_=i?!1:!Ch(v);if(n.length?u+c>r:a+u+c>r){u?(s||f)&&(_?(s||(s=f,f="",h=0,u=h),n.push(s),o.push(u-h),f+=v,h+=c,s="",u=h):(f&&(s+=f,f="",h=0),n.push(s),o.push(u),s=v,u=c)):_?(n.push(f),o.push(h),f=v,h=c):(n.push(v),o.push(c));continue}u+=c,_?(f+=v,h+=c):(f&&(s+=f,f="",h=0),s+=v)}return!n.length&&!s&&(s=e,f="",h=0),f&&(s+=f),s&&(n.push(s),o.push(u)),n.length===1&&(u+=a),{accumWidth:u,lines:n,linesWidths:o}}var ca="__zr_style_"+Math.round(Math.random()*10),vr={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ui={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};vr[ca]=!0;var hn=["z","z2","invisible"],Lh=["invisible"],Mh=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype._init=function(r){for(var i=j(r),a=0;a<i.length;a++){var n=i[a];n==="style"?this.useStyle(r[n]):e.prototype.attrKV.call(this,n,r[n])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(r,i,a,n){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&Sh(this,r,i)||o&&!o[0]&&!o[3])return!1;if(a&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(n&&this.parent)for(var f=this.parent;f;){if(f.ignore)return!1;f=f.parent}return!0},t.prototype.contain=function(r,i){return this.rectContain(r,i)},t.prototype.traverse=function(r,i){r.call(i,this)},t.prototype.rectContain=function(r,i){var a=this.transformCoordToLocal(r,i),n=this.getBoundingRect();return n.contain(a[0],a[1])},t.prototype.getPaintRect=function(){var r=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,a=this.getBoundingRect(),n=this.style,o=n.shadowBlur||0,s=n.shadowOffsetX||0,f=n.shadowOffsetY||0;r=this._paintRect||(this._paintRect=new W(0,0,0,0)),i?W.applyTransform(r,a,i):r.copy(a),(o||s||f)&&(r.width+=o*2+Math.abs(s),r.height+=o*2+Math.abs(f),r.x=Math.min(r.x,r.x+s-o),r.y=Math.min(r.y,r.y+f-o));var h=this.dirtyRectTolerance;r.isZero()||(r.x=Math.floor(r.x-h),r.y=Math.floor(r.y-h),r.width=Math.ceil(r.width+1+h*2),r.height=Math.ceil(r.height+1+h*2))}return r},t.prototype.setPrevPaintRect=function(r){r?(this._prevPaintRect=this._prevPaintRect||new W(0,0,0,0),this._prevPaintRect.copy(r)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(r){return this.animate("style",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(r,i){r!=="style"?e.prototype.attrKV.call(this,r,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(r,i){return typeof r=="string"?this.style[r]=i:$(this.style,r),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(r){r||this.markRedraw(),this.__dirty|=Qr,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&Qr)},t.prototype.styleUpdated=function(){this.__dirty&=~Qr},t.prototype.createStyle=function(r){return fi(vr,r)},t.prototype.useStyle=function(r){r[ca]||(r=this.createStyle(r)),this.__inHover?this.__hoverStyle=r:this.style=r,this.dirtyStyle()},t.prototype.isStyleObject=function(r){return r[ca]},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var i=this._normalState;r.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(r,i,hn)},t.prototype._applyStateObj=function(r,i,a,n,o,s){e.prototype._applyStateObj.call(this,r,i,a,n,o,s);var f=!(i&&n),h;if(i&&i.style?o?n?h=i.style:(h=this._mergeStyle(this.createStyle(),a.style),this._mergeStyle(h,i.style)):(h=this._mergeStyle(this.createStyle(),n?this.style:a.style),this._mergeStyle(h,i.style)):f&&(h=a.style),h)if(o){var u=this.style;if(this.style=this.createStyle(f?{}:u),f)for(var l=j(u),v=0;v<l.length;v++){var c=l[v];c in h&&(h[c]=h[c],this.style[c]=u[c])}for(var _=j(h),v=0;v<_.length;v++){var c=_[v];this.style[c]=this.style[c]}this._transitionState(r,{style:h},s,this.getAnimationStyleProps())}else this.useStyle(h);for(var g=this.__inHover?Lh:hn,v=0;v<g.length;v++){var c=g[v];i&&i[c]!=null?this[c]=i[c]:f&&a[c]!=null&&(this[c]=a[c])}},t.prototype._mergeStates=function(r){for(var i=e.prototype._mergeStates.call(this,r),a,n=0;n<r.length;n++){var o=r[n];o.style&&(a=a||{},this._mergeStyle(a,o.style))}return a&&(i.style=a),i},t.prototype._mergeStyle=function(r,i){return $(r,i),r},t.prototype.getAnimationStyleProps=function(){return ui},t.initDefaultProps=function(){var r=t.prototype;r.type="displayable",r.invisible=!1,r.z=0,r.z2=0,r.zlevel=0,r.culling=!1,r.cursor="pointer",r.rectHover=!1,r.incremental=!1,r._rect=null,r.dirtyRectTolerance=0,r.__dirty=ht|Qr}(),t}(Eo),Ii=new W(0,0,0,0),Oi=new W(0,0,0,0);function Sh(e,t,r){return Ii.copy(e.getBoundingRect()),e.transform&&Ii.applyTransform(e.transform),Oi.width=t,Oi.height=r,!Ii.intersect(Oi)}const de=Mh;var it=Math.min,at=Math.max,Hi=Math.sin,Bi=Math.cos,jt=Math.PI*2,Le=zr(),Me=zr(),Se=zr();function zo(e,t,r){if(e.length!==0){for(var i=e[0],a=i[0],n=i[0],o=i[1],s=i[1],f=1;f<e.length;f++)i=e[f],a=it(a,i[0]),n=at(n,i[0]),o=it(o,i[1]),s=at(s,i[1]);t[0]=a,t[1]=o,r[0]=n,r[1]=s}}function un(e,t,r,i,a,n){a[0]=it(e,r),a[1]=it(t,i),n[0]=at(e,r),n[1]=at(t,i)}var ln=[],vn=[];function Ph(e,t,r,i,a,n,o,s,f,h){var u=To,l=N,v=u(e,r,a,o,ln);f[0]=1/0,f[1]=1/0,h[0]=-1/0,h[1]=-1/0;for(var c=0;c<v;c++){var _=l(e,r,a,o,ln[c]);f[0]=it(_,f[0]),h[0]=at(_,h[0])}v=u(t,i,n,s,vn);for(var c=0;c<v;c++){var g=l(t,i,n,s,vn[c]);f[1]=it(g,f[1]),h[1]=at(g,h[1])}f[0]=it(e,f[0]),h[0]=at(e,h[0]),f[0]=it(o,f[0]),h[0]=at(o,h[0]),f[1]=it(t,f[1]),h[1]=at(t,h[1]),f[1]=it(s,f[1]),h[1]=at(s,h[1])}function Rh(e,t,r,i,a,n,o,s){var f=bo,h=Q,u=at(it(f(e,r,a),1),0),l=at(it(f(t,i,n),1),0),v=h(e,r,a,u),c=h(t,i,n,l);o[0]=it(e,a,v),o[1]=it(t,n,c),s[0]=at(e,a,v),s[1]=at(t,n,c)}function Dh(e,t,r,i,a,n,o,s,f){var h=Rr,u=Dr,l=Math.abs(a-n);if(l%jt<1e-4&&l>1e-4){s[0]=e-r,s[1]=t-i,f[0]=e+r,f[1]=t+i;return}if(Le[0]=Bi(a)*r+e,Le[1]=Hi(a)*i+t,Me[0]=Bi(n)*r+e,Me[1]=Hi(n)*i+t,h(s,Le,Me),u(f,Le,Me),a=a%jt,a<0&&(a=a+jt),n=n%jt,n<0&&(n=n+jt),a>n&&!o?n+=jt:a<n&&o&&(a+=jt),o){var v=n;n=a,a=v}for(var c=0;c<n;c+=Math.PI/2)c>a&&(Se[0]=Bi(c)*r+e,Se[1]=Hi(c)*i+t,h(s,Se,s),u(f,Se,f))}var H={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},tr=[],rr=[],bt=[],Ht=[],Ct=[],Lt=[],ki=Math.min,zi=Math.max,er=Math.cos,ir=Math.sin,Ft=Math.abs,da=Math.PI,kt=da*2,$i=typeof Float32Array<"u",Zr=[];function Yi(e){var t=Math.round(e/da*1e8)/1e8;return t%2*da}function Ah(e,t){var r=Yi(e[0]);r<0&&(r+=kt);var i=r-e[0],a=e[1];a+=i,!t&&a-r>=kt?a=r+kt:t&&r-a>=kt?a=r-kt:!t&&r>a?a=r+(kt-Yi(r-a)):t&&r<a&&(a=r-(kt-Yi(a-r))),e[0]=r,e[1]=a}var xh=function(){function e(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return e.prototype.increaseVersion=function(){this._version++},e.prototype.getVersion=function(){return this._version},e.prototype.setScale=function(t,r,i){i=i||0,i>0&&(this._ux=Ft(i/Ve/t)||0,this._uy=Ft(i/Ve/r)||0)},e.prototype.setDPR=function(t){this.dpr=t},e.prototype.setContext=function(t){this._ctx=t},e.prototype.getContext=function(){return this._ctx},e.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},e.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},e.prototype.moveTo=function(t,r){return this._drawPendingPt(),this.addData(H.M,t,r),this._ctx&&this._ctx.moveTo(t,r),this._x0=t,this._y0=r,this._xi=t,this._yi=r,this},e.prototype.lineTo=function(t,r){var i=Ft(t-this._xi),a=Ft(r-this._yi),n=i>this._ux||a>this._uy;if(this.addData(H.L,t,r),this._ctx&&n&&this._ctx.lineTo(t,r),n)this._xi=t,this._yi=r,this._pendingPtDist=0;else{var o=i*i+a*a;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=r,this._pendingPtDist=o)}return this},e.prototype.bezierCurveTo=function(t,r,i,a,n,o){return this._drawPendingPt(),this.addData(H.C,t,r,i,a,n,o),this._ctx&&this._ctx.bezierCurveTo(t,r,i,a,n,o),this._xi=n,this._yi=o,this},e.prototype.quadraticCurveTo=function(t,r,i,a){return this._drawPendingPt(),this.addData(H.Q,t,r,i,a),this._ctx&&this._ctx.quadraticCurveTo(t,r,i,a),this._xi=i,this._yi=a,this},e.prototype.arc=function(t,r,i,a,n,o){this._drawPendingPt(),Zr[0]=a,Zr[1]=n,Ah(Zr,o),a=Zr[0],n=Zr[1];var s=n-a;return this.addData(H.A,t,r,i,i,a,s,0,o?0:1),this._ctx&&this._ctx.arc(t,r,i,a,n,o),this._xi=er(n)*i+t,this._yi=ir(n)*i+r,this},e.prototype.arcTo=function(t,r,i,a,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,r,i,a,n),this},e.prototype.rect=function(t,r,i,a){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,r,i,a),this.addData(H.R,t,r,i,a),this},e.prototype.closePath=function(){this._drawPendingPt(),this.addData(H.Z);var t=this._ctx,r=this._x0,i=this._y0;return t&&t.closePath(),this._xi=r,this._yi=i,this},e.prototype.fill=function(t){t&&t.fill(),this.toStatic()},e.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},e.prototype.len=function(){return this._len},e.prototype.setData=function(t){var r=t.length;!(this.data&&this.data.length===r)&&$i&&(this.data=new Float32Array(r));for(var i=0;i<r;i++)this.data[i]=t[i];this._len=r},e.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var r=t.length,i=0,a=this._len,n=0;n<r;n++)i+=t[n].len();$i&&this.data instanceof Float32Array&&(this.data=new Float32Array(a+i));for(var n=0;n<r;n++)for(var o=t[n].data,s=0;s<o.length;s++)this.data[a++]=o[s];this._len=a},e.prototype.addData=function(t,r,i,a,n,o,s,f,h){if(!!this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var l=0;l<arguments.length;l++)u[this._len++]=arguments[l]}},e.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},e.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],r=0;r<this._len;r++)t[r]=this.data[r];this.data=t}},e.prototype.toStatic=function(){if(!!this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,$i&&this._len>11&&(this.data=new Float32Array(t)))}},e.prototype.getBoundingRect=function(){bt[0]=bt[1]=Ct[0]=Ct[1]=Number.MAX_VALUE,Ht[0]=Ht[1]=Lt[0]=Lt[1]=-Number.MAX_VALUE;var t=this.data,r=0,i=0,a=0,n=0,o;for(o=0;o<this._len;){var s=t[o++],f=o===1;switch(f&&(r=t[o],i=t[o+1],a=r,n=i),s){case H.M:r=a=t[o++],i=n=t[o++],Ct[0]=a,Ct[1]=n,Lt[0]=a,Lt[1]=n;break;case H.L:un(r,i,t[o],t[o+1],Ct,Lt),r=t[o++],i=t[o++];break;case H.C:Ph(r,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],Ct,Lt),r=t[o++],i=t[o++];break;case H.Q:Rh(r,i,t[o++],t[o++],t[o],t[o+1],Ct,Lt),r=t[o++],i=t[o++];break;case H.A:var h=t[o++],u=t[o++],l=t[o++],v=t[o++],c=t[o++],_=t[o++]+c;o+=1;var g=!t[o++];f&&(a=er(c)*l+h,n=ir(c)*v+u),Dh(h,u,l,v,c,_,g,Ct,Lt),r=er(_)*l+h,i=ir(_)*v+u;break;case H.R:a=r=t[o++],n=i=t[o++];var d=t[o++],p=t[o++];un(a,n,a+d,n+p,Ct,Lt);break;case H.Z:r=a,i=n;break}Rr(bt,bt,Ct),Dr(Ht,Ht,Lt)}return o===0&&(bt[0]=bt[1]=Ht[0]=Ht[1]=0),new W(bt[0],bt[1],Ht[0]-bt[0],Ht[1]-bt[1])},e.prototype._calculateLength=function(){var t=this.data,r=this._len,i=this._ux,a=this._uy,n=0,o=0,s=0,f=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,u=0,l=0,v=0;v<r;){var c=t[v++],_=v===1;_&&(n=t[v],o=t[v+1],s=n,f=o);var g=-1;switch(c){case H.M:n=s=t[v++],o=f=t[v++];break;case H.L:{var d=t[v++],p=t[v++],y=d-n,m=p-o;(Ft(y)>i||Ft(m)>a||v===r-1)&&(g=Math.sqrt(y*y+m*m),n=d,o=p);break}case H.C:{var T=t[v++],b=t[v++],d=t[v++],p=t[v++],w=t[v++],L=t[v++];g=Tf(n,o,T,b,d,p,w,L,10),n=w,o=L;break}case H.Q:{var T=t[v++],b=t[v++],d=t[v++],p=t[v++];g=Lf(n,o,T,b,d,p,10),n=d,o=p;break}case H.A:var C=t[v++],M=t[v++],S=t[v++],P=t[v++],R=t[v++],x=t[v++],A=x+R;v+=1,t[v++],_&&(s=er(R)*S+C,f=ir(R)*P+M),g=zi(S,P)*ki(kt,Math.abs(x)),n=er(A)*S+C,o=ir(A)*P+M;break;case H.R:{s=n=t[v++],f=o=t[v++];var E=t[v++],D=t[v++];g=E*2+D*2;break}case H.Z:{var y=s-n,m=f-o;g=Math.sqrt(y*y+m*m),n=s,o=f;break}}g>=0&&(h[l++]=g,u+=g)}return this._pathLen=u,u},e.prototype.rebuildPath=function(t,r){var i=this.data,a=this._ux,n=this._uy,o=this._len,s,f,h,u,l,v,c=r<1,_,g,d=0,p=0,y,m=0,T,b;if(c&&(this._pathSegLen||this._calculateLength(),_=this._pathSegLen,g=this._pathLen,y=r*g,!y))return;t:for(var w=0;w<o;){var L=i[w++],C=w===1;switch(C&&(h=i[w],u=i[w+1],s=h,f=u),L!==H.L&&m>0&&(t.lineTo(T,b),m=0),L){case H.M:s=h=i[w++],f=u=i[w++],t.moveTo(h,u);break;case H.L:{l=i[w++],v=i[w++];var M=Ft(l-h),S=Ft(v-u);if(M>a||S>n){if(c){var P=_[p++];if(d+P>y){var R=(y-d)/P;t.lineTo(h*(1-R)+l*R,u*(1-R)+v*R);break t}d+=P}t.lineTo(l,v),h=l,u=v,m=0}else{var x=M*M+S*S;x>m&&(T=l,b=v,m=x)}break}case H.C:{var A=i[w++],E=i[w++],D=i[w++],k=i[w++],z=i[w++],Y=i[w++];if(c){var P=_[p++];if(d+P>y){var R=(y-d)/P;Xt(h,A,D,z,R,tr),Xt(u,E,k,Y,R,rr),t.bezierCurveTo(tr[1],rr[1],tr[2],rr[2],tr[3],rr[3]);break t}d+=P}t.bezierCurveTo(A,E,D,k,z,Y),h=z,u=Y;break}case H.Q:{var A=i[w++],E=i[w++],D=i[w++],k=i[w++];if(c){var P=_[p++];if(d+P>y){var R=(y-d)/P;Ze(h,A,D,R,tr),Ze(u,E,k,R,rr),t.quadraticCurveTo(tr[1],rr[1],tr[2],rr[2]);break t}d+=P}t.quadraticCurveTo(A,E,D,k),h=D,u=k;break}case H.A:var q=i[w++],Z=i[w++],X=i[w++],ot=i[w++],st=i[w++],Et=i[w++],Gt=i[w++],qt=!i[w++],Ut=X>ot?X:ot,rt=Ft(X-ot)>.001,U=st+Et,F=!1;if(c){var P=_[p++];d+P>y&&(U=st+Et*(y-d)/P,F=!0),d+=P}if(rt&&t.ellipse?t.ellipse(q,Z,X,ot,Gt,st,U,qt):t.arc(q,Z,Ut,st,U,qt),F)break t;C&&(s=er(st)*X+q,f=ir(st)*ot+Z),h=er(U)*X+q,u=ir(U)*ot+Z;break;case H.R:s=h=i[w],f=u=i[w+1],l=i[w++],v=i[w++];var O=i[w++],Yr=i[w++];if(c){var P=_[p++];if(d+P>y){var Tt=y-d;t.moveTo(l,v),t.lineTo(l+ki(Tt,O),v),Tt-=O,Tt>0&&t.lineTo(l+O,v+ki(Tt,Yr)),Tt-=Yr,Tt>0&&t.lineTo(l+zi(O-Tt,0),v+Yr),Tt-=O,Tt>0&&t.lineTo(l,v+zi(Yr-Tt,0));break t}d+=P}t.rect(l,v,O,Yr);break;case H.Z:if(c){var P=_[p++];if(d+P>y){var R=(y-d)/P;t.lineTo(h*(1-R)+s*R,u*(1-R)+f*R);break t}d+=P}t.closePath(),h=s,u=f}}},e.prototype.clone=function(){var t=new e,r=this.data;return t.data=r.slice?r.slice():Array.prototype.slice.call(r),t._len=this._len,t},e.CMD=H,e.initDefaultProps=function(){var t=e.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),e}();const dr=xh;function mr(e,t,r,i,a,n,o){if(a===0)return!1;var s=a,f=0,h=e;if(o>t+s&&o>i+s||o<t-s&&o<i-s||n>e+s&&n>r+s||n<e-s&&n<r-s)return!1;if(e!==r)f=(t-i)/(e-r),h=(e*i-r*t)/(e-r);else return Math.abs(n-e)<=s/2;var u=f*n-o+h,l=u*u/(f*f+1);return l<=s/2*s/2}function Eh(e,t,r,i,a,n,o,s,f,h,u){if(f===0)return!1;var l=f;if(u>t+l&&u>i+l&&u>n+l&&u>s+l||u<t-l&&u<i-l&&u<n-l&&u<s-l||h>e+l&&h>r+l&&h>a+l&&h>o+l||h<e-l&&h<r-l&&h<a-l&&h<o-l)return!1;var v=wf(e,t,r,i,a,n,o,s,h,u,null);return v<=l/2}function Fh(e,t,r,i,a,n,o,s,f){if(o===0)return!1;var h=o;if(f>t+h&&f>i+h&&f>n+h||f<t-h&&f<i-h&&f<n-h||s>e+h&&s>r+h&&s>a+h||s<e-h&&s<r-h&&s<a-h)return!1;var u=Cf(e,t,r,i,a,n,s,f,null);return u<=h/2}var cn=Math.PI*2;function Pe(e){return e%=cn,e<0&&(e+=cn),e}var Nr=Math.PI*2;function Ih(e,t,r,i,a,n,o,s,f){if(o===0)return!1;var h=o;s-=e,f-=t;var u=Math.sqrt(s*s+f*f);if(u-h>r||u+h<r)return!1;if(Math.abs(i-a)%Nr<1e-4)return!0;if(n){var l=i;i=Pe(a),a=Pe(l)}else i=Pe(i),a=Pe(a);i>a&&(a+=Nr);var v=Math.atan2(f,s);return v<0&&(v+=Nr),v>=i&&v<=a||v+Nr>=i&&v+Nr<=a}function Ot(e,t,r,i,a,n){if(n>t&&n>i||n<t&&n<i||i===t)return 0;var o=(n-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var f=o*(r-e)+e;return f===a?1/0:f>a?s:0}var Bt=dr.CMD,ar=Math.PI*2,Oh=1e-4;function Hh(e,t){return Math.abs(e-t)<Oh}var K=[-1,-1,-1],ct=[-1,-1];function Bh(){var e=ct[0];ct[0]=ct[1],ct[1]=e}function kh(e,t,r,i,a,n,o,s,f,h){if(h>t&&h>i&&h>n&&h>s||h<t&&h<i&&h<n&&h<s)return 0;var u=wo(t,i,n,s,h,K);if(u===0)return 0;for(var l=0,v=-1,c=void 0,_=void 0,g=0;g<u;g++){var d=K[g],p=d===0||d===1?.5:1,y=N(e,r,a,o,d);y<f||(v<0&&(v=To(t,i,n,s,ct),ct[1]<ct[0]&&v>1&&Bh(),c=N(t,i,n,s,ct[0]),v>1&&(_=N(t,i,n,s,ct[1]))),v===2?d<ct[0]?l+=c<t?p:-p:d<ct[1]?l+=_<c?p:-p:l+=s<_?p:-p:d<ct[0]?l+=c<t?p:-p:l+=s<c?p:-p)}return l}function zh(e,t,r,i,a,n,o,s){if(s>t&&s>i&&s>n||s<t&&s<i&&s<n)return 0;var f=bf(t,i,n,s,K);if(f===0)return 0;var h=bo(t,i,n);if(h>=0&&h<=1){for(var u=0,l=Q(t,i,n,h),v=0;v<f;v++){var c=K[v]===0||K[v]===1?.5:1,_=Q(e,r,a,K[v]);_<o||(K[v]<h?u+=l<t?c:-c:u+=n<l?c:-c)}return u}else{var c=K[0]===0||K[0]===1?.5:1,_=Q(e,r,a,K[0]);return _<o?0:n<t?c:-c}}function $h(e,t,r,i,a,n,o,s){if(s-=t,s>r||s<-r)return 0;var f=Math.sqrt(r*r-s*s);K[0]=-f,K[1]=f;var h=Math.abs(i-a);if(h<1e-4)return 0;if(h>=ar-1e-4){i=0,a=ar;var u=n?1:-1;return o>=K[0]+e&&o<=K[1]+e?u:0}if(i>a){var l=i;i=a,a=l}i<0&&(i+=ar,a+=ar);for(var v=0,c=0;c<2;c++){var _=K[c];if(_+e>o){var g=Math.atan2(s,_),u=n?1:-1;g<0&&(g=ar+g),(g>=i&&g<=a||g+ar>=i&&g+ar<=a)&&(g>Math.PI/2&&g<Math.PI*1.5&&(u=-u),v+=u)}}return v}function $o(e,t,r,i,a){for(var n=e.data,o=e.len(),s=0,f=0,h=0,u=0,l=0,v,c,_=0;_<o;){var g=n[_++],d=_===1;switch(g===Bt.M&&_>1&&(r||(s+=Ot(f,h,u,l,i,a))),d&&(f=n[_],h=n[_+1],u=f,l=h),g){case Bt.M:u=n[_++],l=n[_++],f=u,h=l;break;case Bt.L:if(r){if(mr(f,h,n[_],n[_+1],t,i,a))return!0}else s+=Ot(f,h,n[_],n[_+1],i,a)||0;f=n[_++],h=n[_++];break;case Bt.C:if(r){if(Eh(f,h,n[_++],n[_++],n[_++],n[_++],n[_],n[_+1],t,i,a))return!0}else s+=kh(f,h,n[_++],n[_++],n[_++],n[_++],n[_],n[_+1],i,a)||0;f=n[_++],h=n[_++];break;case Bt.Q:if(r){if(Fh(f,h,n[_++],n[_++],n[_],n[_+1],t,i,a))return!0}else s+=zh(f,h,n[_++],n[_++],n[_],n[_+1],i,a)||0;f=n[_++],h=n[_++];break;case Bt.A:var p=n[_++],y=n[_++],m=n[_++],T=n[_++],b=n[_++],w=n[_++];_+=1;var L=!!(1-n[_++]);v=Math.cos(b)*m+p,c=Math.sin(b)*T+y,d?(u=v,l=c):s+=Ot(f,h,v,c,i,a);var C=(i-p)*T/m+p;if(r){if(Ih(p,y,T,b,b+w,L,t,C,a))return!0}else s+=$h(p,y,T,b,b+w,L,C,a);f=Math.cos(b+w)*m+p,h=Math.sin(b+w)*T+y;break;case Bt.R:u=f=n[_++],l=h=n[_++];var M=n[_++],S=n[_++];if(v=u+M,c=l+S,r){if(mr(u,l,v,l,t,i,a)||mr(v,l,v,c,t,i,a)||mr(v,c,u,c,t,i,a)||mr(u,c,u,l,t,i,a))return!0}else s+=Ot(v,l,v,c,i,a),s+=Ot(u,c,u,l,i,a);break;case Bt.Z:if(r){if(mr(f,h,u,l,t,i,a))return!0}else s+=Ot(f,h,u,l,i,a);f=u,h=l;break}}return!r&&!Hh(h,l)&&(s+=Ot(f,h,u,l,i,a)||0),s!==0}function Yh(e,t,r){return $o(e,0,!1,t,r)}function Wh(e,t,r,i){return $o(e,t,!0,r,i)}var Yo=_t({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},vr),Xh={style:_t({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ui.style)},Wi=fe.concat(["invisible","culling","z","z2","zlevel","parent"]),Gh=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.update=function(){var r=this;e.prototype.update.call(this);var i=this.style;if(i.decal){var a=this._decalEl=this._decalEl||new t;a.buildPath===t.prototype.buildPath&&(a.buildPath=function(f){r.buildPath(f,r.shape)}),a.silent=!0;var n=a.style;for(var o in i)n[o]!==i[o]&&(n[o]=i[o]);n.fill=i.fill?i.decal:null,n.decal=null,n.shadowColor=null,i.strokeFirst&&(n.stroke=null);for(var s=0;s<Wi.length;++s)a[Wi[s]]=this[Wi[s]];a.__dirty|=ht}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(r){var i=j(r);this.shape=this.getDefaultShape();var a=this.getDefaultStyle();a&&this.useStyle(a);for(var n=0;n<i.length;n++){var o=i[n],s=r[o];o==="style"?this.style?$(this.style,s):this.useStyle(s):o==="shape"?$(this.shape,s):e.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var r=this.style.fill;if(r!=="none"){if(Ge(r)){var i=Ne(r,0);return i>.5?ha:i>.2?Vf:ua}else if(r)return ua}return ha},t.prototype.getInsideTextStroke=function(r){var i=this.style.fill;if(Ge(i)){var a=this.__zr,n=!!(a&&a.isDarkMode()),o=Ne(r,0)<fa;if(n===o)return i}},t.prototype.buildPath=function(r,i,a){},t.prototype.pathUpdated=function(){this.__dirty&=~Cr},t.prototype.getUpdatedPathProxy=function(r){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,r),this.path},t.prototype.createPathProxy=function(){this.path=new dr(!1)},t.prototype.hasStroke=function(){var r=this.style,i=r.stroke;return!(i==null||i==="none"||!(r.lineWidth>0))},t.prototype.hasFill=function(){var r=this.style,i=r.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var r=this._rect,i=this.style,a=!r;if(a){var n=!1;this.path||(n=!0,this.createPathProxy());var o=this.path;(n||this.__dirty&Cr)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),r=o.getBoundingRect()}if(this._rect=r,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=r.clone());if(this.__dirty||a){s.copy(r);var f=i.strokeNoScale?this.getLineScale():1,h=i.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;h=Math.max(h,u==null?4:u)}f>1e-10&&(s.width+=h/f,s.height+=h/f,s.x-=h/f/2,s.y-=h/f/2)}return s}return r},t.prototype.contain=function(r,i){var a=this.transformCoordToLocal(r,i),n=this.getBoundingRect(),o=this.style;if(r=a[0],i=a[1],n.contain(r,i)){var s=this.path;if(this.hasStroke()){var f=o.lineWidth,h=o.strokeNoScale?this.getLineScale():1;if(h>1e-10&&(this.hasFill()||(f=Math.max(f,this.strokeContainThreshold)),Wh(s,f/h,r,i)))return!0}if(this.hasFill())return Yh(s,r,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Cr,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(r){return this.animate("shape",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():r==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(r,i){r==="shape"?this.setShape(i):e.prototype.attrKV.call(this,r,i)},t.prototype.setShape=function(r,i){var a=this.shape;return a||(a=this.shape={}),typeof r=="string"?a[r]=i:$(a,r),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Cr)},t.prototype.createStyle=function(r){return fi(Yo,r)},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var i=this._normalState;r.shape&&!i.shape&&(i.shape=$({},this.shape))},t.prototype._applyStateObj=function(r,i,a,n,o,s){e.prototype._applyStateObj.call(this,r,i,a,n,o,s);var f=!(i&&n),h;if(i&&i.shape?o?n?h=i.shape:(h=$({},a.shape),$(h,i.shape)):(h=$({},n?this.shape:a.shape),$(h,i.shape)):f&&(h=a.shape),h)if(o){this.shape=$({},this.shape);for(var u={},l=j(h),v=0;v<l.length;v++){var c=l[v];typeof h[c]=="object"?this.shape[c]=h[c]:u[c]=h[c]}this._transitionState(r,{shape:u},s)}else this.shape=h,this.dirtyShape()},t.prototype._mergeStates=function(r){for(var i=e.prototype._mergeStates.call(this,r),a,n=0;n<r.length;n++){var o=r[n];o.shape&&(a=a||{},this._mergeStyle(a,o.shape))}return a&&(i.shape=a),i},t.prototype.getAnimationStyleProps=function(){return Xh},t.prototype.isZeroArea=function(){return!1},t.extend=function(r){var i=function(n){B(o,n);function o(s){var f=n.call(this,s)||this;return r.init&&r.init.call(f,s),f}return o.prototype.getDefaultStyle=function(){return Br(r.style)},o.prototype.getDefaultShape=function(){return Br(r.shape)},o}(t);for(var a in r)typeof r[a]=="function"&&(i.prototype[a]=r[a]);return i},t.initDefaultProps=function(){var r=t.prototype;r.type="path",r.strokeContainThreshold=5,r.segmentIgnoreThreshold=0,r.subPixelOptimize=!1,r.autoBatch=!1,r.__dirty=ht|Qr|Cr}(),t}(de);const G=Gh;var qh=_t({strokeFirst:!0,font:cr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Yo),Wo=function(e){B(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var r=this.style,i=r.stroke;return i!=null&&i!=="none"&&r.lineWidth>0},t.prototype.hasFill=function(){var r=this.style,i=r.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(r){return fi(qh,r)},t.prototype.setBoundingRect=function(r){this._rect=r},t.prototype.getBoundingRect=function(){var r=this.style;if(!this._rect){var i=r.text;i!=null?i+="":i="";var a=ih(i,r.font,r.textAlign,r.textBaseline);if(a.x+=r.x||0,a.y+=r.y||0,this.hasStroke()){var n=r.lineWidth;a.x-=n/2,a.y-=n/2,a.width+=n,a.height+=n}this._rect=a}return this._rect},t.initDefaultProps=function(){var r=t.prototype;r.dirtyRectTolerance=10}(),t}(de);Wo.prototype.type="tspan";const Qe=Wo;var Uh=_t({x:0,y:0},vr),Zh={style:_t({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ui.style)};function Nh(e){return!!(e&&typeof e!="string"&&e.width&&e.height)}var Xo=function(e){B(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.createStyle=function(r){return fi(Uh,r)},t.prototype._getSize=function(r){var i=this.style,a=i[r];if(a!=null)return a;var n=Nh(i.image)?i.image:this.__image;if(!n)return 0;var o=r==="width"?"height":"width",s=i[o];return s==null?n[r]:n[r]/n[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return Zh},t.prototype.getBoundingRect=function(){var r=this.style;return this._rect||(this._rect=new W(r.x||0,r.y||0,this.getWidth(),this.getHeight())),this._rect},t}(de);Xo.prototype.type="image";const Fa=Xo;function Vh(e,t){var r=t.x,i=t.y,a=t.width,n=t.height,o=t.r,s,f,h,u;a<0&&(r=r+a,a=-a),n<0&&(i=i+n,n=-n),typeof o=="number"?s=f=h=u=o:o instanceof Array?o.length===1?s=f=h=u=o[0]:o.length===2?(s=h=o[0],f=u=o[1]):o.length===3?(s=o[0],f=u=o[1],h=o[2]):(s=o[0],f=o[1],h=o[2],u=o[3]):s=f=h=u=0;var l;s+f>a&&(l=s+f,s*=a/l,f*=a/l),h+u>a&&(l=h+u,h*=a/l,u*=a/l),f+h>n&&(l=f+h,f*=n/l,h*=n/l),s+u>n&&(l=s+u,s*=n/l,u*=n/l),e.moveTo(r+s,i),e.lineTo(r+a-f,i),f!==0&&e.arc(r+a-f,i+f,f,-Math.PI/2,0),e.lineTo(r+a,i+n-h),h!==0&&e.arc(r+a-h,i+n-h,h,0,Math.PI/2),e.lineTo(r+u,i+n),u!==0&&e.arc(r+u,i+n-u,u,Math.PI/2,Math.PI),e.lineTo(r,i+s),s!==0&&e.arc(r+s,i+s,s,Math.PI,Math.PI*1.5)}var xr=Math.round;function Qh(e,t,r){if(!!t){var i=t.x1,a=t.x2,n=t.y1,o=t.y2;e.x1=i,e.x2=a,e.y1=n,e.y2=o;var s=r&&r.lineWidth;return s&&(xr(i*2)===xr(a*2)&&(e.x1=e.x2=Er(i,s,!0)),xr(n*2)===xr(o*2)&&(e.y1=e.y2=Er(n,s,!0))),e}}function Kh(e,t,r){if(!!t){var i=t.x,a=t.y,n=t.width,o=t.height;e.x=i,e.y=a,e.width=n,e.height=o;var s=r&&r.lineWidth;return s&&(e.x=Er(i,s,!0),e.y=Er(a,s,!0),e.width=Math.max(Er(i+n,s,!1)-e.x,n===0?0:1),e.height=Math.max(Er(a+o,s,!1)-e.y,o===0?0:1)),e}}function Er(e,t,r){if(!t)return e;var i=xr(e*2);return(i+xr(t))%2===0?i/2:(i+(r?1:-1))/2}var Jh=function(){function e(){this.x=0,this.y=0,this.width=0,this.height=0}return e}(),jh={},Go=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Jh},t.prototype.buildPath=function(r,i){var a,n,o,s;if(this.subPixelOptimize){var f=Kh(jh,i,this.style);a=f.x,n=f.y,o=f.width,s=f.height,f.r=i.r,i=f}else a=i.x,n=i.y,o=i.width,s=i.height;i.r?Vh(r,i):r.rect(a,n,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(G);Go.prototype.type="rect";const Ke=Go;var dn={fill:"#000"},pn=2,tu={style:_t({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ui.style)},qo=function(e){B(t,e);function t(r){var i=e.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=dn,i.attr(r),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){e.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var r=0;r<this._children.length;r++){var i=this._children[r];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var r=this.innerTransformable;r?(r.updateTransform(),r.transform&&(this.transform=r.transform)):e.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(r){var i=this.innerTransformable;return i?i.getLocalTransform(r):e.prototype.getLocalTransform.call(this,r)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),e.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,nu(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var i=0;i<this._children.length;i++)this._children[i].__zr=r},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var r=new W(0,0,0,0),i=this._children,a=[],n=null,o=0;o<i.length;o++){var s=i[o],f=s.getBoundingRect(),h=s.getLocalTransform(a);h?(r.copy(f),r.applyTransform(h),n=n||r.clone(),n.union(r)):(n=n||f.clone(),n.union(f))}this._rect=n||r}return this._rect},t.prototype.setDefaultTextStyle=function(r){this._defaultStyle=r||dn},t.prototype.setTextContent=function(r){},t.prototype._mergeStyle=function(r,i){if(!i)return r;var a=i.rich,n=r.rich||a&&{};return $(r,i),a&&n?(this._mergeRich(n,a),r.rich=n):n&&(r.rich=n),r},t.prototype._mergeRich=function(r,i){for(var a=j(i),n=0;n<a.length;n++){var o=a[n];r[o]=r[o]||{},$(r[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return tu},t.prototype._getOrCreateChild=function(r){var i=this._children[this._childCursor];return(!i||!(i instanceof r))&&(i=new r),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var r=this.style,i=r.font||cr,a=r.padding,n=bn(r),o=gh(n,r),s=Xi(r),f=!!r.backgroundColor,h=o.outerHeight,u=o.outerWidth,l=o.contentWidth,v=o.lines,c=o.lineHeight,_=this._defaultStyle,g=r.x||0,d=r.y||0,p=r.align||_.align||"left",y=r.verticalAlign||_.verticalAlign||"top",m=g,T=Lr(d,o.contentHeight,y);if(s||a){var b=Jr(g,u,p),w=Lr(d,h,y);s&&this._renderBackground(r,r,b,w,u,h)}T+=c/2,a&&(m=Tn(g,p,a),y==="top"?T+=a[0]:y==="bottom"&&(T-=a[2]));for(var L=0,C=!1,M=wn("fill"in r?r.fill:(C=!0,_.fill)),S=mn("stroke"in r?r.stroke:!f&&(!_.autoStroke||C)?(L=pn,_.stroke):null),P=r.textShadowBlur>0,R=r.width!=null&&(r.overflow==="truncate"||r.overflow==="break"||r.overflow==="breakAll"),x=o.calculatedLineHeight,A=0;A<v.length;A++){var E=this._getOrCreateChild(Qe),D=E.createStyle();E.useStyle(D),D.text=v[A],D.x=m,D.y=T,p&&(D.textAlign=p),D.textBaseline="middle",D.opacity=r.opacity,D.strokeFirst=!0,P&&(D.shadowBlur=r.textShadowBlur||0,D.shadowColor=r.textShadowColor||"transparent",D.shadowOffsetX=r.textShadowOffsetX||0,D.shadowOffsetY=r.textShadowOffsetY||0),D.stroke=S,D.fill=M,S&&(D.lineWidth=r.lineWidth||L,D.lineDash=r.lineDash,D.lineDashOffset=r.lineDashOffset||0),D.font=i,gn(D,r),T+=c,R&&E.setBoundingRect(new W(Jr(D.x,r.width,D.textAlign),Lr(D.y,x,D.textBaseline),l,x))}},t.prototype._updateRichTexts=function(){var r=this.style,i=bn(r),a=wh(i,r),n=a.width,o=a.outerWidth,s=a.outerHeight,f=r.padding,h=r.x||0,u=r.y||0,l=this._defaultStyle,v=r.align||l.align,c=r.verticalAlign||l.verticalAlign,_=Jr(h,o,v),g=Lr(u,s,c),d=_,p=g;f&&(d+=f[3],p+=f[0]);var y=d+n;Xi(r)&&this._renderBackground(r,r,_,g,o,s);for(var m=!!r.backgroundColor,T=0;T<a.lines.length;T++){for(var b=a.lines[T],w=b.tokens,L=w.length,C=b.lineHeight,M=b.width,S=0,P=d,R=y,x=L-1,A=void 0;S<L&&(A=w[S],!A.align||A.align==="left");)this._placeToken(A,r,C,p,P,"left",m),M-=A.width,P+=A.width,S++;for(;x>=0&&(A=w[x],A.align==="right");)this._placeToken(A,r,C,p,R,"right",m),M-=A.width,R-=A.width,x--;for(P+=(n-(P-d)-(y-R)-M)/2;S<=x;)A=w[S],this._placeToken(A,r,C,p,P+A.width/2,"center",m),P+=A.width,S++;p+=C}},t.prototype._placeToken=function(r,i,a,n,o,s,f){var h=i.rich[r.styleName]||{};h.text=r.text;var u=r.verticalAlign,l=n+a/2;u==="top"?l=n+r.height/2:u==="bottom"&&(l=n+a-r.height/2);var v=!r.isLineHolder&&Xi(h);v&&this._renderBackground(h,i,s==="right"?o-r.width:s==="center"?o-r.width/2:o,l-r.height/2,r.width,r.height);var c=!!h.backgroundColor,_=r.textPadding;_&&(o=Tn(o,s,_),l-=r.height/2-_[0]-r.innerHeight/2);var g=this._getOrCreateChild(Qe),d=g.createStyle();g.useStyle(d);var p=this._defaultStyle,y=!1,m=0,T=wn("fill"in h?h.fill:"fill"in i?i.fill:(y=!0,p.fill)),b=mn("stroke"in h?h.stroke:"stroke"in i?i.stroke:!c&&!f&&(!p.autoStroke||y)?(m=pn,p.stroke):null),w=h.textShadowBlur>0||i.textShadowBlur>0;d.text=r.text,d.x=o,d.y=l,w&&(d.shadowBlur=h.textShadowBlur||i.textShadowBlur||0,d.shadowColor=h.textShadowColor||i.textShadowColor||"transparent",d.shadowOffsetX=h.textShadowOffsetX||i.textShadowOffsetX||0,d.shadowOffsetY=h.textShadowOffsetY||i.textShadowOffsetY||0),d.textAlign=s,d.textBaseline="middle",d.font=r.font||cr,d.opacity=Be(h.opacity,i.opacity,1),gn(d,h),b&&(d.lineWidth=Be(h.lineWidth,i.lineWidth,m),d.lineDash=pt(h.lineDash,i.lineDash),d.lineDashOffset=i.lineDashOffset||0,d.stroke=b),T&&(d.fill=T);var L=r.contentWidth,C=r.contentHeight;g.setBoundingRect(new W(Jr(d.x,L,d.textAlign),Lr(d.y,C,d.textBaseline),L,C))},t.prototype._renderBackground=function(r,i,a,n,o,s){var f=r.backgroundColor,h=r.borderWidth,u=r.borderColor,l=f&&f.image,v=f&&!l,c=r.borderRadius,_=this,g,d;if(v||r.lineHeight||h&&u){g=this._getOrCreateChild(Ke),g.useStyle(g.createStyle()),g.style.fill=null;var p=g.shape;p.x=a,p.y=n,p.width=o,p.height=s,p.r=c,g.dirtyShape()}if(v){var y=g.style;y.fill=f||null,y.fillOpacity=pt(r.fillOpacity,1)}else if(l){d=this._getOrCreateChild(Fa),d.onload=function(){_.dirtyStyle()};var m=d.style;m.image=f.image,m.x=a,m.y=n,m.width=o,m.height=s}if(h&&u){var y=g.style;y.lineWidth=h,y.stroke=u,y.strokeOpacity=pt(r.strokeOpacity,1),y.lineDash=r.borderDash,y.lineDashOffset=r.borderDashOffset||0,g.strokeContainThreshold=0,g.hasFill()&&g.hasStroke()&&(y.strokeFirst=!0,y.lineWidth*=2)}var T=(g||d).style;T.shadowBlur=r.shadowBlur||0,T.shadowColor=r.shadowColor||"transparent",T.shadowOffsetX=r.shadowOffsetX||0,T.shadowOffsetY=r.shadowOffsetY||0,T.opacity=Be(r.opacity,i.opacity,1)},t.makeFont=function(r){var i="";return au(r)&&(i=[r.fontStyle,r.fontWeight,iu(r.fontSize),r.fontFamily||"sans-serif"].join(" ")),i&&Pr(i)||r.textFont||r.font},t}(de),ru={left:!0,right:1,center:1},eu={top:1,bottom:1,middle:1},_n=["fontStyle","fontWeight","fontSize","fontFamily"];function iu(e){return typeof e=="string"&&(e.indexOf("px")!==-1||e.indexOf("rem")!==-1||e.indexOf("em")!==-1)?e:isNaN(+e)?ba+"px":e+"px"}function gn(e,t){for(var r=0;r<_n.length;r++){var i=_n[r],a=t[i];a!=null&&(e[i]=a)}}function au(e){return e.fontSize!=null||e.fontFamily||e.fontWeight}function nu(e){return yn(e),J(e.rich,yn),e}function yn(e){if(e){e.font=qo.makeFont(e);var t=e.align;t==="middle"&&(t="center"),e.align=t==null||ru[t]?t:"left";var r=e.verticalAlign;r==="center"&&(r="middle"),e.verticalAlign=r==null||eu[r]?r:"top";var i=e.padding;i&&(e.padding=Hs(e.padding))}}function mn(e,t){return e==null||t<=0||e==="transparent"||e==="none"?null:e.image||e.colorStops?"#000":e}function wn(e){return e==null||e==="none"?null:e.image||e.colorStops?"#000":e}function Tn(e,t,r){return t==="right"?e-r[1]:t==="center"?e+r[3]/2-r[1]/2:e+r[3]}function bn(e){var t=e.text;return t!=null&&(t+=""),t}function Xi(e){return!!(e.backgroundColor||e.lineHeight||e.borderWidth&&e.borderColor)}const ev=qo;var wr=dr.CMD,ou=[[],[],[]],Cn=Math.sqrt,su=Math.atan2;function Uo(e,t){if(!!t){var r=e.data,i=e.len(),a,n,o,s,f,h,u=wr.M,l=wr.C,v=wr.L,c=wr.R,_=wr.A,g=wr.Q;for(o=0,s=0;o<i;){switch(a=r[o++],s=o,n=0,a){case u:n=1;break;case v:n=1;break;case l:n=3;break;case g:n=2;break;case _:var d=t[4],p=t[5],y=Cn(t[0]*t[0]+t[1]*t[1]),m=Cn(t[2]*t[2]+t[3]*t[3]),T=su(-t[1]/m,t[0]/y);r[o]*=y,r[o++]+=d,r[o]*=m,r[o++]+=p,r[o++]*=y,r[o++]*=m,r[o++]+=T,r[o++]+=T,o+=2,s=o;break;case c:h[0]=r[o++],h[1]=r[o++],re(h,h,t),r[s++]=h[0],r[s++]=h[1],h[0]+=r[o++],h[1]+=r[o++],re(h,h,t),r[s++]=h[0],r[s++]=h[1]}for(f=0;f<n;f++){var b=ou[f];b[0]=r[o++],b[1]=r[o++],re(b,b,t),r[s++]=b[0],r[s++]=b[1]}}e.increaseVersion()}}var Gi=Math.sqrt,Re=Math.sin,De=Math.cos,Vr=Math.PI;function Ln(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1])}function pa(e,t){return(e[0]*t[0]+e[1]*t[1])/(Ln(e)*Ln(t))}function Mn(e,t){return(e[0]*t[1]<e[1]*t[0]?-1:1)*Math.acos(pa(e,t))}function Sn(e,t,r,i,a,n,o,s,f,h,u){var l=f*(Vr/180),v=De(l)*(e-r)/2+Re(l)*(t-i)/2,c=-1*Re(l)*(e-r)/2+De(l)*(t-i)/2,_=v*v/(o*o)+c*c/(s*s);_>1&&(o*=Gi(_),s*=Gi(_));var g=(a===n?-1:1)*Gi((o*o*(s*s)-o*o*(c*c)-s*s*(v*v))/(o*o*(c*c)+s*s*(v*v)))||0,d=g*o*c/s,p=g*-s*v/o,y=(e+r)/2+De(l)*d-Re(l)*p,m=(t+i)/2+Re(l)*d+De(l)*p,T=Mn([1,0],[(v-d)/o,(c-p)/s]),b=[(v-d)/o,(c-p)/s],w=[(-1*v-d)/o,(-1*c-p)/s],L=Mn(b,w);if(pa(b,w)<=-1&&(L=Vr),pa(b,w)>=1&&(L=0),L<0){var C=Math.round(L/Vr*1e6)/1e6;L=Vr*2+C%2*Vr}u.addData(h,y,m,o,s,T,L,l,n)}var fu=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,hu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function uu(e){var t=new dr;if(!e)return t;var r=0,i=0,a=r,n=i,o,s=dr.CMD,f=e.match(fu);if(!f)return t;for(var h=0;h<f.length;h++){for(var u=f[h],l=u.charAt(0),v=void 0,c=u.match(hu)||[],_=c.length,g=0;g<_;g++)c[g]=parseFloat(c[g]);for(var d=0;d<_;){var p=void 0,y=void 0,m=void 0,T=void 0,b=void 0,w=void 0,L=void 0,C=r,M=i,S=void 0,P=void 0;switch(l){case"l":r+=c[d++],i+=c[d++],v=s.L,t.addData(v,r,i);break;case"L":r=c[d++],i=c[d++],v=s.L,t.addData(v,r,i);break;case"m":r+=c[d++],i+=c[d++],v=s.M,t.addData(v,r,i),a=r,n=i,l="l";break;case"M":r=c[d++],i=c[d++],v=s.M,t.addData(v,r,i),a=r,n=i,l="L";break;case"h":r+=c[d++],v=s.L,t.addData(v,r,i);break;case"H":r=c[d++],v=s.L,t.addData(v,r,i);break;case"v":i+=c[d++],v=s.L,t.addData(v,r,i);break;case"V":i=c[d++],v=s.L,t.addData(v,r,i);break;case"C":v=s.C,t.addData(v,c[d++],c[d++],c[d++],c[d++],c[d++],c[d++]),r=c[d-2],i=c[d-1];break;case"c":v=s.C,t.addData(v,c[d++]+r,c[d++]+i,c[d++]+r,c[d++]+i,c[d++]+r,c[d++]+i),r+=c[d-2],i+=c[d-1];break;case"S":p=r,y=i,S=t.len(),P=t.data,o===s.C&&(p+=r-P[S-4],y+=i-P[S-3]),v=s.C,C=c[d++],M=c[d++],r=c[d++],i=c[d++],t.addData(v,p,y,C,M,r,i);break;case"s":p=r,y=i,S=t.len(),P=t.data,o===s.C&&(p+=r-P[S-4],y+=i-P[S-3]),v=s.C,C=r+c[d++],M=i+c[d++],r+=c[d++],i+=c[d++],t.addData(v,p,y,C,M,r,i);break;case"Q":C=c[d++],M=c[d++],r=c[d++],i=c[d++],v=s.Q,t.addData(v,C,M,r,i);break;case"q":C=c[d++]+r,M=c[d++]+i,r+=c[d++],i+=c[d++],v=s.Q,t.addData(v,C,M,r,i);break;case"T":p=r,y=i,S=t.len(),P=t.data,o===s.Q&&(p+=r-P[S-4],y+=i-P[S-3]),r=c[d++],i=c[d++],v=s.Q,t.addData(v,p,y,r,i);break;case"t":p=r,y=i,S=t.len(),P=t.data,o===s.Q&&(p+=r-P[S-4],y+=i-P[S-3]),r+=c[d++],i+=c[d++],v=s.Q,t.addData(v,p,y,r,i);break;case"A":m=c[d++],T=c[d++],b=c[d++],w=c[d++],L=c[d++],C=r,M=i,r=c[d++],i=c[d++],v=s.A,Sn(C,M,r,i,w,L,m,T,b,v,t);break;case"a":m=c[d++],T=c[d++],b=c[d++],w=c[d++],L=c[d++],C=r,M=i,r+=c[d++],i+=c[d++],v=s.A,Sn(C,M,r,i,w,L,m,T,b,v,t);break}}(l==="z"||l==="Z")&&(v=s.Z,t.addData(v),r=a,i=n),o=v}return t.toStatic(),t}var Zo=function(e){B(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.applyTransform=function(r){},t}(G);function No(e){return e.setData!=null}function Vo(e,t){var r=uu(e),i=$({},t);return i.buildPath=function(a){if(No(a)){a.setData(r.data);var n=a.getContext();n&&a.rebuildPath(n,1)}else{var n=a;r.rebuildPath(n,1)}},i.applyTransform=function(a){Uo(r,a),this.dirtyShape()},i}function lu(e,t){return new Zo(Vo(e,t))}function iv(e,t){var r=Vo(e,t),i=function(a){B(n,a);function n(o){var s=a.call(this,o)||this;return s.applyTransform=r.applyTransform,s.buildPath=r.buildPath,s}return n}(Zo);return i}function av(e,t){for(var r=[],i=e.length,a=0;a<i;a++){var n=e[a];r.push(n.getUpdatedPathProxy(!0))}var o=new G(t);return o.createPathProxy(),o.buildPath=function(s){if(No(s)){s.appendPath(r);var f=s.getContext();f&&s.rebuildPath(f,1)}},o}function Qo(e,t){t=t||{};var r=new G;return e.shape&&r.setShape(e.shape),r.setStyle(e.style),t.bakeTransform?Uo(r.path,e.getComputedTransform()):t.toLocal?r.setLocalTransform(e.getComputedTransform()):r.copyTransform(e),r.buildPath=e.buildPath,r.applyTransform=r.applyTransform,r.z=e.z,r.z2=e.z2,r.zlevel=e.zlevel,r}var vu=function(){function e(){this.cx=0,this.cy=0,this.r=0}return e}(),Ko=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new vu},t.prototype.buildPath=function(r,i){r.moveTo(i.cx+i.r,i.cy),r.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(G);Ko.prototype.type="circle";const cu=Ko;var du=function(){function e(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return e}(),Jo=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new du},t.prototype.buildPath=function(r,i){var a=.5522848,n=i.cx,o=i.cy,s=i.rx,f=i.ry,h=s*a,u=f*a;r.moveTo(n-s,o),r.bezierCurveTo(n-s,o-u,n-h,o-f,n,o-f),r.bezierCurveTo(n+h,o-f,n+s,o-u,n+s,o),r.bezierCurveTo(n+s,o+u,n+h,o+f,n,o+f),r.bezierCurveTo(n-h,o+f,n-s,o+u,n-s,o),r.closePath()},t}(G);Jo.prototype.type="ellipse";const pu=Jo;var jo=Math.PI,qi=jo*2,nr=Math.sin,Tr=Math.cos,_u=Math.acos,V=Math.atan2,Pn=Math.abs,ie=Math.sqrt,jr=Math.max,Mt=Math.min,gt=1e-4;function gu(e,t,r,i,a,n,o,s){var f=r-e,h=i-t,u=o-a,l=s-n,v=l*f-u*h;if(!(v*v<gt))return v=(u*(t-n)-l*(e-a))/v,[e+v*f,t+v*h]}function Ae(e,t,r,i,a,n,o){var s=e-r,f=t-i,h=(o?n:-n)/ie(s*s+f*f),u=h*f,l=-h*s,v=e+u,c=t+l,_=r+u,g=i+l,d=(v+_)/2,p=(c+g)/2,y=_-v,m=g-c,T=y*y+m*m,b=a-n,w=v*g-_*c,L=(m<0?-1:1)*ie(jr(0,b*b*T-w*w)),C=(w*m-y*L)/T,M=(-w*y-m*L)/T,S=(w*m+y*L)/T,P=(-w*y+m*L)/T,R=C-d,x=M-p,A=S-d,E=P-p;return R*R+x*x>A*A+E*E&&(C=S,M=P),{cx:C,cy:M,x0:-u,y0:-l,x1:C*(a/b-1),y1:M*(a/b-1)}}function yu(e){var t;if(ne(e)){var r=e.length;if(!r)return e;r===1?t=[e[0],e[0],0,0]:r===2?t=[e[0],e[0],e[1],e[1]]:r===3?t=e.concat(e[2]):t=e}else t=[e,e,e,e];return t}function mu(e,t){var r,i=jr(t.r,0),a=jr(t.r0||0,0),n=i>0,o=a>0;if(!(!n&&!o)){if(n||(i=a,a=0),a>i){var s=i;i=a,a=s}var f=t.startAngle,h=t.endAngle;if(!(isNaN(f)||isNaN(h))){var u=t.cx,l=t.cy,v=!!t.clockwise,c=Pn(h-f),_=c>qi&&c%qi;if(_>gt&&(c=_),!(i>gt))e.moveTo(u,l);else if(c>qi-gt)e.moveTo(u+i*Tr(f),l+i*nr(f)),e.arc(u,l,i,f,h,!v),a>gt&&(e.moveTo(u+a*Tr(h),l+a*nr(h)),e.arc(u,l,a,h,f,v));else{var g=void 0,d=void 0,p=void 0,y=void 0,m=void 0,T=void 0,b=void 0,w=void 0,L=void 0,C=void 0,M=void 0,S=void 0,P=void 0,R=void 0,x=void 0,A=void 0,E=i*Tr(f),D=i*nr(f),k=a*Tr(h),z=a*nr(h),Y=c>gt;if(Y){var q=t.cornerRadius;q&&(r=yu(q),g=r[0],d=r[1],p=r[2],y=r[3]);var Z=Pn(i-a)/2;if(m=Mt(Z,p),T=Mt(Z,y),b=Mt(Z,g),w=Mt(Z,d),M=L=jr(m,T),S=C=jr(b,w),(L>gt||C>gt)&&(P=i*Tr(h),R=i*nr(h),x=a*Tr(f),A=a*nr(f),c<jo)){var X=gu(E,D,x,A,P,R,k,z);if(X){var ot=E-X[0],st=D-X[1],Et=P-X[0],Gt=R-X[1],qt=1/nr(_u((ot*Et+st*Gt)/(ie(ot*ot+st*st)*ie(Et*Et+Gt*Gt)))/2),Ut=ie(X[0]*X[0]+X[1]*X[1]);M=Mt(L,(i-Ut)/(qt+1)),S=Mt(C,(a-Ut)/(qt-1))}}}if(!Y)e.moveTo(u+E,l+D);else if(M>gt){var rt=Mt(p,M),U=Mt(y,M),F=Ae(x,A,E,D,i,rt,v),O=Ae(P,R,k,z,i,U,v);e.moveTo(u+F.cx+F.x0,l+F.cy+F.y0),M<L&&rt===U?e.arc(u+F.cx,l+F.cy,M,V(F.y0,F.x0),V(O.y0,O.x0),!v):(rt>0&&e.arc(u+F.cx,l+F.cy,rt,V(F.y0,F.x0),V(F.y1,F.x1),!v),e.arc(u,l,i,V(F.cy+F.y1,F.cx+F.x1),V(O.cy+O.y1,O.cx+O.x1),!v),U>0&&e.arc(u+O.cx,l+O.cy,U,V(O.y1,O.x1),V(O.y0,O.x0),!v))}else e.moveTo(u+E,l+D),e.arc(u,l,i,f,h,!v);if(!(a>gt)||!Y)e.lineTo(u+k,l+z);else if(S>gt){var rt=Mt(g,S),U=Mt(d,S),F=Ae(k,z,P,R,a,-U,v),O=Ae(E,D,x,A,a,-rt,v);e.lineTo(u+F.cx+F.x0,l+F.cy+F.y0),S<C&&rt===U?e.arc(u+F.cx,l+F.cy,S,V(F.y0,F.x0),V(O.y0,O.x0),!v):(U>0&&e.arc(u+F.cx,l+F.cy,U,V(F.y0,F.x0),V(F.y1,F.x1),!v),e.arc(u,l,a,V(F.cy+F.y1,F.cx+F.x1),V(O.cy+O.y1,O.cx+O.x1),v),rt>0&&e.arc(u+O.cx,l+O.cy,rt,V(O.y1,O.x1),V(O.y0,O.x0),!v))}else e.lineTo(u+k,l+z),e.arc(u,l,a,h,f,v)}e.closePath()}}}var wu=function(){function e(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return e}(),ts=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new wu},t.prototype.buildPath=function(r,i){mu(r,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(G);ts.prototype.type="sector";const Rn=ts;var Tu=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return e}(),rs=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Tu},t.prototype.buildPath=function(r,i){var a=i.cx,n=i.cy,o=Math.PI*2;r.moveTo(a+i.r,n),r.arc(a,n,i.r,0,o,!1),r.moveTo(a+i.r0,n),r.arc(a,n,i.r0,0,o,!0)},t}(G);rs.prototype.type="ring";const nv=rs;function bu(e,t,r,i){var a=[],n=[],o=[],s=[],f,h,u,l;if(i){u=[1/0,1/0],l=[-1/0,-1/0];for(var v=0,c=e.length;v<c;v++)Rr(u,u,e[v]),Dr(l,l,e[v]);Rr(u,u,i[0]),Dr(l,l,i[1])}for(var v=0,c=e.length;v<c;v++){var _=e[v];if(r)f=e[v?v-1:c-1],h=e[(v+1)%c];else if(v===0||v===c-1){a.push(zs(e[v]));continue}else f=e[v-1],h=e[v+1];$s(n,h,f),vi(n,n,t);var g=Qi(_,f),d=Qi(_,h),p=g+d;p!==0&&(g/=p,d/=p),vi(o,n,-g),vi(s,n,d);var y=Ba([],_,o),m=Ba([],_,s);i&&(Dr(y,y,u),Rr(y,y,l),Dr(m,m,u),Rr(m,m,l)),a.push(y),a.push(m)}return r&&a.push(a.shift()),a}function es(e,t,r){var i=t.smooth,a=t.points;if(a&&a.length>=2){if(i){var n=bu(a,i,r,t.smoothConstraint);e.moveTo(a[0][0],a[0][1]);for(var o=a.length,s=0;s<(r?o:o-1);s++){var f=n[s*2],h=n[s*2+1],u=a[(s+1)%o];e.bezierCurveTo(f[0],f[1],h[0],h[1],u[0],u[1])}}else{e.moveTo(a[0][0],a[0][1]);for(var s=1,l=a.length;s<l;s++)e.lineTo(a[s][0],a[s][1])}r&&e.closePath()}}var Cu=function(){function e(){this.points=null,this.smooth=0,this.smoothConstraint=null}return e}(),is=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Cu},t.prototype.buildPath=function(r,i){es(r,i,!0)},t}(G);is.prototype.type="polygon";const as=is;var Lu=function(){function e(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return e}(),ns=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Lu},t.prototype.buildPath=function(r,i){es(r,i,!1)},t}(G);ns.prototype.type="polyline";const Mu=ns;var Su={},Pu=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return e}(),os=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Pu},t.prototype.buildPath=function(r,i){var a,n,o,s;if(this.subPixelOptimize){var f=Qh(Su,i,this.style);a=f.x1,n=f.y1,o=f.x2,s=f.y2}else a=i.x1,n=i.y1,o=i.x2,s=i.y2;var h=i.percent;h!==0&&(r.moveTo(a,n),h<1&&(o=a*(1-h)+o*h,s=n*(1-h)+s*h),r.lineTo(o,s))},t.prototype.pointAt=function(r){var i=this.shape;return[i.x1*(1-r)+i.x2*r,i.y1*(1-r)+i.y2*r]},t}(G);os.prototype.type="line";const Ru=os;var et=[],Du=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return e}();function Dn(e,t,r){var i=e.cpx2,a=e.cpy2;return i!=null||a!=null?[(r?Za:N)(e.x1,e.cpx1,e.cpx2,e.x2,t),(r?Za:N)(e.y1,e.cpy1,e.cpy2,e.y2,t)]:[(r?Na:Q)(e.x1,e.cpx1,e.x2,t),(r?Na:Q)(e.y1,e.cpy1,e.y2,t)]}var ss=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Du},t.prototype.buildPath=function(r,i){var a=i.x1,n=i.y1,o=i.x2,s=i.y2,f=i.cpx1,h=i.cpy1,u=i.cpx2,l=i.cpy2,v=i.percent;v!==0&&(r.moveTo(a,n),u==null||l==null?(v<1&&(Ze(a,f,o,v,et),f=et[1],o=et[2],Ze(n,h,s,v,et),h=et[1],s=et[2]),r.quadraticCurveTo(f,h,o,s)):(v<1&&(Xt(a,f,u,o,v,et),f=et[1],u=et[2],o=et[3],Xt(n,h,l,s,v,et),h=et[1],l=et[2],s=et[3]),r.bezierCurveTo(f,h,u,l,o,s)))},t.prototype.pointAt=function(r){return Dn(this.shape,r,!1)},t.prototype.tangentAt=function(r){var i=Dn(this.shape,r,!0);return Xs(i,i)},t}(G);ss.prototype.type="bezier-curve";const ov=ss;var Au=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return e}(),fs=function(e){B(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Au},t.prototype.buildPath=function(r,i){var a=i.cx,n=i.cy,o=Math.max(i.r,0),s=i.startAngle,f=i.endAngle,h=i.clockwise,u=Math.cos(s),l=Math.sin(s);r.moveTo(u*o+a,l*o+n),r.arc(a,n,o,s,f,!h)},t}(G);fs.prototype.type="arc";const sv=fs;var xu=function(e){B(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.type="compound",r}return t.prototype._updatePathDirty=function(){for(var r=this.shape.paths,i=this.shapeChanged(),a=0;a<r.length;a++)i=i||r[a].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var r=this.shape.paths||[],i=this.getGlobalScale(),a=0;a<r.length;a++)r[a].path||r[a].createPathProxy(),r[a].path.setScale(i[0],i[1],r[a].segmentIgnoreThreshold)},t.prototype.buildPath=function(r,i){for(var a=i.paths||[],n=0;n<a.length;n++)a[n].buildPath(r,a[n].shape,!0)},t.prototype.afterBrush=function(){for(var r=this.shape.paths||[],i=0;i<r.length;i++)r[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),G.prototype.getBoundingRect.call(this)},t}(G);const fv=xu;var Eu=function(){function e(t){this.colorStops=t||[]}return e.prototype.addColorStop=function(t,r){this.colorStops.push({offset:t,color:r})},e}();const hs=Eu;var Fu=function(e){B(t,e);function t(r,i,a,n,o,s){var f=e.call(this,o)||this;return f.x=r==null?0:r,f.y=i==null?0:i,f.x2=a==null?1:a,f.y2=n==null?0:n,f.type="linear",f.global=s||!1,f}return t}(hs);const Iu=Fu;var Ou=function(e){B(t,e);function t(r,i,a,n,o){var s=e.call(this,n)||this;return s.x=r==null?.5:r,s.y=i==null?.5:i,s.r=a==null?.5:a,s.type="radial",s.global=o||!1,s}return t}(hs);const Hu=Ou;var or=[0,0],sr=[0,0],xe=new I,Ee=new I,Bu=function(){function e(t,r){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new I;for(var i=0;i<2;i++)this._axes[i]=new I;t&&this.fromBoundingRect(t,r)}return e.prototype.fromBoundingRect=function(t,r){var i=this._corners,a=this._axes,n=t.x,o=t.y,s=n+t.width,f=o+t.height;if(i[0].set(n,o),i[1].set(s,o),i[2].set(s,f),i[3].set(n,f),r)for(var h=0;h<4;h++)i[h].transform(r);I.sub(a[0],i[1],i[0]),I.sub(a[1],i[3],i[0]),a[0].normalize(),a[1].normalize();for(var h=0;h<2;h++)this._origin[h]=a[h].dot(i[0])},e.prototype.intersect=function(t,r){var i=!0,a=!r;return xe.set(1/0,1/0),Ee.set(0,0),!this._intersectCheckOneSide(this,t,xe,Ee,a,1)&&(i=!1,a)||!this._intersectCheckOneSide(t,this,xe,Ee,a,-1)&&(i=!1,a)||a||I.copy(r,i?xe:Ee),i},e.prototype._intersectCheckOneSide=function(t,r,i,a,n,o){for(var s=!0,f=0;f<2;f++){var h=this._axes[f];if(this._getProjMinMaxOnAxis(f,t._corners,or),this._getProjMinMaxOnAxis(f,r._corners,sr),or[1]<sr[0]||or[0]>sr[1]){if(s=!1,n)return s;var u=Math.abs(sr[0]-or[1]),l=Math.abs(or[0]-sr[1]);Math.min(u,l)>a.len()&&(u<l?I.scale(a,h,-u*o):I.scale(a,h,l*o))}else if(i){var u=Math.abs(sr[0]-or[1]),l=Math.abs(or[0]-sr[1]);Math.min(u,l)<i.len()&&(u<l?I.scale(i,h,u*o):I.scale(i,h,-l*o))}}return s},e.prototype._getProjMinMaxOnAxis=function(t,r,i){for(var a=this._axes[t],n=this._origin,o=r[0].dot(a)+n[t],s=o,f=o,h=1;h<r.length;h++){var u=r[h].dot(a)+n[t];s=Math.min(u,s),f=Math.max(u,f)}i[0]=s,i[1]=f},e}();const hv=Bu;var ku=[],zu=function(e){B(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.notClear=!0,r.incremental=!0,r._displayables=[],r._temporaryDisplayables=[],r._cursor=0,r}return t.prototype.traverse=function(r,i){r.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(r,i){i?this._temporaryDisplayables.push(r):this._displayables.push(r),this.markRedraw()},t.prototype.addDisplayables=function(r,i){i=i||!1;for(var a=0;a<r.length;a++)this.addDisplayable(r[a],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(r){for(var i=this._cursor;i<this._displayables.length;i++)r&&r(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)r&&r(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var r=this._cursor;r<this._displayables.length;r++){var i=this._displayables[r];i.parent=this,i.update(),i.parent=null}for(var r=0;r<this._temporaryDisplayables.length;r++){var i=this._temporaryDisplayables[r];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var r=new W(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var a=this._displayables[i],n=a.getBoundingRect().clone();a.needLocalTransform()&&n.applyTransform(a.getLocalTransform(ku)),r.union(n)}this._rect=r}return this._rect},t.prototype.contain=function(r,i){var a=this.transformCoordToLocal(r,i),n=this.getBoundingRect();if(n.contain(a[0],a[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(r,i))return!0}return!1},t}(de);const uv=zu;var $u=Math.round(Math.random()*9),Yu=typeof Object.defineProperty=="function",Wu=function(){function e(){this._id="__ec_inner_"+$u++}return e.prototype.get=function(t){return this._guard(t)[this._id]},e.prototype.set=function(t,r){var i=this._guard(t);return Yu?Object.defineProperty(i,this._id,{value:r,enumerable:!1,configurable:!0}):i[this._id]=r,this},e.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},e.prototype.has=function(t){return!!this._guard(t)[this._id]},e.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},e}();const lv=Wu;function ur(e){return isFinite(e)}function Xu(e,t,r){var i=t.x==null?0:t.x,a=t.x2==null?1:t.x2,n=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*r.width+r.x,a=a*r.width+r.x,n=n*r.height+r.y,o=o*r.height+r.y),i=ur(i)?i:0,a=ur(a)?a:1,n=ur(n)?n:0,o=ur(o)?o:0;var s=e.createLinearGradient(i,n,a,o);return s}function Gu(e,t,r){var i=r.width,a=r.height,n=Math.min(i,a),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,f=t.r==null?.5:t.r;t.global||(o=o*i+r.x,s=s*a+r.y,f=f*n),o=ur(o)?o:.5,s=ur(s)?s:.5,f=f>=0&&ur(f)?f:.5;var h=e.createRadialGradient(o,s,0,o,s,f);return h}function _a(e,t,r){for(var i=t.type==="radial"?Gu(e,t,r):Xu(e,t,r),a=t.colorStops,n=0;n<a.length;n++)i.addColorStop(a[n].offset,a[n].color);return i}function qu(e,t){if(e===t||!e&&!t)return!1;if(!e||!t||e.length!==t.length)return!0;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!0;return!1}function Fe(e){return parseInt(e,10)}function Ie(e,t,r){var i=["width","height"][t],a=["clientWidth","clientHeight"][t],n=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(r[i]!=null&&r[i]!=="auto")return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(e);return(e[a]||Fe(s[i])||Fe(e.style[i]))-(Fe(s[n])||0)-(Fe(s[o])||0)|0}function Uu(e,t){return!e||e==="solid"||!(t>0)?null:e==="dashed"?[4*t,2*t]:e==="dotted"?[t]:He(e)?[e]:ne(e)?e:null}function us(e){var t=e.style,r=t.lineDash&&t.lineWidth>0&&Uu(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(r){var a=t.strokeNoScale&&e.getLineScale?e.getLineScale():1;a&&a!==1&&(r=xt(r,function(n){return n/a}),i/=a)}return[r,i]}var Zu=new dr(!0);function Je(e){var t=e.stroke;return!(t==null||t==="none"||!(e.lineWidth>0))}function An(e){return typeof e=="string"&&e!=="none"}function je(e){var t=e.fill;return t!=null&&t!=="none"}function xn(e,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.fillOpacity*t.opacity,e.fill(),e.globalAlpha=r}else e.fill()}function En(e,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.strokeOpacity*t.opacity,e.stroke(),e.globalAlpha=r}else e.stroke()}function ga(e,t,r){var i=Oo(t.image,t.__image,r);if(hi(i)){var a=e.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&a&&a.setTransform){var n=new DOMMatrix;n.translateSelf(t.x||0,t.y||0),n.rotateSelf(0,0,(t.rotation||0)*ks),n.scaleSelf(t.scaleX||1,t.scaleY||1),a.setTransform(n)}return a}}function Nu(e,t,r,i){var a,n=Je(r),o=je(r),s=r.strokePercent,f=s<1,h=!t.path;(!t.silent||f)&&h&&t.createPathProxy();var u=t.path||Zu,l=t.__dirty;if(!i){var v=r.fill,c=r.stroke,_=o&&!!v.colorStops,g=n&&!!c.colorStops,d=o&&!!v.image,p=n&&!!c.image,y=void 0,m=void 0,T=void 0,b=void 0,w=void 0;(_||g)&&(w=t.getBoundingRect()),_&&(y=l?_a(e,v,w):t.__canvasFillGradient,t.__canvasFillGradient=y),g&&(m=l?_a(e,c,w):t.__canvasStrokeGradient,t.__canvasStrokeGradient=m),d&&(T=l||!t.__canvasFillPattern?ga(e,v,t):t.__canvasFillPattern,t.__canvasFillPattern=T),p&&(b=l||!t.__canvasStrokePattern?ga(e,c,t):t.__canvasStrokePattern,t.__canvasStrokePattern=T),_?e.fillStyle=y:d&&(T?e.fillStyle=T:o=!1),g?e.strokeStyle=m:p&&(b?e.strokeStyle=b:n=!1)}var L=t.getGlobalScale();u.setScale(L[0],L[1],t.segmentIgnoreThreshold);var C,M;e.setLineDash&&r.lineDash&&(a=us(t),C=a[0],M=a[1]);var S=!0;(h||l&Cr)&&(u.setDPR(e.dpr),f?u.setContext(null):(u.setContext(e),S=!1),u.reset(),t.buildPath(u,t.shape,i),u.toStatic(),t.pathUpdated()),S&&u.rebuildPath(e,f?s:1),C&&(e.setLineDash(C),e.lineDashOffset=M),i||(r.strokeFirst?(n&&En(e,r),o&&xn(e,r)):(o&&xn(e,r),n&&En(e,r))),C&&e.setLineDash([])}function Vu(e,t,r){var i=t.__image=Oo(r.image,t.__image,t,t.onload);if(!(!i||!hi(i))){var a=r.x||0,n=r.y||0,o=t.getWidth(),s=t.getHeight(),f=i.width/i.height;if(o==null&&s!=null?o=s*f:s==null&&o!=null?s=o/f:o==null&&s==null&&(o=i.width,s=i.height),r.sWidth&&r.sHeight){var h=r.sx||0,u=r.sy||0;e.drawImage(i,h,u,r.sWidth,r.sHeight,a,n,o,s)}else if(r.sx&&r.sy){var h=r.sx,u=r.sy,l=o-h,v=s-u;e.drawImage(i,h,u,l,v,a,n,o,s)}else e.drawImage(i,a,n,o,s)}}function Qu(e,t,r){var i,a=r.text;if(a!=null&&(a+=""),a){e.font=r.font||cr,e.textAlign=r.textAlign,e.textBaseline=r.textBaseline;var n=void 0,o=void 0;e.setLineDash&&r.lineDash&&(i=us(t),n=i[0],o=i[1]),n&&(e.setLineDash(n),e.lineDashOffset=o),r.strokeFirst?(Je(r)&&e.strokeText(a,r.x,r.y),je(r)&&e.fillText(a,r.x,r.y)):(je(r)&&e.fillText(a,r.x,r.y),Je(r)&&e.strokeText(a,r.x,r.y)),n&&e.setLineDash([])}}var Fn=["shadowBlur","shadowOffsetX","shadowOffsetY"],In=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function ls(e,t,r,i,a){var n=!1;if(!i&&(r=r||{},t===r))return!1;if(i||t.opacity!==r.opacity){nt(e,a),n=!0;var o=Math.max(Math.min(t.opacity,1),0);e.globalAlpha=isNaN(o)?vr.opacity:o}(i||t.blend!==r.blend)&&(n||(nt(e,a),n=!0),e.globalCompositeOperation=t.blend||vr.blend);for(var s=0;s<Fn.length;s++){var f=Fn[s];(i||t[f]!==r[f])&&(n||(nt(e,a),n=!0),e[f]=e.dpr*(t[f]||0))}return(i||t.shadowColor!==r.shadowColor)&&(n||(nt(e,a),n=!0),e.shadowColor=t.shadowColor||vr.shadowColor),n}function On(e,t,r,i,a){var n=ue(t,a.inHover),o=i?null:r&&ue(r,a.inHover)||{};if(n===o)return!1;var s=ls(e,n,o,i,a);if((i||n.fill!==o.fill)&&(s||(nt(e,a),s=!0),An(n.fill)&&(e.fillStyle=n.fill)),(i||n.stroke!==o.stroke)&&(s||(nt(e,a),s=!0),An(n.stroke)&&(e.strokeStyle=n.stroke)),(i||n.opacity!==o.opacity)&&(s||(nt(e,a),s=!0),e.globalAlpha=n.opacity==null?1:n.opacity),t.hasStroke()){var f=n.lineWidth,h=f/(n.strokeNoScale&&t.getLineScale?t.getLineScale():1);e.lineWidth!==h&&(s||(nt(e,a),s=!0),e.lineWidth=h)}for(var u=0;u<In.length;u++){var l=In[u],v=l[0];(i||n[v]!==o[v])&&(s||(nt(e,a),s=!0),e[v]=n[v]||l[1])}return s}function Ku(e,t,r,i,a){return ls(e,ue(t,a.inHover),r&&ue(r,a.inHover),i,a)}function vs(e,t){var r=t.transform,i=e.dpr||1;r?e.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):e.setTransform(i,0,0,i,0,0)}function Ju(e,t,r){for(var i=!1,a=0;a<e.length;a++){var n=e[a];i=i||n.isZeroArea(),vs(t,n),t.beginPath(),n.buildPath(t,n.shape),t.clip()}r.allClipped=i}function ju(e,t){return e&&t?e[0]!==t[0]||e[1]!==t[1]||e[2]!==t[2]||e[3]!==t[3]||e[4]!==t[4]||e[5]!==t[5]:!(!e&&!t)}var Hn=1,Bn=2,kn=3,zn=4;function tl(e){var t=je(e),r=Je(e);return!(e.lineDash||!(+t^+r)||t&&typeof e.fill!="string"||r&&typeof e.stroke!="string"||e.strokePercent<1||e.strokeOpacity<1||e.fillOpacity<1)}function nt(e,t){t.batchFill&&e.fill(),t.batchStroke&&e.stroke(),t.batchFill="",t.batchStroke=""}function ue(e,t){return t&&e.__hoverStyle||e.style}function rl(e,t){lr(e,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function lr(e,t,r,i){var a=t.transform;if(!t.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1)){t.__dirty&=~ht,t.__isRendered=!1;return}var n=t.__clipPaths,o=r.prevElClipPaths,s=!1,f=!1;if((!o||qu(n,o))&&(o&&o.length&&(nt(e,r),e.restore(),f=s=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),n&&n.length&&(nt(e,r),e.save(),Ju(n,e,r),s=!0),r.prevElClipPaths=n),r.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var h=r.prevEl;h||(f=s=!0);var u=t instanceof G&&t.autoBatch&&tl(t.style);s||ju(a,h.transform)?(nt(e,r),vs(e,t)):u||nt(e,r);var l=ue(t,r.inHover);t instanceof G?(r.lastDrawType!==Hn&&(f=!0,r.lastDrawType=Hn),On(e,t,h,f,r),(!u||!r.batchFill&&!r.batchStroke)&&e.beginPath(),Nu(e,t,l,u),u&&(r.batchFill=l.fill||"",r.batchStroke=l.stroke||"")):t instanceof Qe?(r.lastDrawType!==kn&&(f=!0,r.lastDrawType=kn),On(e,t,h,f,r),Qu(e,t,l)):t instanceof Fa?(r.lastDrawType!==Bn&&(f=!0,r.lastDrawType=Bn),Ku(e,t,h,f,r),Vu(e,t,l)):t.getTemporalDisplayables&&(r.lastDrawType!==zn&&(f=!0,r.lastDrawType=zn),el(e,t,r)),u&&i&&nt(e,r),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),r.prevEl=t,t.__dirty=0,t.__isRendered=!0}function el(e,t,r){var i=t.getDisplayables(),a=t.getTemporalDisplayables();e.save();var n={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var f=i[o];f.beforeBrush&&f.beforeBrush(),f.innerBeforeBrush(),lr(e,f,n,o===s-1),f.innerAfterBrush(),f.afterBrush&&f.afterBrush(),n.prevEl=f}for(var h=0,u=a.length;h<u;h++){var f=a[h];f.beforeBrush&&f.beforeBrush(),f.innerBeforeBrush(),lr(e,f,n,h===u-1),f.innerAfterBrush(),f.afterBrush&&f.afterBrush(),n.prevEl=f}t.clearTemporalDisplayables(),t.notClear=!0,e.restore()}var il=1e-8;function $n(e,t){return Math.abs(e-t)<il}function vv(e,t,r){var i=0,a=e[0];if(!a)return!1;for(var n=1;n<e.length;n++){var o=e[n];i+=Ot(a[0],a[1],o[0],o[1],t,r),a=o}var s=e[0];return(!$n(a[0],s[0])||!$n(a[1],s[1]))&&(i+=Ot(a[0],a[1],s[0],s[1],t,r)),i!==0}function al(e){if(Ge(e)){var t=new DOMParser;e=t.parseFromString(e,"text/xml")}var r=e;for(r.nodeType===9&&(r=r.firstChild);r.nodeName.toLowerCase()!=="svg"||r.nodeType!==1;)r=r.nextSibling;return r}var Ui,ti={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Yn=j(ti),ri={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Wn=j(ri),nl=function(){function e(){this._defs={},this._root=null}return e.prototype.parse=function(t,r){r=r||{};var i=al(t);this._defsUsePending=[];var a=new Mr;this._root=a;var n=[],o=i.getAttribute("viewBox")||"",s=parseFloat(i.getAttribute("width")||r.width),f=parseFloat(i.getAttribute("height")||r.height);isNaN(s)&&(s=null),isNaN(f)&&(f=null),ft(i,a,null,!0,!1);for(var h=i.firstChild;h;)this._parseNode(h,a,n,null,!1,!1),h=h.nextSibling;fl(this._defs,this._defsUsePending),this._defsUsePending=[];var u,l;if(o){var v=li(o);v.length>=4&&(u={x:parseFloat(v[0]||0),y:parseFloat(v[1]||0),width:parseFloat(v[2]),height:parseFloat(v[3])})}if(u&&s!=null&&f!=null&&(l=cl(u,{x:0,y:0,width:s,height:f}),!r.ignoreViewBox)){var c=a;a=new Mr,a.add(c),c.scaleX=c.scaleY=l.scale,c.x=l.x,c.y=l.y}return!r.ignoreRootClip&&s!=null&&f!=null&&a.setClipPath(new Ke({shape:{x:0,y:0,width:s,height:f}})),{root:a,width:s,height:f,viewBoxRect:u,viewBoxTransform:l,named:n}},e.prototype._parseNode=function(t,r,i,a,n,o){var s=t.nodeName.toLowerCase(),f,h=a;if(s==="defs"&&(n=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")f=r;else{if(!n){var u=Ui[s];if(u&&qe(Ui,s)){f=u.call(this,t,r);var l=t.getAttribute("name");if(l){var v={name:l,namedFrom:null,svgNodeTagLower:s,el:f};i.push(v),s==="g"&&(h=v)}else a&&i.push({name:a.name,namedFrom:a,svgNodeTagLower:s,el:f});r.add(f)}}var c=Xn[s];if(c&&qe(Xn,s)){var _=c.call(this,t),g=t.getAttribute("id");g&&(this._defs[g]=_)}}if(f&&f.isGroup)for(var d=t.firstChild;d;)d.nodeType===1?this._parseNode(d,f,i,h,n,o):d.nodeType===3&&o&&this._parseText(d,f),d=d.nextSibling},e.prototype._parseText=function(t,r){var i=new Qe({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});lt(r,i),ft(t,i,this._defsUsePending,!1,!1),ol(i,r);var a=i.style,n=a.fontSize;n&&n<9&&(a.fontSize=9,i.scaleX*=n/9,i.scaleY*=n/9);var o=(a.fontSize||a.fontFamily)&&[a.fontStyle,a.fontWeight,(a.fontSize||12)+"px",a.fontFamily||"sans-serif"].join(" ");a.font=o;var s=i.getBoundingRect();return this._textX+=s.width,r.add(i),i},e.internalField=function(){Ui={g:function(t,r){var i=new Mr;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i},rect:function(t,r){var i=new Ke;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),i.silent=!0,i},circle:function(t,r){var i=new cu;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),i.silent=!0,i},line:function(t,r){var i=new Ru;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),i.silent=!0,i},ellipse:function(t,r){var i=new pu;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),i.silent=!0,i},polygon:function(t,r){var i=t.getAttribute("points"),a;i&&(a=Un(i));var n=new as({shape:{points:a||[]},silent:!0});return lt(r,n),ft(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,r){var i=t.getAttribute("points"),a;i&&(a=Un(i));var n=new Mu({shape:{points:a||[]},silent:!0});return lt(r,n),ft(t,n,this._defsUsePending,!1,!1),n},image:function(t,r){var i=new Fa;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),i.silent=!0,i},text:function(t,r){var i=t.getAttribute("x")||"0",a=t.getAttribute("y")||"0",n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(i)+parseFloat(n),this._textY=parseFloat(a)+parseFloat(o);var s=new Mr;return lt(r,s),ft(t,s,this._defsUsePending,!1,!0),s},tspan:function(t,r){var i=t.getAttribute("x"),a=t.getAttribute("y");i!=null&&(this._textX=parseFloat(i)),a!=null&&(this._textY=parseFloat(a));var n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",s=new Mr;return lt(r,s),ft(t,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),s},path:function(t,r){var i=t.getAttribute("d")||"",a=lu(i);return lt(r,a),ft(t,a,this._defsUsePending,!1,!1),a.silent=!0,a}}}(),e}(),Xn={lineargradient:function(e){var t=parseInt(e.getAttribute("x1")||"0",10),r=parseInt(e.getAttribute("y1")||"0",10),i=parseInt(e.getAttribute("x2")||"10",10),a=parseInt(e.getAttribute("y2")||"0",10),n=new Iu(t,r,i,a);return Gn(e,n),qn(e,n),n},radialgradient:function(e){var t=parseInt(e.getAttribute("cx")||"0",10),r=parseInt(e.getAttribute("cy")||"0",10),i=parseInt(e.getAttribute("r")||"0",10),a=new Hu(t,r,i);return Gn(e,a),qn(e,a),a}};function Gn(e,t){var r=e.getAttribute("gradientUnits");r==="userSpaceOnUse"&&(t.global=!0)}function qn(e,t){for(var r=e.firstChild;r;){if(r.nodeType===1&&r.nodeName.toLocaleLowerCase()==="stop"){var i=r.getAttribute("offset"),a=void 0;i&&i.indexOf("%")>0?a=parseInt(i,10)/100:i?a=parseFloat(i):a=0;var n={};cs(r,n,n);var o=n.stopColor||r.getAttribute("stop-color")||"#000000";t.colorStops.push({offset:a,color:o})}r=r.nextSibling}}function lt(e,t){e&&e.__inheritedStyle&&(t.__inheritedStyle||(t.__inheritedStyle={}),_t(t.__inheritedStyle,e.__inheritedStyle))}function Un(e){for(var t=li(e),r=[],i=0;i<t.length;i+=2){var a=parseFloat(t[i]),n=parseFloat(t[i+1]);r.push([a,n])}return r}function ft(e,t,r,i,a){var n=t,o=n.__inheritedStyle=n.__inheritedStyle||{},s={};e.nodeType===1&&(ll(e,t),cs(e,o,s),i||vl(e,o,s)),n.style=n.style||{},o.fill!=null&&(n.style.fill=Zn(n,"fill",o.fill,r)),o.stroke!=null&&(n.style.stroke=Zn(n,"stroke",o.stroke,r)),J(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(f){o[f]!=null&&(n.style[f]=parseFloat(o[f]))}),J(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(f){o[f]!=null&&(n.style[f]=o[f])}),a&&(n.__selfStyle=s),o.lineDash&&(n.style.lineDash=xt(li(o.lineDash),function(f){return parseFloat(f)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(n.invisible=!0),o.display==="none"&&(n.ignore=!0)}function ol(e,t){var r=t.__selfStyle;if(r){var i=r.textBaseline,a=i;!i||i==="auto"||i==="baseline"?a="alphabetic":i==="before-edge"||i==="text-before-edge"?a="top":i==="after-edge"||i==="text-after-edge"?a="bottom":(i==="central"||i==="mathematical")&&(a="middle"),e.style.textBaseline=a}var n=t.__inheritedStyle;if(n){var o=n.textAlign,s=o;o&&(o==="middle"&&(s="center"),e.style.textAlign=s)}}var sl=/^url\(\s*#(.*?)\)/;function Zn(e,t,r,i){var a=r&&r.match(sl);if(a){var n=Pr(a[1]);i.push([e,t,n]);return}return r==="none"&&(r=null),r}function fl(e,t){for(var r=0;r<t.length;r++){var i=t[r];i[0].style[i[1]]=e[i[2]]}}var hl=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function li(e){return e.match(hl)||[]}var ul=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Zi=Math.PI/180;function ll(e,t){var r=e.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],a=null;r.replace(ul,function(l,v,c){return i.push(v,c),""});for(var n=i.length-1;n>0;n-=2){var o=i[n],s=i[n-1],f=li(o);switch(a=a||se(),s){case"translate":la(a,a,[parseFloat(f[0]),parseFloat(f[1]||"0")]);break;case"scale":Ao(a,a,[parseFloat(f[0]),parseFloat(f[1]||f[0])]);break;case"rotate":Do(a,a,-parseFloat(f[0])*Zi);break;case"skewX":var h=Math.tan(parseFloat(f[0])*Zi);ee(a,[1,0,h,1,0,0],a);break;case"skewY":var u=Math.tan(parseFloat(f[0])*Zi);ee(a,[1,u,0,1,0,0],a);break;case"matrix":a[0]=parseFloat(f[0]),a[1]=parseFloat(f[1]),a[2]=parseFloat(f[2]),a[3]=parseFloat(f[3]),a[4]=parseFloat(f[4]),a[5]=parseFloat(f[5]);break}}t.setLocalTransform(a)}}var Nn=/([^\s:;]+)\s*:\s*([^:;]+)/g;function cs(e,t,r){var i=e.getAttribute("style");if(!!i){Nn.lastIndex=0;for(var a;(a=Nn.exec(i))!=null;){var n=a[1],o=qe(ti,n)?ti[n]:null;o&&(t[o]=a[2]);var s=qe(ri,n)?ri[n]:null;s&&(r[s]=a[2])}}}function vl(e,t,r){for(var i=0;i<Yn.length;i++){var a=Yn[i],n=e.getAttribute(a);n!=null&&(t[ti[a]]=n)}for(var i=0;i<Wn.length;i++){var a=Wn[i],n=e.getAttribute(a);n!=null&&(r[ri[a]]=n)}}function cl(e,t){var r=t.width/e.width,i=t.height/e.height,a=Math.min(r,i);return{scale:a,x:-(e.x+e.width/2)*a+(t.x+t.width/2),y:-(e.y+e.height/2)*a+(t.y+t.height/2)}}function cv(e,t){var r=new nl;return r.parse(e,t)}function Vn(e,t,r){var i=le.createCanvas(),a=t.getWidth(),n=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=a+"px",o.height=n+"px",i.setAttribute("data-zr-dom-id",e)),i.width=a*r,i.height=n*r,i}var dl=function(e){B(t,e);function t(r,i,a){var n=e.call(this)||this;n.motionBlur=!1,n.lastFrameAlpha=.7,n.dpr=1,n.virtual=!1,n.config={},n.incremental=!1,n.zlevel=0,n.maxRepaintRectCount=5,n.__dirty=!0,n.__firstTimePaint=!0,n.__used=!1,n.__drawIndex=0,n.__startIndex=0,n.__endIndex=0,n.__prevStartIndex=null,n.__prevEndIndex=null;var o;a=a||Ve,typeof r=="string"?o=Vn(r,i,a):zt(r)&&(o=r,r=o.id),n.id=r,n.dom=o;var s=o.style;return s&&(ho(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),n.painter=i,n.dpr=a,n}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var r=this.dpr;this.domBack=Vn("back-"+this.id,this.painter,r),this.ctxBack=this.domBack.getContext("2d"),r!==1&&this.ctxBack.scale(r,r)},t.prototype.createRepaintRects=function(r,i,a,n){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,f=!1,h=new W(0,0,0,0);function u(y){if(!(!y.isFinite()||y.isZero()))if(o.length===0){var m=new W(0,0,0,0);m.copy(y),o.push(m)}else{for(var T=!1,b=1/0,w=0,L=0;L<o.length;++L){var C=o[L];if(C.intersect(y)){var M=new W(0,0,0,0);M.copy(C),M.union(y),o[L]=M,T=!0;break}else if(f){h.copy(y),h.union(C);var S=y.width*y.height,P=C.width*C.height,R=h.width*h.height,x=R-S-P;x<b&&(b=x,w=L)}}if(f&&(o[w].union(y),T=!0),!T){var m=new W(0,0,0,0);m.copy(y),o.push(m)}f||(f=o.length>=s)}}for(var l=this.__startIndex;l<this.__endIndex;++l){var v=r[l];if(v){var c=v.shouldBePainted(a,n,!0,!0),_=v.__isRendered&&(v.__dirty&ht||!c)?v.getPrevPaintRect():null;_&&u(_);var g=c&&(v.__dirty&ht||!v.__isRendered)?v.getPaintRect():null;g&&u(g)}}for(var l=this.__prevStartIndex;l<this.__prevEndIndex;++l){var v=i[l],c=v.shouldBePainted(a,n,!0,!0);if(v&&(!c||!v.__zr)&&v.__isRendered){var _=v.getPrevPaintRect();_&&u(_)}}var d;do{d=!1;for(var l=0;l<o.length;){if(o[l].isZero()){o.splice(l,1);continue}for(var p=l+1;p<o.length;)o[l].intersect(o[p])?(d=!0,o[l].union(o[p]),o.splice(p,1)):p++;l++}}while(d);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(r,i){var a=this.dpr,n=this.dom,o=n.style,s=this.domBack;o&&(o.width=r+"px",o.height=i+"px"),n.width=r*a,n.height=i*a,s&&(s.width=r*a,s.height=i*a,a!==1&&this.ctxBack.scale(a,a))},t.prototype.clear=function(r,i,a){var n=this.dom,o=this.ctx,s=n.width,f=n.height;i=i||this.clearColor;var h=this.motionBlur&&!r,u=this.lastFrameAlpha,l=this.dpr,v=this;h&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,s/l,f/l));var c=this.domBack;function _(g,d,p,y){if(o.clearRect(g,d,p,y),i&&i!=="transparent"){var m=void 0;Sa(i)?(m=i.__canvasGradient||_a(o,i,{x:0,y:0,width:p,height:y}),i.__canvasGradient=m):Is(i)&&(m=ga(o,i,{dirty:function(){v.setUnpainted(),v.__painter.refresh()}})),o.save(),o.fillStyle=m||i,o.fillRect(g,d,p,y),o.restore()}h&&(o.save(),o.globalAlpha=u,o.drawImage(c,g,d,p,y),o.restore())}!a||h?_(0,0,s,f):a.length&&J(a,function(g){_(g.x*l,g.y*l,g.width*l,g.height*l)})},t}($r);const Ni=dl;var Qn=1e5,fr=314159,Oe=.01,pl=.001;function _l(e){return e?e.__builtin__?!0:!(typeof e.resize!="function"||typeof e.refresh!="function"):!1}function gl(e,t){var r=document.createElement("div");return r.style.cssText=["position:relative","width:"+e+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",r}var yl=function(){function e(t,r,i,a){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=$({},i||{}),this.dpr=i.devicePixelRatio||Ve,this._singleCanvas=n,this.root=t;var o=t.style;o&&(ho(t),t.innerHTML=""),this.storage=r;var s=this._zlevelList;this._prevDisplayList=[];var f=this._layers;if(n){var u=t,l=u.width,v=u.height;i.width!=null&&(l=i.width),i.height!=null&&(v=i.height),this.dpr=i.devicePixelRatio||1,u.width=l*this.dpr,u.height=v*this.dpr,this._width=l,this._height=v;var c=new Ni(u,this,this.dpr);c.__builtin__=!0,c.initContext(),f[fr]=c,c.zlevel=fr,s.push(fr),this._domRoot=t}else{this._width=Ie(t,0,i),this._height=Ie(t,1,i);var h=this._domRoot=gl(this._width,this._height);t.appendChild(h)}}return e.prototype.getType=function(){return"canvas"},e.prototype.isSingleCanvas=function(){return this._singleCanvas},e.prototype.getViewportRoot=function(){return this._domRoot},e.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},e.prototype.refresh=function(t){var r=this.storage.getDisplayList(!0),i=this._prevDisplayList,a=this._zlevelList;this._redrawId=Math.random(),this._paintList(r,i,t,this._redrawId);for(var n=0;n<a.length;n++){var o=a[n],s=this._layers[o];if(!s.__builtin__&&s.refresh){var f=n===0?this._backgroundColor:null;s.refresh(f)}}return this._opts.useDirtyRect&&(this._prevDisplayList=r.slice()),this},e.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},e.prototype._paintHoverList=function(t){var r=t.length,i=this._hoverlayer;if(i&&i.clear(),!!r){for(var a={inHover:!0,viewWidth:this._width,viewHeight:this._height},n,o=0;o<r;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(Qn)),n||(n=i.ctx,n.save()),lr(n,s,a,o===r-1))}n&&n.restore()}},e.prototype.getHoverLayer=function(){return this.getLayer(Qn)},e.prototype.paintOne=function(t,r){rl(t,r)},e.prototype._paintList=function(t,r,i,a){if(this._redrawId===a){i=i||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,r,i),o=n.finished,s=n.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(h){h.afterBrush&&h.afterBrush()});else{var f=this;ta(function(){f._paintList(t,r,i,a)})}}},e.prototype._compositeManually=function(){var t=this.getLayer(fr).ctx,r=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,r,i),this.eachBuiltinLayer(function(a){a.virtual&&t.drawImage(a.dom,0,0,r,i)})},e.prototype._doPaintList=function(t,r,i){for(var a=this,n=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var f=this._zlevelList[s],h=this._layers[f];h.__builtin__&&h!==this._hoverlayer&&(h.__dirty||i)&&n.push(h)}for(var u=!0,l=!1,v=function(g){var d=n[g],p=d.ctx,y=o&&d.createRepaintRects(t,r,c._width,c._height),m=i?d.__startIndex:d.__drawIndex,T=!i&&d.incremental&&Date.now,b=T&&Date.now(),w=d.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(d.__startIndex===d.__endIndex)d.clear(!1,w,y);else if(m===d.__startIndex){var L=t[m];(!L.incremental||!L.notClear||i)&&d.clear(!1,w,y)}m===-1&&(console.error("For some unknown reason. drawIndex is -1"),m=d.__startIndex);var C,M=function(x){var A={inHover:!1,allClipped:!1,prevEl:null,viewWidth:a._width,viewHeight:a._height};for(C=m;C<d.__endIndex;C++){var E=t[C];if(E.__inHover&&(l=!0),a._doPaintEl(E,d,o,x,A,C===d.__endIndex-1),T){var D=Date.now()-b;if(D>15)break}}A.prevElClipPaths&&p.restore()};if(y)if(y.length===0)C=d.__endIndex;else for(var S=c.dpr,P=0;P<y.length;++P){var R=y[P];p.save(),p.beginPath(),p.rect(R.x*S,R.y*S,R.width*S,R.height*S),p.clip(),M(R),p.restore()}else p.save(),M(),p.restore();d.__drawIndex=C,d.__drawIndex<d.__endIndex&&(u=!1)},c=this,_=0;_<n.length;_++)v(_);return tt.wxa&&J(this._layers,function(g){g&&g.ctx&&g.ctx.draw&&g.ctx.draw()}),{finished:u,needsRefreshHover:l}},e.prototype._doPaintEl=function(t,r,i,a,n,o){var s=r.ctx;if(i){var f=t.getPaintRect();(!a||f&&f.intersect(a))&&(lr(s,t,n,o),t.setPrevPaintRect(f))}else lr(s,t,n,o)},e.prototype.getLayer=function(t,r){this._singleCanvas&&!this._needsManuallyCompositing&&(t=fr);var i=this._layers[t];return i||(i=new Ni("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?Sr(i,this._layerConfig[t],!0):this._layerConfig[t-Oe]&&Sr(i,this._layerConfig[t-Oe],!0),r&&(i.virtual=r),this.insertLayer(t,i),i.initContext()),i},e.prototype.insertLayer=function(t,r){var i=this._layers,a=this._zlevelList,n=a.length,o=this._domRoot,s=null,f=-1;if(!i[t]&&!!_l(r)){if(n>0&&t>a[0]){for(f=0;f<n-1&&!(a[f]<t&&a[f+1]>t);f++);s=i[a[f]]}if(a.splice(f+1,0,t),i[t]=r,!r.virtual)if(s){var h=s.dom;h.nextSibling?o.insertBefore(r.dom,h.nextSibling):o.appendChild(r.dom)}else o.firstChild?o.insertBefore(r.dom,o.firstChild):o.appendChild(r.dom);r.__painter=this}},e.prototype.eachLayer=function(t,r){for(var i=this._zlevelList,a=0;a<i.length;a++){var n=i[a];t.call(r,this._layers[n],n)}},e.prototype.eachBuiltinLayer=function(t,r){for(var i=this._zlevelList,a=0;a<i.length;a++){var n=i[a],o=this._layers[n];o.__builtin__&&t.call(r,o,n)}},e.prototype.eachOtherLayer=function(t,r){for(var i=this._zlevelList,a=0;a<i.length;a++){var n=i[a],o=this._layers[n];o.__builtin__||t.call(r,o,n)}},e.prototype.getLayers=function(){return this._layers},e.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(l,v){l.__dirty=l.__used=!1});function r(l){n&&(n.__endIndex!==l&&(n.__dirty=!0),n.__endIndex=l)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var a=t[i];if(a.zlevel!==t[i-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}}var n=null,o=0,s,f;for(f=0;f<t.length;f++){var a=t[f],h=a.zlevel,u=void 0;s!==h&&(s=h,o=0),a.incremental?(u=this.getLayer(h+pl,this._needsManuallyCompositing),u.incremental=!0,o=1):u=this.getLayer(h+(o>0?Oe:0),this._needsManuallyCompositing),u.__builtin__||Ma("ZLevel "+h+" has been used by unkown layer "+u.id),u!==n&&(u.__used=!0,u.__startIndex!==f&&(u.__dirty=!0),u.__startIndex=f,u.incremental?u.__drawIndex=-1:u.__drawIndex=f,r(f),n=u),a.__dirty&ht&&!a.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=f))}r(f),this.eachBuiltinLayer(function(l,v){!l.__used&&l.getElementCount()>0&&(l.__dirty=!0,l.__startIndex=l.__endIndex=l.__drawIndex=0),l.__dirty&&l.__drawIndex<0&&(l.__drawIndex=l.__startIndex)})},e.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},e.prototype._clearLayer=function(t){t.clear()},e.prototype.setBackgroundColor=function(t){this._backgroundColor=t,J(this._layers,function(r){r.setUnpainted()})},e.prototype.configLayer=function(t,r){if(r){var i=this._layerConfig;i[t]?Sr(i[t],r,!0):i[t]=r;for(var a=0;a<this._zlevelList.length;a++){var n=this._zlevelList[a];if(n===t||n===t+Oe){var o=this._layers[n];Sr(o,i[t],!0)}}}},e.prototype.delLayer=function(t){var r=this._layers,i=this._zlevelList,a=r[t];!a||(a.dom.parentNode.removeChild(a.dom),delete r[t],i.splice(Pt(i,t),1))},e.prototype.resize=function(t,r){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var a=this._opts,n=this.root;if(t!=null&&(a.width=t),r!=null&&(a.height=r),t=Ie(n,0,a),r=Ie(n,1,a),i.style.display="",this._width!==t||r!==this._height){i.style.width=t+"px",i.style.height=r+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,r);this.refresh(!0)}this._width=t,this._height=r}else{if(t==null||r==null)return;this._width=t,this._height=r,this.getLayer(fr).resize(t,r)}return this},e.prototype.clearLayer=function(t){var r=this._layers[t];r&&r.clear()},e.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},e.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[fr].dom;var r=new Ni("image",this,t.pixelRatio||this.dpr);r.initContext(),r.clear(!1,t.backgroundColor||this._backgroundColor);var i=r.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var a=r.dom.width,n=r.dom.height;this.eachLayer(function(l){l.__builtin__?i.drawImage(l.dom,0,0,a,n):l.renderToCanvas&&(i.save(),l.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),f=0,h=s.length;f<h;f++){var u=s[f];lr(i,u,o,f===h-1)}return r.dom},e.prototype.getWidth=function(){return this._width},e.prototype.getHeight=function(){return this._height},e}();const dv=yl;var St=dr.CMD;function Fr(e,t){return Math.abs(e-t)<1e-5}function ya(e){var t=e.data,r=e.len(),i=[],a,n=0,o=0,s=0,f=0;function h(x,A){a&&a.length>2&&i.push(a),a=[x,A]}function u(x,A,E,D){Fr(x,E)&&Fr(A,D)||a.push(x,A,E,D,E,D)}function l(x,A,E,D,k,z){var Y=Math.abs(A-x),q=Math.tan(Y/4)*4/3,Z=A<x?-1:1,X=Math.cos(x),ot=Math.sin(x),st=Math.cos(A),Et=Math.sin(A),Gt=X*k+E,qt=ot*z+D,Ut=st*k+E,rt=Et*z+D,U=k*q*Z,F=z*q*Z;a.push(Gt-U*ot,qt+F*X,Ut+U*Et,rt-F*st,Ut,rt)}for(var v,c,_,g,d=0;d<r;){var p=t[d++],y=d===1;switch(y&&(n=t[d],o=t[d+1],s=n,f=o,(p===St.L||p===St.C||p===St.Q)&&(a=[s,f])),p){case St.M:n=s=t[d++],o=f=t[d++],h(s,f);break;case St.L:v=t[d++],c=t[d++],u(n,o,v,c),n=v,o=c;break;case St.C:a.push(t[d++],t[d++],t[d++],t[d++],n=t[d++],o=t[d++]);break;case St.Q:v=t[d++],c=t[d++],_=t[d++],g=t[d++],a.push(n+2/3*(v-n),o+2/3*(c-o),_+2/3*(v-_),g+2/3*(c-g),_,g),n=_,o=g;break;case St.A:var m=t[d++],T=t[d++],b=t[d++],w=t[d++],L=t[d++],C=t[d++]+L;d+=1;var M=!t[d++];v=Math.cos(L)*b+m,c=Math.sin(L)*w+T,y?(s=v,f=c,h(s,f)):u(n,o,v,c),n=Math.cos(C)*b+m,o=Math.sin(C)*w+T;for(var S=(M?-1:1)*Math.PI/2,P=L;M?P>C:P<C;P+=S){var R=M?Math.max(P+S,C):Math.min(P+S,C);l(P,R,m,T,b,w)}break;case St.R:s=n=t[d++],f=o=t[d++],v=s+t[d++],c=f+t[d++],h(v,f),u(v,f,v,c),u(v,c,s,c),u(s,c,s,f),u(s,f,v,f);break;case St.Z:a&&u(n,o,s,f),n=s,o=f;break}}return a&&a.length>2&&i.push(a),i}function ma(e,t,r,i,a,n,o,s,f,h){if(Fr(e,r)&&Fr(t,i)&&Fr(a,o)&&Fr(n,s)){f.push(o,s);return}var u=2/h,l=u*u,v=o-e,c=s-t,_=Math.sqrt(v*v+c*c);v/=_,c/=_;var g=r-e,d=i-t,p=a-o,y=n-s,m=g*g+d*d,T=p*p+y*y;if(m<l&&T<l){f.push(o,s);return}var b=v*g+c*d,w=-v*p-c*y,L=m-b*b,C=T-w*w;if(L<l&&b>=0&&C<l&&w>=0){f.push(o,s);return}var M=[],S=[];Xt(e,r,a,o,.5,M),Xt(t,i,n,s,.5,S),ma(M[0],S[0],M[1],S[1],M[2],S[2],M[3],S[3],f,h),ma(M[4],S[4],M[5],S[5],M[6],S[6],M[7],S[7],f,h)}function ml(e,t){var r=ya(e),i=[];t=t||1;for(var a=0;a<r.length;a++){var n=r[a],o=[],s=n[0],f=n[1];o.push(s,f);for(var h=2;h<n.length;){var u=n[h++],l=n[h++],v=n[h++],c=n[h++],_=n[h++],g=n[h++];ma(s,f,u,l,v,c,_,g,o,t),s=_,f=g}i.push(o)}return i}function ds(e,t,r){var i=e[t],a=e[1-t],n=Math.abs(i/a),o=Math.ceil(Math.sqrt(n*r)),s=Math.floor(r/o);s===0&&(s=1,o=r);for(var f=[],h=0;h<o;h++)f.push(s);var u=o*s,l=r-u;if(l>0)for(var h=0;h<l;h++)f[h%o]+=1;return f}function Kn(e,t,r){for(var i=e.r0,a=e.r,n=e.startAngle,o=e.endAngle,s=Math.abs(o-n),f=s*a,h=a-i,u=f>Math.abs(h),l=ds([f,h],u?0:1,t),v=(u?s:h)/l.length,c=0;c<l.length;c++)for(var _=(u?h:s)/l[c],g=0;g<l[c];g++){var d={};u?(d.startAngle=n+v*c,d.endAngle=n+v*(c+1),d.r0=i+_*g,d.r=i+_*(g+1)):(d.startAngle=n+_*g,d.endAngle=n+_*(g+1),d.r0=i+v*c,d.r=i+v*(c+1)),d.clockwise=e.clockwise,d.cx=e.cx,d.cy=e.cy,r.push(d)}}function wl(e,t,r){for(var i=e.width,a=e.height,n=i>a,o=ds([i,a],n?0:1,t),s=n?"width":"height",f=n?"height":"width",h=n?"x":"y",u=n?"y":"x",l=e[s]/o.length,v=0;v<o.length;v++)for(var c=e[f]/o[v],_=0;_<o[v];_++){var g={};g[h]=v*l,g[u]=_*c,g[s]=l,g[f]=c,g.x+=e.x,g.y+=e.y,r.push(g)}}function Jn(e,t,r,i){return e*i-r*t}function Tl(e,t,r,i,a,n,o,s){var f=r-e,h=i-t,u=o-a,l=s-n,v=Jn(u,l,f,h);if(Math.abs(v)<1e-6)return null;var c=e-a,_=t-n,g=Jn(c,_,u,l)/v;return g<0||g>1?null:new I(g*f+e,g*h+t)}function bl(e,t,r){var i=new I;I.sub(i,r,t),i.normalize();var a=new I;I.sub(a,e,t);var n=a.dot(i);return n}function br(e,t){var r=e[e.length-1];r&&r[0]===t[0]&&r[1]===t[1]||e.push(t)}function Cl(e,t,r){for(var i=e.length,a=[],n=0;n<i;n++){var o=e[n],s=e[(n+1)%i],f=Tl(o[0],o[1],s[0],s[1],t.x,t.y,r.x,r.y);f&&a.push({projPt:bl(f,t,r),pt:f,idx:n})}if(a.length<2)return[{points:e},{points:e}];a.sort(function(d,p){return d.projPt-p.projPt});var h=a[0],u=a[a.length-1];if(u.idx<h.idx){var l=h;h=u,u=l}for(var v=[h.pt.x,h.pt.y],c=[u.pt.x,u.pt.y],_=[v],g=[c],n=h.idx+1;n<=u.idx;n++)br(_,e[n].slice());br(_,c),br(_,v);for(var n=u.idx+1;n<=h.idx+i;n++)br(g,e[n%i].slice());return br(g,v),br(g,c),[{points:_},{points:g}]}function jn(e){var t=e.points,r=[],i=[];zo(t,r,i);var a=new W(r[0],r[1],i[0]-r[0],i[1]-r[1]),n=a.width,o=a.height,s=a.x,f=a.y,h=new I,u=new I;return n>o?(h.x=u.x=s+n/2,h.y=f,u.y=f+o):(h.y=u.y=f+o/2,h.x=s,u.x=s+n),Cl(t,h,u)}function ei(e,t,r,i){if(r===1)i.push(t);else{var a=Math.floor(r/2),n=e(t);ei(e,n[0],a,i),ei(e,n[1],r-a,i)}return i}function Ll(e,t){for(var r=[],i=0;i<t;i++)r.push(Qo(e));return r}function Ml(e,t){t.setStyle(e.style),t.z=e.z,t.z2=e.z2,t.zlevel=e.zlevel}function Sl(e){for(var t=[],r=0;r<e.length;)t.push([e[r++],e[r++]]);return t}function Pl(e,t){var r=[],i=e.shape,a;switch(e.type){case"rect":wl(i,t,r),a=Ke;break;case"sector":Kn(i,t,r),a=Rn;break;case"circle":Kn({r0:0,r:i.r,startAngle:0,endAngle:Math.PI*2,cx:i.cx,cy:i.cy},t,r),a=Rn;break;default:var n=e.getComputedTransform(),o=n?Math.sqrt(Math.max(n[0]*n[0]+n[1]*n[1],n[2]*n[2]+n[3]*n[3])):1,s=xt(ml(e.getUpdatedPathProxy(),o),function(p){return Sl(p)}),f=s.length;if(f===0)ei(jn,{points:s[0]},t,r);else if(f===t)for(var h=0;h<f;h++)r.push({points:s[h]});else{var u=0,l=xt(s,function(p){var y=[],m=[];zo(p,y,m);var T=(m[1]-y[1])*(m[0]-y[0]);return u+=T,{poly:p,area:T}});l.sort(function(p,y){return y.area-p.area});for(var v=t,h=0;h<f;h++){var c=l[h];if(v<=0)break;var _=h===f-1?v:Math.ceil(c.area/u*t);_<0||(ei(jn,{points:c.poly},_,r),v-=_)}}a=as;break}if(!a)return Ll(e,t);for(var g=[],h=0;h<r.length;h++){var d=new a;d.setShape(r[h]),Ml(e,d),g.push(d)}return g}function Rl(e,t){var r=e.length,i=t.length;if(r===i)return[e,t];for(var a=[],n=[],o=r<i?e:t,s=Math.min(r,i),f=Math.abs(i-r)/6,h=(s-2)/6,u=Math.ceil(f/h)+1,l=[o[0],o[1]],v=f,c=2;c<s;){var _=o[c-2],g=o[c-1],d=o[c++],p=o[c++],y=o[c++],m=o[c++],T=o[c++],b=o[c++];if(v<=0){l.push(d,p,y,m,T,b);continue}for(var w=Math.min(v,u-1)+1,L=1;L<=w;L++){var C=L/w;Xt(_,d,y,T,C,a),Xt(g,p,m,b,C,n),_=a[3],g=n[3],l.push(a[1],n[1],a[2],n[2],_,g),d=a[5],p=n[5],y=a[6],m=n[6]}v-=w-1}return o===e?[l,t]:[e,l]}function to(e,t){for(var r=e.length,i=e[r-2],a=e[r-1],n=[],o=0;o<t.length;)n[o++]=i,n[o++]=a;return n}function Dl(e,t){for(var r,i,a,n=[],o=[],s=0;s<Math.max(e.length,t.length);s++){var f=e[s],h=t[s],u=void 0,l=void 0;f?h?(r=Rl(f,h),u=r[0],l=r[1],i=u,a=l):(l=to(a||f,f),u=f):(u=to(i||h,h),l=h),n.push(u),o.push(l)}return[n,o]}function ro(e){for(var t=0,r=0,i=0,a=e.length,n=0,o=a-2;n<a;o=n,n+=2){var s=e[o],f=e[o+1],h=e[n],u=e[n+1],l=s*u-h*f;t+=l,r+=(s+h)*l,i+=(f+u)*l}return t===0?[e[0]||0,e[1]||0]:[r/t/3,i/t/3,t]}function Al(e,t,r,i){for(var a=(e.length-2)/6,n=1/0,o=0,s=e.length,f=s-2,h=0;h<a;h++){for(var u=h*6,l=0,v=0;v<s;v+=2){var c=v===0?u:(u+v-2)%f+2,_=e[c]-r[0],g=e[c+1]-r[1],d=t[v]-i[0],p=t[v+1]-i[1],y=d-_,m=p-g;l+=y*y+m*m}l<n&&(n=l,o=h)}return o}function xl(e){for(var t=[],r=e.length,i=0;i<r;i+=2)t[i]=e[r-i-2],t[i+1]=e[r-i-1];return t}function El(e,t,r,i){for(var a=[],n,o=0;o<e.length;o++){var s=e[o],f=t[o],h=ro(s),u=ro(f);n==null&&(n=h[2]<0!=u[2]<0);var l=[],v=[],c=0,_=1/0,g=[],d=s.length;n&&(s=xl(s));for(var p=Al(s,f,h,u)*6,y=d-2,m=0;m<y;m+=2){var T=(p+m)%y+2;l[m+2]=s[T]-h[0],l[m+3]=s[T+1]-h[1]}if(l[0]=s[p]-h[0],l[1]=s[p+1]-h[1],r>0)for(var b=i/r,w=-i/2;w<=i/2;w+=b){for(var L=Math.sin(w),C=Math.cos(w),M=0,m=0;m<s.length;m+=2){var S=l[m],P=l[m+1],R=f[m]-u[0],x=f[m+1]-u[1],A=R*C-x*L,E=R*L+x*C;g[m]=A,g[m+1]=E;var D=A-S,k=E-P;M+=D*D+k*k}if(M<_){_=M,c=w;for(var z=0;z<g.length;z++)v[z]=g[z]}}else for(var Y=0;Y<d;Y+=2)v[Y]=f[Y]-u[0],v[Y+1]=f[Y+1]-u[1];a.push({from:l,to:v,fromCp:h,toCp:u,rotation:-c})}return a}function wa(e){return e.__isCombineMorphing}var ps="__mOriginal_";function ii(e,t,r){var i=ps+t,a=e[i]||e[t];e[i]||(e[i]=e[t]);var n=r.replace,o=r.after,s=r.before;e[t]=function(){var f=arguments,h;return s&&s.apply(this,f),n?h=n.apply(this,f):h=a.apply(this,f),o&&o.apply(this,f),h}}function ae(e,t){var r=ps+t;e[r]&&(e[t]=e[r],e[r]=null)}function eo(e,t){for(var r=0;r<e.length;r++)for(var i=e[r],a=0;a<i.length;){var n=i[a],o=i[a+1];i[a++]=t[0]*n+t[2]*o+t[4],i[a++]=t[1]*n+t[3]*o+t[5]}}function _s(e,t){var r=e.getUpdatedPathProxy(),i=t.getUpdatedPathProxy(),a=Dl(ya(r),ya(i)),n=a[0],o=a[1],s=e.getComputedTransform(),f=t.getComputedTransform();function h(){this.transform=null}s&&eo(n,s),f&&eo(o,f),ii(t,"updateTransform",{replace:h}),t.transform=null;var u=El(n,o,10,Math.PI),l=[];ii(t,"buildPath",{replace:function(v){for(var c=t.__morphT,_=1-c,g=[],d=0;d<u.length;d++){var p=u[d],y=p.from,m=p.to,T=p.rotation*c,b=p.fromCp,w=p.toCp,L=Math.sin(T),C=Math.cos(T);Us(g,b,w,c);for(var M=0;M<y.length;M+=2){var S=y[M],P=y[M+1],R=m[M],x=m[M+1],A=S*_+R*c,E=P*_+x*c;l[M]=A*C-E*L+g[0],l[M+1]=A*L+E*C+g[1]}var D=l[0],k=l[1];v.moveTo(D,k);for(var M=2;M<y.length;){var R=l[M++],x=l[M++],z=l[M++],Y=l[M++],q=l[M++],Z=l[M++];D===R&&k===x&&z===q&&Y===Z?v.lineTo(q,Z):v.bezierCurveTo(R,x,z,Y,q,Z),D=q,k=Z}}}})}function gs(e,t,r){if(!e||!t)return t;var i=r.done,a=r.during;_s(e,t),t.__morphT=0;function n(){ae(t,"buildPath"),ae(t,"updateTransform"),t.__morphT=-1,t.createPathProxy(),t.dirtyShape()}return t.animateTo({__morphT:1},_t({during:function(o){t.dirtyShape(),a&&a(o)},done:function(){n(),i&&i()}},r)),t}function Fl(e,t,r,i,a,n){var o=16;e=a===r?0:Math.round(32767*(e-r)/(a-r)),t=n===i?0:Math.round(32767*(t-i)/(n-i));for(var s=0,f,h=(1<<o)/2;h>0;h/=2){var u=0,l=0;(e&h)>0&&(u=1),(t&h)>0&&(l=1),s+=h*h*(3*u^l),l===0&&(u===1&&(e=h-1-e,t=h-1-t),f=e,e=t,t=f)}return s}function ai(e){var t=1/0,r=1/0,i=-1/0,a=-1/0,n=xt(e,function(s){var f=s.getBoundingRect(),h=s.getComputedTransform(),u=f.x+f.width/2+(h?h[4]:0),l=f.y+f.height/2+(h?h[5]:0);return t=Math.min(u,t),r=Math.min(l,r),i=Math.max(u,i),a=Math.max(l,a),[u,l]}),o=xt(n,function(s,f){return{cp:s,z:Fl(s[0],s[1],t,r,i,a),path:e[f]}});return o.sort(function(s,f){return s.z-f.z}).map(function(s){return s.path})}function ys(e){return Pl(e.path,e.count)}function Ta(){return{fromIndividuals:[],toIndividuals:[],count:0}}function pv(e,t,r){var i=[];function a(b){for(var w=0;w<b.length;w++){var L=b[w];wa(L)?a(L.childrenRef()):L instanceof G&&i.push(L)}}a(e);var n=i.length;if(!n)return Ta();var o=r.dividePath||ys,s=o({path:t,count:n});if(s.length!==n)return console.error("Invalid morphing: unmatched splitted path"),Ta();i=ai(i),s=ai(s);for(var f=r.done,h=r.during,u=r.individualDelay,l=new Aa,v=0;v<n;v++){var c=i[v],_=s[v];_.parent=t,_.copyTransform(l),u||_s(c,_)}t.__isCombineMorphing=!0,t.childrenRef=function(){return s};function g(b){for(var w=0;w<s.length;w++)s[w].addSelfToZr(b)}ii(t,"addSelfToZr",{after:function(b){g(b)}}),ii(t,"removeSelfFromZr",{after:function(b){for(var w=0;w<s.length;w++)s[w].removeSelfFromZr(b)}});function d(){t.__isCombineMorphing=!1,t.__morphT=-1,t.childrenRef=null,ae(t,"addSelfToZr"),ae(t,"removeSelfFromZr")}var p=s.length;if(u)for(var y=p,m=function(){y--,y===0&&(d(),f&&f())},v=0;v<p;v++){var T=u?_t({delay:(r.delay||0)+u(v,p,i[v],s[v]),done:m},r):r;gs(i[v],s[v],T)}else t.__morphT=0,t.animateTo({__morphT:1},_t({during:function(b){for(var w=0;w<p;w++){var L=s[w];L.__morphT=t.__morphT,L.dirtyShape()}h&&h(b)},done:function(){d();for(var b=0;b<e.length;b++)ae(e[b],"updateTransform");f&&f()}},r));return t.__zr&&g(t.__zr),{fromIndividuals:i,toIndividuals:s,count:p}}function _v(e,t,r){var i=t.length,a=[],n=r.dividePath||ys;function o(c){for(var _=0;_<c.length;_++){var g=c[_];wa(g)?o(g.childrenRef()):g instanceof G&&a.push(g)}}if(wa(e)){o(e.childrenRef());var s=a.length;if(s<i)for(var f=0,h=s;h<i;h++)a.push(Qo(a[f++%s]));a.length=i}else{a=n({path:e,count:i});for(var u=e.getComputedTransform(),h=0;h<a.length;h++)a[h].setLocalTransform(u);if(a.length!==i)return console.error("Invalid morphing: unmatched splitted path"),Ta()}a=ai(a),t=ai(t);for(var l=r.individualDelay,h=0;h<i;h++){var v=l?_t({delay:(r.delay||0)+l(h,i,a[h],t[h])},r):r;gs(a[h],t[h],v)}return{fromIndividuals:a,toIndividuals:t,count:t.length}}export{hv as $,Qh as A,Kh as B,Er as C,Qf as D,ee as E,cu as F,pu as G,as as H,Mu as I,Ke as J,Ru as K,Ra as L,ov as M,sv as N,iv as O,G as P,Mr as Q,nv as R,Rn as S,Aa as T,ev as U,uv as V,fv as W,Iu as X,Hu as Y,Fa as Z,W as _,Pt as a,dv as a$,I as a0,Pr as a1,Sr as a2,Br as a3,oo as a4,Hs as a5,zl as a6,Fs as a7,Oa as a8,kl as a9,Kl as aA,Be as aB,Yl as aC,Do as aD,Bl as aE,Nl as aF,nf as aG,al as aH,cv as aI,cl as aJ,de as aK,Kf as aL,se as aM,Ul as aN,Ol as aO,Ql as aP,ce as aQ,Jl as aR,jl as aS,Dt as aT,la as aU,fe as aV,ze as aW,Vi as aX,ef as aY,yt as aZ,Zl as a_,Xl as aa,ql as ab,Ir as ac,ah as ad,lv as ae,le as af,rl as ag,$r as ah,tv as ai,gf as aj,so as ak,he as al,Os as am,rv as an,vv as ao,Rr as ap,Dr as aq,ih as ar,dr as as,Gs as at,Us as au,Cf as av,wf as aw,Pe as ax,wo as ay,N as az,Wl as b,wa as b0,gs as b1,pv as b2,_v as b3,Qo as b4,Gl as c,zt as d,J as e,He as f,$l as g,ne as h,Ge as i,tt as j,Hl as k,$ as l,xt as m,si as n,At as o,j as p,Vl as q,oi as r,Sa as s,pt as t,av as u,Jf as v,re as w,_t as x,qe as y,lu as z};
