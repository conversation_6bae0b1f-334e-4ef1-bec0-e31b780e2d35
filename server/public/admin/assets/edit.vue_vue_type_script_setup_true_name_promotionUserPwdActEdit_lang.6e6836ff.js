import{S as O,R as V,C as q,D as S}from"./element-plus.30e61100.js";import{u as g}from"./vue-router.3fc26f3d.js";import{P as L}from"./index.733ef1a1.js";import{r as i}from"./index.3c94cfbf.js";import{t as N}from"./user.8da48216.js";import"./lodash.ed1a684d.js";import{a as j}from"./useDictOptions.221eb135.js";import{d as b,s as f,r as I,e as T,a1 as w,o as c,c as v,X as u,P as l,u as E,F as X,a8 as z,O as G}from"./@vue.1d905da6.js";function te(t){return i.get({url:"/promotion.user_pwd_act/lists",params:t})}function H(t){return i.post({url:"/promotion.user_pwd_act/add",params:t})}function se(t){return i.post({url:"/promotion.user_pwd_act/counts",params:t})}const J={class:"edit-popup"},K=b({name:"promotionUserPwdActEdit"}),ae=b({...K,props:{dictData:{type:Object,default:()=>({})}},emits:["success","close"],setup(t,{expose:y,emit:p}){const m=f(),n=f(),d=I("add"),{optionsData:P}=j({user:{api:N}}),D=T(()=>"\u65B0\u589E\u7528\u6237\u53E3\u4EE4\u6D3B\u52A8"),_=g(),s=w({act_id:_.query.act_id?Number(_.query.act_id):0,user_id:[]}),R=w({}),h=async o=>{for(const e in s)o[e]!=null&&o[e]!=null&&(s[e]=o[e])},k=async()=>{var e,a;await((e=m.value)==null?void 0:e.validate());const o={...s};d.value==await H(o),(a=n.value)==null||a.close(),p("success")},A=(o="add")=>{var e;d.value=o,(e=n.value)==null||e.open()},C=()=>{p("close")};return y({open:A,setFormData:h}),(o,e)=>{const a=O,x=V,B=q,F=S;return c(),v("div",J,[u(L,{ref_key:"popupRef",ref:n,title:E(D),async:!0,width:"550px",onConfirm:k,onClose:C},{default:l(()=>[u(F,{ref_key:"formRef",ref:m,model:s,"label-width":"90px",rules:R},{default:l(()=>[u(B,{label:"\u8D26\u6237",prop:"user_id"},{default:l(()=>[u(x,{modelValue:s.user_id,"onUpdate:modelValue":e[0]||(e[0]=r=>s.user_id=r),class:"flex-1",multiple:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8D26\u6237",clearable:""},{default:l(()=>[(c(!0),v(X,null,z(E(P).user,(r,U)=>(c(),G(a,{key:U,label:r.name,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title"])])}}});export{ae as _,se as a,te as b};
