import{a as Ue}from"./axios.5636fa37.js";/**!
 * Sortable 1.14.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function De(o,t){var e=Object.keys(o);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(o);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(o,i).enumerable})),e.push.apply(e,n)}return e}function J(o){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?De(Object(e),!0).forEach(function(n){qe(o,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(e)):De(Object(e)).forEach(function(n){Object.defineProperty(o,n,Object.getOwnPropertyDescriptor(e,n))})}return o}function zt(o){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?zt=function(t){return typeof t}:zt=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zt(o)}function qe(o,t,e){return t in o?Object.defineProperty(o,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):o[t]=e,o}function z(){return z=Object.assign||function(o){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(o[n]=e[n])}return o},z.apply(this,arguments)}function $e(o,t){if(o==null)return{};var e={},n=Object.keys(o),i,r;for(r=0;r<n.length;r++)i=n[r],!(t.indexOf(i)>=0)&&(e[i]=o[i]);return e}function Ve(o,t){if(o==null)return{};var e=$e(o,t),n,i;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(o);for(i=0;i<r.length;i++)n=r[i],!(t.indexOf(n)>=0)&&(!Object.prototype.propertyIsEnumerable.call(o,n)||(e[n]=o[n]))}return e}function Ze(o){return Qe(o)||Je(o)||tn(o)||en()}function Qe(o){if(Array.isArray(o))return he(o)}function Je(o){if(typeof Symbol<"u"&&o[Symbol.iterator]!=null||o["@@iterator"]!=null)return Array.from(o)}function tn(o,t){if(!!o){if(typeof o=="string")return he(o,t);var e=Object.prototype.toString.call(o).slice(8,-1);if(e==="Object"&&o.constructor&&(e=o.constructor.name),e==="Map"||e==="Set")return Array.from(o);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return he(o,t)}}function he(o,t){(t==null||t>o.length)&&(t=o.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=o[e];return n}function en(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var nn="1.14.0";function et(o){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(o)}var nt=et(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Xt=et(/Edge/i),_e=et(/firefox/i),Mt=et(/safari/i)&&!et(/chrome/i)&&!et(/android/i),Fe=et(/iP(ad|od|hone)/i),on=et(/chrome/i)&&et(/android/i),Re={capture:!1,passive:!1};function y(o,t,e){o.addEventListener(t,e,!nt&&Re)}function w(o,t,e){o.removeEventListener(t,e,!nt&&Re)}function Zt(o,t){if(!!t){if(t[0]===">"&&(t=t.substring(1)),o)try{if(o.matches)return o.matches(t);if(o.msMatchesSelector)return o.msMatchesSelector(t);if(o.webkitMatchesSelector)return o.webkitMatchesSelector(t)}catch{return!1}return!1}}function rn(o){return o.host&&o!==document&&o.host.nodeType?o.host:o.parentNode}function $(o,t,e,n){if(o){e=e||document;do{if(t!=null&&(t[0]===">"?o.parentNode===e&&Zt(o,t):Zt(o,t))||n&&o===e)return o;if(o===e)break}while(o=rn(o))}return null}var Ce=/\s+/g;function T(o,t,e){if(o&&t)if(o.classList)o.classList[e?"add":"remove"](t);else{var n=(" "+o.className+" ").replace(Ce," ").replace(" "+t+" "," ");o.className=(n+(e?" "+t:"")).replace(Ce," ")}}function h(o,t,e){var n=o&&o.style;if(n){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(o,""):o.currentStyle&&(e=o.currentStyle),t===void 0?e:e[t];!(t in n)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),n[t]=e+(typeof e=="string"?"":"px")}}function ht(o,t){var e="";if(typeof o=="string")e=o;else do{var n=h(o,"transform");n&&n!=="none"&&(e=n+" "+e)}while(!t&&(o=o.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(e)}function ke(o,t,e){if(o){var n=o.getElementsByTagName(t),i=0,r=n.length;if(e)for(;i<r;i++)e(n[i],i);return n}return[]}function Q(){var o=document.scrollingElement;return o||document.documentElement}function O(o,t,e,n,i){if(!(!o.getBoundingClientRect&&o!==window)){var r,a,l,s,u,d,f;if(o!==window&&o.parentNode&&o!==Q()?(r=o.getBoundingClientRect(),a=r.top,l=r.left,s=r.bottom,u=r.right,d=r.height,f=r.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,d=window.innerHeight,f=window.innerWidth),(t||e)&&o!==window&&(i=i||o.parentNode,!nt))do if(i&&i.getBoundingClientRect&&(h(i,"transform")!=="none"||e&&h(i,"position")!=="static")){var p=i.getBoundingClientRect();a-=p.top+parseInt(h(i,"border-top-width")),l-=p.left+parseInt(h(i,"border-left-width")),s=a+r.height,u=l+r.width;break}while(i=i.parentNode);if(n&&o!==window){var E=ht(i||o),b=E&&E.a,S=E&&E.d;E&&(a/=S,l/=b,f/=b,d/=S,s=a+d,u=l+f)}return{top:a,left:l,bottom:s,right:u,width:f,height:d}}}function Oe(o,t,e){for(var n=lt(o,!0),i=O(o)[t];n;){var r=O(n)[e],a=void 0;if(e==="top"||e==="left"?a=i>=r:a=i<=r,!a)return n;if(n===Q())break;n=lt(n,!1)}return!1}function wt(o,t,e,n){for(var i=0,r=0,a=o.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==g.ghost&&(n||a[r]!==g.dragged)&&$(a[r],e.draggable,o,!1)){if(i===t)return a[r];i++}r++}return null}function be(o,t){for(var e=o.lastElementChild;e&&(e===g.ghost||h(e,"display")==="none"||t&&!Zt(e,t));)e=e.previousElementSibling;return e||null}function N(o,t){var e=0;if(!o||!o.parentNode)return-1;for(;o=o.previousElementSibling;)o.nodeName.toUpperCase()!=="TEMPLATE"&&o!==g.clone&&(!t||Zt(o,t))&&e++;return e}function Te(o){var t=0,e=0,n=Q();if(o)do{var i=ht(o),r=i.a,a=i.d;t+=o.scrollLeft*r,e+=o.scrollTop*a}while(o!==n&&(o=o.parentNode));return[t,e]}function an(o,t){for(var e in o)if(!!o.hasOwnProperty(e)){for(var n in t)if(t.hasOwnProperty(n)&&t[n]===o[e][n])return Number(e)}return-1}function lt(o,t){if(!o||!o.getBoundingClientRect)return Q();var e=o,n=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var i=h(e);if(e.clientWidth<e.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return Q();if(n||t)return e;n=!0}}while(e=e.parentNode);return Q()}function ln(o,t){if(o&&t)for(var e in t)t.hasOwnProperty(e)&&(o[e]=t[e]);return o}function oe(o,t){return Math.round(o.top)===Math.round(t.top)&&Math.round(o.left)===Math.round(t.left)&&Math.round(o.height)===Math.round(t.height)&&Math.round(o.width)===Math.round(t.width)}var xt;function Xe(o,t){return function(){if(!xt){var e=arguments,n=this;e.length===1?o.call(n,e[0]):o.apply(n,e),xt=setTimeout(function(){xt=void 0},t)}}}function sn(){clearTimeout(xt),xt=void 0}function Ye(o,t,e){o.scrollLeft+=t,o.scrollTop+=e}function we(o){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(o).cloneNode(!0):e?e(o).clone(!0)[0]:o.cloneNode(!0)}function Ae(o,t){h(o,"position","absolute"),h(o,"top",t.top),h(o,"left",t.left),h(o,"width",t.width),h(o,"height",t.height)}function ie(o){h(o,"position",""),h(o,"top",""),h(o,"left",""),h(o,"width",""),h(o,"height","")}var k="Sortable"+new Date().getTime();function un(){var o=[],t;return{captureAnimationState:function(){if(o=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(i){if(!(h(i,"display")==="none"||i===g.ghost)){o.push({target:i,rect:O(i)});var r=J({},o[o.length-1].rect);if(i.thisAnimationDuration){var a=ht(i,!0);a&&(r.top-=a.f,r.left-=a.e)}i.fromRect=r}})}},addAnimationState:function(n){o.push(n)},removeAnimationState:function(n){o.splice(an(o,{target:n}),1)},animateAll:function(n){var i=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var r=!1,a=0;o.forEach(function(l){var s=0,u=l.target,d=u.fromRect,f=O(u),p=u.prevFromRect,E=u.prevToRect,b=l.rect,S=ht(u,!0);S&&(f.top-=S.f,f.left-=S.e),u.toRect=f,u.thisAnimationDuration&&oe(p,f)&&!oe(d,f)&&(b.top-f.top)/(b.left-f.left)===(d.top-f.top)/(d.left-f.left)&&(s=fn(b,p,E,i.options)),oe(f,d)||(u.prevFromRect=d,u.prevToRect=f,s||(s=i.options.animation),i.animate(u,b,f,s)),s&&(r=!0,a=Math.max(a,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(t),r?t=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),o=[]},animate:function(n,i,r,a){if(a){h(n,"transition",""),h(n,"transform","");var l=ht(this.el),s=l&&l.a,u=l&&l.d,d=(i.left-r.left)/(s||1),f=(i.top-r.top)/(u||1);n.animatingX=!!d,n.animatingY=!!f,h(n,"transform","translate3d("+d+"px,"+f+"px,0)"),this.forRepaintDummy=cn(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function cn(o){return o.offsetWidth}function fn(o,t,e,n){return Math.sqrt(Math.pow(t.top-o.top,2)+Math.pow(t.left-o.left,2))/Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))*n.animation}var pt=[],re={initializeByDefault:!0},Yt={mount:function(t){for(var e in re)re.hasOwnProperty(e)&&!(e in t)&&(t[e]=re[e]);pt.forEach(function(n){if(n.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),pt.push(t)},pluginEvent:function(t,e,n){var i=this;this.eventCanceled=!1,n.cancel=function(){i.eventCanceled=!0};var r=t+"Global";pt.forEach(function(a){!e[a.pluginName]||(e[a.pluginName][r]&&e[a.pluginName][r](J({sortable:e},n)),e.options[a.pluginName]&&e[a.pluginName][t]&&e[a.pluginName][t](J({sortable:e},n)))})},initializePlugins:function(t,e,n,i){pt.forEach(function(l){var s=l.pluginName;if(!(!t.options[s]&&!l.initializeByDefault)){var u=new l(t,e,t.options);u.sortable=t,u.options=t.options,t[s]=u,z(n,u.defaults)}});for(var r in t.options)if(!!t.options.hasOwnProperty(r)){var a=this.modifyOption(t,r,t.options[r]);typeof a<"u"&&(t.options[r]=a)}},getEventProperties:function(t,e){var n={};return pt.forEach(function(i){typeof i.eventProperties=="function"&&z(n,i.eventProperties.call(e[i.pluginName],t))}),n},modifyOption:function(t,e,n){var i;return pt.forEach(function(r){!t[r.pluginName]||r.optionListeners&&typeof r.optionListeners[e]=="function"&&(i=r.optionListeners[e].call(t[r.pluginName],n))}),i}};function At(o){var t=o.sortable,e=o.rootEl,n=o.name,i=o.targetEl,r=o.cloneEl,a=o.toEl,l=o.fromEl,s=o.oldIndex,u=o.newIndex,d=o.oldDraggableIndex,f=o.newDraggableIndex,p=o.originalEvent,E=o.putSortable,b=o.extraEventProperties;if(t=t||e&&e[k],!!t){var S,P=t.options,K="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!nt&&!Xt?S=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(S=document.createEvent("Event"),S.initEvent(n,!0,!0)),S.to=a||e,S.from=l||e,S.item=i||e,S.clone=r,S.oldIndex=s,S.newIndex=u,S.oldDraggableIndex=d,S.newDraggableIndex=f,S.originalEvent=p,S.pullMode=E?E.lastPutMode:void 0;var x=J(J({},b),Yt.getEventProperties(n,t));for(var Y in x)S[Y]=x[Y];e&&e.dispatchEvent(S),P[K]&&P[K].call(t,S)}}var dn=["evt"],B=function(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=n.evt,r=Ve(n,dn);Yt.pluginEvent.bind(g)(t,e,J({dragEl:c,parentEl:A,ghostEl:v,rootEl:C,nextEl:dt,lastDownEl:Ut,cloneEl:I,cloneHidden:at,dragStarted:It,putSortable:F,activeSortable:g.active,originalEvent:i,oldIndex:bt,oldDraggableIndex:Ft,newIndex:W,newDraggableIndex:rt,hideGhostForTarget:Le,unhideGhostForTarget:We,cloneNowHidden:function(){at=!0},cloneNowShown:function(){at=!1},dispatchSortableEvent:function(l){X({sortable:e,name:l,originalEvent:i})}},r))};function X(o){At(J({putSortable:F,cloneEl:I,targetEl:c,rootEl:C,oldIndex:bt,oldDraggableIndex:Ft,newIndex:W,newDraggableIndex:rt},o))}var c,A,v,C,dt,Ut,I,at,bt,W,Ft,rt,Ht,F,vt=!1,Qt=!1,Jt=[],ct,U,ae,le,Ie,Ne,It,gt,Rt,kt=!1,Gt=!1,qt,R,se=[],pe=!1,te=[],ne=typeof document<"u",Lt=Fe,Pe=Xt||nt?"cssFloat":"float",hn=ne&&!on&&!Fe&&"draggable"in document.createElement("div"),Be=function(){if(!!ne){if(nt)return!1;var o=document.createElement("x");return o.style.cssText="pointer-events:auto",o.style.pointerEvents==="auto"}}(),He=function(t,e){var n=h(t),i=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=wt(t,0,e),a=wt(t,1,e),l=r&&h(r),s=a&&h(a),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+O(r).width,d=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+O(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&l.float&&l.float!=="none"){var f=l.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===f)?"vertical":"horizontal"}return r&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=i&&n[Pe]==="none"||a&&n[Pe]==="none"&&u+d>i)?"vertical":"horizontal"},pn=function(t,e,n){var i=n?t.left:t.top,r=n?t.right:t.bottom,a=n?t.width:t.height,l=n?e.left:e.top,s=n?e.right:e.bottom,u=n?e.width:e.height;return i===l||r===s||i+a/2===l+u/2},gn=function(t,e){var n;return Jt.some(function(i){var r=i[k].options.emptyInsertThreshold;if(!(!r||be(i))){var a=O(i),l=t>=a.left-r&&t<=a.right+r,s=e>=a.top-r&&e<=a.bottom+r;if(l&&s)return n=i}}),n},Ge=function(t){function e(r,a){return function(l,s,u,d){var f=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(r==null&&(a||f))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return e(r(l,s,u,d),a)(l,s,u,d);var p=(a?l:s).options.group.name;return r===!0||typeof r=="string"&&r===p||r.join&&r.indexOf(p)>-1}}var n={},i=t.group;(!i||zt(i)!="object")&&(i={name:i}),n.name=i.name,n.checkPull=e(i.pull,!0),n.checkPut=e(i.put),n.revertClone=i.revertClone,t.group=n},Le=function(){!Be&&v&&h(v,"display","none")},We=function(){!Be&&v&&h(v,"display","")};ne&&document.addEventListener("click",function(o){if(Qt)return o.preventDefault(),o.stopPropagation&&o.stopPropagation(),o.stopImmediatePropagation&&o.stopImmediatePropagation(),Qt=!1,!1},!0);var ft=function(t){if(c){t=t.touches?t.touches[0]:t;var e=gn(t.clientX,t.clientY);if(e){var n={};for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[k]._onDragOver(n)}}},mn=function(t){c&&c.parentNode[k]._isOutsideThisEl(t.target)};function g(o,t){if(!(o&&o.nodeType&&o.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(o));this.el=o,this.options=t=z({},t),o[k]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(o.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return He(o,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,l){a.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&!Mt,emptyInsertThreshold:5};Yt.initializePlugins(this,o,e);for(var n in e)!(n in t)&&(t[n]=e[n]);Ge(t);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=t.forceFallback?!1:hn,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?y(o,"pointerdown",this._onTapStart):(y(o,"mousedown",this._onTapStart),y(o,"touchstart",this._onTapStart)),this.nativeDraggable&&(y(o,"dragover",this),y(o,"dragenter",this)),Jt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),z(this,un())}g.prototype={constructor:g,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(gt=null)},_getDirection:function(t,e){return typeof this.options.direction=="function"?this.options.direction.call(this,t,e,c):this.options.direction},_onTapStart:function(t){if(!!t.cancelable){var e=this,n=this.el,i=this.options,r=i.preventOnFilter,a=t.type,l=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,s=(l||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||s,d=i.filter;if(_n(n),!c&&!(/mousedown|pointerdown/.test(a)&&t.button!==0||i.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Mt&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=$(s,i.draggable,n,!1),!(s&&s.animated)&&Ut!==s)){if(bt=N(s),Ft=N(s,i.draggable),typeof d=="function"){if(d.call(this,t,s,this)){X({sortable:e,rootEl:u,name:"filter",targetEl:s,toEl:n,fromEl:n}),B("filter",e,{evt:t}),r&&t.cancelable&&t.preventDefault();return}}else if(d&&(d=d.split(",").some(function(f){if(f=$(u,f.trim(),n,!1),f)return X({sortable:e,rootEl:f,name:"filter",targetEl:s,fromEl:n,toEl:n}),B("filter",e,{evt:t}),!0}),d)){r&&t.cancelable&&t.preventDefault();return}i.handle&&!$(u,i.handle,n,!1)||this._prepareDragStart(t,l,s)}}},_prepareDragStart:function(t,e,n){var i=this,r=i.el,a=i.options,l=r.ownerDocument,s;if(n&&!c&&n.parentNode===r){var u=O(n);if(C=r,c=n,A=c.parentNode,dt=c.nextSibling,Ut=n,Ht=a.group,g.dragged=c,ct={target:c,clientX:(e||t).clientX,clientY:(e||t).clientY},Ie=ct.clientX-u.left,Ne=ct.clientY-u.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,c.style["will-change"]="all",s=function(){if(B("delayEnded",i,{evt:t}),g.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!_e&&i.nativeDraggable&&(c.draggable=!0),i._triggerDragStart(t,e),X({sortable:i,name:"choose",originalEvent:t}),T(c,a.chosenClass,!0)},a.ignore.split(",").forEach(function(d){ke(c,d.trim(),ue)}),y(l,"dragover",ft),y(l,"mousemove",ft),y(l,"touchmove",ft),y(l,"mouseup",i._onDrop),y(l,"touchend",i._onDrop),y(l,"touchcancel",i._onDrop),_e&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),B("delayStart",this,{evt:t}),a.delay&&(!a.delayOnTouchOnly||e)&&(!this.nativeDraggable||!(Xt||nt))){if(g.eventCanceled){this._onDrop();return}y(l,"mouseup",i._disableDelayedDrag),y(l,"touchend",i._disableDelayedDrag),y(l,"touchcancel",i._disableDelayedDrag),y(l,"mousemove",i._delayedDragTouchMoveHandler),y(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&y(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&ue(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;w(t,"mouseup",this._disableDelayedDrag),w(t,"touchend",this._disableDelayedDrag),w(t,"touchcancel",this._disableDelayedDrag),w(t,"mousemove",this._delayedDragTouchMoveHandler),w(t,"touchmove",this._delayedDragTouchMoveHandler),w(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||t.pointerType=="touch"&&t,!this.nativeDraggable||e?this.options.supportPointer?y(document,"pointermove",this._onTouchMove):e?y(document,"touchmove",this._onTouchMove):y(document,"mousemove",this._onTouchMove):(y(c,"dragend",this),y(C,"dragstart",this._onDragStart));try{document.selection?$t(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(t,e){if(vt=!1,C&&c){B("dragStarted",this,{evt:e}),this.nativeDraggable&&y(document,"dragover",mn);var n=this.options;!t&&T(c,n.dragClass,!1),T(c,n.ghostClass,!0),g.active=this,t&&this._appendGhost(),X({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(U){this._lastX=U.clientX,this._lastY=U.clientY,Le();for(var t=document.elementFromPoint(U.clientX,U.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(U.clientX,U.clientY),t!==e);)e=t;if(c.parentNode[k]._isOutsideThisEl(t),e)do{if(e[k]){var n=void 0;if(n=e[k]._onDragOver({clientX:U.clientX,clientY:U.clientY,target:t,rootEl:e}),n&&!this.options.dragoverBubble)break}t=e}while(e=e.parentNode);We()}},_onTouchMove:function(t){if(ct){var e=this.options,n=e.fallbackTolerance,i=e.fallbackOffset,r=t.touches?t.touches[0]:t,a=v&&ht(v,!0),l=v&&a&&a.a,s=v&&a&&a.d,u=Lt&&R&&Te(R),d=(r.clientX-ct.clientX+i.x)/(l||1)+(u?u[0]-se[0]:0)/(l||1),f=(r.clientY-ct.clientY+i.y)/(s||1)+(u?u[1]-se[1]:0)/(s||1);if(!g.active&&!vt){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(v){a?(a.e+=d-(ae||0),a.f+=f-(le||0)):a={a:1,b:0,c:0,d:1,e:d,f};var p="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(v,"webkitTransform",p),h(v,"mozTransform",p),h(v,"msTransform",p),h(v,"transform",p),ae=d,le=f,U=r}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!v){var t=this.options.fallbackOnBody?document.body:C,e=O(c,!0,Lt,!0,t),n=this.options;if(Lt){for(R=t;h(R,"position")==="static"&&h(R,"transform")==="none"&&R!==document;)R=R.parentNode;R!==document.body&&R!==document.documentElement?(R===document&&(R=Q()),e.top+=R.scrollTop,e.left+=R.scrollLeft):R=Q(),se=Te(R)}v=c.cloneNode(!0),T(v,n.ghostClass,!1),T(v,n.fallbackClass,!0),T(v,n.dragClass,!0),h(v,"transition",""),h(v,"transform",""),h(v,"box-sizing","border-box"),h(v,"margin",0),h(v,"top",e.top),h(v,"left",e.left),h(v,"width",e.width),h(v,"height",e.height),h(v,"opacity","0.8"),h(v,"position",Lt?"absolute":"fixed"),h(v,"zIndex","100000"),h(v,"pointerEvents","none"),g.ghost=v,t.appendChild(v),h(v,"transform-origin",Ie/parseInt(v.style.width)*100+"% "+Ne/parseInt(v.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,i=t.dataTransfer,r=n.options;if(B("dragStart",this,{evt:t}),g.eventCanceled){this._onDrop();return}B("setupClone",this),g.eventCanceled||(I=we(c),I.draggable=!1,I.style["will-change"]="",this._hideClone(),T(I,this.options.chosenClass,!1),g.clone=I),n.cloneId=$t(function(){B("clone",n),!g.eventCanceled&&(n.options.removeCloneOnHide||C.insertBefore(I,c),n._hideClone(),X({sortable:n,name:"clone"}))}),!e&&T(c,r.dragClass,!0),e?(Qt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(w(document,"mouseup",n._onDrop),w(document,"touchend",n._onDrop),w(document,"touchcancel",n._onDrop),i&&(i.effectAllowed="move",r.setData&&r.setData.call(n,i,c)),y(document,"drop",n),h(c,"transform","translateZ(0)")),vt=!0,n._dragStartId=$t(n._dragStarted.bind(n,e,t)),y(document,"selectstart",n),It=!0,Mt&&h(document.body,"user-select","none")},_onDragOver:function(t){var e=this.el,n=t.target,i,r,a,l=this.options,s=l.group,u=g.active,d=Ht===s,f=l.sort,p=F||u,E,b=this,S=!1;if(pe)return;function P(_t,je){B(_t,b,J({evt:t,isOwner:d,axis:E?"vertical":"horizontal",revert:a,dragRect:i,targetRect:r,canSort:f,fromSortable:p,target:n,completed:x,onMove:function(Se,ze){return Wt(C,e,c,i,Se,O(Se),t,ze)},changed:Y},je))}function K(){P("dragOverAnimationCapture"),b.captureAnimationState(),b!==p&&p.captureAnimationState()}function x(_t){return P("dragOverCompleted",{insertion:_t}),_t&&(d?u._hideClone():u._showClone(b),b!==p&&(T(c,F?F.options.ghostClass:u.options.ghostClass,!1),T(c,l.ghostClass,!0)),F!==b&&b!==g.active?F=b:b===g.active&&F&&(F=null),p===b&&(b._ignoreWhileAnimating=n),b.animateAll(function(){P("dragOverAnimationComplete"),b._ignoreWhileAnimating=null}),b!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(n===c&&!c.animated||n===e&&!n.animated)&&(gt=null),!l.dragoverBubble&&!t.rootEl&&n!==document&&(c.parentNode[k]._isOutsideThisEl(t.target),!_t&&ft(t)),!l.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),S=!0}function Y(){W=N(c),rt=N(c,l.draggable),X({sortable:b,name:"change",toEl:e,newIndex:W,newDraggableIndex:rt,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),n=$(n,l.draggable,e,!0),P("dragOver"),g.eventCanceled)return S;if(c.contains(t.target)||n.animated&&n.animatingX&&n.animatingY||b._ignoreWhileAnimating===n)return x(!1);if(Qt=!1,u&&!l.disabled&&(d?f||(a=A!==C):F===this||(this.lastPutMode=Ht.checkPull(this,u,c,t))&&s.checkPut(this,u,c,t))){if(E=this._getDirection(t,n)==="vertical",i=O(c),P("dragOverValid"),g.eventCanceled)return S;if(a)return A=C,K(),this._hideClone(),P("revert"),g.eventCanceled||(dt?C.insertBefore(c,dt):C.appendChild(c)),x(!0);var D=be(e,l.draggable);if(!D||yn(t,E,this)&&!D.animated){if(D===c)return x(!1);if(D&&e===t.target&&(n=D),n&&(r=O(n)),Wt(C,e,c,i,n,r,t,!!n)!==!1)return K(),e.appendChild(c),A=e,Y(),x(!0)}else if(D&&wn(t,E,this)){var V=wt(e,0,l,!0);if(V===c)return x(!1);if(n=V,r=O(n),Wt(C,e,c,i,n,r,t,!1)!==!1)return K(),e.insertBefore(c,V),A=e,Y(),x(!0)}else if(n.parentNode===e){r=O(n);var Z=0,st,yt=c.parentNode!==e,G=!pn(c.animated&&c.toRect||i,n.animated&&n.toRect||r,E),Et=E?"top":"left",ot=Oe(n,"top","top")||Oe(c,"top","top"),St=ot?ot.scrollTop:void 0;gt!==n&&(st=r[Et],kt=!1,Gt=!G&&l.invertSwap||yt),Z=En(t,n,r,E,G?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Gt,gt===n);var tt;if(Z!==0){var ut=N(c);do ut-=Z,tt=A.children[ut];while(tt&&(h(tt,"display")==="none"||tt===v))}if(Z===0||tt===n)return x(!1);gt=n,Rt=Z;var Dt=n.nextElementSibling,it=!1;it=Z===1;var Bt=Wt(C,e,c,i,n,r,t,it);if(Bt!==!1)return(Bt===1||Bt===-1)&&(it=Bt===1),pe=!0,setTimeout(bn,30),K(),it&&!Dt?e.appendChild(c):n.parentNode.insertBefore(c,it?Dt:n),ot&&Ye(ot,0,St-ot.scrollTop),A=c.parentNode,st!==void 0&&!Gt&&(qt=Math.abs(st-O(n)[Et])),Y(),x(!0)}if(e.contains(c))return x(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){w(document,"mousemove",this._onTouchMove),w(document,"touchmove",this._onTouchMove),w(document,"pointermove",this._onTouchMove),w(document,"dragover",ft),w(document,"mousemove",ft),w(document,"touchmove",ft)},_offUpEvents:function(){var t=this.el.ownerDocument;w(t,"mouseup",this._onDrop),w(t,"touchend",this._onDrop),w(t,"pointerup",this._onDrop),w(t,"touchcancel",this._onDrop),w(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;if(W=N(c),rt=N(c,n.draggable),B("drop",this,{evt:t}),A=c&&c.parentNode,W=N(c),rt=N(c,n.draggable),g.eventCanceled){this._nulling();return}vt=!1,Gt=!1,kt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ge(this.cloneId),ge(this._dragStartId),this.nativeDraggable&&(w(document,"drop",this),w(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Mt&&h(document.body,"user-select",""),h(c,"transform",""),t&&(It&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),v&&v.parentNode&&v.parentNode.removeChild(v),(C===A||F&&F.lastPutMode!=="clone")&&I&&I.parentNode&&I.parentNode.removeChild(I),c&&(this.nativeDraggable&&w(c,"dragend",this),ue(c),c.style["will-change"]="",It&&!vt&&T(c,F?F.options.ghostClass:this.options.ghostClass,!1),T(c,this.options.chosenClass,!1),X({sortable:this,name:"unchoose",toEl:A,newIndex:null,newDraggableIndex:null,originalEvent:t}),C!==A?(W>=0&&(X({rootEl:A,name:"add",toEl:A,fromEl:C,originalEvent:t}),X({sortable:this,name:"remove",toEl:A,originalEvent:t}),X({rootEl:A,name:"sort",toEl:A,fromEl:C,originalEvent:t}),X({sortable:this,name:"sort",toEl:A,originalEvent:t})),F&&F.save()):W!==bt&&W>=0&&(X({sortable:this,name:"update",toEl:A,originalEvent:t}),X({sortable:this,name:"sort",toEl:A,originalEvent:t})),g.active&&((W==null||W===-1)&&(W=bt,rt=Ft),X({sortable:this,name:"end",toEl:A,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){B("nulling",this),C=c=A=v=dt=I=Ut=at=ct=U=It=W=rt=bt=Ft=gt=Rt=F=Ht=g.dragged=g.ghost=g.clone=g.active=null,te.forEach(function(t){t.checked=!0}),te.length=ae=le=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":c&&(this._onDragOver(t),vn(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],e,n=this.el.children,i=0,r=n.length,a=this.options;i<r;i++)e=n[i],$(e,a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||Dn(e));return t},sort:function(t,e){var n={},i=this.el;this.toArray().forEach(function(r,a){var l=i.children[a];$(l,this.options.draggable,i,!1)&&(n[r]=l)},this),e&&this.captureAnimationState(),t.forEach(function(r){n[r]&&(i.removeChild(n[r]),i.appendChild(n[r]))}),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return $(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(e===void 0)return n[t];var i=Yt.modifyOption(this,t,e);typeof i<"u"?n[t]=i:n[t]=e,t==="group"&&Ge(n)},destroy:function(){B("destroy",this);var t=this.el;t[k]=null,w(t,"mousedown",this._onTapStart),w(t,"touchstart",this._onTapStart),w(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(w(t,"dragover",this),w(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Jt.splice(Jt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!at){if(B("hideClone",this),g.eventCanceled)return;h(I,"display","none"),this.options.removeCloneOnHide&&I.parentNode&&I.parentNode.removeChild(I),at=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(at){if(B("showClone",this),g.eventCanceled)return;c.parentNode==C&&!this.options.group.revertClone?C.insertBefore(I,c):dt?C.insertBefore(I,dt):C.appendChild(I),this.options.group.revertClone&&this.animate(c,I),h(I,"display",""),at=!1}}};function vn(o){o.dataTransfer&&(o.dataTransfer.dropEffect="move"),o.cancelable&&o.preventDefault()}function Wt(o,t,e,n,i,r,a,l){var s,u=o[k],d=u.options.onMove,f;return window.CustomEvent&&!nt&&!Xt?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=t,s.from=o,s.dragged=e,s.draggedRect=n,s.related=i||t,s.relatedRect=r||O(t),s.willInsertAfter=l,s.originalEvent=a,o.dispatchEvent(s),d&&(f=d.call(u,s,a)),f}function ue(o){o.draggable=!1}function bn(){pe=!1}function wn(o,t,e){var n=O(wt(e.el,0,e.options,!0)),i=10;return t?o.clientX<n.left-i||o.clientY<n.top&&o.clientX<n.right:o.clientY<n.top-i||o.clientY<n.bottom&&o.clientX<n.left}function yn(o,t,e){var n=O(be(e.el,e.options.draggable)),i=10;return t?o.clientX>n.right+i||o.clientX<=n.right&&o.clientY>n.bottom&&o.clientX>=n.left:o.clientX>n.right&&o.clientY>n.top||o.clientX<=n.right&&o.clientY>n.bottom+i}function En(o,t,e,n,i,r,a,l){var s=n?o.clientY:o.clientX,u=n?e.height:e.width,d=n?e.top:e.left,f=n?e.bottom:e.right,p=!1;if(!a){if(l&&qt<u*i){if(!kt&&(Rt===1?s>d+u*r/2:s<f-u*r/2)&&(kt=!0),kt)p=!0;else if(Rt===1?s<d+qt:s>f-qt)return-Rt}else if(s>d+u*(1-i)/2&&s<f-u*(1-i)/2)return Sn(t)}return p=p||a,p&&(s<d+u*r/2||s>f-u*r/2)?s>d+u/2?1:-1:0}function Sn(o){return N(c)<N(o)?1:-1}function Dn(o){for(var t=o.tagName+o.className+o.src+o.href+o.textContent,e=t.length,n=0;e--;)n+=t.charCodeAt(e);return n.toString(36)}function _n(o){te.length=0;for(var t=o.getElementsByTagName("input"),e=t.length;e--;){var n=t[e];n.checked&&te.push(n)}}function $t(o){return setTimeout(o,0)}function ge(o){return clearTimeout(o)}ne&&y(document,"touchmove",function(o){(g.active||vt)&&o.cancelable&&o.preventDefault()});g.utils={on:y,off:w,css:h,find:ke,is:function(t,e){return!!$(t,e,t,!1)},extend:ln,throttle:Xe,closest:$,toggleClass:T,clone:we,index:N,nextTick:$t,cancelNextTick:ge,detectDirection:He,getChild:wt};g.get=function(o){return o[k]};g.mount=function(){for(var o=arguments.length,t=new Array(o),e=0;e<o;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(g.utils=J(J({},g.utils),n.utils)),Yt.mount(n)})};g.create=function(o,t){return new g(o,t)};g.version=nn;var M=[],Nt,me,ve=!1,ce,fe,ee,Pt;function Cn(){function o(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return o.prototype={dragStarted:function(e){var n=e.originalEvent;this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):this.options.supportPointer?y(document,"pointermove",this._handleFallbackAutoScroll):n.touches?y(document,"touchmove",this._handleFallbackAutoScroll):y(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var n=e.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?w(document,"dragover",this._handleAutoScroll):(w(document,"pointermove",this._handleFallbackAutoScroll),w(document,"touchmove",this._handleFallbackAutoScroll),w(document,"mousemove",this._handleFallbackAutoScroll)),Me(),Vt(),sn()},nulling:function(){ee=me=Nt=ve=Pt=ce=fe=null,M.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,n){var i=this,r=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,l=document.elementFromPoint(r,a);if(ee=e,n||this.options.forceAutoScrollFallback||Xt||nt||Mt){de(e,this.options,l,n);var s=lt(l,!0);ve&&(!Pt||r!==ce||a!==fe)&&(Pt&&Me(),Pt=setInterval(function(){var u=lt(document.elementFromPoint(r,a),!0);u!==s&&(s=u,Vt()),de(e,i.options,u,n)},10),ce=r,fe=a)}else{if(!this.options.bubbleScroll||lt(l,!0)===Q()){Vt();return}de(e,this.options,lt(l,!1),!1)}}},z(o,{pluginName:"scroll",initializeByDefault:!0})}function Vt(){M.forEach(function(o){clearInterval(o.pid)}),M=[]}function Me(){clearInterval(Pt)}var de=Xe(function(o,t,e,n){if(!!t.scroll){var i=(o.touches?o.touches[0]:o).clientX,r=(o.touches?o.touches[0]:o).clientY,a=t.scrollSensitivity,l=t.scrollSpeed,s=Q(),u=!1,d;me!==e&&(me=e,Vt(),Nt=t.scroll,d=t.scrollFn,Nt===!0&&(Nt=lt(e,!0)));var f=0,p=Nt;do{var E=p,b=O(E),S=b.top,P=b.bottom,K=b.left,x=b.right,Y=b.width,D=b.height,V=void 0,Z=void 0,st=E.scrollWidth,yt=E.scrollHeight,G=h(E),Et=E.scrollLeft,ot=E.scrollTop;E===s?(V=Y<st&&(G.overflowX==="auto"||G.overflowX==="scroll"||G.overflowX==="visible"),Z=D<yt&&(G.overflowY==="auto"||G.overflowY==="scroll"||G.overflowY==="visible")):(V=Y<st&&(G.overflowX==="auto"||G.overflowX==="scroll"),Z=D<yt&&(G.overflowY==="auto"||G.overflowY==="scroll"));var St=V&&(Math.abs(x-i)<=a&&Et+Y<st)-(Math.abs(K-i)<=a&&!!Et),tt=Z&&(Math.abs(P-r)<=a&&ot+D<yt)-(Math.abs(S-r)<=a&&!!ot);if(!M[f])for(var ut=0;ut<=f;ut++)M[ut]||(M[ut]={});(M[f].vx!=St||M[f].vy!=tt||M[f].el!==E)&&(M[f].el=E,M[f].vx=St,M[f].vy=tt,clearInterval(M[f].pid),(St!=0||tt!=0)&&(u=!0,M[f].pid=setInterval(function(){n&&this.layer===0&&g.active._onTouchMove(ee);var Dt=M[this.layer].vy?M[this.layer].vy*l:0,it=M[this.layer].vx?M[this.layer].vx*l:0;typeof d=="function"&&d.call(g.dragged.parentNode[k],it,Dt,o,ee,M[this.layer].el)!=="continue"||Ye(M[this.layer].el,it,Dt)}.bind({layer:f}),24))),f++}while(t.bubbleScroll&&p!==s&&(p=lt(p,!1)));ve=u}},30),Ke=function(t){var e=t.originalEvent,n=t.putSortable,i=t.dragEl,r=t.activeSortable,a=t.dispatchSortableEvent,l=t.hideGhostForTarget,s=t.unhideGhostForTarget;if(!!e){var u=n||r;l();var d=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,f=document.elementFromPoint(d.clientX,d.clientY);s(),u&&!u.el.contains(f)&&(a("spill"),this.onSpill({dragEl:i,putSortable:n}))}};function ye(){}ye.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var i=wt(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(e,i):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:Ke};z(ye,{pluginName:"revertOnSpill"});function Ee(){}Ee.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable,i=n||this.sortable;i.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),i.animateAll()},drop:Ke};z(Ee,{pluginName:"removeOnSpill"});var j;function On(){function o(){this.defaults={swapClass:"sortable-swap-highlight"}}return o.prototype={dragStart:function(e){var n=e.dragEl;j=n},dragOverValid:function(e){var n=e.completed,i=e.target,r=e.onMove,a=e.activeSortable,l=e.changed,s=e.cancel;if(!!a.options.swap){var u=this.sortable.el,d=this.options;if(i&&i!==u){var f=j;r(i)!==!1?(T(i,d.swapClass,!0),j=i):j=null,f&&f!==j&&T(f,d.swapClass,!1)}l(),n(!0),s()}},drop:function(e){var n=e.activeSortable,i=e.putSortable,r=e.dragEl,a=i||this.sortable,l=this.options;j&&T(j,l.swapClass,!1),j&&(l.swap||i&&i.options.swap)&&r!==j&&(a.captureAnimationState(),a!==n&&n.captureAnimationState(),Tn(r,j),a.animateAll(),a!==n&&n.animateAll())},nulling:function(){j=null}},z(o,{pluginName:"swap",eventProperties:function(){return{swapItem:j}}})}function Tn(o,t){var e=o.parentNode,n=t.parentNode,i,r;!e||!n||e.isEqualNode(t)||n.isEqualNode(o)||(i=N(o),r=N(t),e.isEqualNode(n)&&i<r&&r++,e.insertBefore(t,e.children[i]),n.insertBefore(o,n.children[r]))}var m=[],L=[],Ct,q,Ot=!1,H=!1,mt=!1,_,Tt,Kt;function An(){function o(t){for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this));t.options.supportPointer?y(document,"pointerup",this._deselectMultiDrag):(y(document,"mouseup",this._deselectMultiDrag),y(document,"touchend",this._deselectMultiDrag)),y(document,"keydown",this._checkKeyDown),y(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(i,r){var a="";m.length&&q===t?m.forEach(function(l,s){a+=(s?", ":"")+l.textContent}):a=r.textContent,i.setData("Text",a)}}}return o.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(e){var n=e.dragEl;_=n},delayEnded:function(){this.isMultiDrag=~m.indexOf(_)},setupClone:function(e){var n=e.sortable,i=e.cancel;if(!!this.isMultiDrag){for(var r=0;r<m.length;r++)L.push(we(m[r])),L[r].sortableIndex=m[r].sortableIndex,L[r].draggable=!1,L[r].style["will-change"]="",T(L[r],this.options.selectedClass,!1),m[r]===_&&T(L[r],this.options.chosenClass,!1);n._hideClone(),i()}},clone:function(e){var n=e.sortable,i=e.rootEl,r=e.dispatchSortableEvent,a=e.cancel;!this.isMultiDrag||this.options.removeCloneOnHide||m.length&&q===n&&(xe(!0,i),r("clone"),a())},showClone:function(e){var n=e.cloneNowShown,i=e.rootEl,r=e.cancel;!this.isMultiDrag||(xe(!1,i),L.forEach(function(a){h(a,"display","")}),n(),Kt=!1,r())},hideClone:function(e){var n=this;e.sortable;var i=e.cloneNowHidden,r=e.cancel;!this.isMultiDrag||(L.forEach(function(a){h(a,"display","none"),n.options.removeCloneOnHide&&a.parentNode&&a.parentNode.removeChild(a)}),i(),Kt=!0,r())},dragStartGlobal:function(e){e.sortable,!this.isMultiDrag&&q&&q.multiDrag._deselectMultiDrag(),m.forEach(function(n){n.sortableIndex=N(n)}),m=m.sort(function(n,i){return n.sortableIndex-i.sortableIndex}),mt=!0},dragStarted:function(e){var n=this,i=e.sortable;if(!!this.isMultiDrag){if(this.options.sort&&(i.captureAnimationState(),this.options.animation)){m.forEach(function(a){a!==_&&h(a,"position","absolute")});var r=O(_,!1,!0,!0);m.forEach(function(a){a!==_&&Ae(a,r)}),H=!0,Ot=!0}i.animateAll(function(){H=!1,Ot=!1,n.options.animation&&m.forEach(function(a){ie(a)}),n.options.sort&&jt()})}},dragOver:function(e){var n=e.target,i=e.completed,r=e.cancel;H&&~m.indexOf(n)&&(i(!1),r())},revert:function(e){var n=e.fromSortable,i=e.rootEl,r=e.sortable,a=e.dragRect;m.length>1&&(m.forEach(function(l){r.addAnimationState({target:l,rect:H?O(l):a}),ie(l),l.fromRect=a,n.removeAnimationState(l)}),H=!1,In(!this.options.removeCloneOnHide,i))},dragOverCompleted:function(e){var n=e.sortable,i=e.isOwner,r=e.insertion,a=e.activeSortable,l=e.parentEl,s=e.putSortable,u=this.options;if(r){if(i&&a._hideClone(),Ot=!1,u.animation&&m.length>1&&(H||!i&&!a.options.sort&&!s)){var d=O(_,!1,!0,!0);m.forEach(function(p){p!==_&&(Ae(p,d),l.appendChild(p))}),H=!0}if(!i)if(H||jt(),m.length>1){var f=Kt;a._showClone(n),a.options.animation&&!Kt&&f&&L.forEach(function(p){a.addAnimationState({target:p,rect:Tt}),p.fromRect=Tt,p.thisAnimationDuration=null})}else a._showClone(n)}},dragOverAnimationCapture:function(e){var n=e.dragRect,i=e.isOwner,r=e.activeSortable;if(m.forEach(function(l){l.thisAnimationDuration=null}),r.options.animation&&!i&&r.multiDrag.isMultiDrag){Tt=z({},n);var a=ht(_,!0);Tt.top-=a.f,Tt.left-=a.e}},dragOverAnimationComplete:function(){H&&(H=!1,jt())},drop:function(e){var n=e.originalEvent,i=e.rootEl,r=e.parentEl,a=e.sortable,l=e.dispatchSortableEvent,s=e.oldIndex,u=e.putSortable,d=u||this.sortable;if(!!n){var f=this.options,p=r.children;if(!mt)if(f.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),T(_,f.selectedClass,!~m.indexOf(_)),~m.indexOf(_))m.splice(m.indexOf(_),1),Ct=null,At({sortable:a,rootEl:i,name:"deselect",targetEl:_,originalEvt:n});else{if(m.push(_),At({sortable:a,rootEl:i,name:"select",targetEl:_,originalEvt:n}),n.shiftKey&&Ct&&a.el.contains(Ct)){var E=N(Ct),b=N(_);if(~E&&~b&&E!==b){var S,P;for(b>E?(P=E,S=b):(P=b,S=E+1);P<S;P++)~m.indexOf(p[P])||(T(p[P],f.selectedClass,!0),m.push(p[P]),At({sortable:a,rootEl:i,name:"select",targetEl:p[P],originalEvt:n}))}}else Ct=_;q=d}if(mt&&this.isMultiDrag){if(H=!1,(r[k].options.sort||r!==i)&&m.length>1){var K=O(_),x=N(_,":not(."+this.options.selectedClass+")");if(!Ot&&f.animation&&(_.thisAnimationDuration=null),d.captureAnimationState(),!Ot&&(f.animation&&(_.fromRect=K,m.forEach(function(D){if(D.thisAnimationDuration=null,D!==_){var V=H?O(D):K;D.fromRect=V,d.addAnimationState({target:D,rect:V})}})),jt(),m.forEach(function(D){p[x]?r.insertBefore(D,p[x]):r.appendChild(D),x++}),s===N(_))){var Y=!1;m.forEach(function(D){if(D.sortableIndex!==N(D)){Y=!0;return}}),Y&&l("update")}m.forEach(function(D){ie(D)}),d.animateAll()}q=d}(i===r||u&&u.lastPutMode!=="clone")&&L.forEach(function(D){D.parentNode&&D.parentNode.removeChild(D)})}},nullingGlobal:function(){this.isMultiDrag=mt=!1,L.length=0},destroyGlobal:function(){this._deselectMultiDrag(),w(document,"pointerup",this._deselectMultiDrag),w(document,"mouseup",this._deselectMultiDrag),w(document,"touchend",this._deselectMultiDrag),w(document,"keydown",this._checkKeyDown),w(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(e){if(!(typeof mt<"u"&&mt)&&q===this.sortable&&!(e&&$(e.target,this.options.draggable,this.sortable.el,!1))&&!(e&&e.button!==0))for(;m.length;){var n=m[0];T(n,this.options.selectedClass,!1),m.shift(),At({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:n,originalEvt:e})}},_checkKeyDown:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},z(o,{pluginName:"multiDrag",utils:{select:function(e){var n=e.parentNode[k];!n||!n.options.multiDrag||~m.indexOf(e)||(q&&q!==n&&(q.multiDrag._deselectMultiDrag(),q=n),T(e,n.options.selectedClass,!0),m.push(e))},deselect:function(e){var n=e.parentNode[k],i=m.indexOf(e);!n||!n.options.multiDrag||!~i||(T(e,n.options.selectedClass,!1),m.splice(i,1))}},eventProperties:function(){var e=this,n=[],i=[];return m.forEach(function(r){n.push({multiDragElement:r,index:r.sortableIndex});var a;H&&r!==_?a=-1:H?a=N(r,":not(."+e.options.selectedClass+")"):a=N(r),i.push({multiDragElement:r,index:a})}),{items:Ze(m),clones:[].concat(L),oldIndicies:n,newIndicies:i}},optionListeners:{multiDragKey:function(e){return e=e.toLowerCase(),e==="ctrl"?e="Control":e.length>1&&(e=e.charAt(0).toUpperCase()+e.substr(1)),e}}})}function In(o,t){m.forEach(function(e,n){var i=t.children[e.sortableIndex+(o?Number(n):0)];i?t.insertBefore(e,i):t.appendChild(e)})}function xe(o,t){L.forEach(function(e,n){var i=t.children[e.sortableIndex+(o?Number(n):0)];i?t.insertBefore(e,i):t.appendChild(e)})}function jt(){m.forEach(function(o){o!==_&&o.parentNode&&o.parentNode.removeChild(o)})}g.mount(new Cn);g.mount(Ee,ye);const Nn=Object.freeze(Object.defineProperty({__proto__:null,default:g,MultiDrag:An,Sortable:g,Swap:On},Symbol.toStringTag,{value:"Module"})),xn=Ue(Nn);export{xn as r};
