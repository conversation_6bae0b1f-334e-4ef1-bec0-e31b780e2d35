import{L as te,N as ae,c as se,x as oe,J as ne,w as le,K as re,Q as ie,y as me,z as ue}from"./element-plus.f1c966a6.js";import{t as r,e as pe,T as de,h as ce}from"./index.b46f9be1.js";import{H as _e}from"./vue-echarts.8abc5bbd.js";import{a as fe,b as he}from"./overview.5ff1c09f.js";import ye from"./tab-role.16e50621.js";import ge from"./tab-user.5b5d2b91.js";import ve from"./tab-channel.4f0538e4.js";import Ce from"./tab-order.0c19e54f.js";import Te from"./tab-pwd.56ad61a3.js";import{a as xe}from"./role.0196f801.js";import{d as we,a1 as H,r as Ee,j as be,J as Ve,a5 as De,o as d,c as f,a as u,W as o,P as p,V as c,F as v,a8 as B,U as b,O as M,T as V,R as N,a0 as Fe,u as m,k as F,n as ke}from"./@vue.2cd7be6a.js";import"./@vueuse.b996a1f4.js";import"./@element-plus.7d7e593d.js";import"./lodash-es.373b3802.js";import"./dayjs.08446ff3.js";import"./axios.b343fc94.js";import"./async-validator.fb49d0f5.js";import"./@ctrl.b082b0c1.js";import"./@popperjs.36402333.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./lodash.72bc4403.js";import"./vue-router.e8699733.js";import"./pinia.d07b2cc8.js";import"./css-color-function.dc2fc295.js";import"./color.c598a50e.js";import"./clone.31d1ff2e.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./balanced-match.d2a36341.js";import"./ms.a9ae1d6d.js";import"./nprogress.6ec8892d.js";import"./vue-clipboard3.6e3167fd.js";import"./clipboard.83f28523.js";import"./echarts.e4067e9a.js";import"./zrender.d534d95c.js";import"./tslib.60310f1a.js";import"./highlight.js.8c5c7499.js";import"./@highlightjs.155b9419.js";import"./resize-detector.4e96b72b.js";import"./index.vue_vue_type_script_setup_true_lang.67ca2e88.js";import"./index.aa7efbac.js";const Ae={class:"flex items-center h-8"},Be={class:"flex items-center mr-4"},Me={class:"ml-1"},$e={class:"ml-2"},Oe={class:"flex"},Le={style:{color:"#999"}},ze={class:"flex items-center"},Ue=["onClick"],je={style:{color:"rgba(0, 0, 0, 0.45)","font-size":"14px"}},He={style:{"font-size":"30px"}},Ne={key:0,class:"mt-4"},Ie={class:"flex mt-4 justify-end"},Pe={class:"flex mt-4 justify-end"},Re={class:"flex mt-4 justify-end"},Se={class:"flex mt-4 justify-end"},zt=we({__name:"index",setup(Ge){const I=[{name:"\u5B9E\u65F6",type:"real"},{name:"\u6628\u5929",type:"yesterday"},{name:"\u8FD17\u5929",type:"7day"},{name:"\u8FD130\u5929",type:"30day"},{name:"\u81EA\u7136\u6708",type:"month"},{name:"\u81EA\u5B9A\u4E49",type:"custom"}],e=H({time:{type:"real",showMonth:!1,showCustom:!1,month:"",custom:[],t:r(new Date().getTime(),"yyyy-mm-dd hh:MM:ss")},showChart:!0,chartOption:{grid:{left:"52px",right:"52px",bottom:"20px"},xAxis:{nameLocation:"center",type:"category",data:[]},yAxis:{type:"value"},legend:{data:[]},tooltip:{trigger:"axis"},series:[]},active:1,params:{start:r(new Date().getTime()),end:r(new Date().getTime())},dataList:[],dataType:"",list1:[],list2:[],refresh:{time:"-",status:"off",interval:3e4}}),l=H({st:e.params.start,et:e.params.end,mode:e.active,role_id:""}),{pager:n,getLists:C}=pe({fetchFun:fe,params:l}),P=a=>{const t=a.getFullYear(),i=a.getMonth(),h=i===11?t+1:t,_=i===11?0:i+1;return new Date(h,_,0)},R=(a,t)=>{let i=1;const h=1e3*60*60*24;t==="7day"?i=7:t==="30day"&&(i=30);const _=new Date(a.getTime()-h*i);if(t==="yesterday"){const x=r(_.setHours(0,0,0,0)),w=r(_.setHours(23,59,59,999));return l.st=x,l.et=w,e.params={start:x,end:w},`${x}~${w}`}const g=r(_.setHours(0,0,0,0)),y=r(a.getTime()-h);return e.params={start:g,end:y},l.st=g,l.et=y,`${g}~${y}`},S=a=>{if(a===e.time.type)return;if(e.time.month="",e.time.custom=[],e.time.type=a,e.time.showCustom=!1,e.time.showMonth=!1,a==="custom")return e.time.showCustom=!0;if(a==="month")return e.time.showMonth=!0;const t=new Date;a==="real"?(e.time.t=r(t.getTime(),"yyyy-mm-dd hh:MM:ss"),e.params={start:r(t.getTime()),end:r(t.getTime())},l.st=r(t.getTime()),l.et=r(t.getTime())):e.time.t=R(t,a),T()},$=()=>{e.showChart=!1;const a=[];let t=[{type:"line",data:[]},{type:"line",data:[]}];["real","yesterday"].includes(e.time.type)?(e.time.type==="real"?(e.chartOption.legend.data=["\u4ECA\u5929","\u6628\u5929"],t[0].name="\u4ECA\u5929",t[1].name="\u6628\u5929"):(e.chartOption.legend.data=["\u6628\u5929","\u524D\u5929"],t[0].name="\u6628\u5929",t[1].name="\u524D\u5929"),e.list2.forEach(i=>{t[1].data.push(i[e.dataType])})):(e.chartOption.legend.data=[],t.splice(1,1)),e.list1.forEach(i=>{a.push(i.time),t[0].data.push(i[e.dataType])}),e.chartOption.series=t,e.chartOption.xAxis.data=a,ke(()=>e.showChart=!0)},G=async()=>{try{const a=await he(e.params);e.dataList=(a.statis||[]).map(t=>({...t,type:t.rate>=0?"plus":"minus"})),a.statis&&a.statis.length&&(e.dataType=a.statis[0].key),e.list1=a.this,e.list2=a.last,$()}catch{}},J=a=>{e.dataType=a,$()},Y=a=>{const t=new Date().getTime(),i=1e3*60*60*24;return t-i*31<a.getTime()},q=a=>{const t=new Date().getTime()-864e5;return!(a.getTime()<t)},K=()=>{if(!e.time.month)return;const a=r(e.time.month.getTime()),t=r(P(e.time.month).getTime());e.params={start:a,end:t},l.st=a,l.et=t,e.time.t=`${a}~${t}`,T()},Q=()=>{if(e.time.custom.length!==2)return;const a=r(e.time.custom[0].getTime()),t=r(e.time.custom[1].getTime());e.params={start:a,end:t},l.st=a,l.et=t,e.time.t=`${a}~${t}`,T()},k=de(()=>C()),W=()=>{l.mode=e.active,e.active!=5&&(l.role_id="");let a=n.page,t=n.lists;t=[],a=1,Object.assign(n,{page:a,lists:t}),k()},T=()=>{G(),k()},O=()=>{e.refresh.time=r(new Date().getTime(),"y-m-d h:M:s"),T()};let A;const L=()=>{e.refresh.status="on",O(),A=setInterval(O,e.refresh.interval)},z=Ee([]),X=()=>{xe({page_no:1,page_size:1e3,type:"4,5,6"}).then(a=>{z.value=a.lists})};return be(()=>{T(),X()}),Ve(()=>{A&&clearInterval(A)}),(a,t)=>{const i=De("Odometer"),h=se,_=oe,g=te,y=ne,x=le,w=re,Z=ae,U=ie,D=ce,E=me,ee=ue;return d(),f(v,null,[u("div",Ae,[u("div",Be,[o(h,{size:"14"},{default:p(()=>[o(i)]),_:1}),u("p",Me,c(e.time.type==="real"?"\u66F4\u65B0":"\u7EDF\u8BA1")+"\u65F6\u95F4\uFF1A"+c(e.time.t),1)]),(d(),f(v,null,B(I,s=>o(_,{key:s.type,class:"!ml-0",size:"small",type:s.type===e.time.type?"primary":"",text:s.type!==e.time.type,onClick:j=>S(s.type)},{default:p(()=>[b(c(s.name),1)]),_:2},1032,["type","text","onClick"])),64)),u("div",$e,[e.time.showMonth?(d(),M(g,{key:0,modelValue:e.time.month,"onUpdate:modelValue":t[0]||(t[0]=s=>e.time.month=s),type:"month","disabled-date":Y,onChange:K},null,8,["modelValue"])):V("",!0),e.time.showCustom?(d(),M(g,{key:1,modelValue:e.time.custom,"onUpdate:modelValue":t[1]||(t[1]=s=>e.time.custom=s),type:"daterange","default-time":[new Date(new Date().setHours(0,0,0,0)),new Date(new Date().setHours(23,59,59,999))],"disabled-date":q,onChange:Q},null,8,["modelValue","default-time"])):V("",!0)])]),o(U,{class:"!border-none mt-4",shadow:"never"},{default:p(()=>[u("div",Oe,[o(w,{inline:"",size:"small"},{default:p(()=>[o(y,{style:{"--el-text-color-regular":"#999"},label:"\u4E0A\u6B21\u5237\u65B0\u65F6\u95F4:"},{default:p(()=>[u("span",Le,c(e.refresh.time),1)]),_:1}),o(y,null,{default:p(()=>[o(_,{type:"primary",disabled:e.refresh.status==="on",onClick:L},{default:p(()=>[b(c(e.refresh.status==="on"?"\u81EA\u52A8\u5237\u65B0\u4E2D...":"\u81EA\u52A8\u5237\u65B0"),1)]),_:1},8,["disabled"])]),_:1}),o(y,{label:"\u95F4\u9694(ms):"},{default:p(()=>[o(x,{min:1e4,precision:0,step:1e3,modelValue:e.refresh.interval,"onUpdate:modelValue":t[2]||(t[2]=s=>e.refresh.interval=s),onChange:L},null,8,["modelValue"])]),_:1})]),_:1})]),u("div",ze,[(d(!0),f(v,null,B(e.dataList,(s,j)=>(d(),f("div",{key:j,class:N(["po__data",{po__datas:e.dataType===s.key}]),onClick:Je=>J(s.key)},[u("p",je,c(s.text),1),u("p",He,[b(c(s.this),1),s.text.includes("\u7387")?(d(),f(v,{key:0},[b("%")],64)):V("",!0)]),o(Z,{class:"!mt-0 !mb-2"}),u("div",{class:N(["po__dp",s.type])},[t[8]||(t[8]=b(" \u73AF\u6BD4\xA0")),u("span",{style:Fe({color:s.type==="plus"?"#f5222d":"#52c41a"})},c(Math.abs(s.rate))+"%",5)],2)],10,Ue))),128))]),(d(!0),f(v,null,B(e.dataList,s=>(d(),f(v,{key:s.key},[s.key===e.dataType?(d(),f("div",Ne," \u5747\u503C\uFF1A"+c(s.avg),1)):V("",!0)],64))),128)),e.showChart?(d(),M(m(_e),{key:0,style:{height:"293px",width:"100%","margin-top":"33px"},autoresize:!0,option:e.chartOption},null,8,["option"])):V("",!0)]),_:1}),o(U,{class:"!border-none mt-4",shadow:"never"},{default:p(()=>[o(ee,{modelValue:e.active,"onUpdate:modelValue":t[7]||(t[7]=s=>e.active=s),onTabChange:W},{default:p(()=>[o(E,{label:"\u63A8\u5E7F\u89D2\u8272\u5206\u6790",name:1},{default:p(()=>[o(ye,{pager:m(n),params:l},null,8,["pager","params"]),u("div",Ie,[o(D,{modelValue:m(n),"onUpdate:modelValue":t[3]||(t[3]=s=>F(n)?n.value=s:null),onChange:m(C)},null,8,["modelValue","onChange"])])]),_:1}),o(E,{label:"\u63A8\u5E7F\u7528\u6237\u5206\u6790",name:2},{default:p(()=>[o(ge,{pager:m(n),params:l},null,8,["pager","params"]),u("div",Pe,[o(D,{modelValue:m(n),"onUpdate:modelValue":t[4]||(t[4]=s=>F(n)?n.value=s:null),onChange:m(C)},null,8,["modelValue","onChange"])])]),_:1}),o(E,{label:"\u63A8\u5E7F\u6E20\u9053\u5206\u6790",name:3},{default:p(()=>[o(ve,{pager:m(n),params:l},null,8,["pager","params"])]),_:1}),o(E,{label:"\u63A8\u5E7F\u8BA2\u5355\u5206\u6790",name:4},{default:p(()=>[o(Ce,{pager:m(n),params:l},null,8,["pager","params"]),u("div",Re,[o(D,{modelValue:m(n),"onUpdate:modelValue":t[5]||(t[5]=s=>F(n)?n.value=s:null),onChange:m(C)},null,8,["modelValue","onChange"])])]),_:1}),o(E,{label:"\u63A8\u5E7F\u5BC6\u4EE4\u5206\u6790",name:5},{default:p(()=>[o(Te,{pager:m(n),params:l,"role-options":m(z),onGetlist:m(k)},null,8,["pager","params","role-options","onGetlist"]),u("div",Se,[o(D,{modelValue:m(n),"onUpdate:modelValue":t[6]||(t[6]=s=>F(n)?n.value=s:null),onChange:m(C)},null,8,["modelValue","onChange"])])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}});export{zt as default};
