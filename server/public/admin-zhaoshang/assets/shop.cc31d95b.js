import{a3 as J,L as K,I as W,J as X,O as Z,P as ee,w as le,x as te,K as ae,Q as se,A as oe,a as ue,B as ne,D as ie}from"./element-plus.f1c966a6.js";import{e as re,F as de,G as pe,f as me,h as _e,H as ce}from"./index.b46f9be1.js";import{_ as be}from"./index.vue_vue_type_script_setup_true_lang.67ca2e88.js";import{o as fe,l as ye}from"./shop.cacb434f.js";import{s as k,a as Fe}from"./brand.449e3271.js";import{d as ve,a1 as D,e as Ve,w as g,ai as ge,o as p,c as f,W as l,P as o,F as y,a8 as F,O as c,u as _,a as v,U as E,Q as A,V as w,k as Ee}from"./@vue.2cd7be6a.js";import"./@vueuse.b996a1f4.js";import"./@element-plus.7d7e593d.js";import"./lodash-es.373b3802.js";import"./dayjs.08446ff3.js";import"./axios.b343fc94.js";import"./async-validator.fb49d0f5.js";import"./@ctrl.b082b0c1.js";import"./@popperjs.36402333.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./lodash.72bc4403.js";import"./vue-router.e8699733.js";import"./pinia.d07b2cc8.js";import"./css-color-function.dc2fc295.js";import"./color.c598a50e.js";import"./clone.31d1ff2e.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./balanced-match.d2a36341.js";import"./ms.a9ae1d6d.js";import"./nprogress.6ec8892d.js";import"./vue-clipboard3.6e3167fd.js";import"./clipboard.83f28523.js";import"./echarts.e4067e9a.js";import"./zrender.d534d95c.js";import"./tslib.60310f1a.js";import"./highlight.js.8c5c7499.js";import"./@highlightjs.155b9419.js";import"./index.aa7efbac.js";const we={class:"flex items-center"},Be={style:{display:"inline-block"}},he={class:"line-clamp-2 whitespace-normal"},Ce={class:"line-clamp-2 whitespace-normal"},xe={class:"flex mt-4 justify-end"},pl=ve({__name:"shop",setup(Le){const U={lazy:!0,checkStrictly:!0,lazyLoad:async(s,t)=>{const{level:d,value:n}=s;let i=ce,r=[];d===1&&(r=await M(n),r.length?i=r.map(V=>({value:V,label:V,leaf:!0})):t([])),(d===0||r.length)&&t(i)}},a=D({keyword:void 0,citys:"",business_district:"",schools:"",status:void 0,brand_name:"",category_name:"",month_sale_start:void 0,month_sale_end:void 0,is_valid:void 0,time_type:1,start_time:void 0,end_time:void 0,priority:"",is_new:void 0,source_type:void 0}),u=D({cityList:[],citys:[],business:[],businessList:[],schools:[],categoryList:[],time:[]}),S=Ve(()=>{if(!u.businessList.length)return[];let s=[];return u.citys.forEach(t=>{const d=u.businessList.find(n=>n.city===t);d&&d.schools&&d.schools.length&&s.push(...d.schools)}),s}),{pager:b,getLists:B,resetParams:Y,resetPage:P}=re({fetchFun:k,params:a}),z=async()=>{const s=await me({type:2});u.cityList=s.lists||[]},M=async s=>{const t={level:s,citys:a.citys};try{const{lists:d}=await fe(t);return d||[]}catch{}},O=async()=>{try{const s=await ye();u.businessList=s.lists||[]}catch{}},I=async()=>{try{const{lists:s}=await Fe({page_no:1,page_size:2e3,type:2});u.categoryList=s||[]}catch{}},N=s=>{(!s||s.length!==2)&&(u.business=[])},T=()=>{u.time=[],a.month_sale_start=void 0,a.month_sale_end=void 0,u.citys=[],u.business=[],u.schools=[],Y()};return g(()=>u.citys,s=>{s&&s.length?a.citys=s.join():a.citys=""},{deep:!0}),g(()=>u.business,s=>{s&&s.length?a.business_district=s[1]:a.business_district=""},{deep:!0}),g(()=>u.schools,s=>{s&&s.length?a.schools=s.join():a.schools=""},{deep:!0}),g(()=>u.time,s=>{s&&s.length?(a.start_time=s[0]+" 00:00:00",a.end_time=s[1]+" 23:59:59"):(a.start_time=void 0,a.end_time=void 0)},{deep:!0}),I(),z(),B(),O(),(s,t)=>{const d=W,n=X,i=Z,r=ee,V=J,h=le,j=K,C=te,q=be,Q=ae,x=se,m=oe,L=ue,R=ne,$=_e,G=ge("perms"),H=ie;return p(),f(y,null,[l(x,{class:"!border-none mb-4",shadow:"never"},{default:o(()=>[l(Q,{class:"mb-[-16px]",model:a,inline:""},{default:o(()=>[l(n,{label:"\u5E97\u94FA\u4FE1\u606F",prop:"keyword"},{default:o(()=>[l(d,{class:"w-[200px]",modelValue:a.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>a.keyword=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0/\u8054\u7CFB\u65B9\u5F0F"},null,8,["modelValue"])]),_:1}),l(n,{label:"\u5E97\u94FA\u57CE\u5E02"},{default:o(()=>[l(r,{modelValue:u.citys,"onUpdate:modelValue":t[1]||(t[1]=e=>u.citys=e),class:"w-[200px]",filterable:"",clearable:"",multiple:"","collapse-tags":""},{default:o(()=>[(p(!0),f(y,null,F(u.cityList,e=>(p(),c(i,{key:e.sname,label:e.sname,value:e.sname},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(n,{label:"\u5E97\u94FA\u5546\u5708"},{default:o(()=>[l(V,{modelValue:u.business,"onUpdate:modelValue":t[2]||(t[2]=e=>u.business=e),props:U,"show-all-levels":!1,onChange:N,clearable:""},null,8,["modelValue"])]),_:1}),(p(),c(n,{label:"\u9644\u8FD1\u5B66\u6821",key:a.citys},{default:o(()=>[l(r,{modelValue:u.schools,"onUpdate:modelValue":t[3]||(t[3]=e=>u.schools=e),class:"w-[200px]",clearable:"",filterable:"",multiple:"","collapse-tags":""},{default:o(()=>[(p(!0),f(y,null,F(S.value,e=>(p(),c(i,{key:e.id,label:e.name,value:e.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),l(n,{label:"\u63A8\u5E7F\u72B6\u6001",prop:"status"},{default:o(()=>[l(r,{class:"w-[200px]",modelValue:a.status,"onUpdate:modelValue":t[4]||(t[4]=e=>a.status=e),clearable:""},{default:o(()=>[l(i,{label:"\u63A8\u5E7F\u4E2D",value:"1"}),l(i,{label:"\u672A\u62A5\u540D",value:"2"})]),_:1},8,["modelValue"])]),_:1}),l(n,{label:"\u662F\u5426\u65B0\u5E97",prop:"is_new"},{default:o(()=>[l(r,{class:"w-[200px]",modelValue:a.is_new,"onUpdate:modelValue":t[5]||(t[5]=e=>a.is_new=e),clearable:""},{default:o(()=>[l(i,{label:"\u662F",value:"1"}),l(i,{label:"\u5426",value:"0"})]),_:1},8,["modelValue"])]),_:1}),l(n,{label:"\u54C1\u724C\u540D\u79F0",prop:"brand_name"},{default:o(()=>[l(d,{modelValue:a.brand_name,"onUpdate:modelValue":t[6]||(t[6]=e=>a.brand_name=e),class:"w-[200px]"},null,8,["modelValue"])]),_:1}),l(n,{label:"\u5546\u5BB6\u5206\u7C7B",prop:"category_name"},{default:o(()=>[l(r,{modelValue:a.category_name,"onUpdate:modelValue":t[7]||(t[7]=e=>a.category_name=e),class:"w-[200px]",clearable:""},{default:o(()=>[(p(!0),f(y,null,F(u.categoryList,e=>(p(),c(i,{key:e.id,value:e.name,label:e.name},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(n,{label:"\u5E97\u94FA\u7B49\u7EA7",prop:"priority"},{default:o(()=>[l(r,{modelValue:a.priority,"onUpdate:modelValue":t[8]||(t[8]=e=>a.priority=e),class:"w-[200px]",clearable:""},{default:o(()=>[(p(!0),f(y,null,F(_(de),e=>(p(),c(i,{key:e.value,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(n,{label:"\u5E97\u94FA\u6708\u552E"},{default:o(()=>[v("div",we,[l(h,{modelValue:a.month_sale_start,"onUpdate:modelValue":t[9]||(t[9]=e=>a.month_sale_start=e),class:"w-[96px]","step-strictly":"",controls:!1,precision:0,min:0},null,8,["modelValue"]),t[16]||(t[16]=E(" ~ ")),l(h,{modelValue:a.month_sale_end,"onUpdate:modelValue":t[10]||(t[10]=e=>a.month_sale_end=e),class:"w-[96px]","step-strictly":"",controls:!1,precision:0,min:a.month_sale_start||0},null,8,["modelValue","min"])])]),_:1}),l(n,{label:"\u5E97\u94FA\u6765\u6E90"},{default:o(()=>[l(r,{class:"w-[200px]",modelValue:a.source_type,"onUpdate:modelValue":t[11]||(t[11]=e=>a.source_type=e),clearable:""},{default:o(()=>[(p(!0),f(y,null,F(_(pe),e=>(p(),c(i,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(n,{label:"\u5546\u6237\u72B6\u6001",prop:"is_valid"},{default:o(()=>[l(r,{class:"w-[200px]",modelValue:a.is_valid,"onUpdate:modelValue":t[12]||(t[12]=e=>a.is_valid=e),clearable:""},{default:o(()=>[l(i,{value:"1",label:"\u6709\u6548"}),l(i,{value:"0",label:"\u65E0\u6548"})]),_:1},8,["modelValue"])]),_:1}),v("div",Be,[l(n,{label:"\u65F6\u95F4\u7C7B\u578B",prop:"time_type"},{default:o(()=>[l(r,{modelValue:a.time_type,"onUpdate:modelValue":t[13]||(t[13]=e=>a.time_type=e),class:"w-[100px]"},{default:o(()=>[l(i,{label:"\u5F55\u5165\u65F6\u95F4",value:1}),l(i,{label:"\u66F4\u65B0\u65F6\u95F4",value:3})]),_:1},8,["modelValue"])]),_:1}),l(n,null,{default:o(()=>[l(j,{modelValue:u.time,"onUpdate:modelValue":t[14]||(t[14]=e=>u.time=e),type:"daterange","start-placeholder":"\u5F00\u59CB\u65F6\u95F4","end-placeholder":"\u7ED3\u675F\u65F6\u95F4",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1})]),l(n,null,{default:o(()=>[l(C,{type:"primary",onClick:_(P)},{default:o(()=>t[17]||(t[17]=[E("\u67E5\u8BE2")])),_:1,__:[17]},8,["onClick"]),l(C,{onClick:T},{default:o(()=>t[18]||(t[18]=[E("\u91CD\u7F6E")])),_:1,__:[18]}),A(l(q,{class:"ml-2.5","fetch-fun":_(k),params:a,"page-size":_(b).size},null,8,["fetch-fun","params","page-size"]),[[G,["brand.shop.export.button"]]])]),_:1})]),_:1},8,["model"])]),_:1}),A((p(),c(x,{class:"!border-none",shadow:"never"},{default:o(()=>[l(R,{data:_(b).lists},{default:o(()=>[l(m,{label:"\u5E97\u94FA\u540D\u79F0",prop:"name","show-overflow-tooltip":""}),l(m,{label:"\u5546\u5BB6\u5206\u7C7B",prop:"category_name"}),l(m,{label:"\u54C1\u724C\u540D\u79F0",prop:"brand_name"}),l(m,{label:"\u5E97\u94FA\u6708\u552E",prop:"month_sale"}),l(m,{label:"\u5E97\u94FA\u8BC4\u5206",prop:"score"}),l(m,{label:"\u8054\u7CFB\u65B9\u5F0F",prop:"tel"}),l(m,{label:"\u5E97\u94FA\u5730\u5740",prop:"address","show-overflow-tooltip":""},{default:o(({row:e})=>[l(L,{content:e.address,placement:"top"},{default:o(()=>[v("div",he,w(e.address||"--"),1)]),_:2},1032,["content"])]),_:1}),l(m,{label:"\u5E97\u94FA\u5546\u5708"},{default:o(({row:e})=>[l(L,{content:e.business_district,placement:"top"},{default:o(()=>[v("div",Ce,w(e.business_district||"--"),1)]),_:2},1032,["content"])]),_:1}),l(m,{label:"\u662F\u5426\u54C1\u724C"},{default:o(({row:e})=>[E(w(e.is_brand===1?"\u662F":"\u5426"),1)]),_:1}),l(m,{label:"\u5F55\u5165\u65F6\u95F4",prop:"created_at"}),l(m,{label:"\u66F4\u65B0\u65F6\u95F4",prop:"updated_at"})]),_:1},8,["data"]),v("div",xe,[l($,{modelValue:_(b),"onUpdate:modelValue":t[15]||(t[15]=e=>Ee(b)?b.value=e:null),onChange:_(B)},null,8,["modelValue","onChange"])])]),_:1})),[[H,_(b).loading]])],64)}}});export{pl as default};
