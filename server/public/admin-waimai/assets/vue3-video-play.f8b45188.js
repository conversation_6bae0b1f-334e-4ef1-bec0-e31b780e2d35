import{e as xt,o as $,c as Z,U as Ct,$ as wt,u as j,bq as Pt,bp as Ft,ab as Tt,d as jt,a as V,Q as ht,R as ft,Y as et,_ as st,r as Dt,S as ut,a1 as qt,Z as Ee,w as ue,l as de,I as ce,W as Jt,T as Xt,aa as zt,a0 as te,M as Te,L as Se,V as be,aj as Ht,bU as ie,n as he,q as Le}from"./@vue.9e429daf.js";var Ae=Object.defineProperty,De=Object.defineProperties,ke=Object.getOwnPropertyDescriptors,ne=Object.getOwnPropertySymbols,Re=Object.prototype.hasOwnProperty,_e=Object.prototype.propertyIsEnumerable,ae=(N,w,f)=>w in N?Ae(N,w,{enumerable:!0,configurable:!0,writable:!0,value:f}):N[w]=f,ee=(N,w)=>{for(var f in w||(w={}))Re.call(w,f)&&ae(N,f,w[f]);if(ne)for(var f of ne(w))_e.call(w,f)&&ae(N,f,w[f]);return N},re=(N,w)=>De(N,ke(w));function se(N,w,f,_){var T,A=!1,R=0;function I(){T&&clearTimeout(T)}function k(){for(var o=arguments.length,L=new Array(o),m=0;m<o;m++)L[m]=arguments[m];var h=this,E=Date.now()-R;function y(){R=Date.now(),f.apply(h,L)}function d(){T=void 0}A||(_&&!T&&y(),I(),_===void 0&&E>N?y():w!==!0&&(T=setTimeout(_?d:y,_===void 0?N-E:N)))}return typeof w!="boolean"&&(_=f,f=w,w=void 0),k.cancel=function(){I(),A=!0},k}function oe(N,w,f){return f===void 0?se(N,w,!1):se(N,f,w!==!1)}function Ie(N){return N&&N.__esModule&&Object.prototype.hasOwnProperty.call(N,"default")?N.default:N}var fe={exports:{}};typeof window<"u"&&(fe.exports=function(N){var w={};function f(_){if(w[_])return w[_].exports;var T=w[_]={i:_,l:!1,exports:{}};return N[_].call(T.exports,T,T.exports,f),T.l=!0,T.exports}return f.m=N,f.c=w,f.d=function(_,T,A){f.o(_,T)||Object.defineProperty(_,T,{enumerable:!0,get:A})},f.r=function(_){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(_,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(_,"__esModule",{value:!0})},f.t=function(_,T){if(1&T&&(_=f(_)),8&T||4&T&&typeof _=="object"&&_&&_.__esModule)return _;var A=Object.create(null);if(f.r(A),Object.defineProperty(A,"default",{enumerable:!0,value:_}),2&T&&typeof _!="string")for(var R in _)f.d(A,R,function(I){return _[I]}.bind(null,R));return A},f.n=function(_){var T=_&&_.__esModule?function(){return _.default}:function(){return _};return f.d(T,"a",T),T},f.o=function(_,T){return Object.prototype.hasOwnProperty.call(_,T)},f.p="/dist/",f(f.s="./src/hls.ts")}({"./node_modules/eventemitter3/index.js":function(N,w,f){var _=Object.prototype.hasOwnProperty,T="~";function A(){}function R(L,m,h){this.fn=L,this.context=m,this.once=h||!1}function I(L,m,h,E,y){if(typeof h!="function")throw new TypeError("The listener must be a function");var d=new R(h,E||L,y),t=T?T+m:m;return L._events[t]?L._events[t].fn?L._events[t]=[L._events[t],d]:L._events[t].push(d):(L._events[t]=d,L._eventsCount++),L}function k(L,m){--L._eventsCount==0?L._events=new A:delete L._events[m]}function o(){this._events=new A,this._eventsCount=0}Object.create&&(A.prototype=Object.create(null),new A().__proto__||(T=!1)),o.prototype.eventNames=function(){var L,m,h=[];if(this._eventsCount===0)return h;for(m in L=this._events)_.call(L,m)&&h.push(T?m.slice(1):m);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(L)):h},o.prototype.listeners=function(L){var m=T?T+L:L,h=this._events[m];if(!h)return[];if(h.fn)return[h.fn];for(var E=0,y=h.length,d=new Array(y);E<y;E++)d[E]=h[E].fn;return d},o.prototype.listenerCount=function(L){var m=T?T+L:L,h=this._events[m];return h?h.fn?1:h.length:0},o.prototype.emit=function(L,m,h,E,y,d){var t=T?T+L:L;if(!this._events[t])return!1;var a,e,s=this._events[t],u=arguments.length;if(s.fn){switch(s.once&&this.removeListener(L,s.fn,void 0,!0),u){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,m),!0;case 3:return s.fn.call(s.context,m,h),!0;case 4:return s.fn.call(s.context,m,h,E),!0;case 5:return s.fn.call(s.context,m,h,E,y),!0;case 6:return s.fn.call(s.context,m,h,E,y,d),!0}for(e=1,a=new Array(u-1);e<u;e++)a[e-1]=arguments[e];s.fn.apply(s.context,a)}else{var n,l=s.length;for(e=0;e<l;e++)switch(s[e].once&&this.removeListener(L,s[e].fn,void 0,!0),u){case 1:s[e].fn.call(s[e].context);break;case 2:s[e].fn.call(s[e].context,m);break;case 3:s[e].fn.call(s[e].context,m,h);break;case 4:s[e].fn.call(s[e].context,m,h,E);break;default:if(!a)for(n=1,a=new Array(u-1);n<u;n++)a[n-1]=arguments[n];s[e].fn.apply(s[e].context,a)}}return!0},o.prototype.on=function(L,m,h){return I(this,L,m,h,!1)},o.prototype.once=function(L,m,h){return I(this,L,m,h,!0)},o.prototype.removeListener=function(L,m,h,E){var y=T?T+L:L;if(!this._events[y])return this;if(!m)return k(this,y),this;var d=this._events[y];if(d.fn)d.fn!==m||E&&!d.once||h&&d.context!==h||k(this,y);else{for(var t=0,a=[],e=d.length;t<e;t++)(d[t].fn!==m||E&&!d[t].once||h&&d[t].context!==h)&&a.push(d[t]);a.length?this._events[y]=a.length===1?a[0]:a:k(this,y)}return this},o.prototype.removeAllListeners=function(L){var m;return L?(m=T?T+L:L,this._events[m]&&k(this,m)):(this._events=new A,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=T,o.EventEmitter=o,N.exports=o},"./node_modules/url-toolkit/src/url-toolkit.js":function(N,w,f){var _,T,A,R,I;_=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/?#]*)?((?:[^\/?#]*\/)*[^;?#]*)?(;[^?#]*)?(\?[^#]*)?(#[^]*)?$/,T=/^([^\/?#]*)([^]*)$/,A=/(?:\/|^)\.(?=\/)/g,R=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,I={buildAbsoluteURL:function(k,o,L){if(L=L||{},k=k.trim(),!(o=o.trim())){if(!L.alwaysNormalize)return k;var m=I.parseURL(k);if(!m)throw new Error("Error trying to parse base URL.");return m.path=I.normalizePath(m.path),I.buildURLFromParts(m)}var h=I.parseURL(o);if(!h)throw new Error("Error trying to parse relative URL.");if(h.scheme)return L.alwaysNormalize?(h.path=I.normalizePath(h.path),I.buildURLFromParts(h)):o;var E=I.parseURL(k);if(!E)throw new Error("Error trying to parse base URL.");if(!E.netLoc&&E.path&&E.path[0]!=="/"){var y=T.exec(E.path);E.netLoc=y[1],E.path=y[2]}E.netLoc&&!E.path&&(E.path="/");var d={scheme:E.scheme,netLoc:h.netLoc,path:null,params:h.params,query:h.query,fragment:h.fragment};if(!h.netLoc&&(d.netLoc=E.netLoc,h.path[0]!=="/"))if(h.path){var t=E.path,a=t.substring(0,t.lastIndexOf("/")+1)+h.path;d.path=I.normalizePath(a)}else d.path=E.path,h.params||(d.params=E.params,h.query||(d.query=E.query));return d.path===null&&(d.path=L.alwaysNormalize?I.normalizePath(h.path):h.path),I.buildURLFromParts(d)},parseURL:function(k){var o=_.exec(k);return o?{scheme:o[1]||"",netLoc:o[2]||"",path:o[3]||"",params:o[4]||"",query:o[5]||"",fragment:o[6]||""}:null},normalizePath:function(k){for(k=k.split("").reverse().join("").replace(A,"");k.length!==(k=k.replace(R,"")).length;);return k.split("").reverse().join("")},buildURLFromParts:function(k){return k.scheme+k.netLoc+k.path+k.params+k.query+k.fragment}},N.exports=I},"./node_modules/webworkify-webpack/index.js":function(N,w,f){function _(o){var L={};function m(E){if(L[E])return L[E].exports;var y=L[E]={i:E,l:!1,exports:{}};return o[E].call(y.exports,y,y.exports,m),y.l=!0,y.exports}m.m=o,m.c=L,m.i=function(E){return E},m.d=function(E,y,d){m.o(E,y)||Object.defineProperty(E,y,{configurable:!1,enumerable:!0,get:d})},m.r=function(E){Object.defineProperty(E,"__esModule",{value:!0})},m.n=function(E){var y=E&&E.__esModule?function(){return E.default}:function(){return E};return m.d(y,"a",y),y},m.o=function(E,y){return Object.prototype.hasOwnProperty.call(E,y)},m.p="/",m.oe=function(E){throw console.error(E),E};var h=m(m.s=ENTRY_MODULE);return h.default||h}var T="[\\.|\\-|\\+|\\w|/|@]+",A="\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)";function R(o){return(o+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function I(o,L,m){var h={};h[m]=[];var E=L.toString(),y=E.match(/^function\s?\w*\(\w+,\s*\w+,\s*(\w+)\)/);if(!y)return h;for(var d,t=y[1],a=new RegExp("(\\\\n|\\W)"+R(t)+A,"g");d=a.exec(E);)d[3]!=="dll-reference"&&h[m].push(d[3]);for(a=new RegExp("\\("+R(t)+'\\("(dll-reference\\s('+T+'))"\\)\\)'+A,"g");d=a.exec(E);)o[d[2]]||(h[m].push(d[1]),o[d[2]]=f(d[1]).m),h[d[2]]=h[d[2]]||[],h[d[2]].push(d[4]);for(var e,s=Object.keys(h),u=0;u<s.length;u++)for(var n=0;n<h[s[u]].length;n++)e=h[s[u]][n],isNaN(1*e)||(h[s[u]][n]=1*h[s[u]][n]);return h}function k(o){return Object.keys(o).reduce(function(L,m){return L||o[m].length>0},!1)}N.exports=function(o,L){L=L||{};var m={main:f.m},h=L.all?{main:Object.keys(m.main)}:function(a,e){for(var s={main:[e]},u={main:[]},n={main:{}};k(s);)for(var l=Object.keys(s),p=0;p<l.length;p++){var g=l[p],v=s[g].pop();if(n[g]=n[g]||{},!n[g][v]&&a[g][v]){n[g][v]=!0,u[g]=u[g]||[],u[g].push(v);for(var r=I(a,a[g][v],g),i=Object.keys(r),c=0;c<i.length;c++)s[i[c]]=s[i[c]]||[],s[i[c]]=s[i[c]].concat(r[i[c]])}}return u}(m,o),E="";Object.keys(h).filter(function(a){return a!=="main"}).forEach(function(a){for(var e=0;h[a][e];)e++;h[a].push(e),m[a][e]="(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",E=E+"var "+a+" = ("+_.toString().replace("ENTRY_MODULE",JSON.stringify(e))+")({"+h[a].map(function(s){return JSON.stringify(s)+": "+m[a][s].toString()}).join(",")+`});
`}),E=E+"new (("+_.toString().replace("ENTRY_MODULE",JSON.stringify(o))+")({"+h.main.map(function(a){return JSON.stringify(a)+": "+m.main[a].toString()}).join(",")+"}))(self);";var y=new window.Blob([E],{type:"text/javascript"});if(L.bare)return y;var d=(window.URL||window.webkitURL||window.mozURL||window.msURL).createObjectURL(y),t=new window.Worker(d);return t.objectURL=d,t}},"./src/config.ts":function(N,w,f){f.r(w),f.d(w,"hlsDefaultConfig",function(){return l}),f.d(w,"mergeConfig",function(){return p}),f.d(w,"enableStreamingMode",function(){return g});var _=f("./src/controller/abr-controller.ts"),T=f("./src/controller/audio-stream-controller.ts"),A=f("./src/controller/audio-track-controller.ts"),R=f("./src/controller/subtitle-stream-controller.ts"),I=f("./src/controller/subtitle-track-controller.ts"),k=f("./src/controller/buffer-controller.ts"),o=f("./src/controller/timeline-controller.ts"),L=f("./src/controller/cap-level-controller.ts"),m=f("./src/controller/fps-controller.ts"),h=f("./src/controller/eme-controller.ts"),E=f("./src/utils/xhr-loader.ts"),y=f("./src/utils/fetch-loader.ts"),d=f("./src/utils/cues.ts"),t=f("./src/utils/mediakeys-helper.ts"),a=f("./src/utils/logger.ts");function e(){return(e=Object.assign||function(v){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var c in i)Object.prototype.hasOwnProperty.call(i,c)&&(v[c]=i[c])}return v}).apply(this,arguments)}function s(v,r){var i=Object.keys(v);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(v);r&&(c=c.filter(function(S){return Object.getOwnPropertyDescriptor(v,S).enumerable})),i.push.apply(i,c)}return i}function u(v){for(var r=1;r<arguments.length;r++){var i=arguments[r]!=null?arguments[r]:{};r%2?s(Object(i),!0).forEach(function(c){n(v,c,i[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(v,Object.getOwnPropertyDescriptors(i)):s(Object(i)).forEach(function(c){Object.defineProperty(v,c,Object.getOwnPropertyDescriptor(i,c))})}return v}function n(v,r,i){return r in v?Object.defineProperty(v,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):v[r]=i,v}var l=u(u({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,maxBufferSize:6e7,maxBufferHole:.1,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,enableSoftwareAES:!0,manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,startLevel:void 0,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:E.default,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:_.default,bufferController:k.default,capLevelController:L.default,fpsController:m.default,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystemOptions:{},requestMediaKeySystemAccessFunc:t.requestMediaKeySystemAccess,testBandwidth:!0,progressive:!1,lowLatencyMode:!0},{cueHandler:d.default,enableCEA708Captions:!0,enableWebVTT:!0,enableIMSC1:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}),{},{subtitleStreamController:R.SubtitleStreamController,subtitleTrackController:I.default,timelineController:o.TimelineController,audioStreamController:T.default,audioTrackController:A.default,emeController:h.default});function p(v,r){if((r.liveSyncDurationCount||r.liveMaxLatencyDurationCount)&&(r.liveSyncDuration||r.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(r.liveMaxLatencyDurationCount!==void 0&&(r.liveSyncDurationCount===void 0||r.liveMaxLatencyDurationCount<=r.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(r.liveMaxLatencyDuration!==void 0&&(r.liveSyncDuration===void 0||r.liveMaxLatencyDuration<=r.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');return e({},v,r)}function g(v){var r=v.loader;r!==y.default&&r!==E.default?(a.logger.log("[config]: Custom loader detected, cannot enable progressive streaming"),v.progressive=!1):Object(y.fetchSupported)()&&(v.loader=y.default,v.progressive=!0,v.enableSoftwareAES=!0,a.logger.log("[config]: Progressive streaming enabled, using FetchLoader"))}},"./src/controller/abr-controller.ts":function(N,w,f){f.r(w);var _=f("./src/polyfills/number.ts"),T=f("./src/utils/ewma-bandwidth-estimator.ts"),A=f("./src/events.ts"),R=f("./src/utils/buffer-helper.ts"),I=f("./src/errors.ts"),k=f("./src/types/loader.ts"),o=f("./src/utils/logger.ts");function L(h,E){for(var y=0;y<E.length;y++){var d=E[y];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(h,d.key,d)}}var m=function(){function h(t){this.hls=void 0,this.lastLoadedFragLevel=0,this._nextAutoLevel=-1,this.timer=void 0,this.onCheck=this._abandonRulesCheck.bind(this),this.fragCurrent=null,this.partCurrent=null,this.bitrateTestDelay=0,this.bwEstimator=void 0,this.hls=t;var a=t.config;this.bwEstimator=new T.default(a.abrEwmaSlowVoD,a.abrEwmaFastVoD,a.abrEwmaDefaultEstimate),this.registerListeners()}var E,y,d=h.prototype;return d.registerListeners=function(){var t=this.hls;t.on(A.Events.FRAG_LOADING,this.onFragLoading,this),t.on(A.Events.FRAG_LOADED,this.onFragLoaded,this),t.on(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),t.on(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.on(A.Events.ERROR,this.onError,this)},d.unregisterListeners=function(){var t=this.hls;t.off(A.Events.FRAG_LOADING,this.onFragLoading,this),t.off(A.Events.FRAG_LOADED,this.onFragLoaded,this),t.off(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),t.off(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.off(A.Events.ERROR,this.onError,this)},d.destroy=function(){this.unregisterListeners(),this.clearTimer(),this.hls=this.onCheck=null,this.fragCurrent=this.partCurrent=null},d.onFragLoading=function(t,a){var e,s=a.frag;s.type===k.PlaylistLevelType.MAIN&&(this.timer||(this.fragCurrent=s,this.partCurrent=(e=a.part)!=null?e:null,this.timer=self.setInterval(this.onCheck,100)))},d.onLevelLoaded=function(t,a){var e=this.hls.config;a.details.live?this.bwEstimator.update(e.abrEwmaSlowLive,e.abrEwmaFastLive):this.bwEstimator.update(e.abrEwmaSlowVoD,e.abrEwmaFastVoD)},d._abandonRulesCheck=function(){var t=this.fragCurrent,a=this.partCurrent,e=this.hls,s=e.autoLevelEnabled,u=e.config,n=e.media;if(t&&n){var l=a?a.stats:t.stats,p=a?a.duration:t.duration;if(l.aborted)return o.logger.warn("frag loader destroy or aborted, disarm abandonRules"),this.clearTimer(),void(this._nextAutoLevel=-1);if(s&&!n.paused&&n.playbackRate&&n.readyState){var g=performance.now()-l.loading.start,v=Math.abs(n.playbackRate);if(!(g<=500*p/v)){var r=e.levels,i=e.minAutoLevel,c=r[t.level],S=l.total||Math.max(l.loaded,Math.round(p*c.maxBitrate/8)),b=Math.max(1,l.bwEstimate?l.bwEstimate/8:1e3*l.loaded/g),D=(S-l.loaded)/b,O=n.currentTime,C=(R.BufferHelper.bufferInfo(n,O,u.maxBufferHole).end-O)/v;if(!(C>=2*p/v||D<=C)){var x,P=Number.POSITIVE_INFINITY;for(x=t.level-1;x>i&&!((P=p*r[x].maxBitrate/(6.4*b))<C);x--);if(!(P>=D)){var F=this.bwEstimator.getEstimate();o.logger.warn("Fragment "+t.sn+(a?" part "+a.index:"")+" of level "+t.level+" is loading too slowly and will cause an underbuffer; aborting and switching to level "+x+`
      Current BW estimate: `+(Object(_.isFiniteNumber)(F)?(F/1024).toFixed(3):"Unknown")+` Kb/s
      Estimated load time for current fragment: `+D.toFixed(3)+` s
      Estimated load time for the next fragment: `+P.toFixed(3)+` s
      Time to underbuffer: `+C.toFixed(3)+" s"),e.nextLoadLevel=x,this.bwEstimator.sample(g,l.loaded),this.clearTimer(),t.loader&&(this.fragCurrent=this.partCurrent=null,t.loader.abort()),e.trigger(A.Events.FRAG_LOAD_EMERGENCY_ABORTED,{frag:t,part:a,stats:l})}}}}}},d.onFragLoaded=function(t,a){var e=a.frag,s=a.part;if(e.type===k.PlaylistLevelType.MAIN&&Object(_.isFiniteNumber)(e.sn)){var u=s?s.stats:e.stats,n=s?s.duration:e.duration;if(this.clearTimer(),this.lastLoadedFragLevel=e.level,this._nextAutoLevel=-1,this.hls.config.abrMaxWithRealBitrate){var l=this.hls.levels[e.level],p=(l.loaded?l.loaded.bytes:0)+u.loaded,g=(l.loaded?l.loaded.duration:0)+n;l.loaded={bytes:p,duration:g},l.realBitrate=Math.round(8*p/g)}if(e.bitrateTest){var v={stats:u,frag:e,part:s,id:e.type};this.onFragBuffered(A.Events.FRAG_BUFFERED,v),e.bitrateTest=!1}}},d.onFragBuffered=function(t,a){var e=a.frag,s=a.part,u=s?s.stats:e.stats;if(!u.aborted&&e.type===k.PlaylistLevelType.MAIN&&e.sn!=="initSegment"){var n=u.parsing.end-u.loading.start;this.bwEstimator.sample(n,u.loaded),u.bwEstimate=this.bwEstimator.getEstimate(),e.bitrateTest?this.bitrateTestDelay=n/1e3:this.bitrateTestDelay=0}},d.onError=function(t,a){switch(a.details){case I.ErrorDetails.FRAG_LOAD_ERROR:case I.ErrorDetails.FRAG_LOAD_TIMEOUT:this.clearTimer()}},d.clearTimer=function(){self.clearInterval(this.timer),this.timer=void 0},d.getNextABRAutoLevel=function(){var t=this.fragCurrent,a=this.partCurrent,e=this.hls,s=e.maxAutoLevel,u=e.config,n=e.minAutoLevel,l=e.media,p=a?a.duration:t?t.duration:0,g=l?l.currentTime:0,v=l&&l.playbackRate!==0?Math.abs(l.playbackRate):1,r=this.bwEstimator?this.bwEstimator.getEstimate():u.abrEwmaDefaultEstimate,i=(R.BufferHelper.bufferInfo(l,g,u.maxBufferHole).end-g)/v,c=this.findBestLevel(r,n,s,i,u.abrBandWidthFactor,u.abrBandWidthUpFactor);if(c>=0)return c;o.logger.trace((i?"rebuffering expected":"buffer is empty")+", finding optimal quality level");var S=p?Math.min(p,u.maxStarvationDelay):u.maxStarvationDelay,b=u.abrBandWidthFactor,D=u.abrBandWidthUpFactor;if(!i){var O=this.bitrateTestDelay;O&&(S=(p?Math.min(p,u.maxLoadingDelay):u.maxLoadingDelay)-O,o.logger.trace("bitrate test took "+Math.round(1e3*O)+"ms, set first fragment max fetchDuration to "+Math.round(1e3*S)+" ms"),b=D=1)}return c=this.findBestLevel(r,n,s,i+S,b,D),Math.max(c,0)},d.findBestLevel=function(t,a,e,s,u,n){for(var l,p=this.fragCurrent,g=this.partCurrent,v=this.lastLoadedFragLevel,r=this.hls.levels,i=r[v],c=!(i==null||(l=i.details)===null||l===void 0||!l.live),S=i==null?void 0:i.codecSet,b=g?g.duration:p?p.duration:0,D=e;D>=a;D--){var O=r[D];if(O&&(!S||O.codecSet===S)){var C=O.details,x=(g?C==null?void 0:C.partTarget:C==null?void 0:C.averagetargetduration)||b,P=void 0;P=D<=v?u*t:n*t;var F=r[D].maxBitrate,M=F*x/P;if(o.logger.trace("level/adjustedbw/bitrate/avgDuration/maxFetchDuration/fetchDuration: "+D+"/"+Math.round(P)+"/"+F+"/"+x+"/"+s+"/"+M),P>F&&(!M||c&&!this.bitrateTestDelay||M<s))return D}}return-1},E=h,(y=[{key:"nextAutoLevel",get:function(){var t=this._nextAutoLevel,a=this.bwEstimator;if(!(t===-1||a&&a.canEstimate()))return t;var e=this.getNextABRAutoLevel();return t!==-1&&(e=Math.min(t,e)),e},set:function(t){this._nextAutoLevel=t}}])&&L(E.prototype,y),h}();w.default=m},"./src/controller/audio-stream-controller.ts":function(N,w,f){f.r(w);var _=f("./src/polyfills/number.ts"),T=f("./src/controller/base-stream-controller.ts"),A=f("./src/events.ts"),R=f("./src/utils/buffer-helper.ts"),I=f("./src/controller/fragment-tracker.ts"),k=f("./src/types/level.ts"),o=f("./src/types/loader.ts"),L=f("./src/loader/fragment.ts"),m=f("./src/demux/chunk-cache.ts"),h=f("./src/demux/transmuxer-interface.ts"),E=f("./src/types/transmuxer.ts"),y=f("./src/controller/fragment-finders.ts"),d=f("./src/utils/discontinuities.ts"),t=f("./src/errors.ts"),a=f("./src/utils/logger.ts");function e(){return(e=Object.assign||function(n){for(var l=1;l<arguments.length;l++){var p=arguments[l];for(var g in p)Object.prototype.hasOwnProperty.call(p,g)&&(n[g]=p[g])}return n}).apply(this,arguments)}function s(n,l){return(s=Object.setPrototypeOf||function(p,g){return p.__proto__=g,p})(n,l)}var u=function(n){var l,p;function g(r,i){var c;return(c=n.call(this,r,i,"[audio-stream-controller]")||this).videoBuffer=null,c.videoTrackCC=-1,c.waitingVideoCC=-1,c.audioSwitch=!1,c.trackId=-1,c.waitingData=null,c.mainDetails=null,c.bufferFlushed=!1,c._registerListeners(),c}p=n,(l=g).prototype=Object.create(p.prototype),l.prototype.constructor=l,s(l,p);var v=g.prototype;return v.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null},v._registerListeners=function(){var r=this.hls;r.on(A.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.on(A.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.on(A.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.on(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.on(A.Events.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),r.on(A.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.on(A.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),r.on(A.Events.ERROR,this.onError,this),r.on(A.Events.BUFFER_RESET,this.onBufferReset,this),r.on(A.Events.BUFFER_CREATED,this.onBufferCreated,this),r.on(A.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.on(A.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),r.on(A.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v._unregisterListeners=function(){var r=this.hls;r.off(A.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.off(A.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.off(A.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.off(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.off(A.Events.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),r.off(A.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.off(A.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),r.off(A.Events.ERROR,this.onError,this),r.off(A.Events.BUFFER_RESET,this.onBufferReset,this),r.off(A.Events.BUFFER_CREATED,this.onBufferCreated,this),r.off(A.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.off(A.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),r.off(A.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v.onInitPtsFound=function(r,i){var c=i.frag,S=i.id,b=i.initPTS;if(S==="main"){var D=c.cc;this.initPTS[c.cc]=b,this.log("InitPTS for cc: "+D+" found from main: "+b),this.videoTrackCC=D,this.state===T.State.WAITING_INIT_PTS&&this.tick()}},v.startLoad=function(r){if(!this.levels)return this.startPosition=r,void(this.state=T.State.STOPPED);var i=this.lastCurrentTime;this.stopLoad(),this.setInterval(100),this.fragLoadError=0,i>0&&r===-1?(this.log("Override startPosition with lastCurrentTime @"+i.toFixed(3)),this.state=T.State.IDLE):(this.loadedmetadata=!1,this.state=T.State.WAITING_TRACK),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=r,this.tick()},v.doTick=function(){switch(this.state){case T.State.IDLE:this.doTickIdle();break;case T.State.WAITING_TRACK:var r,i=this.levels,c=this.trackId,S=i==null||(r=i[c])===null||r===void 0?void 0:r.details;if(S){if(this.waitForCdnTuneIn(S))break;this.state=T.State.WAITING_INIT_PTS}break;case T.State.FRAG_LOADING_WAITING_RETRY:var b,D=performance.now(),O=this.retryDate;(!O||D>=O||(b=this.media)!==null&&b!==void 0&&b.seeking)&&(this.log("RetryDate reached, switch back to IDLE state"),this.state=T.State.IDLE);break;case T.State.WAITING_INIT_PTS:var C=this.waitingData;if(C){var x=C.frag,P=C.part,F=C.cache,M=C.complete;if(this.initPTS[x.cc]!==void 0){this.waitingData=null,this.waitingVideoCC=-1,this.state=T.State.FRAG_LOADING;var B={frag:x,part:P,payload:F.flush(),networkDetails:null};this._handleFragmentLoadProgress(B),M&&n.prototype._handleFragmentLoadComplete.call(this,B)}else if(this.videoTrackCC!==this.waitingVideoCC)a.logger.log("Waiting fragment cc ("+x.cc+") cancelled because video is at cc "+this.videoTrackCC),this.clearWaitingFragment();else{var U=this.getLoadPosition(),G=R.BufferHelper.bufferInfo(this.mediaBuffer,U,this.config.maxBufferHole);Object(y.fragmentWithinToleranceTest)(G.end,this.config.maxFragLookUpTolerance,x)<0&&(a.logger.log("Waiting fragment cc ("+x.cc+") @ "+x.start+" cancelled because another fragment at "+G.end+" is needed"),this.clearWaitingFragment())}}else this.state=T.State.IDLE}this.onTickEnd()},v.clearWaitingFragment=function(){var r=this.waitingData;r&&(this.fragmentTracker.removeFragment(r.frag),this.waitingData=null,this.waitingVideoCC=-1,this.state=T.State.IDLE)},v.onTickEnd=function(){var r=this.media;if(r&&r.readyState){var i=(this.mediaBuffer?this.mediaBuffer:r).buffered;!this.loadedmetadata&&i.length&&(this.loadedmetadata=!0),this.lastCurrentTime=r.currentTime}},v.doTickIdle=function(){var r,i,c=this.hls,S=this.levels,b=this.media,D=this.trackId,O=c.config;if(S&&S[D]&&(b||!this.startFragRequested&&O.startFragPrefetch)){var C=S[D].details;if(!C||C.live&&this.levelLastLoaded!==D||this.waitForCdnTuneIn(C))this.state=T.State.WAITING_TRACK;else{this.bufferFlushed&&(this.bufferFlushed=!1,this.afterBufferFlushed(this.mediaBuffer?this.mediaBuffer:this.media,L.ElementaryStreamTypes.AUDIO,o.PlaylistLevelType.AUDIO));var x=this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:this.media,o.PlaylistLevelType.AUDIO);if(x!==null){var P=x.len,F=this.getMaxBufferLength(),M=this.audioSwitch;if(!(P>=F)||M){if(!M&&this._streamEnded(x,C))return c.trigger(A.Events.BUFFER_EOS,{type:"audio"}),void(this.state=T.State.ENDED);var B=C.fragments[0].start,U=x.end;if(M){var G=this.getLoadPosition();U=G,C.PTSKnown&&G<B&&(x.end>B||x.nextStart)&&(this.log("Alt audio track ahead of main track, seek to start of alt audio track"),b.currentTime=B+.05)}var K=this.getNextFragment(U,C);K?((r=K.decryptdata)===null||r===void 0?void 0:r.keyFormat)!=="identity"||(i=K.decryptdata)!==null&&i!==void 0&&i.key?this.loadFragment(K,C,U):this.loadKey(K,C):this.bufferFlushed=!0}}}}},v.getMaxBufferLength=function(){var r=n.prototype.getMaxBufferLength.call(this),i=this.getFwdBufferInfo(this.videoBuffer?this.videoBuffer:this.media,o.PlaylistLevelType.MAIN);return i===null?r:Math.max(r,i.len)},v.onMediaDetaching=function(){this.videoBuffer=null,n.prototype.onMediaDetaching.call(this)},v.onAudioTracksUpdated=function(r,i){var c=i.audioTracks;this.resetTransmuxer(),this.levels=c.map(function(S){return new k.Level(S)})},v.onAudioTrackSwitching=function(r,i){var c=!!i.url;this.trackId=i.id;var S=this.fragCurrent;S!=null&&S.loader&&S.loader.abort(),this.fragCurrent=null,this.clearWaitingFragment(),c?this.setInterval(100):this.resetTransmuxer(),c?(this.audioSwitch=!0,this.state=T.State.IDLE):this.state=T.State.STOPPED,this.tick()},v.onManifestLoading=function(){this.mainDetails=null,this.fragmentTracker.removeAllFragments(),this.startPosition=this.lastCurrentTime=0,this.bufferFlushed=!1},v.onLevelLoaded=function(r,i){this.mainDetails=i.details},v.onAudioTrackLoaded=function(r,i){var c,S=this.levels,b=i.details,D=i.id;if(S){this.log("Track "+D+" loaded ["+b.startSN+","+b.endSN+"],duration:"+b.totalduration);var O=S[D],C=0;if(b.live||(c=O.details)!==null&&c!==void 0&&c.live){var x=this.mainDetails;if(b.fragments[0]||(b.deltaUpdateFailed=!0),b.deltaUpdateFailed||!x)return;!O.details&&b.hasProgramDateTime&&x.hasProgramDateTime?(Object(d.alignPDT)(b,x),C=b.fragments[0].start):C=this.alignPlaylists(b,O.details)}O.details=b,this.levelLastLoaded=D,this.startFragRequested||!this.mainDetails&&b.live||this.setStartPosition(O.details,C),this.state!==T.State.WAITING_TRACK||this.waitForCdnTuneIn(b)||(this.state=T.State.IDLE),this.tick()}else this.warn("Audio tracks were reset while loading level "+D)},v._handleFragmentLoadProgress=function(r){var i,c=r.frag,S=r.part,b=r.payload,D=this.config,O=this.trackId,C=this.levels;if(C){var x=C[O];console.assert(x,"Audio track is defined on fragment load progress");var P=x.details;console.assert(P,"Audio track details are defined on fragment load progress");var F=D.defaultAudioCodec||x.audioCodec||"mp4a.40.2",M=this.transmuxer;M||(M=this.transmuxer=new h.default(this.hls,o.PlaylistLevelType.AUDIO,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)));var B=this.initPTS[c.cc],U=(i=c.initSegment)===null||i===void 0?void 0:i.data;if(B!==void 0){var G=S?S.index:-1,K=G!==-1,H=new E.ChunkMetadata(c.level,c.sn,c.stats.chunkCount,b.byteLength,G,K);M.push(b,U,F,"",c,S,P.totalduration,!1,H,B)}else a.logger.log("Unknown video PTS for cc "+c.cc+", waiting for video PTS before demuxing audio frag "+c.sn+" of ["+P.startSN+" ,"+P.endSN+"],track "+O),(this.waitingData=this.waitingData||{frag:c,part:S,cache:new m.default,complete:!1}).cache.push(new Uint8Array(b)),this.waitingVideoCC=this.videoTrackCC,this.state=T.State.WAITING_INIT_PTS}else this.warn("Audio tracks were reset while fragment load was in progress. Fragment "+c.sn+" of level "+c.level+" will not be buffered")},v._handleFragmentLoadComplete=function(r){this.waitingData?this.waitingData.complete=!0:n.prototype._handleFragmentLoadComplete.call(this,r)},v.onBufferReset=function(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1},v.onBufferCreated=function(r,i){var c=i.tracks.audio;c&&(this.mediaBuffer=c.buffer),i.tracks.video&&(this.videoBuffer=i.tracks.video.buffer)},v.onFragBuffered=function(r,i){var c=i.frag,S=i.part;c.type===o.PlaylistLevelType.AUDIO&&(this.fragContextChanged(c)?this.warn("Fragment "+c.sn+(S?" p: "+S.index:"")+" of level "+c.level+" finished buffering, but was aborted. state: "+this.state+", audioSwitch: "+this.audioSwitch):(c.sn!=="initSegment"&&(this.fragPrevious=c,this.audioSwitch&&(this.audioSwitch=!1,this.hls.trigger(A.Events.AUDIO_TRACK_SWITCHED,{id:this.trackId}))),this.fragBufferedComplete(c,S)))},v.onError=function(r,i){switch(i.details){case t.ErrorDetails.FRAG_LOAD_ERROR:case t.ErrorDetails.FRAG_LOAD_TIMEOUT:case t.ErrorDetails.KEY_LOAD_ERROR:case t.ErrorDetails.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(o.PlaylistLevelType.AUDIO,i);break;case t.ErrorDetails.AUDIO_TRACK_LOAD_ERROR:case t.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT:this.state!==T.State.ERROR&&this.state!==T.State.STOPPED&&(this.state=i.fatal?T.State.ERROR:T.State.IDLE,this.warn(i.details+" while loading frag, switching to "+this.state+" state"));break;case t.ErrorDetails.BUFFER_FULL_ERROR:if(i.parent==="audio"&&(this.state===T.State.PARSING||this.state===T.State.PARSED)){var c=!0,S=this.getFwdBufferInfo(this.mediaBuffer,o.PlaylistLevelType.AUDIO);S&&S.len>.5&&(c=!this.reduceMaxBufferLength(S.len)),c&&(this.warn("Buffer full error also media.currentTime is not buffered, flush audio buffer"),this.fragCurrent=null,n.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio")),this.resetLoadingState()}}},v.onBufferFlushed=function(r,i){i.type===L.ElementaryStreamTypes.AUDIO&&(this.bufferFlushed=!0)},v._handleTransmuxComplete=function(r){var i,c="audio",S=this.hls,b=r.remuxResult,D=r.chunkMeta,O=this.getCurrentContext(D);if(!O)return this.warn("The loading context changed while buffering fragment "+D.sn+" of level "+D.level+". This chunk will not be buffered."),void this.resetLiveStartWhenNotLoaded(D.level);var C=O.frag,x=O.part,P=b.audio,F=b.text,M=b.id3,B=b.initSegment;if(!this.fragContextChanged(C)){if(this.state=T.State.PARSING,this.audioSwitch&&P&&this.completeAudioSwitch(),B!=null&&B.tracks&&(this._bufferInitSegment(B.tracks,C,D),S.trigger(A.Events.FRAG_PARSING_INIT_SEGMENT,{frag:C,id:c,tracks:B.tracks})),P){var U=P.startPTS,G=P.endPTS,K=P.startDTS,H=P.endDTS;x&&(x.elementaryStreams[L.ElementaryStreamTypes.AUDIO]={startPTS:U,endPTS:G,startDTS:K,endDTS:H}),C.setElementaryStreamInfo(L.ElementaryStreamTypes.AUDIO,U,G,K,H),this.bufferFragmentData(P,C,x,D)}if(M!=null&&(i=M.samples)!==null&&i!==void 0&&i.length){var Y=e({frag:C,id:c},M);S.trigger(A.Events.FRAG_PARSING_METADATA,Y)}if(F){var W=e({frag:C,id:c},F);S.trigger(A.Events.FRAG_PARSING_USERDATA,W)}}},v._bufferInitSegment=function(r,i,c){if(this.state===T.State.PARSING){r.video&&delete r.video;var S=r.audio;if(S){S.levelCodec=S.codec,S.id="audio",this.log("Init audio buffer, container:"+S.container+", codecs[parsed]=["+S.codec+"]"),this.hls.trigger(A.Events.BUFFER_CODECS,r);var b=S.initSegment;if(b!=null&&b.byteLength){var D={type:"audio",frag:i,part:null,chunkMeta:c,parent:i.type,data:b};this.hls.trigger(A.Events.BUFFER_APPENDING,D)}this.tick()}}},v.loadFragment=function(r,i,c){var S=this.fragmentTracker.getState(r);this.fragCurrent=r,(this.audioSwitch||S===I.FragmentState.NOT_LOADED||S===I.FragmentState.PARTIAL)&&(r.sn==="initSegment"?this._loadInitSegment(r):i.live&&!Object(_.isFiniteNumber)(this.initPTS[r.cc])?(this.log("Waiting for video PTS in continuity counter "+r.cc+" of live stream before loading audio fragment "+r.sn+" of level "+this.trackId),this.state=T.State.WAITING_INIT_PTS):(this.startFragRequested=!0,n.prototype.loadFragment.call(this,r,i,c)))},v.completeAudioSwitch=function(){var r=this.hls,i=this.media,c=this.trackId;i&&(this.log("Switching audio track : flushing all audio"),n.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio")),this.audioSwitch=!1,r.trigger(A.Events.AUDIO_TRACK_SWITCHED,{id:c})},g}(T.default);w.default=u},"./src/controller/audio-track-controller.ts":function(N,w,f){f.r(w);var _=f("./src/events.ts"),T=f("./src/errors.ts"),A=f("./src/controller/base-playlist-controller.ts"),R=f("./src/types/loader.ts");function I(L,m){for(var h=0;h<m.length;h++){var E=m[h];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(L,E.key,E)}}function k(L,m){return(k=Object.setPrototypeOf||function(h,E){return h.__proto__=E,h})(L,m)}var o=function(L){var m,h;function E(a){var e;return(e=L.call(this,a,"[audio-track-controller]")||this).tracks=[],e.groupId=null,e.tracksInGroup=[],e.trackId=-1,e.trackName="",e.selectDefaultTrack=!0,e.registerListeners(),e}h=L,(m=E).prototype=Object.create(h.prototype),m.prototype.constructor=m,k(m,h);var y,d,t=E.prototype;return t.registerListeners=function(){var a=this.hls;a.on(_.Events.MANIFEST_LOADING,this.onManifestLoading,this),a.on(_.Events.MANIFEST_PARSED,this.onManifestParsed,this),a.on(_.Events.LEVEL_LOADING,this.onLevelLoading,this),a.on(_.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),a.on(_.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),a.on(_.Events.ERROR,this.onError,this)},t.unregisterListeners=function(){var a=this.hls;a.off(_.Events.MANIFEST_LOADING,this.onManifestLoading,this),a.off(_.Events.MANIFEST_PARSED,this.onManifestParsed,this),a.off(_.Events.LEVEL_LOADING,this.onLevelLoading,this),a.off(_.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),a.off(_.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),a.off(_.Events.ERROR,this.onError,this)},t.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,L.prototype.destroy.call(this)},t.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.trackName="",this.selectDefaultTrack=!0},t.onManifestParsed=function(a,e){this.tracks=e.audioTracks||[]},t.onAudioTrackLoaded=function(a,e){var s=e.id,u=e.details,n=this.tracksInGroup[s];if(n){var l=n.details;n.details=e.details,this.log("audioTrack "+s+" loaded ["+u.startSN+"-"+u.endSN+"]"),s===this.trackId&&(this.retryCount=0,this.playlistLoaded(s,e,l))}else this.warn("Invalid audio track id "+s)},t.onLevelLoading=function(a,e){this.switchLevel(e.level)},t.onLevelSwitching=function(a,e){this.switchLevel(e.level)},t.switchLevel=function(a){var e=this.hls.levels[a];if(e!=null&&e.audioGroupIds){var s=e.audioGroupIds[e.urlId];if(this.groupId!==s){this.groupId=s;var u=this.tracks.filter(function(l){return!s||l.groupId===s});this.selectDefaultTrack&&!u.some(function(l){return l.default})&&(this.selectDefaultTrack=!1),this.tracksInGroup=u;var n={audioTracks:u};this.log("Updating audio tracks, "+u.length+' track(s) found in "'+s+'" group-id'),this.hls.trigger(_.Events.AUDIO_TRACKS_UPDATED,n),this.selectInitialTrack()}}},t.onError=function(a,e){L.prototype.onError.call(this,a,e),!e.fatal&&e.context&&e.context.type===R.PlaylistContextType.AUDIO_TRACK&&e.context.id===this.trackId&&e.context.groupId===this.groupId&&this.retryLoadingOrFail(e)},t.setAudioTrack=function(a){var e=this.tracksInGroup;if(a<0||a>=e.length)this.warn("Invalid id passed to audio-track controller");else{this.clearTimer();var s=e[this.trackId];this.log("Now switching to audio-track index "+a);var u=e[a],n=u.id,l=u.groupId,p=l===void 0?"":l,g=u.name,v=u.type,r=u.url;if(this.trackId=a,this.trackName=g,this.selectDefaultTrack=!1,this.hls.trigger(_.Events.AUDIO_TRACK_SWITCHING,{id:n,groupId:p,name:g,type:v,url:r}),!u.details||u.details.live){var i=this.switchParams(u.url,s==null?void 0:s.details);this.loadPlaylist(i)}}},t.selectInitialTrack=function(){var a=this.tracksInGroup;console.assert(a.length,"Initial audio track should be selected when tracks are known");var e=this.trackName,s=this.findTrackId(e)||this.findTrackId();s!==-1?this.setAudioTrack(s):(this.warn("No track found for running audio group-ID: "+this.groupId),this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.MEDIA_ERROR,details:T.ErrorDetails.AUDIO_TRACK_LOAD_ERROR,fatal:!0}))},t.findTrackId=function(a){for(var e=this.tracksInGroup,s=0;s<e.length;s++){var u=e[s];if((!this.selectDefaultTrack||u.default)&&(!a||a===u.name))return u.id}return-1},t.loadPlaylist=function(a){var e=this.tracksInGroup[this.trackId];if(this.shouldLoadTrack(e)){var s=e.id,u=e.groupId,n=e.url;if(a)try{n=a.addDirectives(n)}catch(l){this.warn("Could not construct new URL with HLS Delivery Directives: "+l)}this.log("loading audio-track playlist for id: "+s),this.clearTimer(),this.hls.trigger(_.Events.AUDIO_TRACK_LOADING,{url:n,id:s,groupId:u,deliveryDirectives:a||null})}},y=E,(d=[{key:"audioTracks",get:function(){return this.tracksInGroup}},{key:"audioTrack",get:function(){return this.trackId},set:function(a){this.selectDefaultTrack=!1,this.setAudioTrack(a)}}])&&I(y.prototype,d),E}(A.default);w.default=o},"./src/controller/base-playlist-controller.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return k});var _=f("./src/polyfills/number.ts"),T=f("./src/types/level.ts"),A=f("./src/controller/level-helper.ts"),R=f("./src/utils/logger.ts"),I=f("./src/errors.ts"),k=function(){function o(m,h){this.hls=void 0,this.timer=-1,this.canLoad=!1,this.retryCount=0,this.log=void 0,this.warn=void 0,this.log=R.logger.log.bind(R.logger,h+":"),this.warn=R.logger.warn.bind(R.logger,h+":"),this.hls=m}var L=o.prototype;return L.destroy=function(){this.clearTimer(),this.hls=this.log=this.warn=null},L.onError=function(m,h){h.fatal&&h.type===I.ErrorTypes.NETWORK_ERROR&&this.clearTimer()},L.clearTimer=function(){clearTimeout(this.timer),this.timer=-1},L.startLoad=function(){this.canLoad=!0,this.retryCount=0,this.loadPlaylist()},L.stopLoad=function(){this.canLoad=!1,this.clearTimer()},L.switchParams=function(m,h){var E=h==null?void 0:h.renditionReports;if(E)for(var y=0;y<E.length;y++){var d=E[y],t=""+d.URI;if(t===m.substr(-t.length)){var a=parseInt(d["LAST-MSN"]),e=parseInt(d["LAST-PART"]);if(h&&this.hls.config.lowLatencyMode){var s=Math.min(h.age-h.partTarget,h.targetduration);e!==void 0&&s>h.partTarget&&(e+=1)}if(Object(_.isFiniteNumber)(a))return new T.HlsUrlParameters(a,Object(_.isFiniteNumber)(e)?e:void 0,T.HlsSkip.No)}}},L.loadPlaylist=function(m){},L.shouldLoadTrack=function(m){return this.canLoad&&m&&!!m.url&&(!m.details||m.details.live)},L.playlistLoaded=function(m,h,E){var y=this,d=h.details,t=h.stats,a=t.loading.end?Math.max(0,self.performance.now()-t.loading.end):0;if(d.advancedDateTime=Date.now()-a,d.live||E!=null&&E.live){if(d.reloaded(E),E&&this.log("live playlist "+m+" "+(d.advanced?"REFRESHED "+d.lastPartSn+"-"+d.lastPartIndex:"MISSED")),E&&d.fragments.length>0&&Object(A.mergeDetails)(E,d),!this.canLoad||!d.live)return;var e,s=void 0,u=void 0;if(d.canBlockReload&&d.endSN&&d.advanced){var n=this.hls.config.lowLatencyMode,l=d.lastPartSn,p=d.endSN,g=d.lastPartIndex,v=l===p;g!==-1?(s=v?p+1:l,u=v?n?0:g:g+1):s=p+1;var r=d.age,i=r+d.ageHeader,c=Math.min(i-d.partTarget,1.5*d.targetduration);if(c>0){if(E&&c>E.tuneInGoal)this.warn("CDN Tune-in goal increased from: "+E.tuneInGoal+" to: "+c+" with playlist age: "+d.age),c=0;else{var S=Math.floor(c/d.targetduration);s+=S,u!==void 0&&(u+=Math.round(c%d.targetduration/d.partTarget)),this.log("CDN Tune-in age: "+d.ageHeader+"s last advanced "+r.toFixed(2)+"s goal: "+c+" skip sn "+S+" to part "+u)}d.tuneInGoal=c}if(e=this.getDeliveryDirectives(d,h.deliveryDirectives,s,u),n||!v)return void this.loadPlaylist(e)}else e=this.getDeliveryDirectives(d,h.deliveryDirectives,s,u);var b=Object(A.computeReloadInterval)(d,t);s!==void 0&&d.canBlockReload&&(b-=d.partTarget||1),this.log("reload live playlist "+m+" in "+Math.round(b)+" ms"),this.timer=self.setTimeout(function(){return y.loadPlaylist(e)},b)}else this.clearTimer()},L.getDeliveryDirectives=function(m,h,E,y){var d=Object(T.getSkipValue)(m,E);return h!=null&&h.skip&&m.deltaUpdateFailed&&(E=h.msn,y=h.part,d=T.HlsSkip.No),new T.HlsUrlParameters(E,y,d)},L.retryLoadingOrFail=function(m){var h,E=this,y=this.hls.config,d=this.retryCount<y.levelLoadingMaxRetry;if(d)if(this.retryCount++,m.details.indexOf("LoadTimeOut")>-1&&(h=m.context)!==null&&h!==void 0&&h.deliveryDirectives)this.warn("retry playlist loading #"+this.retryCount+' after "'+m.details+'"'),this.loadPlaylist();else{var t=Math.min(Math.pow(2,this.retryCount)*y.levelLoadingRetryDelay,y.levelLoadingMaxRetryTimeout);this.timer=self.setTimeout(function(){return E.loadPlaylist()},t),this.warn("retry playlist loading #"+this.retryCount+" in "+t+' ms after "'+m.details+'"')}else this.warn('cannot recover from error "'+m.details+'"'),this.clearTimer(),m.fatal=!0;return d},o}()},"./src/controller/base-stream-controller.ts":function(N,w,f){f.r(w),f.d(w,"State",function(){return n}),f.d(w,"default",function(){return l});var _=f("./src/polyfills/number.ts"),T=f("./src/task-loop.ts"),A=f("./src/controller/fragment-tracker.ts"),R=f("./src/utils/buffer-helper.ts"),I=f("./src/utils/logger.ts"),k=f("./src/events.ts"),o=f("./src/errors.ts"),L=f("./src/types/transmuxer.ts"),m=f("./src/utils/mp4-tools.ts"),h=f("./src/utils/discontinuities.ts"),E=f("./src/controller/fragment-finders.ts"),y=f("./src/controller/level-helper.ts"),d=f("./src/loader/fragment-loader.ts"),t=f("./src/crypt/decrypter.ts"),a=f("./src/utils/time-ranges.ts"),e=f("./src/types/loader.ts");function s(p,g){for(var v=0;v<g.length;v++){var r=g[v];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(p,r.key,r)}}function u(p,g){return(u=Object.setPrototypeOf||function(v,r){return v.__proto__=r,v})(p,g)}var n={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_TRACK:"WAITING_TRACK",PARSING:"PARSING",PARSED:"PARSED",BACKTRACKING:"BACKTRACKING",ENDED:"ENDED",ERROR:"ERROR",WAITING_INIT_PTS:"WAITING_INIT_PTS",WAITING_LEVEL:"WAITING_LEVEL"},l=function(p){var g,v;function r(b,D,O){var C;return(C=p.call(this)||this).hls=void 0,C.fragPrevious=null,C.fragCurrent=null,C.fragmentTracker=void 0,C.transmuxer=null,C._state=n.STOPPED,C.media=void 0,C.mediaBuffer=void 0,C.config=void 0,C.bitrateTest=!1,C.lastCurrentTime=0,C.nextLoadPosition=0,C.startPosition=0,C.loadedmetadata=!1,C.fragLoadError=0,C.retryDate=0,C.levels=null,C.fragmentLoader=void 0,C.levelLastLoaded=null,C.startFragRequested=!1,C.decrypter=void 0,C.initPTS=[],C.onvseeking=null,C.onvended=null,C.logPrefix="",C.log=void 0,C.warn=void 0,C.logPrefix=O,C.log=I.logger.log.bind(I.logger,O+":"),C.warn=I.logger.warn.bind(I.logger,O+":"),C.hls=b,C.fragmentLoader=new d.default(b.config),C.fragmentTracker=D,C.config=b.config,C.decrypter=new t.default(b,b.config),b.on(k.Events.KEY_LOADED,C.onKeyLoaded,function(x){if(x===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return x}(C)),C}v=p,(g=r).prototype=Object.create(v.prototype),g.prototype.constructor=g,u(g,v);var i,c,S=r.prototype;return S.doTick=function(){this.onTickEnd()},S.onTickEnd=function(){},S.startLoad=function(b){},S.stopLoad=function(){this.fragmentLoader.abort();var b=this.fragCurrent;b&&this.fragmentTracker.removeFragment(b),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=n.STOPPED},S._streamEnded=function(b,D){var O=this.fragCurrent,C=this.fragmentTracker;if(!D.live&&O&&O.sn===D.endSN&&!b.nextStart){var x=C.getState(O);return x===A.FragmentState.PARTIAL||x===A.FragmentState.OK}return!1},S.onMediaAttached=function(b,D){var O=this.media=this.mediaBuffer=D.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),O.addEventListener("seeking",this.onvseeking),O.addEventListener("ended",this.onvended);var C=this.config;this.levels&&C.autoStartLoad&&this.state===n.STOPPED&&this.startLoad(C.startPosition)},S.onMediaDetaching=function(){var b=this.media;b!=null&&b.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),b&&(b.removeEventListener("seeking",this.onvseeking),b.removeEventListener("ended",this.onvended),this.onvseeking=this.onvended=null),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()},S.onMediaSeeking=function(){var b=this.config,D=this.fragCurrent,O=this.media,C=this.mediaBuffer,x=this.state,P=O?O.currentTime:0,F=R.BufferHelper.bufferInfo(C||O,P,b.maxBufferHole);if(this.log("media seeking to "+(Object(_.isFiniteNumber)(P)?P.toFixed(3):P)+", state: "+x),x===n.ENDED)this.resetLoadingState();else if(D&&!F.len){var M=b.maxFragLookUpTolerance,B=D.start-M,U=P>D.start+D.duration+M;(P<B||U)&&(U&&D.loader&&(this.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),D.loader.abort()),this.resetLoadingState())}O&&(this.lastCurrentTime=P),this.loadedmetadata||F.len||(this.nextLoadPosition=this.startPosition=P),this.tickImmediate()},S.onMediaEnded=function(){this.startPosition=this.lastCurrentTime=0},S.onKeyLoaded=function(b,D){if(this.state===n.KEY_LOADING&&D.frag===this.fragCurrent&&this.levels){this.state=n.IDLE;var O=this.levels[D.frag.level].details;O&&this.loadFragment(D.frag,O,D.frag.start)}},S.onHandlerDestroying=function(){this.stopLoad(),p.prototype.onHandlerDestroying.call(this)},S.onHandlerDestroyed=function(){this.state=n.STOPPED,this.hls.off(k.Events.KEY_LOADED,this.onKeyLoaded,this),this.fragmentLoader&&this.fragmentLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.fragmentLoader=this.fragmentTracker=null,p.prototype.onHandlerDestroyed.call(this)},S.loadKey=function(b,D){this.log("Loading key for "+b.sn+" of ["+D.startSN+"-"+D.endSN+"], "+(this.logPrefix==="[stream-controller]"?"level":"track")+" "+b.level),this.state=n.KEY_LOADING,this.fragCurrent=b,this.hls.trigger(k.Events.KEY_LOADING,{frag:b})},S.loadFragment=function(b,D,O){this._loadFragForPlayback(b,D,O)},S._loadFragForPlayback=function(b,D,O){var C=this;this._doFragLoad(b,D,O,function(x){if(C.fragContextChanged(b))return C.warn("Fragment "+b.sn+(x.part?" p: "+x.part.index:"")+" of level "+b.level+" was dropped during download."),void C.fragmentTracker.removeFragment(b);b.stats.chunkCount++,C._handleFragmentLoadProgress(x)}).then(function(x){if(x){C.fragLoadError=0;var P=C.state;if(!C.fragContextChanged(b))return"payload"in x&&(C.log("Loaded fragment "+b.sn+" of level "+b.level),C.hls.trigger(k.Events.FRAG_LOADED,x),C.state===n.BACKTRACKING)?(C.fragmentTracker.backtrack(b,x),void C.resetFragmentLoading(b)):void C._handleFragmentLoadComplete(x);(P===n.FRAG_LOADING||P===n.BACKTRACKING||!C.fragCurrent&&P===n.PARSING)&&(C.fragmentTracker.removeFragment(b),C.state=n.IDLE)}}).catch(function(x){C.warn(x),C.resetFragmentLoading(b)})},S.flushMainBuffer=function(b,D,O){if(O===void 0&&(O=null),b-D){var C={startOffset:b,endOffset:D,type:O};this.fragLoadError=0,this.hls.trigger(k.Events.BUFFER_FLUSHING,C)}},S._loadInitSegment=function(b){var D=this;this._doFragLoad(b).then(function(O){if(!O||D.fragContextChanged(b)||!D.levels)throw new Error("init load aborted");return O}).then(function(O){var C=D.hls,x=O.payload,P=b.decryptdata;if(x&&x.byteLength>0&&P&&P.key&&P.iv&&P.method==="AES-128"){var F=self.performance.now();return D.decrypter.webCryptoDecrypt(new Uint8Array(x),P.key.buffer,P.iv.buffer).then(function(M){var B=self.performance.now();return C.trigger(k.Events.FRAG_DECRYPTED,{frag:b,payload:M,stats:{tstart:F,tdecrypt:B}}),O.payload=M,O})}return O}).then(function(O){var C=D.fragCurrent,x=D.hls,P=D.levels;if(!P)throw new Error("init load aborted, missing levels");var F=P[b.level].details;console.assert(F,"Level details are defined when init segment is loaded");var M=b.stats;D.state=n.IDLE,D.fragLoadError=0,b.data=new Uint8Array(O.payload),M.parsing.start=M.buffering.start=self.performance.now(),M.parsing.end=M.buffering.end=self.performance.now(),O.frag===C&&x.trigger(k.Events.FRAG_BUFFERED,{stats:M,frag:C,part:null,id:b.type}),D.tick()}).catch(function(O){D.warn(O),D.resetFragmentLoading(b)})},S.fragContextChanged=function(b){var D=this.fragCurrent;return!b||!D||b.level!==D.level||b.sn!==D.sn||b.urlId!==D.urlId},S.fragBufferedComplete=function(b,D){var O=this.mediaBuffer?this.mediaBuffer:this.media;this.log("Buffered "+b.type+" sn: "+b.sn+(D?" part: "+D.index:"")+" of "+(this.logPrefix==="[stream-controller]"?"level":"track")+" "+b.level+" "+a.default.toString(R.BufferHelper.getBuffered(O))),this.state=n.IDLE,this.tick()},S._handleFragmentLoadComplete=function(b){var D=this.transmuxer;if(D){var O=b.frag,C=b.part,x=b.partsLoaded,P=!x||x.length===0||x.some(function(M){return!M}),F=new L.ChunkMetadata(O.level,O.sn,O.stats.chunkCount+1,0,C?C.index:-1,!P);D.flush(F)}},S._handleFragmentLoadProgress=function(b){},S._doFragLoad=function(b,D,O,C){var x=this;if(O===void 0&&(O=null),!this.levels)throw new Error("frag load aborted, missing levels");if(O=Math.max(b.start,O||0),this.config.lowLatencyMode&&D){var P=D.partList;if(P&&C){O>b.end&&D.fragmentHint&&(b=D.fragmentHint);var F=this.getNextPart(P,b,O);if(F>-1){var M=P[F];return this.log("Loading part sn: "+b.sn+" p: "+M.index+" cc: "+b.cc+" of playlist ["+D.startSN+"-"+D.endSN+"] parts [0-"+F+"-"+(P.length-1)+"] "+(this.logPrefix==="[stream-controller]"?"level":"track")+": "+b.level+", target: "+parseFloat(O.toFixed(3))),this.nextLoadPosition=M.start+M.duration,this.state=n.FRAG_LOADING,this.hls.trigger(k.Events.FRAG_LOADING,{frag:b,part:P[F],targetBufferTime:O}),this.doFragPartsLoad(b,P,F,C).catch(function(B){return x.handleFragLoadError(B)})}if(!b.url||this.loadedEndOfParts(P,O))return Promise.resolve(null)}}return this.log("Loading fragment "+b.sn+" cc: "+b.cc+" "+(D?"of ["+D.startSN+"-"+D.endSN+"] ":"")+(this.logPrefix==="[stream-controller]"?"level":"track")+": "+b.level+", target: "+parseFloat(O.toFixed(3))),Object(_.isFiniteNumber)(b.sn)&&!this.bitrateTest&&(this.nextLoadPosition=b.start+b.duration),this.state=n.FRAG_LOADING,this.hls.trigger(k.Events.FRAG_LOADING,{frag:b,targetBufferTime:O}),this.fragmentLoader.load(b,C).catch(function(B){return x.handleFragLoadError(B)})},S.doFragPartsLoad=function(b,D,O,C){var x=this;return new Promise(function(P,F){var M=[];(function B(U){var G=D[U];x.fragmentLoader.loadPart(b,G,C).then(function(K){M[G.index]=K;var H=K.part;x.hls.trigger(k.Events.FRAG_LOADED,K);var Y=D[U+1];if(!Y||Y.fragment!==b)return P({frag:b,part:H,partsLoaded:M});B(U+1)}).catch(F)})(O)})},S.handleFragLoadError=function(b){var D=b.data;return D&&D.details===o.ErrorDetails.INTERNAL_ABORTED?this.handleFragLoadAborted(D.frag,D.part):this.hls.trigger(k.Events.ERROR,D),null},S._handleTransmuxerFlush=function(b){var D=this.getCurrentContext(b);if(D&&this.state===n.PARSING){var O=D.frag,C=D.part,x=D.level,P=self.performance.now();O.stats.parsing.end=P,C&&(C.stats.parsing.end=P),this.updateLevelTiming(O,C,x,b.partial)}else this.fragCurrent||(this.state=n.IDLE)},S.getCurrentContext=function(b){var D=this.levels,O=b.level,C=b.sn,x=b.part;if(!D||!D[O])return this.warn("Levels object was unset while buffering fragment "+C+" of level "+O+". The current chunk will not be buffered."),null;var P=D[O],F=x>-1?Object(y.getPartWith)(P,C,x):null,M=F?F.fragment:Object(y.getFragmentWithSN)(P,C,this.fragCurrent);return M?{frag:M,part:F,level:P}:null},S.bufferFragmentData=function(b,D,O,C){if(b&&this.state===n.PARSING){var x=b.data1,P=b.data2,F=x;if(x&&P&&(F=Object(m.appendUint8Array)(x,P)),F&&F.length){var M={type:b.type,frag:D,part:O,chunkMeta:C,parent:D.type,data:F};this.hls.trigger(k.Events.BUFFER_APPENDING,M),b.dropped&&b.independent&&!O&&this.flushBufferGap(D)}}},S.flushBufferGap=function(b){var D=this.media;if(D)if(R.BufferHelper.isBuffered(D,D.currentTime)){var O=D.currentTime,C=R.BufferHelper.bufferInfo(D,O,0),x=b.duration,P=Math.min(2*this.config.maxFragLookUpTolerance,.25*x),F=Math.max(Math.min(b.start-P,C.end-P),O+P);b.start-F>P&&this.flushMainBuffer(F,b.start)}else this.flushMainBuffer(0,b.start)},S.getFwdBufferInfo=function(b,D){var O=this.config,C=this.getLoadPosition();if(!Object(_.isFiniteNumber)(C))return null;var x=R.BufferHelper.bufferInfo(b,C,O.maxBufferHole);if(x.len===0&&x.nextStart!==void 0){var P=this.fragmentTracker.getBufferedFrag(C,D);if(P&&x.nextStart<P.end)return R.BufferHelper.bufferInfo(b,C,Math.max(x.nextStart,O.maxBufferHole))}return x},S.getMaxBufferLength=function(b){var D,O=this.config;return D=b?Math.max(8*O.maxBufferSize/b,O.maxBufferLength):O.maxBufferLength,Math.min(D,O.maxMaxBufferLength)},S.reduceMaxBufferLength=function(b){var D=this.config,O=b||D.maxBufferLength;return D.maxMaxBufferLength>=O&&(D.maxMaxBufferLength/=2,this.warn("Reduce max buffer length to "+D.maxMaxBufferLength+"s"),!0)},S.getNextFragment=function(b,D){var O,C,x=D.fragments,P=x.length;if(!P)return null;var F,M=this.config,B=x[0].start;if(D.live){var U=M.initialLiveManifestSize;if(P<U)return this.warn("Not enough fragments to start playback (have: "+P+", need: "+U+")"),null;D.PTSKnown||this.startFragRequested||this.startPosition!==-1||(F=this.getInitialLiveFragment(D,x),this.startPosition=F?this.hls.liveSyncPosition||F.start:b)}else b<=B&&(F=x[0]);if(!F){var G=M.lowLatencyMode?D.partEnd:D.fragmentEnd;F=this.getFragmentAtPosition(b,G,D)}return(O=F)===null||O===void 0||!O.initSegment||(C=F)!==null&&C!==void 0&&C.initSegment.data||this.bitrateTest||(F=F.initSegment),F},S.getNextPart=function(b,D,O){for(var C=-1,x=!1,P=!0,F=0,M=b.length;F<M;F++){var B=b[F];if(P=P&&!B.independent,C>-1&&O<B.start)break;var U=B.loaded;!U&&(x||B.independent||P)&&B.fragment===D&&(C=F),x=U}return C},S.loadedEndOfParts=function(b,D){var O=b[b.length-1];return O&&D>O.start&&O.loaded},S.getInitialLiveFragment=function(b,D){var O=this.fragPrevious,C=null;if(O){if(b.hasProgramDateTime&&(this.log("Live playlist, switching playlist, load frag with same PDT: "+O.programDateTime),C=Object(E.findFragmentByPDT)(D,O.endProgramDateTime,this.config.maxFragLookUpTolerance)),!C){var x=O.sn+1;if(x>=b.startSN&&x<=b.endSN){var P=D[x-b.startSN];O.cc===P.cc&&(C=P,this.log("Live playlist, switching playlist, load frag with next SN: "+C.sn))}C||(C=Object(E.findFragWithCC)(D,O.cc))&&this.log("Live playlist, switching playlist, load frag with same CC: "+C.sn)}}else{var F=this.hls.liveSyncPosition;F!==null&&(C=this.getFragmentAtPosition(F,this.bitrateTest?b.fragmentEnd:b.edge,b))}return C},S.getFragmentAtPosition=function(b,D,O){var C,x=this.config,P=this.fragPrevious,F=O.fragments,M=O.endSN,B=O.fragmentHint,U=x.maxFragLookUpTolerance,G=!!(x.lowLatencyMode&&O.partList&&B);if(G&&B&&!this.bitrateTest&&(F=F.concat(B),M=B.sn),b<D){var K=b>D-U?0:U;C=Object(E.findFragmentByPTS)(P,F,b,K)}else C=F[F.length-1];if(C){var H=C.sn-O.startSN,Y=P&&C.level===P.level,W=F[H+1];if(this.fragmentTracker.getState(C)===A.FragmentState.BACKTRACKED){C=null;for(var q=H;F[q]&&this.fragmentTracker.getState(F[q])===A.FragmentState.BACKTRACKED;)C=P?F[q--]:F[--q];C||(C=W)}else P&&C.sn===P.sn&&!G&&Y&&(C.sn<M&&this.fragmentTracker.getState(W)!==A.FragmentState.OK?(this.log("SN "+C.sn+" just loaded, load next one: "+W.sn),C=W):C=null)}return C},S.synchronizeToLiveEdge=function(b){var D=this.config,O=this.media;if(O){var C=this.hls.liveSyncPosition,x=O.currentTime,P=b.fragments[0].start,F=b.edge,M=x>=P-D.maxFragLookUpTolerance&&x<=F;if(C!==null&&O.duration>C&&(x<C||!M)){var B=D.liveMaxLatencyDuration!==void 0?D.liveMaxLatencyDuration:D.liveMaxLatencyDurationCount*b.targetduration;(!M&&O.readyState<4||x<F-B)&&(this.loadedmetadata||(this.nextLoadPosition=C),O.readyState&&(this.warn("Playback: "+x.toFixed(3)+" is located too far from the end of live sliding playlist: "+F+", reset currentTime to : "+C.toFixed(3)),O.currentTime=C))}}},S.alignPlaylists=function(b,D){var O=this.levels,C=this.levelLastLoaded,x=this.fragPrevious,P=C!==null?O[C]:null,F=b.fragments.length;if(!F)return this.warn("No fragments in live playlist"),0;var M=b.fragments[0].start,B=!D,U=b.alignedSliding&&Object(_.isFiniteNumber)(M);if(B||!U&&!M){Object(h.alignStream)(x,P,b);var G=b.fragments[0].start;return this.log("Live playlist sliding: "+G.toFixed(2)+" start-sn: "+(D?D.startSN:"na")+"->"+b.startSN+" prev-sn: "+(x?x.sn:"na")+" fragments: "+F),G}return M},S.waitForCdnTuneIn=function(b){return b.live&&b.canBlockReload&&b.tuneInGoal>Math.max(b.partHoldBack,3*b.partTarget)},S.setStartPosition=function(b,D){var O=this.startPosition;if(O<D&&(O=-1),O===-1||this.lastCurrentTime===-1){var C=b.startTimeOffset;Object(_.isFiniteNumber)(C)?(O=D+C,C<0&&(O+=b.totalduration),O=Math.min(Math.max(D,O),D+b.totalduration),this.log("Start time offset "+C+" found in playlist, adjust startPosition to "+O),this.startPosition=O):b.live?O=this.hls.liveSyncPosition||D:this.startPosition=O=0,this.lastCurrentTime=O}this.nextLoadPosition=O},S.getLoadPosition=function(){var b=this.media,D=0;return this.loadedmetadata&&b?D=b.currentTime:this.nextLoadPosition&&(D=this.nextLoadPosition),D},S.handleFragLoadAborted=function(b,D){this.transmuxer&&b.sn!=="initSegment"&&b.stats.aborted&&(this.warn("Fragment "+b.sn+(D?" part"+D.index:"")+" of level "+b.level+" was aborted"),this.resetFragmentLoading(b))},S.resetFragmentLoading=function(b){this.fragCurrent&&this.fragContextChanged(b)||(this.state=n.IDLE)},S.onFragmentOrKeyLoadError=function(b,D){if(!D.fatal){var O=D.frag;if(O&&O.type===b){var C=this.fragCurrent;console.assert(C&&O.sn===C.sn&&O.level===C.level&&O.urlId===C.urlId,"Frag load error must match current frag to retry");var x=this.config;if(this.fragLoadError+1<=x.fragLoadingMaxRetry){if(this.resetLiveStartWhenNotLoaded(O.level))return;var P=Math.min(Math.pow(2,this.fragLoadError)*x.fragLoadingRetryDelay,x.fragLoadingMaxRetryTimeout);this.warn("Fragment "+O.sn+" of "+b+" "+O.level+" failed to load, retrying in "+P+"ms"),this.retryDate=self.performance.now()+P,this.fragLoadError++,this.state=n.FRAG_LOADING_WAITING_RETRY}else D.levelRetry?(b===e.PlaylistLevelType.AUDIO&&(this.fragCurrent=null),this.fragLoadError=0,this.state=n.IDLE):(I.logger.error(D.details+" reaches max retry, redispatch as fatal ..."),D.fatal=!0,this.hls.stopLoad(),this.state=n.ERROR)}}},S.afterBufferFlushed=function(b,D,O){if(b){var C=R.BufferHelper.getBuffered(b);this.fragmentTracker.detectEvictedFragments(D,C,O),this.state===n.ENDED&&this.resetLoadingState()}},S.resetLoadingState=function(){this.fragCurrent=null,this.fragPrevious=null,this.state=n.IDLE},S.resetLiveStartWhenNotLoaded=function(b){if(!this.loadedmetadata){this.startFragRequested=!1;var D=this.levels?this.levels[b].details:null;if(D!=null&&D.live)return this.startPosition=-1,this.setStartPosition(D,0),this.resetLoadingState(),!0;this.nextLoadPosition=this.startPosition}return!1},S.updateLevelTiming=function(b,D,O,C){var x=this,P=O.details;console.assert(!!P,"level.details must be defined"),Object.keys(b.elementaryStreams).reduce(function(F,M){var B=b.elementaryStreams[M];if(B){var U=B.endPTS-B.startPTS;if(U<=0)return x.warn("Could not parse fragment "+b.sn+" "+M+" duration reliably ("+U+") resetting transmuxer to fallback to playlist timing"),x.resetTransmuxer(),F||!1;var G=C?0:Object(y.updateFragPTSDTS)(P,b,B.startPTS,B.endPTS,B.startDTS,B.endDTS);return x.hls.trigger(k.Events.LEVEL_PTS_UPDATED,{details:P,level:O,drift:G,type:M,frag:b,start:B.startPTS,end:B.endPTS}),!0}return F},!1)?(this.state=n.PARSED,this.hls.trigger(k.Events.FRAG_PARSED,{frag:b,part:D})):this.resetLoadingState()},S.resetTransmuxer=function(){this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null)},i=r,(c=[{key:"state",get:function(){return this._state},set:function(b){var D=this._state;D!==b&&(this._state=b,this.log(D+"->"+b))}}])&&s(i.prototype,c),r}(T.default)},"./src/controller/buffer-controller.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return E});var _=f("./src/polyfills/number.ts"),T=f("./src/events.ts"),A=f("./src/utils/logger.ts"),R=f("./src/errors.ts"),I=f("./src/utils/buffer-helper.ts"),k=f("./src/utils/mediasource-helper.ts"),o=f("./src/loader/fragment.ts"),L=f("./src/controller/buffer-operation-queue.ts"),m=Object(k.getMediaSource)(),h=/([ha]vc.)(?:\.[^.,]+)+/,E=function(){function y(t){var a=this;this.details=null,this._objectUrl=null,this.operationQueue=void 0,this.listeners=void 0,this.hls=void 0,this.bufferCodecEventsExpected=0,this._bufferCodecEventsTotal=0,this.media=null,this.mediaSource=null,this.appendError=0,this.tracks={},this.pendingTracks={},this.sourceBuffer=void 0,this._onMediaSourceOpen=function(){var e=a.hls,s=a.media,u=a.mediaSource;A.logger.log("[buffer-controller]: Media source opened"),s&&(a.updateMediaElementDuration(),e.trigger(T.Events.MEDIA_ATTACHED,{media:s})),u&&u.removeEventListener("sourceopen",a._onMediaSourceOpen),a.checkPendingTracks()},this._onMediaSourceClose=function(){A.logger.log("[buffer-controller]: Media source closed")},this._onMediaSourceEnded=function(){A.logger.log("[buffer-controller]: Media source ended")},this.hls=t,this._initSourceBuffer(),this.registerListeners()}var d=y.prototype;return d.hasSourceTypes=function(){return this.getSourceBufferTypes().length>0||Object.keys(this.pendingTracks).length>0},d.destroy=function(){this.unregisterListeners(),this.details=null},d.registerListeners=function(){var t=this.hls;t.on(T.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(T.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.on(T.Events.BUFFER_RESET,this.onBufferReset,this),t.on(T.Events.BUFFER_APPENDING,this.onBufferAppending,this),t.on(T.Events.BUFFER_CODECS,this.onBufferCodecs,this),t.on(T.Events.BUFFER_EOS,this.onBufferEos,this),t.on(T.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(T.Events.LEVEL_UPDATED,this.onLevelUpdated,this),t.on(T.Events.FRAG_PARSED,this.onFragParsed,this),t.on(T.Events.FRAG_CHANGED,this.onFragChanged,this)},d.unregisterListeners=function(){var t=this.hls;t.off(T.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(T.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.off(T.Events.BUFFER_RESET,this.onBufferReset,this),t.off(T.Events.BUFFER_APPENDING,this.onBufferAppending,this),t.off(T.Events.BUFFER_CODECS,this.onBufferCodecs,this),t.off(T.Events.BUFFER_EOS,this.onBufferEos,this),t.off(T.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(T.Events.LEVEL_UPDATED,this.onLevelUpdated,this),t.off(T.Events.FRAG_PARSED,this.onFragParsed,this),t.off(T.Events.FRAG_CHANGED,this.onFragChanged,this)},d._initSourceBuffer=function(){this.sourceBuffer={},this.operationQueue=new L.default(this.sourceBuffer),this.listeners={audio:[],video:[],audiovideo:[]}},d.onManifestParsed=function(t,a){var e=2;(a.audio&&!a.video||!a.altAudio)&&(e=1),this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=e,this.details=null,A.logger.log(this.bufferCodecEventsExpected+" bufferCodec event(s) expected")},d.onMediaAttaching=function(t,a){var e=this.media=a.media;if(e&&m){var s=this.mediaSource=new m;s.addEventListener("sourceopen",this._onMediaSourceOpen),s.addEventListener("sourceended",this._onMediaSourceEnded),s.addEventListener("sourceclose",this._onMediaSourceClose),e.src=self.URL.createObjectURL(s),this._objectUrl=e.src}},d.onMediaDetaching=function(){var t=this.media,a=this.mediaSource,e=this._objectUrl;if(a){if(A.logger.log("[buffer-controller]: media source detaching"),a.readyState==="open")try{a.endOfStream()}catch(s){A.logger.warn("[buffer-controller]: onMediaDetaching: "+s.message+" while calling endOfStream")}this.onBufferReset(),a.removeEventListener("sourceopen",this._onMediaSourceOpen),a.removeEventListener("sourceended",this._onMediaSourceEnded),a.removeEventListener("sourceclose",this._onMediaSourceClose),t&&(e&&self.URL.revokeObjectURL(e),t.src===e?(t.removeAttribute("src"),t.load()):A.logger.warn("[buffer-controller]: media.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={}}this.hls.trigger(T.Events.MEDIA_DETACHED,void 0)},d.onBufferReset=function(){var t=this;this.getSourceBufferTypes().forEach(function(a){var e=t.sourceBuffer[a];try{e&&(t.removeBufferListeners(a),t.mediaSource&&t.mediaSource.removeSourceBuffer(e),t.sourceBuffer[a]=void 0)}catch(s){A.logger.warn("[buffer-controller]: Failed to reset the "+a+" buffer",s)}}),this._initSourceBuffer()},d.onBufferCodecs=function(t,a){var e=this,s=this.getSourceBufferTypes().length;Object.keys(a).forEach(function(u){if(s){var n=e.tracks[u];if(n&&typeof n.buffer.changeType=="function"){var l=a[u],p=l.codec,g=l.levelCodec,v=l.container;if((n.levelCodec||n.codec).replace(h,"$1")!==(g||p).replace(h,"$1")){var r=v+";codecs="+(g||p);e.appendChangeType(u,r)}}}else e.pendingTracks[u]=a[u]}),s||(this.bufferCodecEventsExpected=Math.max(this.bufferCodecEventsExpected-1,0),this.mediaSource&&this.mediaSource.readyState==="open"&&this.checkPendingTracks())},d.appendChangeType=function(t,a){var e=this,s=this.operationQueue,u={execute:function(){var n=e.sourceBuffer[t];n&&(A.logger.log("[buffer-controller]: changing "+t+" sourceBuffer type to "+a),n.changeType(a)),s.shiftAndExecuteNext(t)},onStart:function(){},onComplete:function(){},onError:function(n){A.logger.warn("[buffer-controller]: Failed to change "+t+" SourceBuffer type",n)}};s.append(u,t)},d.onBufferAppending=function(t,a){var e=this,s=this.hls,u=this.operationQueue,n=this.tracks,l=a.data,p=a.type,g=a.frag,v=a.part,r=a.chunkMeta,i=r.buffering[p],c=self.performance.now();i.start=c;var S=g.stats.buffering,b=v?v.stats.buffering:null;S.start===0&&(S.start=c),b&&b.start===0&&(b.start=c);var D=n.audio,O=p==="audio"&&r.id===1&&(D==null?void 0:D.container)==="audio/mpeg",C={execute:function(){if(i.executeStart=self.performance.now(),O){var x=e.sourceBuffer[p];if(x){var P=g.start-x.timestampOffset;Math.abs(P)>=.1&&(A.logger.log("[buffer-controller]: Updating audio SourceBuffer timestampOffset to "+g.start+" (delta: "+P+") sn: "+g.sn+")"),x.timestampOffset=g.start)}}e.appendExecutor(l,p)},onStart:function(){},onComplete:function(){var x=self.performance.now();i.executeEnd=i.end=x,S.first===0&&(S.first=x),b&&b.first===0&&(b.first=x);var P=e.sourceBuffer,F={};for(var M in P)F[M]=I.BufferHelper.getBuffered(P[M]);e.appendError=0,e.hls.trigger(T.Events.BUFFER_APPENDED,{type:p,frag:g,part:v,chunkMeta:r,parent:g.type,timeRanges:F})},onError:function(x){A.logger.error("[buffer-controller]: Error encountered while trying to append to the "+p+" SourceBuffer",x);var P={type:R.ErrorTypes.MEDIA_ERROR,parent:g.type,details:R.ErrorDetails.BUFFER_APPEND_ERROR,err:x,fatal:!1};x.code===DOMException.QUOTA_EXCEEDED_ERR?P.details=R.ErrorDetails.BUFFER_FULL_ERROR:(e.appendError++,P.details=R.ErrorDetails.BUFFER_APPEND_ERROR,e.appendError>s.config.appendErrorMaxRetry&&(A.logger.error("[buffer-controller]: Failed "+s.config.appendErrorMaxRetry+" times to append segment in sourceBuffer"),P.fatal=!0)),s.trigger(T.Events.ERROR,P)}};u.append(C,p)},d.onBufferFlushing=function(t,a){var e=this,s=this.operationQueue,u=function(n){return{execute:e.removeExecutor.bind(e,n,a.startOffset,a.endOffset),onStart:function(){},onComplete:function(){e.hls.trigger(T.Events.BUFFER_FLUSHED,{type:n})},onError:function(l){A.logger.warn("[buffer-controller]: Failed to remove from "+n+" SourceBuffer",l)}}};a.type?s.append(u(a.type),a.type):this.getSourceBufferTypes().forEach(function(n){s.append(u(n),n)})},d.onFragParsed=function(t,a){var e=this,s=a.frag,u=a.part,n=[],l=u?u.elementaryStreams:s.elementaryStreams;l[o.ElementaryStreamTypes.AUDIOVIDEO]?n.push("audiovideo"):(l[o.ElementaryStreamTypes.AUDIO]&&n.push("audio"),l[o.ElementaryStreamTypes.VIDEO]&&n.push("video")),n.length===0&&A.logger.warn("Fragments must have at least one ElementaryStreamType set. type: "+s.type+" level: "+s.level+" sn: "+s.sn),this.blockBuffers(function(){var p=self.performance.now();s.stats.buffering.end=p,u&&(u.stats.buffering.end=p);var g=u?u.stats:s.stats;e.hls.trigger(T.Events.FRAG_BUFFERED,{frag:s,part:u,stats:g,id:s.type})},n)},d.onFragChanged=function(t,a){this.flushBackBuffer()},d.onBufferEos=function(t,a){var e=this;this.getSourceBufferTypes().reduce(function(s,u){var n=e.sourceBuffer[u];return a.type&&a.type!==u||n&&!n.ended&&(n.ended=!0,A.logger.log("[buffer-controller]: "+u+" sourceBuffer now EOS")),s&&!(n&&!n.ended)},!0)&&this.blockBuffers(function(){var s=e.mediaSource;s&&s.readyState==="open"&&s.endOfStream()})},d.onLevelUpdated=function(t,a){var e=a.details;e.fragments.length&&(this.details=e,this.getSourceBufferTypes().length?this.blockBuffers(this.updateMediaElementDuration.bind(this)):this.updateMediaElementDuration())},d.flushBackBuffer=function(){var t=this.hls,a=this.details,e=this.media,s=this.sourceBuffer;if(e&&a!==null){var u=this.getSourceBufferTypes();if(u.length){var n=a.live&&t.config.liveBackBufferLength!==null?t.config.liveBackBufferLength:t.config.backBufferLength;if(Object(_.isFiniteNumber)(n)&&!(n<0)){var l=e.currentTime,p=a.levelTargetDuration,g=Math.max(n,p),v=Math.floor(l/p)*p-g;u.forEach(function(r){var i=s[r];if(i){var c=I.BufferHelper.getBuffered(i);c.length>0&&v>c.start(0)&&(t.trigger(T.Events.BACK_BUFFER_REACHED,{bufferEnd:v}),a.live&&t.trigger(T.Events.LIVE_BACK_BUFFER_REACHED,{bufferEnd:v}),t.trigger(T.Events.BUFFER_FLUSHING,{startOffset:0,endOffset:v,type:r}))}})}}}},d.updateMediaElementDuration=function(){if(this.details&&this.media&&this.mediaSource&&this.mediaSource.readyState==="open"){var t=this.details,a=this.hls,e=this.media,s=this.mediaSource,u=t.fragments[0].start+t.totalduration,n=e.duration,l=Object(_.isFiniteNumber)(s.duration)?s.duration:0;t.live&&a.config.liveDurationInfinity?(A.logger.log("[buffer-controller]: Media Source duration is set to Infinity"),s.duration=1/0,this.updateSeekableRange(t)):(u>l&&u>n||!Object(_.isFiniteNumber)(n))&&(A.logger.log("[buffer-controller]: Updating Media Source duration to "+u.toFixed(3)),s.duration=u)}},d.updateSeekableRange=function(t){var a=this.mediaSource,e=t.fragments;if(e.length&&t.live&&a!=null&&a.setLiveSeekableRange){var s=Math.max(0,e[0].start),u=Math.max(s,s+t.totalduration);a.setLiveSeekableRange(s,u)}},d.checkPendingTracks=function(){var t=this.bufferCodecEventsExpected,a=this.operationQueue,e=this.pendingTracks,s=Object.keys(e).length;if(s&&!t||s===2){this.createSourceBuffers(e),this.pendingTracks={};var u=this.getSourceBufferTypes();if(u.length===0)return void this.hls.trigger(T.Events.ERROR,{type:R.ErrorTypes.MEDIA_ERROR,details:R.ErrorDetails.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,reason:"could not create source buffer for media codec(s)"});u.forEach(function(n){a.executeNext(n)})}},d.createSourceBuffers=function(t){var a=this.sourceBuffer,e=this.mediaSource;if(!e)throw Error("createSourceBuffers called when mediaSource was null");var s=0;for(var u in t)if(!a[u]){var n=t[u];if(!n)throw Error("source buffer exists for track "+u+", however track does not");var l=n.levelCodec||n.codec,p=n.container+";codecs="+l;A.logger.log("[buffer-controller]: creating sourceBuffer("+p+")");try{var g=a[u]=e.addSourceBuffer(p),v=u;this.addBufferListener(v,"updatestart",this._onSBUpdateStart),this.addBufferListener(v,"updateend",this._onSBUpdateEnd),this.addBufferListener(v,"error",this._onSBUpdateError),this.tracks[u]={buffer:g,codec:l,container:n.container,levelCodec:n.levelCodec,id:n.id},s++}catch(r){A.logger.error("[buffer-controller]: error while trying to add sourceBuffer: "+r.message),this.hls.trigger(T.Events.ERROR,{type:R.ErrorTypes.MEDIA_ERROR,details:R.ErrorDetails.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:r,mimeType:p})}}s&&this.hls.trigger(T.Events.BUFFER_CREATED,{tracks:this.tracks})},d._onSBUpdateStart=function(t){this.operationQueue.current(t).onStart()},d._onSBUpdateEnd=function(t){var a=this.operationQueue;a.current(t).onComplete(),a.shiftAndExecuteNext(t)},d._onSBUpdateError=function(t,a){A.logger.error("[buffer-controller]: "+t+" SourceBuffer error",a),this.hls.trigger(T.Events.ERROR,{type:R.ErrorTypes.MEDIA_ERROR,details:R.ErrorDetails.BUFFER_APPENDING_ERROR,fatal:!1});var e=this.operationQueue.current(t);e&&e.onError(a)},d.removeExecutor=function(t,a,e){var s=this.media,u=this.mediaSource,n=this.operationQueue,l=this.sourceBuffer[t];if(!s||!u||!l)return A.logger.warn("[buffer-controller]: Attempting to remove from the "+t+" SourceBuffer, but it does not exist"),void n.shiftAndExecuteNext(t);var p=Object(_.isFiniteNumber)(s.duration)?s.duration:1/0,g=Object(_.isFiniteNumber)(u.duration)?u.duration:1/0,v=Math.max(0,a),r=Math.min(e,p,g);r>v?(A.logger.log("[buffer-controller]: Removing ["+v+","+r+"] from the "+t+" SourceBuffer"),console.assert(!l.updating,t+" sourceBuffer must not be updating"),l.remove(v,r)):n.shiftAndExecuteNext(t)},d.appendExecutor=function(t,a){var e=this.operationQueue,s=this.sourceBuffer[a];if(!s)return A.logger.warn("[buffer-controller]: Attempting to append to the "+a+" SourceBuffer, but it does not exist"),void e.shiftAndExecuteNext(a);s.ended=!1,console.assert(!s.updating,a+" sourceBuffer must not be updating"),s.appendBuffer(t)},d.blockBuffers=function(t,a){var e=this;if(a===void 0&&(a=this.getSourceBufferTypes()),!a.length)return A.logger.log("[buffer-controller]: Blocking operation requested, but no SourceBuffers exist"),void Promise.resolve(t);var s=this.operationQueue,u=a.map(function(n){return s.appendBlocker(n)});Promise.all(u).then(function(){t(),a.forEach(function(n){var l=e.sourceBuffer[n];l&&l.updating||s.shiftAndExecuteNext(n)})})},d.getSourceBufferTypes=function(){return Object.keys(this.sourceBuffer)},d.addBufferListener=function(t,a,e){var s=this.sourceBuffer[t];if(s){var u=e.bind(this,t);this.listeners[t].push({event:a,listener:u}),s.addEventListener(a,u)}},d.removeBufferListeners=function(t){var a=this.sourceBuffer[t];a&&this.listeners[t].forEach(function(e){a.removeEventListener(e.event,e.listener)})},y}()},"./src/controller/buffer-operation-queue.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return T});var _=f("./src/utils/logger.ts"),T=function(){function A(I){this.buffers=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.buffers=I}var R=A.prototype;return R.append=function(I,k){var o=this.queues[k];o.push(I),o.length===1&&this.buffers[k]&&this.executeNext(k)},R.insertAbort=function(I,k){this.queues[k].unshift(I),this.executeNext(k)},R.appendBlocker=function(I){var k,o=new Promise(function(m){k=m}),L={execute:k,onStart:function(){},onComplete:function(){},onError:function(){}};return this.append(L,I),o},R.executeNext=function(I){var k=this.buffers,o=this.queues,L=k[I],m=o[I];if(m.length){var h=m[0];try{h.execute()}catch(E){_.logger.warn("[buffer-operation-queue]: Unhandled exception executing the current operation"),h.onError(E),L&&L.updating||(m.shift(),this.executeNext(I))}}},R.shiftAndExecuteNext=function(I){this.queues[I].shift(),this.executeNext(I)},R.current=function(I){return this.queues[I][0]},A}()},"./src/controller/cap-level-controller.ts":function(N,w,f){f.r(w);var _=f("./src/events.ts");function T(R,I){for(var k=0;k<I.length;k++){var o=I[k];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(R,o.key,o)}}var A=function(){function R(m){this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.hls=void 0,this.streamController=void 0,this.clientRect=void 0,this.hls=m,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}var I,k,o,L=R.prototype;return L.setStreamController=function(m){this.streamController=m},L.destroy=function(){this.unregisterListener(),this.hls.config.capLevelToPlayerSize&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null},L.registerListeners=function(){var m=this.hls;m.on(_.Events.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),m.on(_.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),m.on(_.Events.MANIFEST_PARSED,this.onManifestParsed,this),m.on(_.Events.BUFFER_CODECS,this.onBufferCodecs,this),m.on(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this)},L.unregisterListener=function(){var m=this.hls;m.off(_.Events.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),m.off(_.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),m.off(_.Events.MANIFEST_PARSED,this.onManifestParsed,this),m.off(_.Events.BUFFER_CODECS,this.onBufferCodecs,this),m.off(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this)},L.onFpsDropLevelCapping=function(m,h){R.isLevelAllowed(h.droppedLevel,this.restrictedLevels)&&this.restrictedLevels.push(h.droppedLevel)},L.onMediaAttaching=function(m,h){this.media=h.media instanceof HTMLVideoElement?h.media:null},L.onManifestParsed=function(m,h){var E=this.hls;this.restrictedLevels=[],this.firstLevel=h.firstLevel,E.config.capLevelToPlayerSize&&h.video&&this.startCapping()},L.onBufferCodecs=function(m,h){this.hls.config.capLevelToPlayerSize&&h.video&&this.startCapping()},L.onMediaDetaching=function(){this.stopCapping()},L.detectPlayerSize=function(){if(this.media&&this.mediaHeight>0&&this.mediaWidth>0){var m=this.hls.levels;if(m.length){var h=this.hls;h.autoLevelCapping=this.getMaxLevel(m.length-1),h.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=h.autoLevelCapping}}},L.getMaxLevel=function(m){var h=this,E=this.hls.levels;if(!E.length)return-1;var y=E.filter(function(d,t){return R.isLevelAllowed(t,h.restrictedLevels)&&t<=m});return this.clientRect=null,R.getMaxLevelByMediaSize(y,this.mediaWidth,this.mediaHeight)},L.startCapping=function(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,this.hls.firstLevel=this.getMaxLevel(this.firstLevel),self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())},L.stopCapping=function(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)},L.getDimensions=function(){if(this.clientRect)return this.clientRect;var m=this.media,h={width:0,height:0};if(m){var E=m.getBoundingClientRect();h.width=E.width,h.height=E.height,h.width||h.height||(h.width=E.right-E.left||m.width||0,h.height=E.bottom-E.top||m.height||0)}return this.clientRect=h,h},R.isLevelAllowed=function(m,h){return h===void 0&&(h=[]),h.indexOf(m)===-1},R.getMaxLevelByMediaSize=function(m,h,E){if(!m||!m.length)return-1;for(var y,d,t=m.length-1,a=0;a<m.length;a+=1){var e=m[a];if((e.width>=h||e.height>=E)&&(y=e,!(d=m[a+1])||y.width!==d.width||y.height!==d.height)){t=a;break}}return t},I=R,o=[{key:"contentScaleFactor",get:function(){var m=1;try{m=self.devicePixelRatio}catch{}return m}}],(k=[{key:"mediaWidth",get:function(){return this.getDimensions().width*R.contentScaleFactor}},{key:"mediaHeight",get:function(){return this.getDimensions().height*R.contentScaleFactor}}])&&T(I.prototype,k),o&&T(I,o),R}();w.default=A},"./src/controller/eme-controller.ts":function(N,w,f){f.r(w);var _=f("./src/events.ts"),T=f("./src/errors.ts"),A=f("./src/utils/logger.ts"),R=f("./src/utils/mediakeys-helper.ts");function I(o,L){for(var m=0;m<L.length;m++){var h=L[m];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(o,h.key,h)}}var k=function(){function o(E){this.hls=void 0,this._widevineLicenseUrl=void 0,this._licenseXhrSetup=void 0,this._licenseResponseCallback=void 0,this._emeEnabled=void 0,this._requestMediaKeySystemAccess=void 0,this._drmSystemOptions=void 0,this._config=void 0,this._mediaKeysList=[],this._media=null,this._hasSetMediaKeys=!1,this._requestLicenseFailureCount=0,this.mediaKeysPromise=null,this._onMediaEncrypted=this.onMediaEncrypted.bind(this),this.hls=E,this._config=E.config,this._widevineLicenseUrl=this._config.widevineLicenseUrl,this._licenseXhrSetup=this._config.licenseXhrSetup,this._licenseResponseCallback=this._config.licenseResponseCallback,this._emeEnabled=this._config.emeEnabled,this._requestMediaKeySystemAccess=this._config.requestMediaKeySystemAccessFunc,this._drmSystemOptions=this._config.drmSystemOptions,this._registerListeners()}var L,m,h=o.prototype;return h.destroy=function(){this._unregisterListeners(),this.hls=this._onMediaEncrypted=null,this._requestMediaKeySystemAccess=null},h._registerListeners=function(){this.hls.on(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(_.Events.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.on(_.Events.MANIFEST_PARSED,this.onManifestParsed,this)},h._unregisterListeners=function(){this.hls.off(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(_.Events.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.off(_.Events.MANIFEST_PARSED,this.onManifestParsed,this)},h.getLicenseServerUrl=function(E){switch(E){case R.KeySystems.WIDEVINE:if(!this._widevineLicenseUrl)break;return this._widevineLicenseUrl}throw new Error('no license server URL configured for key-system "'+E+'"')},h._attemptKeySystemAccess=function(E,y,d){var t=this,a=function(s,u,n,l){switch(s){case R.KeySystems.WIDEVINE:return function(p,g,v){var r={audioCapabilities:[],videoCapabilities:[]};return p.forEach(function(i){r.audioCapabilities.push({contentType:'audio/mp4; codecs="'+i+'"',robustness:v.audioRobustness||""})}),g.forEach(function(i){r.videoCapabilities.push({contentType:'video/mp4; codecs="'+i+'"',robustness:v.videoRobustness||""})}),[r]}(u,n,l);default:throw new Error("Unknown key-system: "+s)}}(E,y,d,this._drmSystemOptions);A.logger.log("Requesting encrypted media key-system access");var e=this.requestMediaKeySystemAccess(E,a);this.mediaKeysPromise=e.then(function(s){return t._onMediaKeySystemAccessObtained(E,s)}),e.catch(function(s){A.logger.error('Failed to obtain key-system "'+E+'" access:',s)})},h._onMediaKeySystemAccessObtained=function(E,y){var d=this;A.logger.log('Access for key-system "'+E+'" obtained');var t={mediaKeysSessionInitialized:!1,mediaKeySystemAccess:y,mediaKeySystemDomain:E};this._mediaKeysList.push(t);var a=Promise.resolve().then(function(){return y.createMediaKeys()}).then(function(e){return t.mediaKeys=e,A.logger.log('Media-keys created for key-system "'+E+'"'),d._onMediaKeysCreated(),e});return a.catch(function(e){A.logger.error("Failed to create media-keys:",e)}),a},h._onMediaKeysCreated=function(){var E=this;this._mediaKeysList.forEach(function(y){y.mediaKeysSession||(y.mediaKeysSession=y.mediaKeys.createSession(),E._onNewMediaKeySession(y.mediaKeysSession))})},h._onNewMediaKeySession=function(E){var y=this;A.logger.log("New key-system session "+E.sessionId),E.addEventListener("message",function(d){y._onKeySessionMessage(E,d.message)},!1)},h._onKeySessionMessage=function(E,y){A.logger.log("Got EME message event, creating license request"),this._requestLicense(y,function(d){A.logger.log("Received license data (length: "+(d&&d.byteLength)+"), updating key-session"),E.update(d)})},h.onMediaEncrypted=function(E){var y=this;if(A.logger.log('Media is encrypted using "'+E.initDataType+'" init data type'),!this.mediaKeysPromise)return A.logger.error("Fatal: Media is encrypted but no CDM access or no keys have been requested"),void this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_NO_KEYS,fatal:!0});var d=function(t){y._media&&(y._attemptSetMediaKeys(t),y._generateRequestWithPreferredKeySession(E.initDataType,E.initData))};this.mediaKeysPromise.then(d).catch(d)},h._attemptSetMediaKeys=function(E){if(!this._media)throw new Error("Attempted to set mediaKeys without first attaching a media element");if(!this._hasSetMediaKeys){var y=this._mediaKeysList[0];if(!y||!y.mediaKeys)return A.logger.error("Fatal: Media is encrypted but no CDM access or no keys have been obtained yet"),void this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_NO_KEYS,fatal:!0});A.logger.log("Setting keys for encrypted media"),this._media.setMediaKeys(y.mediaKeys),this._hasSetMediaKeys=!0}},h._generateRequestWithPreferredKeySession=function(E,y){var d=this,t=this._mediaKeysList[0];if(!t)return A.logger.error("Fatal: Media is encrypted but not any key-system access has been obtained yet"),void this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_NO_ACCESS,fatal:!0});if(t.mediaKeysSessionInitialized)A.logger.warn("Key-Session already initialized but requested again");else{var a=t.mediaKeysSession;if(!a)return A.logger.error("Fatal: Media is encrypted but no key-session existing"),void this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_NO_SESSION,fatal:!0});if(!y)return A.logger.warn("Fatal: initData required for generating a key session is null"),void this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_NO_INIT_DATA,fatal:!0});A.logger.log('Generating key-session request for "'+E+'" init data type'),t.mediaKeysSessionInitialized=!0,a.generateRequest(E,y).then(function(){A.logger.debug("Key-session generation succeeded")}).catch(function(e){A.logger.error("Error generating key-session request:",e),d.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_NO_SESSION,fatal:!1})})}},h._createLicenseXhr=function(E,y,d){var t=new XMLHttpRequest;t.responseType="arraybuffer",t.onreadystatechange=this._onLicenseRequestReadyStageChange.bind(this,t,E,y,d);var a=this._licenseXhrSetup;if(a)try{a.call(this.hls,t,E),a=void 0}catch(e){A.logger.error(e)}try{t.readyState||t.open("POST",E,!0),a&&a.call(this.hls,t,E)}catch(e){throw new Error("issue setting up KeySystem license XHR "+e)}return t},h._onLicenseRequestReadyStageChange=function(E,y,d,t){switch(E.readyState){case 4:if(E.status===200){this._requestLicenseFailureCount=0,A.logger.log("License request succeeded");var a=E.response,e=this._licenseResponseCallback;if(e)try{a=e.call(this.hls,E,y)}catch(u){A.logger.error(u)}t(a)}else{if(A.logger.error("License Request XHR failed ("+y+"). Status: "+E.status+" ("+E.statusText+")"),this._requestLicenseFailureCount++,this._requestLicenseFailureCount>3)return void this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0});var s=3-this._requestLicenseFailureCount+1;A.logger.warn("Retrying license request, "+s+" attempts left"),this._requestLicense(d,t)}}},h._generateLicenseRequestChallenge=function(E,y){switch(E.mediaKeySystemDomain){case R.KeySystems.WIDEVINE:return y}throw new Error("unsupported key-system: "+E.mediaKeySystemDomain)},h._requestLicense=function(E,y){A.logger.log("Requesting content license for key-system");var d=this._mediaKeysList[0];if(!d)return A.logger.error("Fatal error: Media is encrypted but no key-system access has been obtained yet"),void this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_NO_ACCESS,fatal:!0});try{var t=this.getLicenseServerUrl(d.mediaKeySystemDomain),a=this._createLicenseXhr(t,E,y);A.logger.log("Sending license request to URL: "+t);var e=this._generateLicenseRequestChallenge(d,E);a.send(e)}catch(s){A.logger.error("Failure requesting DRM license: "+s),this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.KEY_SYSTEM_ERROR,details:T.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0})}},h.onMediaAttached=function(E,y){if(this._emeEnabled){var d=y.media;this._media=d,d.addEventListener("encrypted",this._onMediaEncrypted)}},h.onMediaDetached=function(){var E=this._media,y=this._mediaKeysList;E&&(E.removeEventListener("encrypted",this._onMediaEncrypted),this._media=null,this._mediaKeysList=[],Promise.all(y.map(function(d){if(d.mediaKeysSession)return d.mediaKeysSession.close().catch(function(){})})).then(function(){return E.setMediaKeys(null)}).catch(function(){}))},h.onManifestParsed=function(E,y){if(this._emeEnabled){var d=y.levels.map(function(a){return a.audioCodec}).filter(function(a){return!!a}),t=y.levels.map(function(a){return a.videoCodec}).filter(function(a){return!!a});this._attemptKeySystemAccess(R.KeySystems.WIDEVINE,d,t)}},L=o,(m=[{key:"requestMediaKeySystemAccess",get:function(){if(!this._requestMediaKeySystemAccess)throw new Error("No requestMediaKeySystemAccess function configured");return this._requestMediaKeySystemAccess}}])&&I(L.prototype,m),o}();w.default=k},"./src/controller/fps-controller.ts":function(N,w,f){f.r(w);var _=f("./src/events.ts"),T=f("./src/utils/logger.ts"),A=function(){function R(k){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=k,this.registerListeners()}var I=R.prototype;return I.setStreamController=function(k){this.streamController=k},I.registerListeners=function(){this.hls.on(_.Events.MEDIA_ATTACHING,this.onMediaAttaching,this)},I.unregisterListeners=function(){this.hls.off(_.Events.MEDIA_ATTACHING,this.onMediaAttaching)},I.destroy=function(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null},I.onMediaAttaching=function(k,o){var L=this.hls.config;if(L.capLevelOnFPSDrop){var m=o.media instanceof self.HTMLVideoElement?o.media:null;this.media=m,m&&typeof m.getVideoPlaybackQuality=="function"&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),L.fpsDroppedMonitoringPeriod)}},I.checkFPS=function(k,o,L){var m=performance.now();if(o){if(this.lastTime){var h=m-this.lastTime,E=L-this.lastDroppedFrames,y=o-this.lastDecodedFrames,d=1e3*E/h,t=this.hls;if(t.trigger(_.Events.FPS_DROP,{currentDropped:E,currentDecoded:y,totalDroppedFrames:L}),d>0&&E>t.config.fpsDroppedMonitoringThreshold*y){var a=t.currentLevel;T.logger.warn("drop FPS ratio greater than max allowed value for currentLevel: "+a),a>0&&(t.autoLevelCapping===-1||t.autoLevelCapping>=a)&&(a-=1,t.trigger(_.Events.FPS_DROP_LEVEL_CAPPING,{level:a,droppedLevel:t.currentLevel}),t.autoLevelCapping=a,this.streamController.nextLevelSwitch())}}this.lastTime=m,this.lastDroppedFrames=L,this.lastDecodedFrames=o}},I.checkFPSInterval=function(){var k=this.media;if(k)if(this.isVideoPlaybackQualityAvailable){var o=k.getVideoPlaybackQuality();this.checkFPS(k,o.totalVideoFrames,o.droppedVideoFrames)}else this.checkFPS(k,k.webkitDecodedFrameCount,k.webkitDroppedFrameCount)},R}();w.default=A},"./src/controller/fragment-finders.ts":function(N,w,f){f.r(w),f.d(w,"findFragmentByPDT",function(){return A}),f.d(w,"findFragmentByPTS",function(){return R}),f.d(w,"fragmentWithinToleranceTest",function(){return I}),f.d(w,"pdtWithinToleranceTest",function(){return k}),f.d(w,"findFragWithCC",function(){return o});var _=f("./src/polyfills/number.ts"),T=f("./src/utils/binary-search.ts");function A(L,m,h){if(m===null||!Array.isArray(L)||!L.length||!Object(_.isFiniteNumber)(m)||m<(L[0].programDateTime||0)||m>=(L[L.length-1].endProgramDateTime||0))return null;h=h||0;for(var E=0;E<L.length;++E){var y=L[E];if(k(m,h,y))return y}return null}function R(L,m,h,E){h===void 0&&(h=0),E===void 0&&(E=0);var y=null;if(L?y=m[L.sn-m[0].sn+1]||null:h===0&&m[0].start===0&&(y=m[0]),y&&I(h,E,y)===0)return y;var d=T.default.search(m,I.bind(null,h,E));return d||y}function I(L,m,h){L===void 0&&(L=0),m===void 0&&(m=0);var E=Math.min(m,h.duration+(h.deltaPTS?h.deltaPTS:0));return h.start+h.duration-E<=L?1:h.start-E>L&&h.start?-1:0}function k(L,m,h){var E=1e3*Math.min(m,h.duration+(h.deltaPTS?h.deltaPTS:0));return(h.endProgramDateTime||0)-E>L}function o(L,m){return T.default.search(L,function(h){return h.cc<m?1:h.cc>m?-1:0})}},"./src/controller/fragment-tracker.ts":function(N,w,f){f.r(w),f.d(w,"FragmentState",function(){return _}),f.d(w,"FragmentTracker",function(){return I});var _,T,A=f("./src/events.ts"),R=f("./src/types/loader.ts");(T=_||(_={})).NOT_LOADED="NOT_LOADED",T.BACKTRACKED="BACKTRACKED",T.APPENDING="APPENDING",T.PARTIAL="PARTIAL",T.OK="OK";var I=function(){function L(h){this.activeFragment=null,this.activeParts=null,this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hls=h,this._registerListeners()}var m=L.prototype;return m._registerListeners=function(){var h=this.hls;h.on(A.Events.BUFFER_APPENDED,this.onBufferAppended,this),h.on(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),h.on(A.Events.FRAG_LOADED,this.onFragLoaded,this)},m._unregisterListeners=function(){var h=this.hls;h.off(A.Events.BUFFER_APPENDED,this.onBufferAppended,this),h.off(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),h.off(A.Events.FRAG_LOADED,this.onFragLoaded,this)},m.destroy=function(){this._unregisterListeners(),this.fragments=this.timeRanges=null},m.getAppendedFrag=function(h,E){if(E===R.PlaylistLevelType.MAIN){var y=this.activeFragment,d=this.activeParts;if(!y)return null;if(d)for(var t=d.length;t--;){var a=d[t],e=a?a.end:y.appendedPTS;if(a.start<=h&&e!==void 0&&h<=e)return t>9&&(this.activeParts=d.slice(t-9)),a}else if(y.start<=h&&y.appendedPTS!==void 0&&h<=y.appendedPTS)return y}return this.getBufferedFrag(h,E)},m.getBufferedFrag=function(h,E){for(var y=this.fragments,d=Object.keys(y),t=d.length;t--;){var a=y[d[t]];if((a==null?void 0:a.body.type)===E&&a.buffered){var e=a.body;if(e.start<=h&&h<=e.end)return e}}return null},m.detectEvictedFragments=function(h,E,y){var d=this;Object.keys(this.fragments).forEach(function(t){var a=d.fragments[t];if(a)if(a.buffered){var e=a.range[h];e&&e.time.some(function(s){var u=!d.isTimeBuffered(s.startPTS,s.endPTS,E);return u&&d.removeFragment(a.body),u})}else a.body.type===y&&d.removeFragment(a.body)})},m.detectPartialFragments=function(h){var E=this,y=this.timeRanges,d=h.frag,t=h.part;if(y&&d.sn!=="initSegment"){var a=o(d),e=this.fragments[a];e&&(Object.keys(y).forEach(function(s){var u=d.elementaryStreams[s];if(u){var n=y[s],l=t!==null||u.partial===!0;e.range[s]=E.getBufferedTimes(d,t,l,n)}}),e.backtrack=e.loaded=null,Object.keys(e.range).length?e.buffered=!0:this.removeFragment(e.body))}},m.fragBuffered=function(h){var E=o(h),y=this.fragments[E];y&&(y.backtrack=y.loaded=null,y.buffered=!0)},m.getBufferedTimes=function(h,E,y,d){for(var t={time:[],partial:y},a=E?E.start:h.start,e=E?E.end:h.end,s=h.minEndPTS||e,u=h.maxStartPTS||a,n=0;n<d.length;n++){var l=d.start(n)-this.bufferPadding,p=d.end(n)+this.bufferPadding;if(u>=l&&s<=p){t.time.push({startPTS:Math.max(a,d.start(n)),endPTS:Math.min(e,d.end(n))});break}if(a<p&&e>l)t.partial=!0,t.time.push({startPTS:Math.max(a,d.start(n)),endPTS:Math.min(e,d.end(n))});else if(e<=l)break}return t},m.getPartialFragment=function(h){var E,y,d,t=null,a=0,e=this.bufferPadding,s=this.fragments;return Object.keys(s).forEach(function(u){var n=s[u];n&&k(n)&&(y=n.body.start-e,d=n.body.end+e,h>=y&&h<=d&&(E=Math.min(h-y,d-h),a<=E&&(t=n.body,a=E)))}),t},m.getState=function(h){var E=o(h),y=this.fragments[E];return y?y.buffered?k(y)?_.PARTIAL:_.OK:y.backtrack?_.BACKTRACKED:_.APPENDING:_.NOT_LOADED},m.backtrack=function(h,E){var y=o(h),d=this.fragments[y];if(!d||d.backtrack)return null;var t=d.backtrack=E||d.loaded;return d.loaded=null,t},m.getBacktrackData=function(h){var E=o(h),y=this.fragments[E];if(y){var d,t=y.backtrack;if(t!=null&&(d=t.payload)!==null&&d!==void 0&&d.byteLength)return t;this.removeFragment(h)}return null},m.isTimeBuffered=function(h,E,y){for(var d,t,a=0;a<y.length;a++){if(d=y.start(a)-this.bufferPadding,t=y.end(a)+this.bufferPadding,h>=d&&E<=t)return!0;if(E<=d)return!1}return!1},m.onFragLoaded=function(h,E){var y=E.frag,d=E.part;if(y.sn!=="initSegment"&&!y.bitrateTest&&!d){var t=o(y);this.fragments[t]={body:y,loaded:E,backtrack:null,buffered:!1,range:Object.create(null)}}},m.onBufferAppended=function(h,E){var y=this,d=E.frag,t=E.part,a=E.timeRanges;if(d.type===R.PlaylistLevelType.MAIN)if(this.activeFragment=d,t){var e=this.activeParts;e||(this.activeParts=e=[]),e.push(t)}else this.activeParts=null;this.timeRanges=a,Object.keys(a).forEach(function(s){var u=a[s];if(y.detectEvictedFragments(s,u),!t)for(var n=0;n<u.length;n++)d.appendedPTS=Math.max(u.end(n),d.appendedPTS||0)})},m.onFragBuffered=function(h,E){this.detectPartialFragments(E)},m.hasFragment=function(h){var E=o(h);return!!this.fragments[E]},m.removeFragmentsInRange=function(h,E,y){var d=this;Object.keys(this.fragments).forEach(function(t){var a=d.fragments[t];if(a&&a.buffered){var e=a.body;e.type===y&&e.start<E&&e.end>h&&d.removeFragment(e)}})},m.removeFragment=function(h){var E=o(h);h.stats.loaded=0,h.clearElementaryStreamInfo(),delete this.fragments[E]},m.removeAllFragments=function(){this.fragments=Object.create(null),this.activeFragment=null,this.activeParts=null},L}();function k(L){var m,h;return L.buffered&&(((m=L.range.video)===null||m===void 0?void 0:m.partial)||((h=L.range.audio)===null||h===void 0?void 0:h.partial))}function o(L){return L.type+"_"+L.level+"_"+L.urlId+"_"+L.sn}},"./src/controller/gap-controller.ts":function(N,w,f){f.r(w),f.d(w,"STALL_MINIMUM_DURATION_MS",function(){return I}),f.d(w,"MAX_START_GAP_JUMP",function(){return k}),f.d(w,"SKIP_BUFFER_HOLE_STEP_SECONDS",function(){return o}),f.d(w,"SKIP_BUFFER_RANGE_START",function(){return L}),f.d(w,"default",function(){return m});var _=f("./src/utils/buffer-helper.ts"),T=f("./src/errors.ts"),A=f("./src/events.ts"),R=f("./src/utils/logger.ts"),I=250,k=2,o=.1,L=.05,m=function(){function h(y,d,t,a){this.config=void 0,this.media=void 0,this.fragmentTracker=void 0,this.hls=void 0,this.nudgeRetry=0,this.stallReported=!1,this.stalled=null,this.moved=!1,this.seeking=!1,this.config=y,this.media=d,this.fragmentTracker=t,this.hls=a}var E=h.prototype;return E.destroy=function(){this.hls=this.fragmentTracker=this.media=null},E.poll=function(y){var d=this.config,t=this.media,a=this.stalled,e=t.currentTime,s=t.seeking,u=this.seeking&&!s,n=!this.seeking&&s;if(this.seeking=s,e===y){if((n||u)&&(this.stalled=null),!t.paused&&!t.ended&&t.playbackRate!==0&&_.BufferHelper.getBuffered(t).length){var l=_.BufferHelper.bufferInfo(t,e,0),p=l.len>0,g=l.nextStart||0;if(p||g){if(s){var v=l.len>k,r=!g||g-e>k&&!this.fragmentTracker.getPartialFragment(e);if(v||r)return;this.moved=!1}if(!this.moved&&this.stalled!==null){var i,c=Math.max(g,l.start||0)-e,S=this.hls.levels?this.hls.levels[this.hls.currentLevel]:null,b=!(S==null||(i=S.details)===null||i===void 0)&&i.live?2*S.details.targetduration:k;if(c>0&&c<=b)return void this._trySkipBufferHole(null)}var D=self.performance.now();if(a!==null){var O=D-a;!s&&O>=I&&this._reportStall(l.len);var C=_.BufferHelper.bufferInfo(t,e,d.maxBufferHole);this._tryFixBufferStall(C,O)}else this.stalled=D}}}else if(this.moved=!0,a!==null){if(this.stallReported){var x=self.performance.now()-a;R.logger.warn("playback not stuck anymore @"+e+", after "+Math.round(x)+"ms"),this.stallReported=!1}this.stalled=null,this.nudgeRetry=0}},E._tryFixBufferStall=function(y,d){var t=this.config,a=this.fragmentTracker,e=this.media.currentTime,s=a.getPartialFragment(e);s&&this._trySkipBufferHole(s)||y.len>t.maxBufferHole&&d>1e3*t.highBufferWatchdogPeriod&&(R.logger.warn("Trying to nudge playhead over buffer-hole"),this.stalled=null,this._tryNudgeBuffer())},E._reportStall=function(y){var d=this.hls,t=this.media;this.stallReported||(this.stallReported=!0,R.logger.warn("Playback stalling at @"+t.currentTime+" due to low buffer (buffer="+y+")"),d.trigger(A.Events.ERROR,{type:T.ErrorTypes.MEDIA_ERROR,details:T.ErrorDetails.BUFFER_STALLED_ERROR,fatal:!1,buffer:y}))},E._trySkipBufferHole=function(y){for(var d=this.config,t=this.hls,a=this.media,e=a.currentTime,s=0,u=_.BufferHelper.getBuffered(a),n=0;n<u.length;n++){var l=u.start(n);if(e+d.maxBufferHole>=s&&e<l){var p=Math.max(l+L,a.currentTime+o);return R.logger.warn("skipping hole, adjusting currentTime from "+e+" to "+p),this.moved=!0,this.stalled=null,a.currentTime=p,y&&t.trigger(A.Events.ERROR,{type:T.ErrorTypes.MEDIA_ERROR,details:T.ErrorDetails.BUFFER_SEEK_OVER_HOLE,fatal:!1,reason:"fragment loaded with buffer holes, seeking from "+e+" to "+p,frag:y}),p}s=u.end(n)}return 0},E._tryNudgeBuffer=function(){var y=this.config,d=this.hls,t=this.media,a=t.currentTime,e=(this.nudgeRetry||0)+1;if(this.nudgeRetry=e,e<y.nudgeMaxRetry){var s=a+e*y.nudgeOffset;R.logger.warn("Nudging 'currentTime' from "+a+" to "+s),t.currentTime=s,d.trigger(A.Events.ERROR,{type:T.ErrorTypes.MEDIA_ERROR,details:T.ErrorDetails.BUFFER_NUDGE_ON_STALL,fatal:!1})}else R.logger.error("Playhead still not moving while enough data buffered @"+a+" after "+y.nudgeMaxRetry+" nudges"),d.trigger(A.Events.ERROR,{type:T.ErrorTypes.MEDIA_ERROR,details:T.ErrorDetails.BUFFER_STALLED_ERROR,fatal:!0})},h}()},"./src/controller/id3-track-controller.ts":function(N,w,f){f.r(w);var _=f("./src/events.ts"),T=f("./src/utils/texttrack-utils.ts"),A=f("./src/demux/id3.ts"),R=function(){function I(o){this.hls=void 0,this.id3Track=null,this.media=null,this.hls=o,this._registerListeners()}var k=I.prototype;return k.destroy=function(){this._unregisterListeners()},k._registerListeners=function(){var o=this.hls;o.on(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),o.on(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this),o.on(_.Events.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),o.on(_.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},k._unregisterListeners=function(){var o=this.hls;o.off(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),o.off(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this),o.off(_.Events.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),o.off(_.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},k.onMediaAttached=function(o,L){this.media=L.media},k.onMediaDetaching=function(){this.id3Track&&(Object(T.clearCurrentCues)(this.id3Track),this.id3Track=null,this.media=null)},k.getID3Track=function(o){if(this.media){for(var L=0;L<o.length;L++){var m=o[L];if(m.kind==="metadata"&&m.label==="id3")return Object(T.sendAddTrackEvent)(m,this.media),m}return this.media.addTextTrack("metadata","id3")}},k.onFragParsingMetadata=function(o,L){if(this.media){var m=L.frag,h=L.samples;this.id3Track||(this.id3Track=this.getID3Track(this.media.textTracks),this.id3Track.mode="hidden");for(var E=self.WebKitDataCue||self.VTTCue||self.TextTrackCue,y=0;y<h.length;y++){var d=A.getID3Frames(h[y].data);if(d){var t=h[y].pts,a=y<h.length-1?h[y+1].pts:m.end;a-t<=0&&(a=t+.25);for(var e=0;e<d.length;e++){var s=d[e];if(!A.isTimeStampFrame(s)){var u=new E(t,a,"");u.value=s,this.id3Track.addCue(u)}}}}}},k.onBufferFlushing=function(o,L){var m=L.startOffset,h=L.endOffset,E=L.type;if(!E||E==="audio"){var y=this.id3Track;y&&Object(T.removeCuesInRange)(y,m,h)}},I}();w.default=R},"./src/controller/latency-controller.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return I});var _=f("./src/errors.ts"),T=f("./src/events.ts"),A=f("./src/utils/logger.ts");function R(k,o){for(var L=0;L<o.length;L++){var m=o[L];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(k,m.key,m)}}var I=function(){function k(h){var E=this;this.hls=void 0,this.config=void 0,this.media=null,this.levelDetails=null,this.currentTime=0,this.stallCount=0,this._latency=null,this.timeupdateHandler=function(){return E.timeupdate()},this.hls=h,this.config=h.config,this.registerListeners()}var o,L,m=k.prototype;return m.destroy=function(){this.unregisterListeners(),this.onMediaDetaching(),this.levelDetails=null,this.hls=this.timeupdateHandler=null},m.registerListeners=function(){this.hls.on(T.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(T.Events.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.on(T.Events.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(T.Events.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.on(T.Events.ERROR,this.onError,this)},m.unregisterListeners=function(){this.hls.off(T.Events.MEDIA_ATTACHED,this.onMediaAttached),this.hls.off(T.Events.MEDIA_DETACHING,this.onMediaDetaching),this.hls.off(T.Events.MANIFEST_LOADING,this.onManifestLoading),this.hls.off(T.Events.LEVEL_UPDATED,this.onLevelUpdated),this.hls.off(T.Events.ERROR,this.onError)},m.onMediaAttached=function(h,E){this.media=E.media,this.media.addEventListener("timeupdate",this.timeupdateHandler)},m.onMediaDetaching=function(){this.media&&(this.media.removeEventListener("timeupdate",this.timeupdateHandler),this.media=null)},m.onManifestLoading=function(){this.levelDetails=null,this._latency=null,this.stallCount=0},m.onLevelUpdated=function(h,E){var y=E.details;this.levelDetails=y,y.advanced&&this.timeupdate(),!y.live&&this.media&&this.media.removeEventListener("timeupdate",this.timeupdateHandler)},m.onError=function(h,E){E.details===_.ErrorDetails.BUFFER_STALLED_ERROR&&(this.stallCount++,A.logger.warn("[playback-rate-controller]: Stall detected, adjusting target latency"))},m.timeupdate=function(){var h=this.media,E=this.levelDetails;if(h&&E){this.currentTime=h.currentTime;var y=this.computeLatency();if(y!==null){this._latency=y;var d=this.config,t=d.lowLatencyMode,a=d.maxLiveSyncPlaybackRate;if(t&&a!==1){var e=this.targetLatency;if(e!==null){var s=y-e,u=s<Math.min(this.maxLatency,e+E.targetduration);if(E.live&&u&&s>.05&&this.forwardBufferLength>1){var n=Math.min(2,Math.max(1,a)),l=Math.round(2/(1+Math.exp(-.75*s-this.edgeStalled))*20)/20;h.playbackRate=Math.min(n,Math.max(1,l))}else h.playbackRate!==1&&h.playbackRate!==0&&(h.playbackRate=1)}}}}},m.estimateLiveEdge=function(){var h=this.levelDetails;return h===null?null:h.edge+h.age},m.computeLatency=function(){var h=this.estimateLiveEdge();return h===null?null:h-this.currentTime},o=k,(L=[{key:"latency",get:function(){return this._latency||0}},{key:"maxLatency",get:function(){var h=this.config,E=this.levelDetails;return h.liveMaxLatencyDuration!==void 0?h.liveMaxLatencyDuration:E?h.liveMaxLatencyDurationCount*E.targetduration:0}},{key:"targetLatency",get:function(){var h=this.levelDetails;if(h===null)return null;var E=h.holdBack,y=h.partHoldBack,d=h.targetduration,t=this.config,a=t.liveSyncDuration,e=t.liveSyncDurationCount,s=t.lowLatencyMode,u=this.hls.userConfig,n=s&&y||E;(u.liveSyncDuration||u.liveSyncDurationCount||n===0)&&(n=a!==void 0?a:e*d);var l=d;return n+Math.min(1*this.stallCount,l)}},{key:"liveSyncPosition",get:function(){var h=this.estimateLiveEdge(),E=this.targetLatency,y=this.levelDetails;if(h===null||E===null||y===null)return null;var d=y.edge,t=h-E-this.edgeStalled,a=d-y.totalduration,e=d-(this.config.lowLatencyMode&&y.partTarget||y.targetduration);return Math.min(Math.max(a,t),e)}},{key:"drift",get:function(){var h=this.levelDetails;return h===null?1:h.drift}},{key:"edgeStalled",get:function(){var h=this.levelDetails;if(h===null)return 0;var E=3*(this.config.lowLatencyMode&&h.partTarget||h.targetduration);return Math.max(h.age-E,0)}},{key:"forwardBufferLength",get:function(){var h=this.media,E=this.levelDetails;if(!h||!E)return 0;var y=h.buffered.length;return y?h.buffered.end(y-1):E.edge-this.currentTime}}])&&R(o.prototype,L),k}()},"./src/controller/level-controller.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return y});var _=f("./src/types/level.ts"),T=f("./src/events.ts"),A=f("./src/errors.ts"),R=f("./src/utils/codecs.ts"),I=f("./src/controller/level-helper.ts"),k=f("./src/controller/base-playlist-controller.ts"),o=f("./src/types/loader.ts");function L(){return(L=Object.assign||function(d){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var e in a)Object.prototype.hasOwnProperty.call(a,e)&&(d[e]=a[e])}return d}).apply(this,arguments)}function m(d,t){for(var a=0;a<t.length;a++){var e=t[a];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(d,e.key,e)}}function h(d,t){return(h=Object.setPrototypeOf||function(a,e){return a.__proto__=e,a})(d,t)}var E=/chrome|firefox/.test(navigator.userAgent.toLowerCase()),y=function(d){var t,a;function e(l){var p;return(p=d.call(this,l,"[level-controller]")||this)._levels=[],p._firstLevel=-1,p._startLevel=void 0,p.currentLevelIndex=-1,p.manualLevelIndex=-1,p.onParsedComplete=void 0,p._registerListeners(),p}a=d,(t=e).prototype=Object.create(a.prototype),t.prototype.constructor=t,h(t,a);var s,u,n=e.prototype;return n._registerListeners=function(){var l=this.hls;l.on(T.Events.MANIFEST_LOADED,this.onManifestLoaded,this),l.on(T.Events.LEVEL_LOADED,this.onLevelLoaded,this),l.on(T.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),l.on(T.Events.FRAG_LOADED,this.onFragLoaded,this),l.on(T.Events.ERROR,this.onError,this)},n._unregisterListeners=function(){var l=this.hls;l.off(T.Events.MANIFEST_LOADED,this.onManifestLoaded,this),l.off(T.Events.LEVEL_LOADED,this.onLevelLoaded,this),l.off(T.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),l.off(T.Events.FRAG_LOADED,this.onFragLoaded,this),l.off(T.Events.ERROR,this.onError,this)},n.destroy=function(){this._unregisterListeners(),this.manualLevelIndex=-1,this._levels.length=0,d.prototype.destroy.call(this)},n.startLoad=function(){this._levels.forEach(function(l){l.loadError=0}),d.prototype.startLoad.call(this)},n.onManifestLoaded=function(l,p){var g,v,r=[],i=[],c=[],S={},b=!1,D=!1,O=!1;if(p.levels.forEach(function(F){var M=F.attrs;b=b||!(!F.width||!F.height),D=D||!!F.videoCodec,O=O||!!F.audioCodec,E&&F.audioCodec&&F.audioCodec.indexOf("mp4a.40.34")!==-1&&(F.audioCodec=void 0);var B=F.bitrate+"-"+F.attrs.RESOLUTION+"-"+F.attrs.CODECS;(v=S[B])?v.url.push(F.url):(v=new _.Level(F),S[B]=v,r.push(v)),M&&(M.AUDIO&&Object(I.addGroupId)(v,"audio",M.AUDIO),M.SUBTITLES&&Object(I.addGroupId)(v,"text",M.SUBTITLES))}),(b||D)&&O&&(r=r.filter(function(F){var M=F.videoCodec,B=F.width,U=F.height;return!!M||!(!B||!U)})),r=r.filter(function(F){var M=F.audioCodec,B=F.videoCodec;return(!M||Object(R.isCodecSupportedInMp4)(M,"audio"))&&(!B||Object(R.isCodecSupportedInMp4)(B,"video"))}),p.audioTracks&&(i=p.audioTracks.filter(function(F){return!F.audioCodec||Object(R.isCodecSupportedInMp4)(F.audioCodec,"audio")}),Object(I.assignTrackIdsByGroup)(i)),p.subtitles&&(c=p.subtitles,Object(I.assignTrackIdsByGroup)(c)),r.length>0){g=r[0].bitrate,r.sort(function(F,M){return F.bitrate-M.bitrate}),this._levels=r;for(var C=0;C<r.length;C++)if(r[C].bitrate===g){this._firstLevel=C,this.log("manifest loaded, "+r.length+" level(s) found, first bitrate: "+g);break}var x=O&&!D,P={levels:r,audioTracks:i,subtitleTracks:c,firstLevel:this._firstLevel,stats:p.stats,audio:O,video:D,altAudio:!x&&i.some(function(F){return!!F.url})};this.hls.trigger(T.Events.MANIFEST_PARSED,P),(this.hls.config.autoStartLoad||this.hls.forceStartLoad)&&this.hls.startLoad(this.hls.config.startPosition)}else this.hls.trigger(T.Events.ERROR,{type:A.ErrorTypes.MEDIA_ERROR,details:A.ErrorDetails.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:p.url,reason:"no level with compatible codecs found in manifest"})},n.onError=function(l,p){if(d.prototype.onError.call(this,l,p),!p.fatal){var g=p.context,v=this._levels[this.currentLevelIndex];if(g&&(g.type===o.PlaylistContextType.AUDIO_TRACK&&v.audioGroupIds&&g.groupId===v.audioGroupIds[v.urlId]||g.type===o.PlaylistContextType.SUBTITLE_TRACK&&v.textGroupIds&&g.groupId===v.textGroupIds[v.urlId]))this.redundantFailover(this.currentLevelIndex);else{var r,i=!1,c=!0;switch(p.details){case A.ErrorDetails.FRAG_LOAD_ERROR:case A.ErrorDetails.FRAG_LOAD_TIMEOUT:case A.ErrorDetails.KEY_LOAD_ERROR:case A.ErrorDetails.KEY_LOAD_TIMEOUT:if(p.frag){var S=this._levels[p.frag.level];S?(S.fragmentError++,S.fragmentError>this.hls.config.fragLoadingMaxRetry&&(r=p.frag.level)):r=p.frag.level}break;case A.ErrorDetails.LEVEL_LOAD_ERROR:case A.ErrorDetails.LEVEL_LOAD_TIMEOUT:g&&(g.deliveryDirectives&&(c=!1),r=g.level),i=!0;break;case A.ErrorDetails.REMUX_ALLOC_ERROR:r=p.level,i=!0}r!==void 0&&this.recoverLevel(p,r,i,c)}}},n.recoverLevel=function(l,p,g,v){var r=l.details,i=this._levels[p];if(i.loadError++,g){if(!this.retryLoadingOrFail(l))return void(this.currentLevelIndex=-1);l.levelRetry=!0}if(v){var c=i.url.length;if(c>1&&i.loadError<c)l.levelRetry=!0,this.redundantFailover(p);else if(this.manualLevelIndex===-1){var S=p===0?this._levels.length-1:p-1;this.currentLevelIndex!==S&&this._levels[S].loadError===0&&(this.warn(r+": switch to "+S),l.levelRetry=!0,this.hls.nextAutoLevel=S)}}},n.redundantFailover=function(l){var p=this._levels[l],g=p.url.length;if(g>1){var v=(p.urlId+1)%g;this.warn("Switching to redundant URL-id "+v),this._levels.forEach(function(r){r.urlId=v}),this.level=l}},n.onFragLoaded=function(l,p){var g=p.frag;if(g!==void 0&&g.type===o.PlaylistLevelType.MAIN){var v=this._levels[g.level];v!==void 0&&(v.fragmentError=0,v.loadError=0)}},n.onLevelLoaded=function(l,p){var g,v,r=p.level,i=p.details,c=this._levels[r];if(!c)return this.warn("Invalid level index "+r),void((v=p.deliveryDirectives)!==null&&v!==void 0&&v.skip&&(i.deltaUpdateFailed=!0));r===this.currentLevelIndex?(c.fragmentError===0&&(c.loadError=0,this.retryCount=0),this.playlistLoaded(r,p,c.details)):(g=p.deliveryDirectives)!==null&&g!==void 0&&g.skip&&(i.deltaUpdateFailed=!0)},n.onAudioTrackSwitched=function(l,p){var g=this.hls.levels[this.currentLevelIndex];if(g&&g.audioGroupIds){for(var v=-1,r=this.hls.audioTracks[p.id].groupId,i=0;i<g.audioGroupIds.length;i++)if(g.audioGroupIds[i]===r){v=i;break}v!==g.urlId&&(g.urlId=v,this.startLoad())}},n.loadPlaylist=function(l){var p=this.currentLevelIndex,g=this._levels[p];if(this.canLoad&&g&&g.url.length>0){var v=g.urlId,r=g.url[v];if(l)try{r=l.addDirectives(r)}catch(i){this.warn("Could not construct new URL with HLS Delivery Directives: "+i)}this.log("Attempt loading level index "+p+(l?" at sn "+l.msn+" part "+l.part:"")+" with URL-id "+v+" "+r),this.clearTimer(),this.hls.trigger(T.Events.LEVEL_LOADING,{url:r,level:p,id:v,deliveryDirectives:l||null})}},n.removeLevel=function(l,p){var g=function(r,i){return i!==p},v=this._levels.filter(function(r,i){return i!==l||r.url.length>1&&p!==void 0&&(r.url=r.url.filter(g),r.audioGroupIds&&(r.audioGroupIds=r.audioGroupIds.filter(g)),r.textGroupIds&&(r.textGroupIds=r.textGroupIds.filter(g)),r.urlId=0,!0)}).map(function(r,i){var c=r.details;return c!=null&&c.fragments&&c.fragments.forEach(function(S){S.level=i}),r});this._levels=v,this.hls.trigger(T.Events.LEVELS_UPDATED,{levels:v})},s=e,(u=[{key:"levels",get:function(){return this._levels.length===0?null:this._levels}},{key:"level",get:function(){return this.currentLevelIndex},set:function(l){var p,g=this._levels;if(g.length!==0&&(this.currentLevelIndex!==l||(p=g[l])===null||p===void 0||!p.details)){if(l<0||l>=g.length){var v=l<0;if(this.hls.trigger(T.Events.ERROR,{type:A.ErrorTypes.OTHER_ERROR,details:A.ErrorDetails.LEVEL_SWITCH_ERROR,level:l,fatal:v,reason:"invalid level idx"}),v)return;l=Math.min(l,g.length-1)}this.clearTimer();var r=this.currentLevelIndex,i=g[r],c=g[l];this.log("switching to level "+l+" from "+r),this.currentLevelIndex=l;var S=L({},c,{level:l,maxBitrate:c.maxBitrate,uri:c.uri,urlId:c.urlId});delete S._urlId,this.hls.trigger(T.Events.LEVEL_SWITCHING,S);var b=c.details;if(!b||b.live){var D=this.switchParams(c.uri,i==null?void 0:i.details);this.loadPlaylist(D)}}}},{key:"manualLevel",get:function(){return this.manualLevelIndex},set:function(l){this.manualLevelIndex=l,this._startLevel===void 0&&(this._startLevel=l),l!==-1&&(this.level=l)}},{key:"firstLevel",get:function(){return this._firstLevel},set:function(l){this._firstLevel=l}},{key:"startLevel",get:function(){if(this._startLevel===void 0){var l=this.hls.config.startLevel;return l!==void 0?l:this._firstLevel}return this._startLevel},set:function(l){this._startLevel=l}},{key:"nextLoadLevel",get:function(){return this.manualLevelIndex!==-1?this.manualLevelIndex:this.hls.nextAutoLevel},set:function(l){this.level=l,this.manualLevelIndex===-1&&(this.hls.nextAutoLevel=l)}}])&&m(s.prototype,u),e}(k.default)},"./src/controller/level-helper.ts":function(N,w,f){f.r(w),f.d(w,"addGroupId",function(){return A}),f.d(w,"assignTrackIdsByGroup",function(){return R}),f.d(w,"updatePTS",function(){return I}),f.d(w,"updateFragPTSDTS",function(){return o}),f.d(w,"mergeDetails",function(){return L}),f.d(w,"mapPartIntersection",function(){return m}),f.d(w,"mapFragmentIntersection",function(){return h}),f.d(w,"adjustSliding",function(){return E}),f.d(w,"addSliding",function(){return y}),f.d(w,"computeReloadInterval",function(){return d}),f.d(w,"getFragmentWithSN",function(){return t}),f.d(w,"getPartWith",function(){return a});var _=f("./src/polyfills/number.ts"),T=f("./src/utils/logger.ts");function A(e,s,u){switch(s){case"audio":e.audioGroupIds||(e.audioGroupIds=[]),e.audioGroupIds.push(u);break;case"text":e.textGroupIds||(e.textGroupIds=[]),e.textGroupIds.push(u)}}function R(e){var s={};e.forEach(function(u){var n=u.groupId||"";u.id=s[n]=s[n]||0,s[n]++})}function I(e,s,u){k(e[s],e[u])}function k(e,s){var u=s.startPTS;if(Object(_.isFiniteNumber)(u)){var n,l=0;s.sn>e.sn?(l=u-e.start,n=e):(l=e.start-u,n=s),n.duration!==l&&(n.duration=l)}else s.sn>e.sn?e.cc===s.cc&&e.minEndPTS?s.start=e.start+(e.minEndPTS-e.start):s.start=e.start+e.duration:s.start=Math.max(e.start-s.duration,0)}function o(e,s,u,n,l,p){n-u<=0&&(T.logger.warn("Fragment should have a positive duration",s),n=u+s.duration,p=l+s.duration);var g=u,v=n,r=s.startPTS,i=s.endPTS;if(Object(_.isFiniteNumber)(r)){var c=Math.abs(r-u);Object(_.isFiniteNumber)(s.deltaPTS)?s.deltaPTS=Math.max(c,s.deltaPTS):s.deltaPTS=c,g=Math.max(u,r),u=Math.min(u,r),l=Math.min(l,s.startDTS),v=Math.min(n,i),n=Math.max(n,i),p=Math.max(p,s.endDTS)}s.duration=n-u;var S=u-s.start;s.appendedPTS=n,s.start=s.startPTS=u,s.maxStartPTS=g,s.startDTS=l,s.endPTS=n,s.minEndPTS=v,s.endDTS=p;var b,D=s.sn;if(!e||D<e.startSN||D>e.endSN)return 0;var O=D-e.startSN,C=e.fragments;for(C[O]=s,b=O;b>0;b--)k(C[b],C[b-1]);for(b=O;b<C.length-1;b++)k(C[b],C[b+1]);return e.fragmentHint&&k(C[C.length-1],e.fragmentHint),e.PTSKnown=e.alignedSliding=!0,S}function L(e,s){for(var u=null,n=e.fragments,l=n.length-1;l>=0;l--){var p=n[l].initSegment;if(p){u=p;break}}e.fragmentHint&&delete e.fragmentHint.endPTS;var g,v=0;if(h(e,s,function(D,O){var C;D.relurl&&(v=D.cc-O.cc),Object(_.isFiniteNumber)(D.startPTS)&&Object(_.isFiniteNumber)(D.endPTS)&&(O.start=O.startPTS=D.startPTS,O.startDTS=D.startDTS,O.appendedPTS=D.appendedPTS,O.maxStartPTS=D.maxStartPTS,O.endPTS=D.endPTS,O.endDTS=D.endDTS,O.minEndPTS=D.minEndPTS,O.duration=D.endPTS-D.startPTS,O.duration&&(g=O),s.PTSKnown=s.alignedSliding=!0),O.elementaryStreams=D.elementaryStreams,O.loader=D.loader,O.stats=D.stats,O.urlId=D.urlId,D.initSegment?(O.initSegment=D.initSegment,u=D.initSegment):O.initSegment&&O.initSegment.relurl!=((C=u)===null||C===void 0?void 0:C.relurl)||(O.initSegment=u)}),s.skippedSegments&&(s.deltaUpdateFailed=s.fragments.some(function(D){return!D}),s.deltaUpdateFailed)){T.logger.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(var r=s.skippedSegments;r--;)s.fragments.shift();s.startSN=s.fragments[0].sn,s.startCC=s.fragments[0].cc}var i=s.fragments;if(v){T.logger.warn("discontinuity sliding from playlist, take drift into account");for(var c=0;c<i.length;c++)i[c].cc+=v}s.skippedSegments&&(s.startCC=s.fragments[0].cc),m(e.partList,s.partList,function(D,O){O.elementaryStreams=D.elementaryStreams,O.stats=D.stats}),g?o(s,g,g.startPTS,g.endPTS,g.startDTS,g.endDTS):E(e,s),i.length&&(s.totalduration=s.edge-i[0].start),s.driftStartTime=e.driftStartTime,s.driftStart=e.driftStart;var S=s.advancedDateTime;if(s.advanced&&S){var b=s.edge;s.driftStart||(s.driftStartTime=S,s.driftStart=b),s.driftEndTime=S,s.driftEnd=b}else s.driftEndTime=e.driftEndTime,s.driftEnd=e.driftEnd,s.advancedDateTime=e.advancedDateTime}function m(e,s,u){if(e&&s)for(var n=0,l=0,p=e.length;l<=p;l++){var g=e[l],v=s[l+n];g&&v&&g.index===v.index&&g.fragment.sn===v.fragment.sn?u(g,v):n--}}function h(e,s,u){for(var n=s.skippedSegments,l=Math.max(e.startSN,s.startSN)-s.startSN,p=(e.fragmentHint?1:0)+(n?s.endSN:Math.min(e.endSN,s.endSN))-s.startSN,g=s.startSN-e.startSN,v=s.fragmentHint?s.fragments.concat(s.fragmentHint):s.fragments,r=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments,i=l;i<=p;i++){var c=r[g+i],S=v[i];n&&!S&&i<n&&(S=s.fragments[i]=c),c&&S&&u(c,S)}}function E(e,s){var u=s.startSN+s.skippedSegments-e.startSN,n=e.fragments;u<0||u>=n.length||y(s,n[u].start)}function y(e,s){if(s){for(var u=e.fragments,n=e.skippedSegments;n<u.length;n++)u[n].start+=s;e.fragmentHint&&(e.fragmentHint.start+=s)}}function d(e,s){var u,n=1e3*e.levelTargetDuration,l=n/2,p=e.age,g=p>0&&p<3*n,v=s.loading.end-s.loading.start,r=e.availabilityDelay;if(e.updated===!1)if(g){var i=333*e.misses;u=Math.max(Math.min(l,2*v),i),e.availabilityDelay=(e.availabilityDelay||0)+u}else u=l;else g?(r=Math.min(r||n/2,p),e.availabilityDelay=r,u=r+n-p):u=n-v;return Math.round(u)}function t(e,s,u){if(!e||!e.details)return null;var n=e.details,l=n.fragments[s-n.startSN];return l||((l=n.fragmentHint)&&l.sn===s?l:s<n.startSN&&u&&u.sn===s?u:null)}function a(e,s,u){if(!e||!e.details)return null;var n=e.details.partList;if(n)for(var l=n.length;l--;){var p=n[l];if(p.index===u&&p.fragment.sn===s)return p}return null}},"./src/controller/stream-controller.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return e});var _=f("./src/polyfills/number.ts"),T=f("./src/controller/base-stream-controller.ts"),A=f("./src/is-supported.ts"),R=f("./src/events.ts"),I=f("./src/utils/buffer-helper.ts"),k=f("./src/controller/fragment-tracker.ts"),o=f("./src/types/loader.ts"),L=f("./src/loader/fragment.ts"),m=f("./src/demux/transmuxer-interface.ts"),h=f("./src/types/transmuxer.ts"),E=f("./src/controller/gap-controller.ts"),y=f("./src/errors.ts"),d=f("./src/utils/logger.ts");function t(s,u){for(var n=0;n<u.length;n++){var l=u[n];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(s,l.key,l)}}function a(s,u){return(a=Object.setPrototypeOf||function(n,l){return n.__proto__=l,n})(s,u)}var e=function(s){var u,n;function l(r,i){var c;return(c=s.call(this,r,i,"[stream-controller]")||this).audioCodecSwap=!1,c.gapController=null,c.level=-1,c._forceStartLoad=!1,c.altAudio=!1,c.audioOnly=!1,c.fragPlaying=null,c.onvplaying=null,c.onvseeked=null,c.fragLastKbps=0,c.stalled=!1,c.couldBacktrack=!1,c.audioCodecSwitch=!1,c.videoBuffer=null,c._registerListeners(),c}n=s,(u=l).prototype=Object.create(n.prototype),u.prototype.constructor=u,a(u,n);var p,g,v=l.prototype;return v._registerListeners=function(){var r=this.hls;r.on(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.on(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.on(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.on(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),r.on(R.Events.LEVEL_LOADING,this.onLevelLoading,this),r.on(R.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.on(R.Events.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),r.on(R.Events.ERROR,this.onError,this),r.on(R.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.on(R.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),r.on(R.Events.BUFFER_CREATED,this.onBufferCreated,this),r.on(R.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.on(R.Events.LEVELS_UPDATED,this.onLevelsUpdated,this),r.on(R.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v._unregisterListeners=function(){var r=this.hls;r.off(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.off(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.off(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.off(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),r.off(R.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.off(R.Events.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),r.off(R.Events.ERROR,this.onError,this),r.off(R.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.off(R.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),r.off(R.Events.BUFFER_CREATED,this.onBufferCreated,this),r.off(R.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.off(R.Events.LEVELS_UPDATED,this.onLevelsUpdated,this),r.off(R.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v.onHandlerDestroying=function(){this._unregisterListeners(),this.onMediaDetaching()},v.startLoad=function(r){if(this.levels){var i=this.lastCurrentTime,c=this.hls;if(this.stopLoad(),this.setInterval(100),this.level=-1,this.fragLoadError=0,!this.startFragRequested){var S=c.startLevel;S===-1&&(c.config.testBandwidth?(S=0,this.bitrateTest=!0):S=c.nextAutoLevel),this.level=c.nextLoadLevel=S,this.loadedmetadata=!1}i>0&&r===-1&&(this.log("Override startPosition with lastCurrentTime @"+i.toFixed(3)),r=i),this.state=T.State.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=r,this.tick()}else this._forceStartLoad=!0,this.state=T.State.STOPPED},v.stopLoad=function(){this._forceStartLoad=!1,s.prototype.stopLoad.call(this)},v.doTick=function(){switch(this.state){case T.State.IDLE:this.doTickIdle();break;case T.State.WAITING_LEVEL:var r,i=this.levels,c=this.level,S=i==null||(r=i[c])===null||r===void 0?void 0:r.details;if(S&&(!S.live||this.levelLastLoaded===this.level)){if(this.waitForCdnTuneIn(S))break;this.state=T.State.IDLE;break}break;case T.State.FRAG_LOADING_WAITING_RETRY:var b,D=self.performance.now(),O=this.retryDate;(!O||D>=O||(b=this.media)!==null&&b!==void 0&&b.seeking)&&(this.log("retryDate reached, switch back to IDLE state"),this.state=T.State.IDLE)}this.onTickEnd()},v.onTickEnd=function(){s.prototype.onTickEnd.call(this),this.checkBuffer(),this.checkFragmentChanged()},v.doTickIdle=function(){var r,i,c=this.hls,S=this.levelLastLoaded,b=this.levels,D=this.media,O=c.config,C=c.nextLoadLevel;if(S!==null&&(D||!this.startFragRequested&&O.startFragPrefetch)&&(!this.altAudio||!this.audioOnly)&&b&&b[C]){var x=b[C];this.level=c.nextLoadLevel=C;var P=x.details;if(!P||this.state===T.State.WAITING_LEVEL||P.live&&this.levelLastLoaded!==C)this.state=T.State.WAITING_LEVEL;else{var F=this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:D,o.PlaylistLevelType.MAIN);if(F!==null&&!(F.len>=this.getMaxBufferLength(x.maxBitrate))){if(this._streamEnded(F,P)){var M={};return this.altAudio&&(M.type="video"),this.hls.trigger(R.Events.BUFFER_EOS,M),void(this.state=T.State.ENDED)}var B=F.end,U=this.getNextFragment(B,P);if(this.couldBacktrack&&!this.fragPrevious&&U&&U.sn!=="initSegment"){var G=U.sn-P.startSN;G>1&&(U=P.fragments[G-1],this.fragmentTracker.removeFragment(U))}if(U&&this.fragmentTracker.getState(U)===k.FragmentState.OK&&this.nextLoadPosition>B){var K=this.audioOnly&&!this.altAudio?L.ElementaryStreamTypes.AUDIO:L.ElementaryStreamTypes.VIDEO;this.afterBufferFlushed(D,K,o.PlaylistLevelType.MAIN),U=this.getNextFragment(this.nextLoadPosition,P)}U&&(!U.initSegment||U.initSegment.data||this.bitrateTest||(U=U.initSegment),((r=U.decryptdata)===null||r===void 0?void 0:r.keyFormat)!=="identity"||(i=U.decryptdata)!==null&&i!==void 0&&i.key?this.loadFragment(U,P,B):this.loadKey(U,P))}}}},v.loadFragment=function(r,i,c){var S,b=this.fragmentTracker.getState(r);if(this.fragCurrent=r,b===k.FragmentState.BACKTRACKED){var D=this.fragmentTracker.getBacktrackData(r);if(D)return this._handleFragmentLoadProgress(D),void this._handleFragmentLoadComplete(D);b=k.FragmentState.NOT_LOADED}b===k.FragmentState.NOT_LOADED||b===k.FragmentState.PARTIAL?r.sn==="initSegment"?this._loadInitSegment(r):this.bitrateTest?(r.bitrateTest=!0,this.log("Fragment "+r.sn+" of level "+r.level+" is being downloaded to test bitrate and will not be buffered"),this._loadBitrateTestFrag(r)):(this.startFragRequested=!0,s.prototype.loadFragment.call(this,r,i,c)):b===k.FragmentState.APPENDING?this.reduceMaxBufferLength(r.duration)&&this.fragmentTracker.removeFragment(r):((S=this.media)===null||S===void 0?void 0:S.buffered.length)===0&&this.fragmentTracker.removeAllFragments()},v.getAppendedFrag=function(r){var i=this.fragmentTracker.getAppendedFrag(r,o.PlaylistLevelType.MAIN);return i&&"fragment"in i?i.fragment:i},v.getBufferedFrag=function(r){return this.fragmentTracker.getBufferedFrag(r,o.PlaylistLevelType.MAIN)},v.followingBufferedFrag=function(r){return r?this.getBufferedFrag(r.end+.5):null},v.immediateLevelSwitch=function(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)},v.nextLevelSwitch=function(){var r=this.levels,i=this.media;if(i!=null&&i.readyState){var c,S=this.getAppendedFrag(i.currentTime);if(S&&S.start>1&&this.flushMainBuffer(0,S.start-1),!i.paused&&r){var b=r[this.hls.nextLoadLevel],D=this.fragLastKbps;c=D&&this.fragCurrent?this.fragCurrent.duration*b.maxBitrate/(1e3*D)+1:0}else c=0;var O=this.getBufferedFrag(i.currentTime+c);if(O){var C=this.followingBufferedFrag(O);if(C){this.abortCurrentFrag();var x=C.maxStartPTS?C.maxStartPTS:C.start,P=C.duration,F=Math.max(O.end,x+Math.min(Math.max(P-this.config.maxFragLookUpTolerance,.5*P),.75*P));this.flushMainBuffer(F,Number.POSITIVE_INFINITY)}}}},v.abortCurrentFrag=function(){var r=this.fragCurrent;this.fragCurrent=null,r!=null&&r.loader&&r.loader.abort(),this.state===T.State.KEY_LOADING&&(this.state=T.State.IDLE),this.nextLoadPosition=this.getLoadPosition()},v.flushMainBuffer=function(r,i){s.prototype.flushMainBuffer.call(this,r,i,this.altAudio?"video":null)},v.onMediaAttached=function(r,i){s.prototype.onMediaAttached.call(this,r,i);var c=i.media;this.onvplaying=this.onMediaPlaying.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),c.addEventListener("playing",this.onvplaying),c.addEventListener("seeked",this.onvseeked),this.gapController=new E.default(this.config,c,this.fragmentTracker,this.hls)},v.onMediaDetaching=function(){var r=this.media;r&&(r.removeEventListener("playing",this.onvplaying),r.removeEventListener("seeked",this.onvseeked),this.onvplaying=this.onvseeked=null,this.videoBuffer=null),this.fragPlaying=null,this.gapController&&(this.gapController.destroy(),this.gapController=null),s.prototype.onMediaDetaching.call(this)},v.onMediaPlaying=function(){this.tick()},v.onMediaSeeked=function(){var r=this.media,i=r?r.currentTime:null;Object(_.isFiniteNumber)(i)&&this.log("Media seeked to "+i.toFixed(3)),this.tick()},v.onManifestLoading=function(){this.log("Trigger BUFFER_RESET"),this.hls.trigger(R.Events.BUFFER_RESET,void 0),this.fragmentTracker.removeAllFragments(),this.couldBacktrack=this.stalled=!1,this.startPosition=this.lastCurrentTime=0,this.fragPlaying=null},v.onManifestParsed=function(r,i){var c,S=!1,b=!1;i.levels.forEach(function(D){(c=D.audioCodec)&&(c.indexOf("mp4a.40.2")!==-1&&(S=!0),c.indexOf("mp4a.40.5")!==-1&&(b=!0))}),this.audioCodecSwitch=S&&b&&!Object(A.changeTypeSupported)(),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=i.levels,this.startFragRequested=!1},v.onLevelLoading=function(r,i){var c=this.levels;if(c&&this.state===T.State.IDLE){var S=c[i.level];(!S.details||S.details.live&&this.levelLastLoaded!==i.level||this.waitForCdnTuneIn(S.details))&&(this.state=T.State.WAITING_LEVEL)}},v.onLevelLoaded=function(r,i){var c,S=this.levels,b=i.level,D=i.details,O=D.totalduration;if(S){this.log("Level "+b+" loaded ["+D.startSN+","+D.endSN+"], cc ["+D.startCC+", "+D.endCC+"] duration:"+O);var C=this.fragCurrent;!C||this.state!==T.State.FRAG_LOADING&&this.state!==T.State.FRAG_LOADING_WAITING_RETRY||C.level!==i.level&&C.loader&&(this.state=T.State.IDLE,C.loader.abort());var x=S[b],P=0;if(D.live||(c=x.details)!==null&&c!==void 0&&c.live){if(D.fragments[0]||(D.deltaUpdateFailed=!0),D.deltaUpdateFailed)return;P=this.alignPlaylists(D,x.details)}if(x.details=D,this.levelLastLoaded=b,this.hls.trigger(R.Events.LEVEL_UPDATED,{details:D,level:b}),this.state===T.State.WAITING_LEVEL){if(this.waitForCdnTuneIn(D))return;this.state=T.State.IDLE}this.startFragRequested?D.live&&this.synchronizeToLiveEdge(D):this.setStartPosition(D,P),this.tick()}else this.warn("Levels were reset while loading level "+b)},v._handleFragmentLoadProgress=function(r){var i,c=r.frag,S=r.part,b=r.payload,D=this.levels;if(D){var O=D[c.level],C=O.details;if(C){var x=O.videoCodec,P=C.PTSKnown||!C.live,F=(i=c.initSegment)===null||i===void 0?void 0:i.data,M=this._getAudioCodec(O),B=this.transmuxer=this.transmuxer||new m.default(this.hls,o.PlaylistLevelType.MAIN,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),U=S?S.index:-1,G=U!==-1,K=new h.ChunkMetadata(c.level,c.sn,c.stats.chunkCount,b.byteLength,U,G),H=this.initPTS[c.cc];B.push(b,F,M,x,c,S,C.totalduration,P,K,H)}else this.warn("Dropping fragment "+c.sn+" of level "+c.level+" after level details were reset")}else this.warn("Levels were reset while fragment load was in progress. Fragment "+c.sn+" of level "+c.level+" will not be buffered")},v.onAudioTrackSwitching=function(r,i){var c=this.altAudio,S=!!i.url,b=i.id;if(!S){if(this.mediaBuffer!==this.media){this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;var D=this.fragCurrent;D!=null&&D.loader&&(this.log("Switching to main audio track, cancel main fragment load"),D.loader.abort()),this.resetTransmuxer(),this.resetLoadingState()}else this.audioOnly&&this.resetTransmuxer();var O=this.hls;c&&O.trigger(R.Events.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:"audio"}),O.trigger(R.Events.AUDIO_TRACK_SWITCHED,{id:b})}},v.onAudioTrackSwitched=function(r,i){var c=i.id,S=!!this.hls.audioTracks[c].url;if(S){var b=this.videoBuffer;b&&this.mediaBuffer!==b&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=b)}this.altAudio=S,this.tick()},v.onBufferCreated=function(r,i){var c,S,b=i.tracks,D=!1;for(var O in b){var C=b[O];if(C.id==="main"){if(S=O,c=C,O==="video"){var x=b[O];x&&(this.videoBuffer=x.buffer)}}else D=!0}D&&c?(this.log("Alternate track found, use "+S+".buffered to schedule main fragment loading"),this.mediaBuffer=c.buffer):this.mediaBuffer=this.media},v.onFragBuffered=function(r,i){var c=i.frag,S=i.part;if(!c||c.type===o.PlaylistLevelType.MAIN){if(this.fragContextChanged(c))return this.warn("Fragment "+c.sn+(S?" p: "+S.index:"")+" of level "+c.level+" finished buffering, but was aborted. state: "+this.state),void(this.state===T.State.PARSED&&(this.state=T.State.IDLE));var b=S?S.stats:c.stats;this.fragLastKbps=Math.round(8*b.total/(b.buffering.end-b.loading.first)),c.sn!=="initSegment"&&(this.fragPrevious=c),this.fragBufferedComplete(c,S)}},v.onError=function(r,i){switch(i.details){case y.ErrorDetails.FRAG_LOAD_ERROR:case y.ErrorDetails.FRAG_LOAD_TIMEOUT:case y.ErrorDetails.KEY_LOAD_ERROR:case y.ErrorDetails.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(o.PlaylistLevelType.MAIN,i);break;case y.ErrorDetails.LEVEL_LOAD_ERROR:case y.ErrorDetails.LEVEL_LOAD_TIMEOUT:this.state!==T.State.ERROR&&(i.fatal?(this.warn(""+i.details),this.state=T.State.ERROR):i.levelRetry||this.state!==T.State.WAITING_LEVEL||(this.state=T.State.IDLE));break;case y.ErrorDetails.BUFFER_FULL_ERROR:if(i.parent==="main"&&(this.state===T.State.PARSING||this.state===T.State.PARSED)){var c=!0,S=this.getFwdBufferInfo(this.media,o.PlaylistLevelType.MAIN);S&&S.len>.5&&(c=!this.reduceMaxBufferLength(S.len)),c&&(this.warn("buffer full error also media.currentTime is not buffered, flush main"),this.immediateLevelSwitch()),this.resetLoadingState()}}},v.checkBuffer=function(){var r=this.media,i=this.gapController;if(r&&i&&r.readyState){var c=I.BufferHelper.getBuffered(r);!this.loadedmetadata&&c.length?(this.loadedmetadata=!0,this.seekToStartPos()):i.poll(this.lastCurrentTime),this.lastCurrentTime=r.currentTime}},v.onFragLoadEmergencyAborted=function(){this.state=T.State.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tickImmediate()},v.onBufferFlushed=function(r,i){var c=i.type;if(c!==L.ElementaryStreamTypes.AUDIO||this.audioOnly&&!this.altAudio){var S=(c===L.ElementaryStreamTypes.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;this.afterBufferFlushed(S,c,o.PlaylistLevelType.MAIN)}},v.onLevelsUpdated=function(r,i){this.levels=i.levels},v.swapAudioCodec=function(){this.audioCodecSwap=!this.audioCodecSwap},v.seekToStartPos=function(){var r=this.media,i=r.currentTime,c=this.startPosition;if(c>=0&&i<c){if(r.seeking)return void d.logger.log("could not seek to "+c+", already seeking at "+i);var S=I.BufferHelper.getBuffered(r),b=(S.length?S.start(0):0)-c;b>0&&b<this.config.maxBufferHole&&(d.logger.log("adjusting start position by "+b+" to match buffer start"),c+=b,this.startPosition=c),this.log("seek to target start position "+c+" from current time "+i),r.currentTime=c}},v._getAudioCodec=function(r){var i=this.config.defaultAudioCodec||r.audioCodec;return this.audioCodecSwap&&i&&(this.log("Swapping audio codec"),i=i.indexOf("mp4a.40.5")!==-1?"mp4a.40.2":"mp4a.40.5"),i},v._loadBitrateTestFrag=function(r){var i=this;this._doFragLoad(r).then(function(c){var S=i.hls;if(c&&!S.nextLoadLevel&&!i.fragContextChanged(r)){i.fragLoadError=0,i.state=T.State.IDLE,i.startFragRequested=!1,i.bitrateTest=!1;var b=r.stats;b.parsing.start=b.parsing.end=b.buffering.start=b.buffering.end=self.performance.now(),S.trigger(R.Events.FRAG_LOADED,c)}})},v._handleTransmuxComplete=function(r){var i,c="main",S=this.hls,b=r.remuxResult,D=r.chunkMeta,O=this.getCurrentContext(D);if(!O)return this.warn("The loading context changed while buffering fragment "+D.sn+" of level "+D.level+". This chunk will not be buffered."),void this.resetLiveStartWhenNotLoaded(D.level);var C=O.frag,x=O.part,P=O.level,F=b.video,M=b.text,B=b.id3,U=b.initSegment,G=this.altAudio?void 0:b.audio;if(!this.fragContextChanged(C)){if(this.state=T.State.PARSING,U){U.tracks&&(this._bufferInitSegment(P,U.tracks,C,D),S.trigger(R.Events.FRAG_PARSING_INIT_SEGMENT,{frag:C,id:c,tracks:U.tracks}));var K=U.initPTS,H=U.timescale;Object(_.isFiniteNumber)(K)&&(this.initPTS[C.cc]=K,S.trigger(R.Events.INIT_PTS_FOUND,{frag:C,id:c,initPTS:K,timescale:H}))}if(F&&b.independent!==!1){if(P.details){var Y=F.startPTS,W=F.endPTS,q=F.startDTS,Q=F.endDTS;if(x)x.elementaryStreams[F.type]={startPTS:Y,endPTS:W,startDTS:q,endDTS:Q};else if(F.firstKeyFrame&&F.independent&&(this.couldBacktrack=!0),F.dropped&&F.independent){if(this.getLoadPosition()+this.config.maxBufferHole<Y)return void this.backtrack(C);C.setElementaryStreamInfo(F.type,C.start,W,C.start,Q,!0)}C.setElementaryStreamInfo(F.type,Y,W,q,Q),this.bufferFragmentData(F,C,x,D)}}else if(b.independent===!1)return void this.backtrack(C);if(G){var tt=G.startPTS,rt=G.endPTS,z=G.startDTS,at=G.endDTS;x&&(x.elementaryStreams[L.ElementaryStreamTypes.AUDIO]={startPTS:tt,endPTS:rt,startDTS:z,endDTS:at}),C.setElementaryStreamInfo(L.ElementaryStreamTypes.AUDIO,tt,rt,z,at),this.bufferFragmentData(G,C,x,D)}if(B!=null&&(i=B.samples)!==null&&i!==void 0&&i.length){var X={frag:C,id:c,samples:B.samples};S.trigger(R.Events.FRAG_PARSING_METADATA,X)}if(M){var J={frag:C,id:c,samples:M.samples};S.trigger(R.Events.FRAG_PARSING_USERDATA,J)}}},v._bufferInitSegment=function(r,i,c,S){var b=this;if(this.state===T.State.PARSING){this.audioOnly=!!i.audio&&!i.video,this.altAudio&&!this.audioOnly&&delete i.audio;var D=i.audio,O=i.video,C=i.audiovideo;if(D){var x=r.audioCodec,P=navigator.userAgent.toLowerCase();this.audioCodecSwitch&&(x&&(x=x.indexOf("mp4a.40.5")!==-1?"mp4a.40.2":"mp4a.40.5"),D.metadata.channelCount!==1&&P.indexOf("firefox")===-1&&(x="mp4a.40.5")),P.indexOf("android")!==-1&&D.container!=="audio/mpeg"&&(x="mp4a.40.2",this.log("Android: force audio codec to "+x)),r.audioCodec&&r.audioCodec!==x&&this.log('Swapping manifest audio codec "'+r.audioCodec+'" for "'+x+'"'),D.levelCodec=x,D.id="main",this.log("Init audio buffer, container:"+D.container+", codecs[selected/level/parsed]=["+(x||"")+"/"+(r.audioCodec||"")+"/"+D.codec+"]")}O&&(O.levelCodec=r.videoCodec,O.id="main",this.log("Init video buffer, container:"+O.container+", codecs[level/parsed]=["+(r.videoCodec||"")+"/"+O.codec+"]")),C&&this.log("Init audiovideo buffer, container:"+C.container+", codecs[level/parsed]=["+(r.attrs.CODECS||"")+"/"+C.codec+"]"),this.hls.trigger(R.Events.BUFFER_CODECS,i),Object.keys(i).forEach(function(F){var M=i[F].initSegment;M!=null&&M.byteLength&&b.hls.trigger(R.Events.BUFFER_APPENDING,{type:F,data:M,frag:c,part:null,chunkMeta:S,parent:c.type})}),this.tick()}},v.backtrack=function(r){this.couldBacktrack=!0,this.resetTransmuxer(),this.flushBufferGap(r);var i=this.fragmentTracker.backtrack(r);this.fragPrevious=null,this.nextLoadPosition=r.start,i?this.resetFragmentLoading(r):this.state=T.State.BACKTRACKING},v.checkFragmentChanged=function(){var r=this.media,i=null;if(r&&r.readyState>1&&r.seeking===!1){var c=r.currentTime;if(I.BufferHelper.isBuffered(r,c)?i=this.getAppendedFrag(c):I.BufferHelper.isBuffered(r,c+.1)&&(i=this.getAppendedFrag(c+.1)),i){var S=this.fragPlaying,b=i.level;S&&i.sn===S.sn&&S.level===b&&i.urlId===S.urlId||(this.hls.trigger(R.Events.FRAG_CHANGED,{frag:i}),S&&S.level===b||this.hls.trigger(R.Events.LEVEL_SWITCHED,{level:b}),this.fragPlaying=i)}}},p=l,(g=[{key:"nextLevel",get:function(){var r=this.nextBufferedFrag;return r?r.level:-1}},{key:"currentLevel",get:function(){var r=this.media;if(r){var i=this.getAppendedFrag(r.currentTime);if(i)return i.level}return-1}},{key:"nextBufferedFrag",get:function(){var r=this.media;if(r){var i=this.getAppendedFrag(r.currentTime);return this.followingBufferedFrag(i)}return null}},{key:"forceStartLoad",get:function(){return this._forceStartLoad}}])&&t(p.prototype,g),l}(T.default)},"./src/controller/subtitle-stream-controller.ts":function(N,w,f){f.r(w),f.d(w,"SubtitleStreamController",function(){return d});var _=f("./src/events.ts"),T=f("./src/utils/logger.ts"),A=f("./src/utils/buffer-helper.ts"),R=f("./src/controller/fragment-finders.ts"),I=f("./src/utils/discontinuities.ts"),k=f("./src/controller/level-helper.ts"),o=f("./src/controller/fragment-tracker.ts"),L=f("./src/controller/base-stream-controller.ts"),m=f("./src/types/loader.ts"),h=f("./src/types/level.ts");function E(t,a){for(var e=0;e<a.length;e++){var s=a[e];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}function y(t,a){return(y=Object.setPrototypeOf||function(e,s){return e.__proto__=s,e})(t,a)}var d=function(t){var a,e;function s(p,g){var v;return(v=t.call(this,p,g,"[subtitle-stream-controller]")||this).levels=[],v.currentTrackId=-1,v.tracksBuffered=[],v.mainDetails=null,v._registerListeners(),v}e=t,(a=s).prototype=Object.create(e.prototype),a.prototype.constructor=a,y(a,e);var u,n,l=s.prototype;return l.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null},l._registerListeners=function(){var p=this.hls;p.on(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),p.on(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this),p.on(_.Events.MANIFEST_LOADING,this.onManifestLoading,this),p.on(_.Events.LEVEL_LOADED,this.onLevelLoaded,this),p.on(_.Events.ERROR,this.onError,this),p.on(_.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),p.on(_.Events.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),p.on(_.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),p.on(_.Events.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),p.on(_.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},l._unregisterListeners=function(){var p=this.hls;p.off(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),p.off(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this),p.off(_.Events.MANIFEST_LOADING,this.onManifestLoading,this),p.off(_.Events.LEVEL_LOADED,this.onLevelLoaded,this),p.off(_.Events.ERROR,this.onError,this),p.off(_.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),p.off(_.Events.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),p.off(_.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),p.off(_.Events.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),p.off(_.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},l.startLoad=function(){this.stopLoad(),this.state=L.State.IDLE,this.setInterval(500),this.tick()},l.onManifestLoading=function(){this.mainDetails=null,this.fragmentTracker.removeAllFragments()},l.onLevelLoaded=function(p,g){this.mainDetails=g.details},l.onSubtitleFragProcessed=function(p,g){var v=g.frag,r=g.success;if(this.fragPrevious=v,this.state=L.State.IDLE,r){var i=this.tracksBuffered[this.currentTrackId];if(i){for(var c,S=v.start,b=0;b<i.length;b++)if(S>=i[b].start&&S<=i[b].end){c=i[b];break}var D=v.start+v.duration;c?c.end=D:(c={start:S,end:D},i.push(c)),this.fragmentTracker.fragBuffered(v)}}},l.onBufferFlushing=function(p,g){var v=g.startOffset,r=g.endOffset;if(v===0&&r!==Number.POSITIVE_INFINITY){var i=this.currentTrackId,c=this.levels;if(!c.length||!c[i]||!c[i].details)return;var S=r-c[i].details.targetduration;if(S<=0)return;g.endOffsetSubtitles=Math.max(0,S),this.tracksBuffered.forEach(function(b){for(var D=0;D<b.length;)if(b[D].end<=S)b.shift();else{if(!(b[D].start<S))break;b[D].start=S,D++}}),this.fragmentTracker.removeFragmentsInRange(v,S,m.PlaylistLevelType.SUBTITLE)}},l.onError=function(p,g){var v,r=g.frag;r&&r.type===m.PlaylistLevelType.SUBTITLE&&((v=this.fragCurrent)!==null&&v!==void 0&&v.loader&&this.fragCurrent.loader.abort(),this.state=L.State.IDLE)},l.onSubtitleTracksUpdated=function(p,g){var v=this,r=g.subtitleTracks;this.tracksBuffered=[],this.levels=r.map(function(i){return new h.Level(i)}),this.fragmentTracker.removeAllFragments(),this.fragPrevious=null,this.levels.forEach(function(i){v.tracksBuffered[i.id]=[]}),this.mediaBuffer=null},l.onSubtitleTrackSwitch=function(p,g){if(this.currentTrackId=g.id,this.levels.length&&this.currentTrackId!==-1){var v=this.levels[this.currentTrackId];v!=null&&v.details?(this.mediaBuffer=this.mediaBufferTimeRanges,this.setInterval(500)):this.mediaBuffer=null}else this.clearInterval()},l.onSubtitleTrackLoaded=function(p,g){var v,r=g.details,i=g.id,c=this.currentTrackId,S=this.levels;if(S.length){var b=S[c];if(!(i>=S.length||i!==c)&&b){if(this.mediaBuffer=this.mediaBufferTimeRanges,r.live||(v=b.details)!==null&&v!==void 0&&v.live){var D=this.mainDetails;if(r.deltaUpdateFailed||!D)return;var O=D.fragments[0];b.details?this.alignPlaylists(r,b.details)===0&&O&&Object(k.addSliding)(r,O.start):r.hasProgramDateTime&&D.hasProgramDateTime?Object(I.alignPDT)(r,D):O&&Object(k.addSliding)(r,O.start)}b.details=r,this.levelLastLoaded=i,this.tick(),r.live&&!this.fragCurrent&&this.media&&this.state===L.State.IDLE&&(Object(R.findFragmentByPTS)(null,r.fragments,this.media.currentTime,0)||(this.warn("Subtitle playlist not aligned with playback"),b.details=void 0))}}},l._handleFragmentLoadComplete=function(p){var g=p.frag,v=p.payload,r=g.decryptdata,i=this.hls;if(!this.fragContextChanged(g)&&v&&v.byteLength>0&&r&&r.key&&r.iv&&r.method==="AES-128"){var c=performance.now();this.decrypter.webCryptoDecrypt(new Uint8Array(v),r.key.buffer,r.iv.buffer).then(function(S){var b=performance.now();i.trigger(_.Events.FRAG_DECRYPTED,{frag:g,payload:S,stats:{tstart:c,tdecrypt:b}})})}},l.doTick=function(){if(this.media){if(this.state===L.State.IDLE){var p,g=this.currentTrackId,v=this.levels;if(!v.length||!v[g]||!v[g].details)return;var r=v[g].details,i=r.targetduration,c=this.config,S=this.media,b=A.BufferHelper.bufferedInfo(this.mediaBufferTimeRanges,S.currentTime-i,c.maxBufferHole),D=b.end;if(b.len>this.getMaxBufferLength()+i)return;console.assert(r,"Subtitle track details are defined on idle subtitle stream controller tick");var O,C=r.fragments,x=C.length,P=r.edge,F=this.fragPrevious;if(D<P){var M=c.maxFragLookUpTolerance;F&&r.hasProgramDateTime&&(O=Object(R.findFragmentByPDT)(C,F.endProgramDateTime,M)),O||!(O=Object(R.findFragmentByPTS)(F,C,D,M))&&F&&F.start<C[0].start&&(O=C[0])}else O=C[x-1];(p=O)!==null&&p!==void 0&&p.encrypted?(T.logger.log("Loading key for "+O.sn),this.state=L.State.KEY_LOADING,this.hls.trigger(_.Events.KEY_LOADING,{frag:O})):O&&this.fragmentTracker.getState(O)===o.FragmentState.NOT_LOADED&&this.loadFragment(O,r,D)}}else this.state=L.State.IDLE},l.loadFragment=function(p,g,v){this.fragCurrent=p,t.prototype.loadFragment.call(this,p,g,v)},u=s,(n=[{key:"mediaBufferTimeRanges",get:function(){return this.tracksBuffered[this.currentTrackId]||[]}}])&&E(u.prototype,n),s}(L.default)},"./src/controller/subtitle-track-controller.ts":function(N,w,f){f.r(w);var _=f("./src/events.ts"),T=f("./src/utils/texttrack-utils.ts"),A=f("./src/controller/base-playlist-controller.ts"),R=f("./src/types/loader.ts");function I(m,h){for(var E=0;E<h.length;E++){var y=h[E];y.enumerable=y.enumerable||!1,y.configurable=!0,"value"in y&&(y.writable=!0),Object.defineProperty(m,y.key,y)}}function k(m,h){return(k=Object.setPrototypeOf||function(E,y){return E.__proto__=y,E})(m,h)}var o=function(m){var h,E;function y(e){var s;return(s=m.call(this,e,"[subtitle-track-controller]")||this).media=null,s.tracks=[],s.groupId=null,s.tracksInGroup=[],s.trackId=-1,s.selectDefaultTrack=!0,s.queuedDefaultTrack=-1,s.trackChangeListener=function(){return s.onTextTracksChanged()},s.asyncPollTrackChange=function(){return s.pollTrackChange(0)},s.useTextTrackPolling=!1,s.subtitlePollingInterval=-1,s.subtitleDisplay=!0,s.registerListeners(),s}E=m,(h=y).prototype=Object.create(E.prototype),h.prototype.constructor=h,k(h,E);var d,t,a=y.prototype;return a.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.trackChangeListener=this.asyncPollTrackChange=null,m.prototype.destroy.call(this)},a.registerListeners=function(){var e=this.hls;e.on(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(_.Events.MANIFEST_LOADING,this.onManifestLoading,this),e.on(_.Events.MANIFEST_PARSED,this.onManifestParsed,this),e.on(_.Events.LEVEL_LOADING,this.onLevelLoading,this),e.on(_.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(_.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.on(_.Events.ERROR,this.onError,this)},a.unregisterListeners=function(){var e=this.hls;e.off(_.Events.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(_.Events.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(_.Events.MANIFEST_LOADING,this.onManifestLoading,this),e.off(_.Events.MANIFEST_PARSED,this.onManifestParsed,this),e.off(_.Events.LEVEL_LOADING,this.onLevelLoading,this),e.off(_.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(_.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.off(_.Events.ERROR,this.onError,this)},a.onMediaAttached=function(e,s){this.media=s.media,this.media&&(this.queuedDefaultTrack>-1&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=-1),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.pollTrackChange(500):this.media.textTracks.addEventListener("change",this.asyncPollTrackChange))},a.pollTrackChange=function(e){self.clearInterval(this.subtitlePollingInterval),this.subtitlePollingInterval=self.setInterval(this.trackChangeListener,e)},a.onMediaDetaching=function(){this.media&&(self.clearInterval(this.subtitlePollingInterval),this.useTextTrackPolling||this.media.textTracks.removeEventListener("change",this.asyncPollTrackChange),this.trackId>-1&&(this.queuedDefaultTrack=this.trackId),L(this.media.textTracks).forEach(function(e){Object(T.clearCurrentCues)(e)}),this.subtitleTrack=-1,this.media=null)},a.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.selectDefaultTrack=!0},a.onManifestParsed=function(e,s){this.tracks=s.subtitleTracks},a.onSubtitleTrackLoaded=function(e,s){var u=s.id,n=s.details,l=this.trackId,p=this.tracksInGroup[l];if(p){var g=p.details;p.details=s.details,this.log("subtitle track "+u+" loaded ["+n.startSN+"-"+n.endSN+"]"),u===this.trackId&&(this.retryCount=0,this.playlistLoaded(u,s,g))}else this.warn("Invalid subtitle track id "+u)},a.onLevelLoading=function(e,s){this.switchLevel(s.level)},a.onLevelSwitching=function(e,s){this.switchLevel(s.level)},a.switchLevel=function(e){var s=this.hls.levels[e];if(s!=null&&s.textGroupIds){var u=s.textGroupIds[s.urlId];if(this.groupId!==u){var n=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0,l=this.tracks.filter(function(v){return!u||v.groupId===u});this.tracksInGroup=l;var p=this.findTrackId(n==null?void 0:n.name)||this.findTrackId();this.groupId=u;var g={subtitleTracks:l};this.log("Updating subtitle tracks, "+l.length+' track(s) found in "'+u+'" group-id'),this.hls.trigger(_.Events.SUBTITLE_TRACKS_UPDATED,g),p!==-1&&this.setSubtitleTrack(p,n)}}},a.findTrackId=function(e){for(var s=this.tracksInGroup,u=0;u<s.length;u++){var n=s[u];if((!this.selectDefaultTrack||n.default)&&(!e||e===n.name))return n.id}return-1},a.onError=function(e,s){m.prototype.onError.call(this,e,s),!s.fatal&&s.context&&s.context.type===R.PlaylistContextType.SUBTITLE_TRACK&&s.context.id===this.trackId&&s.context.groupId===this.groupId&&this.retryLoadingOrFail(s)},a.loadPlaylist=function(e){var s=this.tracksInGroup[this.trackId];if(this.shouldLoadTrack(s)){var u=s.id,n=s.groupId,l=s.url;if(e)try{l=e.addDirectives(l)}catch(p){this.warn("Could not construct new URL with HLS Delivery Directives: "+p)}this.log("Loading subtitle playlist for id "+u),this.hls.trigger(_.Events.SUBTITLE_TRACK_LOADING,{url:l,id:u,groupId:n,deliveryDirectives:e||null})}},a.toggleTrackModes=function(e){var s=this,u=this.media,n=this.subtitleDisplay,l=this.trackId;if(u){var p=L(u.textTracks),g=p.filter(function(i){return i.groupId===s.groupId});if(e===-1)[].slice.call(p).forEach(function(i){i.mode="disabled"});else{var v=g[l];v&&(v.mode="disabled")}var r=g[e];r&&(r.mode=n?"showing":"hidden")}},a.setSubtitleTrack=function(e,s){var u,n=this.tracksInGroup;if(this.media){if(this.trackId!==e&&this.toggleTrackModes(e),!(this.trackId===e&&(e===-1||(u=n[e])!==null&&u!==void 0&&u.details)||e<-1||e>=n.length)){this.clearTimer();var l=n[e];if(this.log("Switching to subtitle track "+e),this.trackId=e,l){var p=l.id,g=l.groupId,v=g===void 0?"":g,r=l.name,i=l.type,c=l.url;this.hls.trigger(_.Events.SUBTITLE_TRACK_SWITCH,{id:p,groupId:v,name:r,type:i,url:c});var S=this.switchParams(l.url,s==null?void 0:s.details);this.loadPlaylist(S)}else this.hls.trigger(_.Events.SUBTITLE_TRACK_SWITCH,{id:e})}}else this.queuedDefaultTrack=e},a.onTextTracksChanged=function(){if(this.useTextTrackPolling||self.clearInterval(this.subtitlePollingInterval),this.media&&this.hls.config.renderTextTracksNatively){for(var e=-1,s=L(this.media.textTracks),u=0;u<s.length;u++)if(s[u].mode==="hidden")e=u;else if(s[u].mode==="showing"){e=u;break}this.subtitleTrack!==e&&(this.subtitleTrack=e)}},d=y,(t=[{key:"subtitleTracks",get:function(){return this.tracksInGroup}},{key:"subtitleTrack",get:function(){return this.trackId},set:function(e){this.selectDefaultTrack=!1;var s=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0;this.setSubtitleTrack(e,s)}}])&&I(d.prototype,t),y}(A.default);function L(m){for(var h=[],E=0;E<m.length;E++){var y=m[E];y.kind==="subtitles"&&y.label&&h.push(m[E])}return h}w.default=o},"./src/controller/timeline-controller.ts":function(N,w,f){f.r(w),f.d(w,"TimelineController",function(){return h});var _=f("./src/polyfills/number.ts"),T=f("./src/events.ts"),A=f("./src/utils/cea-608-parser.ts"),R=f("./src/utils/output-filter.ts"),I=f("./src/utils/webvtt-parser.ts"),k=f("./src/utils/texttrack-utils.ts"),o=f("./src/utils/imsc1-ttml-parser.ts"),L=f("./src/types/loader.ts"),m=f("./src/utils/logger.ts"),h=function(){function y(t){if(this.hls=void 0,this.media=null,this.config=void 0,this.enabled=!0,this.Cues=void 0,this.textTracks=[],this.tracks=[],this.initPTS=[],this.timescale=[],this.unparsedVttFrags=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.cea608Parser1=void 0,this.cea608Parser2=void 0,this.lastSn=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!1}},this.captionsProperties=void 0,this.hls=t,this.config=t.config,this.Cues=t.config.cueHandler,this.captionsProperties={textTrack1:{label:this.config.captionsTextTrack1Label,languageCode:this.config.captionsTextTrack1LanguageCode},textTrack2:{label:this.config.captionsTextTrack2Label,languageCode:this.config.captionsTextTrack2LanguageCode},textTrack3:{label:this.config.captionsTextTrack3Label,languageCode:this.config.captionsTextTrack3LanguageCode},textTrack4:{label:this.config.captionsTextTrack4Label,languageCode:this.config.captionsTextTrack4LanguageCode}},this.config.enableCEA708Captions){var a=new R.default(this,"textTrack1"),e=new R.default(this,"textTrack2"),s=new R.default(this,"textTrack3"),u=new R.default(this,"textTrack4");this.cea608Parser1=new A.default(1,a,e),this.cea608Parser2=new A.default(3,s,u)}t.on(T.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(T.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.Events.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(T.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.on(T.Events.FRAG_LOADING,this.onFragLoading,this),t.on(T.Events.FRAG_LOADED,this.onFragLoaded,this),t.on(T.Events.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.on(T.Events.FRAG_DECRYPTED,this.onFragDecrypted,this),t.on(T.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),t.on(T.Events.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.on(T.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)}var d=y.prototype;return d.destroy=function(){var t=this.hls;t.off(T.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(T.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.Events.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(T.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.off(T.Events.FRAG_LOADING,this.onFragLoading,this),t.off(T.Events.FRAG_LOADED,this.onFragLoaded,this),t.off(T.Events.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.off(T.Events.FRAG_DECRYPTED,this.onFragDecrypted,this),t.off(T.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),t.off(T.Events.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.off(T.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),this.hls=this.config=this.cea608Parser1=this.cea608Parser2=null},d.addCues=function(t,a,e,s,u){for(var n,l,p,g,v=!1,r=u.length;r--;){var i=u[r],c=(n=i[0],l=i[1],p=a,g=e,Math.min(l,g)-Math.max(n,p));if(c>=0&&(i[0]=Math.min(i[0],a),i[1]=Math.max(i[1],e),v=!0,c/(e-a)>.5))return}if(v||u.push([a,e]),this.config.renderTextTracksNatively){var S=this.captionsTracks[t];this.Cues.newCue(S,a,e,s)}else{var b=this.Cues.newCue(null,a,e,s);this.hls.trigger(T.Events.CUES_PARSED,{type:"captions",cues:b,track:t})}},d.onInitPtsFound=function(t,a){var e=this,s=a.frag,u=a.id,n=a.initPTS,l=a.timescale,p=this.unparsedVttFrags;u==="main"&&(this.initPTS[s.cc]=n,this.timescale[s.cc]=l),p.length&&(this.unparsedVttFrags=[],p.forEach(function(g){e.onFragLoaded(T.Events.FRAG_LOADED,g)}))},d.getExistingTrack=function(t){var a=this.media;if(a)for(var e=0;e<a.textTracks.length;e++){var s=a.textTracks[e];if(s[t])return s}return null},d.createCaptionsTrack=function(t){this.config.renderTextTracksNatively?this.createNativeTrack(t):this.createNonNativeTrack(t)},d.createNativeTrack=function(t){if(!this.captionsTracks[t]){var a=this.captionsProperties,e=this.captionsTracks,s=this.media,u=a[t],n=u.label,l=u.languageCode,p=this.getExistingTrack(t);if(p)e[t]=p,Object(k.clearCurrentCues)(e[t]),Object(k.sendAddTrackEvent)(e[t],s);else{var g=this.createTextTrack("captions",n,l);g&&(g[t]=!0,e[t]=g)}}},d.createNonNativeTrack=function(t){if(!this.nonNativeCaptionsTracks[t]){var a=this.captionsProperties[t];if(a){var e={_id:t,label:a.label,kind:"captions",default:!!a.media&&!!a.media.default,closedCaptions:a.media};this.nonNativeCaptionsTracks[t]=e,this.hls.trigger(T.Events.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:[e]})}}},d.createTextTrack=function(t,a,e){var s=this.media;if(s)return s.addTextTrack(t,a,e)},d.onMediaAttaching=function(t,a){this.media=a.media,this._cleanTracks()},d.onMediaDetaching=function(){var t=this.captionsTracks;Object.keys(t).forEach(function(a){Object(k.clearCurrentCues)(t[a]),delete t[a]}),this.nonNativeCaptionsTracks={}},d.onManifestLoading=function(){this.lastSn=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!1}},this._cleanTracks(),this.tracks=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.textTracks=[],this.unparsedVttFrags=this.unparsedVttFrags||[],this.initPTS=[],this.timescale=[],this.cea608Parser1&&this.cea608Parser2&&(this.cea608Parser1.reset(),this.cea608Parser2.reset())},d._cleanTracks=function(){var t=this.media;if(t){var a=t.textTracks;if(a)for(var e=0;e<a.length;e++)Object(k.clearCurrentCues)(a[e])}},d.onSubtitleTracksUpdated=function(t,a){var e=this;this.textTracks=[];var s=a.subtitleTracks||[],u=s.some(function(g){return g.textCodec===o.IMSC1_CODEC});if(this.config.enableWebVTT||u&&this.config.enableIMSC1){var n=this.tracks&&s&&this.tracks.length===s.length;if(this.tracks=s||[],this.config.renderTextTracksNatively){var l=this.media?this.media.textTracks:[];this.tracks.forEach(function(g,v){var r;if(v<l.length){for(var i=null,c=0;c<l.length;c++)if(E(l[c],g)){i=l[c];break}i&&(r=i)}r?Object(k.clearCurrentCues)(r):(r=e.createTextTrack("subtitles",g.name,g.lang))&&(r.mode="disabled"),r&&(r.groupId=g.groupId,e.textTracks.push(r))})}else if(!n&&this.tracks&&this.tracks.length){var p=this.tracks.map(function(g){return{label:g.name,kind:g.type.toLowerCase(),default:g.default,subtitleTrack:g}});this.hls.trigger(T.Events.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:p})}}},d.onManifestLoaded=function(t,a){var e=this;this.config.enableCEA708Captions&&a.captions&&a.captions.forEach(function(s){var u=/(?:CC|SERVICE)([1-4])/.exec(s.instreamId);if(u){var n="textTrack"+u[1],l=e.captionsProperties[n];l&&(l.label=s.name,s.lang&&(l.languageCode=s.lang),l.media=s)}})},d.onFragLoading=function(t,a){var e=this.cea608Parser1,s=this.cea608Parser2,u=this.lastSn;if(this.enabled&&e&&s&&a.frag.type===L.PlaylistLevelType.MAIN){var n=a.frag.sn;n!==u+1&&(e.reset(),s.reset()),this.lastSn=n}},d.onFragLoaded=function(t,a){var e=a.frag,s=a.payload,u=this.initPTS,n=this.unparsedVttFrags;if(e.type===L.PlaylistLevelType.SUBTITLE)if(s.byteLength){if(!Object(_.isFiniteNumber)(u[e.cc]))return n.push(a),void(u.length&&this.hls.trigger(T.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e,error:new Error("Missing initial subtitle PTS")}));var l=e.decryptdata;if(l==null||l.key==null||l.method!=="AES-128"){var p=this.tracks[e.level],g=this.vttCCs;g[e.cc]||(g[e.cc]={start:e.start,prevCC:this.prevCC,new:!0},this.prevCC=e.cc),p&&p.textCodec===o.IMSC1_CODEC?this._parseIMSC1(e,s):this._parseVTTs(e,s,g)}}else this.hls.trigger(T.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e,error:new Error("Empty subtitle payload")})},d._parseIMSC1=function(t,a){var e=this,s=this.hls;Object(o.parseIMSC1)(a,this.initPTS[t.cc],this.timescale[t.cc],function(u){e._appendCues(u,t.level),s.trigger(T.Events.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})},function(u){m.logger.log("Failed to parse IMSC1: "+u),s.trigger(T.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:u})})},d._parseVTTs=function(t,a,e){var s=this,u=this.hls;Object(I.parseWebVTT)(a,this.initPTS[t.cc],this.timescale[t.cc],e,t.cc,t.start,function(n){s._appendCues(n,t.level),u.trigger(T.Events.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})},function(n){s._fallbackToIMSC1(t,a),m.logger.log("Failed to parse VTT cue: "+n),u.trigger(T.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:n})})},d._fallbackToIMSC1=function(t,a){var e=this,s=this.tracks[t.level];s.textCodec||Object(o.parseIMSC1)(a,this.initPTS[t.cc],this.timescale[t.cc],function(){s.textCodec=o.IMSC1_CODEC,e._parseIMSC1(t,a)},function(){s.textCodec="wvtt"})},d._appendCues=function(t,a){var e=this.hls;if(this.config.renderTextTracksNatively){var s=this.textTracks[a];if(s.mode==="disabled")return;t.forEach(function(n){return Object(k.addCueToTrack)(s,n)})}else{var u=this.tracks[a].default?"default":"subtitles"+a;e.trigger(T.Events.CUES_PARSED,{type:"subtitles",cues:t,track:u})}},d.onFragDecrypted=function(t,a){var e=a.frag;if(e.type===L.PlaylistLevelType.SUBTITLE){if(!Object(_.isFiniteNumber)(this.initPTS[e.cc]))return void this.unparsedVttFrags.push(a);this.onFragLoaded(T.Events.FRAG_LOADED,a)}},d.onSubtitleTracksCleared=function(){this.tracks=[],this.captionsTracks={}},d.onFragParsingUserdata=function(t,a){var e=this.cea608Parser1,s=this.cea608Parser2;if(this.enabled&&e&&s)for(var u=0;u<a.samples.length;u++){var n=a.samples[u].bytes;if(n){var l=this.extractCea608Data(n);e.addData(a.samples[u].pts,l[0]),s.addData(a.samples[u].pts,l[1])}}},d.onBufferFlushing=function(t,a){var e=a.startOffset,s=a.endOffset,u=a.endOffsetSubtitles,n=a.type,l=this.media;if(l&&!(l.currentTime<s)){if(!n||n==="video"){var p=this.captionsTracks;Object.keys(p).forEach(function(v){return Object(k.removeCuesInRange)(p[v],e,s)})}if(this.config.renderTextTracksNatively&&e===0&&u!==void 0){var g=this.textTracks;Object.keys(g).forEach(function(v){return Object(k.removeCuesInRange)(g[v],e,u)})}}},d.extractCea608Data=function(t){for(var a=31&t[0],e=2,s=[[],[]],u=0;u<a;u++){var n=t[e++],l=127&t[e++],p=127&t[e++],g=3&n;l===0&&p===0||(4&n)!=0&&(g!==0&&g!==1||(s[g].push(l),s[g].push(p)))}return s},y}();function E(y,d){return y&&y.label===d.name&&!(y.textTrack1||y.textTrack2)}},"./src/crypt/aes-crypto.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return _});var _=function(){function T(A,R){this.subtle=void 0,this.aesIV=void 0,this.subtle=A,this.aesIV=R}return T.prototype.decrypt=function(A,R){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},R,A)},T}()},"./src/crypt/aes-decryptor.ts":function(N,w,f){f.r(w),f.d(w,"removePadding",function(){return T}),f.d(w,"default",function(){return A});var _=f("./src/utils/typed-array.ts");function T(R){var I=R.byteLength,k=I&&new DataView(R.buffer).getUint8(I-1);return k?Object(_.sliceUint8)(R,0,I-k):R}var A=function(){function R(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}var I=R.prototype;return I.uint8ArrayToUint32Array_=function(k){for(var o=new DataView(k),L=new Uint32Array(4),m=0;m<4;m++)L[m]=o.getUint32(4*m);return L},I.initTable=function(){var k=this.sBox,o=this.invSBox,L=this.subMix,m=L[0],h=L[1],E=L[2],y=L[3],d=this.invSubMix,t=d[0],a=d[1],e=d[2],s=d[3],u=new Uint32Array(256),n=0,l=0,p=0;for(p=0;p<256;p++)u[p]=p<128?p<<1:p<<1^283;for(p=0;p<256;p++){var g=l^l<<1^l<<2^l<<3^l<<4;g=g>>>8^255&g^99,k[n]=g,o[g]=n;var v=u[n],r=u[v],i=u[r],c=257*u[g]^16843008*g;m[n]=c<<24|c>>>8,h[n]=c<<16|c>>>16,E[n]=c<<8|c>>>24,y[n]=c,c=16843009*i^65537*r^257*v^16843008*n,t[g]=c<<24|c>>>8,a[g]=c<<16|c>>>16,e[g]=c<<8|c>>>24,s[g]=c,n?(n=v^u[u[u[i^v]]],l^=u[u[l]]):n=l=1}},I.expandKey=function(k){for(var o=this.uint8ArrayToUint32Array_(k),L=!0,m=0;m<o.length&&L;)L=o[m]===this.key[m],m++;if(!L){this.key=o;var h=this.keySize=o.length;if(h!==4&&h!==6&&h!==8)throw new Error("Invalid aes key size="+h);var E,y,d,t,a=this.ksRows=4*(h+6+1),e=this.keySchedule=new Uint32Array(a),s=this.invKeySchedule=new Uint32Array(a),u=this.sBox,n=this.rcon,l=this.invSubMix,p=l[0],g=l[1],v=l[2],r=l[3];for(E=0;E<a;E++)E<h?d=e[E]=o[E]:(t=d,E%h==0?(t=u[(t=t<<8|t>>>24)>>>24]<<24|u[t>>>16&255]<<16|u[t>>>8&255]<<8|u[255&t],t^=n[E/h|0]<<24):h>6&&E%h==4&&(t=u[t>>>24]<<24|u[t>>>16&255]<<16|u[t>>>8&255]<<8|u[255&t]),e[E]=d=(e[E-h]^t)>>>0);for(y=0;y<a;y++)E=a-y,t=3&y?e[E]:e[E-4],s[y]=y<4||E<=4?t:p[u[t>>>24]]^g[u[t>>>16&255]]^v[u[t>>>8&255]]^r[u[255&t]],s[y]=s[y]>>>0}},I.networkToHostOrderSwap=function(k){return k<<24|(65280&k)<<8|(16711680&k)>>8|k>>>24},I.decrypt=function(k,o,L){for(var m,h,E,y,d,t,a,e,s,u,n,l,p,g,v=this.keySize+6,r=this.invKeySchedule,i=this.invSBox,c=this.invSubMix,S=c[0],b=c[1],D=c[2],O=c[3],C=this.uint8ArrayToUint32Array_(L),x=C[0],P=C[1],F=C[2],M=C[3],B=new Int32Array(k),U=new Int32Array(B.length),G=this.networkToHostOrderSwap;o<B.length;){for(s=G(B[o]),u=G(B[o+1]),n=G(B[o+2]),l=G(B[o+3]),d=s^r[0],t=l^r[1],a=n^r[2],e=u^r[3],p=4,g=1;g<v;g++)m=S[d>>>24]^b[t>>16&255]^D[a>>8&255]^O[255&e]^r[p],h=S[t>>>24]^b[a>>16&255]^D[e>>8&255]^O[255&d]^r[p+1],E=S[a>>>24]^b[e>>16&255]^D[d>>8&255]^O[255&t]^r[p+2],y=S[e>>>24]^b[d>>16&255]^D[t>>8&255]^O[255&a]^r[p+3],d=m,t=h,a=E,e=y,p+=4;m=i[d>>>24]<<24^i[t>>16&255]<<16^i[a>>8&255]<<8^i[255&e]^r[p],h=i[t>>>24]<<24^i[a>>16&255]<<16^i[e>>8&255]<<8^i[255&d]^r[p+1],E=i[a>>>24]<<24^i[e>>16&255]<<16^i[d>>8&255]<<8^i[255&t]^r[p+2],y=i[e>>>24]<<24^i[d>>16&255]<<16^i[t>>8&255]<<8^i[255&a]^r[p+3],U[o]=G(m^x),U[o+1]=G(y^P),U[o+2]=G(E^F),U[o+3]=G(h^M),x=s,P=u,F=n,M=l,o+=4}return U.buffer},R}()},"./src/crypt/decrypter.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return o});var _=f("./src/crypt/aes-crypto.ts"),T=f("./src/crypt/fast-aes-key.ts"),A=f("./src/crypt/aes-decryptor.ts"),R=f("./src/utils/logger.ts"),I=f("./src/utils/mp4-tools.ts"),k=f("./src/utils/typed-array.ts"),o=function(){function L(h,E,y){var d=(y===void 0?{}:y).removePKCS7Padding,t=d===void 0||d;if(this.logEnabled=!0,this.observer=void 0,this.config=void 0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.observer=h,this.config=E,this.removePKCS7Padding=t,t)try{var a=self.crypto;a&&(this.subtle=a.subtle||a.webkitSubtle)}catch{}this.subtle===null&&(this.config.enableSoftwareAES=!0)}var m=L.prototype;return m.destroy=function(){this.observer=null},m.isSync=function(){return this.config.enableSoftwareAES},m.flush=function(){var h=this.currentResult;if(h){var E=new Uint8Array(h);return this.reset(),this.removePKCS7Padding?Object(A.removePadding)(E):E}this.reset()},m.reset=function(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)},m.decrypt=function(h,E,y,d){if(this.config.enableSoftwareAES){this.softwareDecrypt(new Uint8Array(h),E,y);var t=this.flush();t&&d(t.buffer)}else this.webCryptoDecrypt(new Uint8Array(h),E,y).then(d)},m.softwareDecrypt=function(h,E,y){var d=this.currentIV,t=this.currentResult,a=this.remainderData;this.logOnce("JS AES decrypt"),a&&(h=Object(I.appendUint8Array)(a,h),this.remainderData=null);var e=this.getValidChunk(h);if(!e.length)return null;d&&(y=d);var s=this.softwareDecrypter;s||(s=this.softwareDecrypter=new A.default),s.expandKey(E);var u=t;return this.currentResult=s.decrypt(e.buffer,0,y),this.currentIV=Object(k.sliceUint8)(e,-16).buffer,u||null},m.webCryptoDecrypt=function(h,E,y){var d=this,t=this.subtle;return this.key===E&&this.fastAesKey||(this.key=E,this.fastAesKey=new T.default(t,E)),this.fastAesKey.expandKey().then(function(a){return t?new _.default(t,y).decrypt(h.buffer,a):Promise.reject(new Error("web crypto not initialized"))}).catch(function(a){return d.onWebCryptoError(a,h,E,y)})},m.onWebCryptoError=function(h,E,y,d){return R.logger.warn("[decrypter.ts]: WebCrypto Error, disable WebCrypto API:",h),this.config.enableSoftwareAES=!0,this.logEnabled=!0,this.softwareDecrypt(E,y,d)},m.getValidChunk=function(h){var E=h,y=h.length-h.length%16;return y!==h.length&&(E=Object(k.sliceUint8)(h,0,y),this.remainderData=Object(k.sliceUint8)(h,y)),E},m.logOnce=function(h){this.logEnabled&&(R.logger.log("[decrypter.ts]: "+h),this.logEnabled=!1)},L}()},"./src/crypt/fast-aes-key.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return _});var _=function(){function T(A,R){this.subtle=void 0,this.key=void 0,this.subtle=A,this.key=R}return T.prototype.expandKey=function(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])},T}()},"./src/demux/aacdemuxer.ts":function(N,w,f){f.r(w);var _=f("./src/demux/base-audio-demuxer.ts"),T=f("./src/demux/adts.ts"),A=f("./src/utils/logger.ts"),R=f("./src/demux/id3.ts");function I(o,L){return(I=Object.setPrototypeOf||function(m,h){return m.__proto__=h,m})(o,L)}var k=function(o){var L,m;function h(y,d){var t;return(t=o.call(this)||this).observer=void 0,t.config=void 0,t.observer=y,t.config=d,t}m=o,(L=h).prototype=Object.create(m.prototype),L.prototype.constructor=L,I(L,m);var E=h.prototype;return E.resetInitSegment=function(y,d,t){o.prototype.resetInitSegment.call(this,y,d,t),this._audioTrack={container:"audio/adts",type:"audio",id:0,pid:-1,sequenceNumber:0,isAAC:!0,samples:[],manifestCodec:y,duration:t,inputTimeScale:9e4,dropped:0}},h.probe=function(y){if(!y)return!1;for(var d=(R.getID3Data(y,0)||[]).length,t=y.length;d<t;d++)if(T.probe(y,d))return A.logger.log("ADTS sync word found !"),!0;return!1},E.canParse=function(y,d){return T.canParse(y,d)},E.appendFrame=function(y,d,t){T.initTrackConfig(y,this.observer,d,t,y.manifestCodec);var a=T.appendFrame(y,d,t,this.initPTS,this.frameIndex);if(a&&a.missing===0)return a},h}(_.default);k.minProbeByteLength=9,w.default=k},"./src/demux/adts.ts":function(N,w,f){f.r(w),f.d(w,"getAudioConfig",function(){return R}),f.d(w,"isHeaderPattern",function(){return I}),f.d(w,"getHeaderLength",function(){return k}),f.d(w,"getFullFrameLength",function(){return o}),f.d(w,"canGetFrameLength",function(){return L}),f.d(w,"isHeader",function(){return m}),f.d(w,"canParse",function(){return h}),f.d(w,"probe",function(){return E}),f.d(w,"initTrackConfig",function(){return y}),f.d(w,"getFrameDuration",function(){return d}),f.d(w,"parseFrameHeader",function(){return t}),f.d(w,"appendFrame",function(){return a});var _=f("./src/utils/logger.ts"),T=f("./src/errors.ts"),A=f("./src/events.ts");function R(e,s,u,n){var l,p,g,v,r=navigator.userAgent.toLowerCase(),i=n,c=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];l=1+((192&s[u+2])>>>6);var S=(60&s[u+2])>>>2;if(!(S>c.length-1))return g=(1&s[u+2])<<2,g|=(192&s[u+3])>>>6,_.logger.log("manifest codec:"+n+", ADTS type:"+l+", samplingIndex:"+S),/firefox/i.test(r)?S>=6?(l=5,v=new Array(4),p=S-3):(l=2,v=new Array(2),p=S):r.indexOf("android")!==-1?(l=2,v=new Array(2),p=S):(l=5,v=new Array(4),n&&(n.indexOf("mp4a.40.29")!==-1||n.indexOf("mp4a.40.5")!==-1)||!n&&S>=6?p=S-3:((n&&n.indexOf("mp4a.40.2")!==-1&&(S>=6&&g===1||/vivaldi/i.test(r))||!n&&g===1)&&(l=2,v=new Array(2)),p=S)),v[0]=l<<3,v[0]|=(14&S)>>1,v[1]|=(1&S)<<7,v[1]|=g<<3,l===5&&(v[1]|=(14&p)>>1,v[2]=(1&p)<<7,v[2]|=8,v[3]=0),{config:v,samplerate:c[S],channelCount:g,codec:"mp4a.40."+l,manifestCodec:i};e.trigger(A.Events.ERROR,{type:T.ErrorTypes.MEDIA_ERROR,details:T.ErrorDetails.FRAG_PARSING_ERROR,fatal:!0,reason:"invalid ADTS sampling index:"+S})}function I(e,s){return e[s]===255&&(246&e[s+1])==240}function k(e,s){return 1&e[s+1]?7:9}function o(e,s){return(3&e[s+3])<<11|e[s+4]<<3|(224&e[s+5])>>>5}function L(e,s){return s+5<e.length}function m(e,s){return s+1<e.length&&I(e,s)}function h(e,s){return L(e,s)&&I(e,s)&&o(e,s)<=e.length-s}function E(e,s){if(m(e,s)){var u=k(e,s);if(s+u>=e.length)return!1;var n=o(e,s);if(n<=u)return!1;var l=s+n;return l===e.length||m(e,l)}return!1}function y(e,s,u,n,l){if(!e.samplerate){var p=R(s,u,n,l);if(!p)return;e.config=p.config,e.samplerate=p.samplerate,e.channelCount=p.channelCount,e.codec=p.codec,e.manifestCodec=p.manifestCodec,_.logger.log("parsed codec:"+e.codec+", rate:"+p.samplerate+", channels:"+p.channelCount)}}function d(e){return 9216e4/e}function t(e,s,u,n,l){var p=k(e,s),g=o(e,s);if((g-=p)>0)return{headerLength:p,frameLength:g,stamp:u+n*l}}function a(e,s,u,n,l){var p=t(s,u,n,l,d(e.samplerate));if(p){var g,v=p.frameLength,r=p.headerLength,i=p.stamp,c=r+v,S=Math.max(0,u+c-s.length);S?(g=new Uint8Array(c-r)).set(s.subarray(u+r,s.length),0):g=s.subarray(u+r,u+c);var b={unit:g,pts:i};return S||e.samples.push(b),{sample:b,length:c,missing:S}}}},"./src/demux/base-audio-demuxer.ts":function(N,w,f){f.r(w),f.d(w,"initPTSFn",function(){return o});var _=f("./src/polyfills/number.ts"),T=f("./src/demux/id3.ts"),A=f("./src/demux/dummy-demuxed-track.ts"),R=f("./src/utils/mp4-tools.ts"),I=f("./src/utils/typed-array.ts"),k=function(){function L(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.initPTS=null}var m=L.prototype;return m.resetInitSegment=function(h,E,y){this._id3Track={type:"id3",id:0,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}},m.resetTimeStamp=function(){},m.resetContiguity=function(){},m.canParse=function(h,E){return!1},m.appendFrame=function(h,E,y){},m.demux=function(h,E){this.cachedData&&(h=Object(R.appendUint8Array)(this.cachedData,h),this.cachedData=null);var y,d,t=T.getID3Data(h,0),a=t?t.length:0,e=this._audioTrack,s=this._id3Track,u=t?T.getTimeStamp(t):void 0,n=h.length;for(this.frameIndex!==0&&this.initPTS!==null||(this.initPTS=o(u,E)),t&&t.length>0&&s.samples.push({pts:this.initPTS,dts:this.initPTS,data:t}),d=this.initPTS;a<n;){if(this.canParse(h,a)){var l=this.appendFrame(e,h,a);l?(this.frameIndex++,d=l.sample.pts,y=a+=l.length):a=n}else T.canParse(h,a)?(t=T.getID3Data(h,a),s.samples.push({pts:d,dts:d,data:t}),y=a+=t.length):a++;if(a===n&&y!==n){var p=Object(I.sliceUint8)(h,y);this.cachedData?this.cachedData=Object(R.appendUint8Array)(this.cachedData,p):this.cachedData=p}}return{audioTrack:e,avcTrack:Object(A.dummyTrack)(),id3Track:s,textTrack:Object(A.dummyTrack)()}},m.demuxSampleAes=function(h,E,y){return Promise.reject(new Error("["+this+"] This demuxer does not support Sample-AES decryption"))},m.flush=function(h){var E=this.cachedData;return E&&(this.cachedData=null,this.demux(E,0)),this.frameIndex=0,{audioTrack:this._audioTrack,avcTrack:Object(A.dummyTrack)(),id3Track:this._id3Track,textTrack:Object(A.dummyTrack)()}},m.destroy=function(){},L}(),o=function(L,m){return Object(_.isFiniteNumber)(L)?90*L:9e4*m};w.default=k},"./src/demux/chunk-cache.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return _});var _=function(){function T(){this.chunks=[],this.dataLength=0}var A=T.prototype;return A.push=function(R){this.chunks.push(R),this.dataLength+=R.length},A.flush=function(){var R,I=this.chunks,k=this.dataLength;return I.length?(R=I.length===1?I[0]:function(o,L){for(var m=new Uint8Array(L),h=0,E=0;E<o.length;E++){var y=o[E];m.set(y,h),h+=y.length}return m}(I,k),this.reset(),R):new Uint8Array(0)},A.reset=function(){this.chunks.length=0,this.dataLength=0},T}()},"./src/demux/dummy-demuxed-track.ts":function(N,w,f){function _(){return{type:"",id:-1,pid:-1,inputTimeScale:9e4,sequenceNumber:-1,samples:[],dropped:0}}f.r(w),f.d(w,"dummyTrack",function(){return _})},"./src/demux/exp-golomb.ts":function(N,w,f){f.r(w);var _=f("./src/utils/logger.ts"),T=function(){function A(I){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=I,this.bytesAvailable=I.byteLength,this.word=0,this.bitsAvailable=0}var R=A.prototype;return R.loadWord=function(){var I=this.data,k=this.bytesAvailable,o=I.byteLength-k,L=new Uint8Array(4),m=Math.min(4,k);if(m===0)throw new Error("no bytes available");L.set(I.subarray(o,o+m)),this.word=new DataView(L.buffer).getUint32(0),this.bitsAvailable=8*m,this.bytesAvailable-=m},R.skipBits=function(I){var k;this.bitsAvailable>I?(this.word<<=I,this.bitsAvailable-=I):(I-=this.bitsAvailable,I-=(k=I>>3)>>3,this.bytesAvailable-=k,this.loadWord(),this.word<<=I,this.bitsAvailable-=I)},R.readBits=function(I){var k=Math.min(this.bitsAvailable,I),o=this.word>>>32-k;return I>32&&_.logger.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=k,this.bitsAvailable>0?this.word<<=k:this.bytesAvailable>0&&this.loadWord(),(k=I-k)>0&&this.bitsAvailable?o<<k|this.readBits(k):o},R.skipLZ=function(){var I;for(I=0;I<this.bitsAvailable;++I)if((this.word&2147483648>>>I)!=0)return this.word<<=I,this.bitsAvailable-=I,I;return this.loadWord(),I+this.skipLZ()},R.skipUEG=function(){this.skipBits(1+this.skipLZ())},R.skipEG=function(){this.skipBits(1+this.skipLZ())},R.readUEG=function(){var I=this.skipLZ();return this.readBits(I+1)-1},R.readEG=function(){var I=this.readUEG();return 1&I?1+I>>>1:-1*(I>>>1)},R.readBoolean=function(){return this.readBits(1)===1},R.readUByte=function(){return this.readBits(8)},R.readUShort=function(){return this.readBits(16)},R.readUInt=function(){return this.readBits(32)},R.skipScalingList=function(I){for(var k=8,o=8,L=0;L<I;L++)o!==0&&(o=(k+this.readEG()+256)%256),k=o===0?k:o},R.readSPS=function(){var I,k,o,L=0,m=0,h=0,E=0,y=this.readUByte.bind(this),d=this.readBits.bind(this),t=this.readUEG.bind(this),a=this.readBoolean.bind(this),e=this.skipBits.bind(this),s=this.skipEG.bind(this),u=this.skipUEG.bind(this),n=this.skipScalingList.bind(this);y();var l=y();if(d(5),e(3),y(),u(),l===100||l===110||l===122||l===244||l===44||l===83||l===86||l===118||l===128){var p=t();if(p===3&&e(1),u(),u(),e(1),a())for(k=p!==3?8:12,o=0;o<k;o++)a()&&n(o<6?16:64)}u();var g=t();if(g===0)t();else if(g===1)for(e(1),s(),s(),I=t(),o=0;o<I;o++)s();u(),e(1);var v=t(),r=t(),i=d(1);i===0&&e(1),e(1),a()&&(L=t(),m=t(),h=t(),E=t());var c=[1,1];if(a()&&a())switch(y()){case 1:c=[1,1];break;case 2:c=[12,11];break;case 3:c=[10,11];break;case 4:c=[16,11];break;case 5:c=[40,33];break;case 6:c=[24,11];break;case 7:c=[20,11];break;case 8:c=[32,11];break;case 9:c=[80,33];break;case 10:c=[18,11];break;case 11:c=[15,11];break;case 12:c=[64,33];break;case 13:c=[160,99];break;case 14:c=[4,3];break;case 15:c=[3,2];break;case 16:c=[2,1];break;case 255:c=[y()<<8|y(),y()<<8|y()]}return{width:Math.ceil(16*(v+1)-2*L-2*m),height:(2-i)*(r+1)*16-(i?2:4)*(h+E),pixelRatio:c}},R.readSliceType=function(){return this.readUByte(),this.readUEG(),this.readUEG()},A}();w.default=T},"./src/demux/id3.ts":function(N,w,f){f.r(w),f.d(w,"isHeader",function(){return T}),f.d(w,"isFooter",function(){return A}),f.d(w,"getID3Data",function(){return R}),f.d(w,"canParse",function(){return k}),f.d(w,"getTimeStamp",function(){return o}),f.d(w,"isTimeStampFrame",function(){return L}),f.d(w,"getID3Frames",function(){return h}),f.d(w,"decodeFrame",function(){return E}),f.d(w,"utf8ArrayToStr",function(){return e}),f.d(w,"testables",function(){return s});var _,T=function(u,n){return n+10<=u.length&&u[n]===73&&u[n+1]===68&&u[n+2]===51&&u[n+3]<255&&u[n+4]<255&&u[n+6]<128&&u[n+7]<128&&u[n+8]<128&&u[n+9]<128},A=function(u,n){return n+10<=u.length&&u[n]===51&&u[n+1]===68&&u[n+2]===73&&u[n+3]<255&&u[n+4]<255&&u[n+6]<128&&u[n+7]<128&&u[n+8]<128&&u[n+9]<128},R=function(u,n){for(var l=n,p=0;T(u,n);)p+=10,p+=I(u,n+6),A(u,n+10)&&(p+=10),n+=p;if(p>0)return u.subarray(l,l+p)},I=function(u,n){var l=0;return l=(127&u[n])<<21,l|=(127&u[n+1])<<14,l|=(127&u[n+2])<<7,l|=127&u[n+3]},k=function(u,n){return T(u,n)&&I(u,n+6)+10<=u.length-n},o=function(u){for(var n=h(u),l=0;l<n.length;l++){var p=n[l];if(L(p))return a(p)}},L=function(u){return u&&u.key==="PRIV"&&u.info==="com.apple.streaming.transportStreamTimestamp"},m=function(u){var n=String.fromCharCode(u[0],u[1],u[2],u[3]),l=I(u,4);return{type:n,size:l,data:u.subarray(10,10+l)}},h=function(u){for(var n=0,l=[];T(u,n);){for(var p=I(u,n+6),g=(n+=10)+p;n+8<g;){var v=m(u.subarray(n)),r=E(v);r&&l.push(r),n+=v.size+10}A(u,n)&&(n+=10)}return l},E=function(u){return u.type==="PRIV"?y(u):u.type[0]==="W"?t(u):d(u)},y=function(u){if(!(u.size<2)){var n=e(u.data,!0),l=new Uint8Array(u.data.subarray(n.length+1));return{key:u.type,info:n,data:l.buffer}}},d=function(u){if(!(u.size<2)){if(u.type==="TXXX"){var n=1,l=e(u.data.subarray(n),!0);n+=l.length+1;var p=e(u.data.subarray(n));return{key:u.type,info:l,data:p}}var g=e(u.data.subarray(1));return{key:u.type,data:g}}},t=function(u){if(u.type==="WXXX"){if(u.size<2)return;var n=1,l=e(u.data.subarray(n),!0);n+=l.length+1;var p=e(u.data.subarray(n));return{key:u.type,info:l,data:p}}var g=e(u.data);return{key:u.type,data:g}},a=function(u){if(u.data.byteLength===8){var n=new Uint8Array(u.data),l=1&n[3],p=(n[4]<<23)+(n[5]<<15)+(n[6]<<7)+n[7];return p/=45,l&&(p+=4772185884e-2),Math.round(p)}},e=function(u,n){n===void 0&&(n=!1);var l=(_||self.TextDecoder===void 0||(_=new self.TextDecoder("utf-8")),_);if(l){var p=l.decode(u);if(n){var g=p.indexOf("\0");return g!==-1?p.substring(0,g):p}return p.replace(/\0/g,"")}for(var v,r,i,c=u.length,S="",b=0;b<c;){if((v=u[b++])===0&&n)return S;if(v!==0&&v!==3)switch(v>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:S+=String.fromCharCode(v);break;case 12:case 13:r=u[b++],S+=String.fromCharCode((31&v)<<6|63&r);break;case 14:r=u[b++],i=u[b++],S+=String.fromCharCode((15&v)<<12|(63&r)<<6|(63&i)<<0)}}return S},s={decodeTextFrame:d}},"./src/demux/mp3demuxer.ts":function(N,w,f){f.r(w);var _=f("./src/demux/base-audio-demuxer.ts"),T=f("./src/demux/id3.ts"),A=f("./src/utils/logger.ts"),R=f("./src/demux/mpegaudio.ts");function I(o,L){return(I=Object.setPrototypeOf||function(m,h){return m.__proto__=h,m})(o,L)}var k=function(o){var L,m;function h(){return o.apply(this,arguments)||this}m=o,(L=h).prototype=Object.create(m.prototype),L.prototype.constructor=L,I(L,m);var E=h.prototype;return E.resetInitSegment=function(y,d,t){o.prototype.resetInitSegment.call(this,y,d,t),this._audioTrack={container:"audio/mpeg",type:"audio",id:0,pid:-1,sequenceNumber:0,isAAC:!1,samples:[],manifestCodec:y,duration:t,inputTimeScale:9e4,dropped:0}},h.probe=function(y){if(!y)return!1;for(var d=(T.getID3Data(y,0)||[]).length,t=y.length;d<t;d++)if(R.probe(y,d))return A.logger.log("MPEG Audio sync word found !"),!0;return!1},E.canParse=function(y,d){return R.canParse(y,d)},E.appendFrame=function(y,d,t){if(this.initPTS!==null)return R.appendFrame(y,d,t,this.initPTS,this.frameIndex)},h}(_.default);k.minProbeByteLength=4,w.default=k},"./src/demux/mp4demuxer.ts":function(N,w,f){f.r(w);var _=f("./src/utils/mp4-tools.ts"),T=f("./src/demux/dummy-demuxed-track.ts"),A=function(){function R(k,o){this.remainderData=null,this.config=void 0,this.config=o}var I=R.prototype;return I.resetTimeStamp=function(){},I.resetInitSegment=function(){},I.resetContiguity=function(){},R.probe=function(k){return Object(_.findBox)({data:k,start:0,end:Math.min(k.length,16384)},["moof"]).length>0},I.demux=function(k){var o=k,L=Object(T.dummyTrack)();if(this.config.progressive){this.remainderData&&(o=Object(_.appendUint8Array)(this.remainderData,k));var m=Object(_.segmentValidRange)(o);this.remainderData=m.remainder,L.samples=m.valid||new Uint8Array}else L.samples=o;return{audioTrack:Object(T.dummyTrack)(),avcTrack:L,id3Track:Object(T.dummyTrack)(),textTrack:Object(T.dummyTrack)()}},I.flush=function(){var k=Object(T.dummyTrack)();return k.samples=this.remainderData||new Uint8Array,this.remainderData=null,{audioTrack:Object(T.dummyTrack)(),avcTrack:k,id3Track:Object(T.dummyTrack)(),textTrack:Object(T.dummyTrack)()}},I.demuxSampleAes=function(k,o,L){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))},I.destroy=function(){},R}();A.minProbeByteLength=1024,w.default=A},"./src/demux/mpegaudio.ts":function(N,w,f){f.r(w),f.d(w,"appendFrame",function(){return k}),f.d(w,"parseHeader",function(){return o}),f.d(w,"isHeaderPattern",function(){return L}),f.d(w,"isHeader",function(){return m}),f.d(w,"canParse",function(){return h}),f.d(w,"probe",function(){return E});var _=null,T=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],A=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],R=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],I=[0,1,1,4];function k(y,d,t,a,e){if(!(t+24>d.length)){var s=o(d,t);if(s&&t+s.frameLength<=d.length){var u=a+e*(9e4*s.samplesPerFrame/s.sampleRate),n={unit:d.subarray(t,t+s.frameLength),pts:u,dts:u};return y.config=[],y.channelCount=s.channelCount,y.samplerate=s.sampleRate,y.samples.push(n),{sample:n,length:s.frameLength,missing:0}}}}function o(y,d){var t=y[d+1]>>3&3,a=y[d+1]>>1&3,e=y[d+2]>>4&15,s=y[d+2]>>2&3;if(t!==1&&e!==0&&e!==15&&s!==3){var u=y[d+2]>>1&1,n=y[d+3]>>6,l=1e3*T[14*(t===3?3-a:a===3?3:4)+e-1],p=A[3*(t===3?0:t===2?1:2)+s],g=n===3?1:2,v=R[t][a],r=I[a],i=8*v*r,c=Math.floor(v*l/p+u)*r;if(_===null){var S=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);_=S?parseInt(S[1]):0}return!!_&&_<=87&&a===2&&l>=224e3&&n===0&&(y[d+3]=128|y[d+3]),{sampleRate:p,channelCount:g,frameLength:c,samplesPerFrame:i}}}function L(y,d){return y[d]===255&&(224&y[d+1])==224&&(6&y[d+1])!=0}function m(y,d){return d+1<y.length&&L(y,d)}function h(y,d){return L(y,d)&&4<=y.length-d}function E(y,d){if(d+1<y.length&&L(y,d)){var t=o(y,d),a=4;t!=null&&t.frameLength&&(a=t.frameLength);var e=d+a;return e===y.length||m(y,e)}return!1}},"./src/demux/sample-aes.ts":function(N,w,f){f.r(w);var _=f("./src/crypt/decrypter.ts"),T=f("./src/demux/tsdemuxer.ts"),A=function(){function R(k,o,L){this.keyData=void 0,this.decrypter=void 0,this.keyData=L,this.decrypter=new _.default(k,o,{removePKCS7Padding:!1})}var I=R.prototype;return I.decryptBuffer=function(k,o){this.decrypter.decrypt(k,this.keyData.key.buffer,this.keyData.iv.buffer,o)},I.decryptAacSample=function(k,o,L,m){var h=k[o].unit,E=h.subarray(16,h.length-h.length%16),y=E.buffer.slice(E.byteOffset,E.byteOffset+E.length),d=this;this.decryptBuffer(y,function(t){var a=new Uint8Array(t);h.set(a,16),m||d.decryptAacSamples(k,o+1,L)})},I.decryptAacSamples=function(k,o,L){for(;;o++){if(o>=k.length)return void L();if(!(k[o].unit.length<32)){var m=this.decrypter.isSync();if(this.decryptAacSample(k,o,L,m),!m)return}}},I.getAvcEncryptedData=function(k){for(var o=16*Math.floor((k.length-48)/160)+16,L=new Int8Array(o),m=0,h=32;h<=k.length-16;h+=160,m+=16)L.set(k.subarray(h,h+16),m);return L},I.getAvcDecryptedUnit=function(k,o){for(var L=new Uint8Array(o),m=0,h=32;h<=k.length-16;h+=160,m+=16)k.set(L.subarray(m,m+16),h);return k},I.decryptAvcSample=function(k,o,L,m,h,E){var y=Object(T.discardEPB)(h.data),d=this.getAvcEncryptedData(y),t=this;this.decryptBuffer(d.buffer,function(a){h.data=t.getAvcDecryptedUnit(y,a),E||t.decryptAvcSamples(k,o,L+1,m)})},I.decryptAvcSamples=function(k,o,L,m){if(k instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;o++,L=0){if(o>=k.length)return void m();for(var h=k[o].units;!(L>=h.length);L++){var E=h[L];if(!(E.data.length<=48||E.type!==1&&E.type!==5)){var y=this.decrypter.isSync();if(this.decryptAvcSample(k,o,L,m,E,y),!y)return}}}},R}();w.default=A},"./src/demux/transmuxer-interface.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return m});var _=f("./node_modules/webworkify-webpack/index.js"),T=f("./src/events.ts"),A=f("./src/demux/transmuxer.ts"),R=f("./src/utils/logger.ts"),I=f("./src/errors.ts"),k=f("./src/utils/mediasource-helper.ts"),o=f("./node_modules/eventemitter3/index.js"),L=Object(k.getMediaSource)()||{isTypeSupported:function(){return!1}},m=function(){function h(y,d,t,a){var e=this;this.hls=void 0,this.id=void 0,this.observer=void 0,this.frag=null,this.part=null,this.worker=void 0,this.onwmsg=void 0,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0,this.hls=y,this.id=d,this.onTransmuxComplete=t,this.onFlush=a;var s=y.config,u=function(g,v){(v=v||{}).frag=e.frag,v.id=e.id,y.trigger(g,v)};this.observer=new o.EventEmitter,this.observer.on(T.Events.FRAG_DECRYPTED,u),this.observer.on(T.Events.ERROR,u);var n={mp4:L.isTypeSupported("video/mp4"),mpeg:L.isTypeSupported("audio/mpeg"),mp3:L.isTypeSupported('audio/mp4; codecs="mp3"')},l=navigator.vendor;if(s.enableWorker&&typeof Worker<"u"){var p;R.logger.log("demuxing in webworker");try{p=this.worker=_("./src/demux/transmuxer-worker.ts"),this.onwmsg=this.onWorkerMessage.bind(this),p.addEventListener("message",this.onwmsg),p.onerror=function(g){y.trigger(T.Events.ERROR,{type:I.ErrorTypes.OTHER_ERROR,details:I.ErrorDetails.INTERNAL_EXCEPTION,fatal:!0,event:"demuxerWorker",error:new Error(g.message+"  ("+g.filename+":"+g.lineno+")")})},p.postMessage({cmd:"init",typeSupported:n,vendor:l,id:d,config:JSON.stringify(s)})}catch(g){R.logger.warn("Error in worker:",g),R.logger.error("Error while initializing DemuxerWorker, fallback to inline"),p&&self.URL.revokeObjectURL(p.objectURL),this.transmuxer=new A.default(this.observer,n,s,l,d),this.worker=null}}else this.transmuxer=new A.default(this.observer,n,s,l,d)}var E=h.prototype;return E.destroy=function(){var y=this.worker;if(y)y.removeEventListener("message",this.onwmsg),y.terminate(),this.worker=null;else{var d=this.transmuxer;d&&(d.destroy(),this.transmuxer=null)}var t=this.observer;t&&t.removeAllListeners(),this.observer=null},E.push=function(y,d,t,a,e,s,u,n,l,p){var g=this;l.transmuxing.start=self.performance.now();var v=this.transmuxer,r=this.worker,i=s?s.start:e.start,c=e.decryptdata,S=this.frag,b=!(S&&e.cc===S.cc),D=!(S&&l.level===S.level),O=S?l.sn-S.sn:-1,C=this.part?l.part-this.part.index:1,x=!D&&(O===1||O===0&&C===1),P=self.performance.now();(D||O||e.stats.parsing.start===0)&&(e.stats.parsing.start=P),!s||!C&&x||(s.stats.parsing.start=P);var F=new A.TransmuxState(b,x,n,D,i);if(!x||b){R.logger.log("[transmuxer-interface, "+e.type+"]: Starting new transmux session for sn: "+l.sn+" p: "+l.part+" level: "+l.level+" id: "+l.id+`
        discontinuity: `+b+`
        trackSwitch: `+D+`
        contiguous: `+x+`
        accurateTimeOffset: `+n+`
        timeOffset: `+i);var M=new A.TransmuxConfig(t,a,d,u,p);this.configureTransmuxer(M)}if(this.frag=e,this.part=s,r)r.postMessage({cmd:"demux",data:y,decryptdata:c,chunkMeta:l,state:F},y instanceof ArrayBuffer?[y]:[]);else if(v){var B=v.push(y,c,l,F);Object(A.isPromise)(B)?B.then(function(U){g.handleTransmuxComplete(U)}):this.handleTransmuxComplete(B)}},E.flush=function(y){var d=this;y.transmuxing.start=self.performance.now();var t=this.transmuxer,a=this.worker;if(a)a.postMessage({cmd:"flush",chunkMeta:y});else if(t){var e=t.flush(y);Object(A.isPromise)(e)?e.then(function(s){d.handleFlushResult(s,y)}):this.handleFlushResult(e,y)}},E.handleFlushResult=function(y,d){var t=this;y.forEach(function(a){t.handleTransmuxComplete(a)}),this.onFlush(d)},E.onWorkerMessage=function(y){var d=y.data,t=this.hls;switch(d.event){case"init":self.URL.revokeObjectURL(this.worker.objectURL);break;case"transmuxComplete":this.handleTransmuxComplete(d.data);break;case"flush":this.onFlush(d.data);break;default:d.data=d.data||{},d.data.frag=this.frag,d.data.id=this.id,t.trigger(d.event,d.data)}},E.configureTransmuxer=function(y){var d=this.worker,t=this.transmuxer;d?d.postMessage({cmd:"configure",config:y}):t&&t.configure(y)},E.handleTransmuxComplete=function(y){y.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(y)},h}()},"./src/demux/transmuxer-worker.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return I});var _=f("./src/demux/transmuxer.ts"),T=f("./src/events.ts"),A=f("./src/utils/logger.ts"),R=f("./node_modules/eventemitter3/index.js");function I(m){var h=new R.EventEmitter,E=function(y,d){m.postMessage({event:y,data:d})};h.on(T.Events.FRAG_DECRYPTED,E),h.on(T.Events.ERROR,E),m.addEventListener("message",function(y){var d=y.data;switch(d.cmd){case"init":var t=JSON.parse(d.config);m.transmuxer=new _.default(h,d.typeSupported,t,d.vendor,d.id),Object(A.enableLogs)(t.debug),E("init",null);break;case"configure":m.transmuxer.configure(d.config);break;case"demux":var a=m.transmuxer.push(d.data,d.decryptdata,d.chunkMeta,d.state);Object(_.isPromise)(a)?a.then(function(u){k(m,u)}):k(m,a);break;case"flush":var e=d.chunkMeta,s=m.transmuxer.flush(e);Object(_.isPromise)(s)?s.then(function(u){L(m,u,e)}):L(m,s,e)}})}function k(m,h){if((E=h.remuxResult).audio||E.video||E.text||E.id3||E.initSegment){var E,y=[],d=h.remuxResult,t=d.audio,a=d.video;t&&o(y,t),a&&o(y,a),m.postMessage({event:"transmuxComplete",data:h},y)}}function o(m,h){h.data1&&m.push(h.data1.buffer),h.data2&&m.push(h.data2.buffer)}function L(m,h,E){h.forEach(function(y){k(m,y)}),m.postMessage({event:"flush",data:E})}},"./src/demux/transmuxer.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return e}),f.d(w,"isPromise",function(){return u}),f.d(w,"TransmuxConfig",function(){return n}),f.d(w,"TransmuxState",function(){return l});var _,T=f("./src/events.ts"),A=f("./src/errors.ts"),R=f("./src/crypt/decrypter.ts"),I=f("./src/demux/aacdemuxer.ts"),k=f("./src/demux/mp4demuxer.ts"),o=f("./src/demux/tsdemuxer.ts"),L=f("./src/demux/mp3demuxer.ts"),m=f("./src/remux/mp4-remuxer.ts"),h=f("./src/remux/passthrough-remuxer.ts"),E=f("./src/demux/chunk-cache.ts"),y=f("./src/utils/mp4-tools.ts"),d=f("./src/utils/logger.ts");try{_=self.performance.now.bind(self.performance)}catch{d.logger.debug("Unable to use Performance API on this environment"),_=self.Date.now}var t=[{demux:o.default,remux:m.default},{demux:k.default,remux:h.default},{demux:I.default,remux:m.default},{demux:L.default,remux:m.default}],a=1024;t.forEach(function(p){var g=p.demux;a=Math.max(a,g.minProbeByteLength)});var e=function(){function p(v,r,i,c,S){this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.vendor=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.cache=new E.default,this.observer=v,this.typeSupported=r,this.config=i,this.vendor=c,this.id=S}var g=p.prototype;return g.configure=function(v){this.transmuxConfig=v,this.decrypter&&this.decrypter.reset()},g.push=function(v,r,i,c){var S=this,b=i.transmuxing;b.executeStart=_();var D=new Uint8Array(v),O=this.cache,C=this.config,x=this.currentTransmuxState,P=this.transmuxConfig;c&&(this.currentTransmuxState=c);var F=function(nt,it){var dt=null;return nt.byteLength>0&&it!=null&&it.key!=null&&it.iv!==null&&it.method!=null&&(dt=it),dt}(D,r);if(F&&F.method==="AES-128"){var M=this.getDecrypter();if(!C.enableSoftwareAES)return this.decryptionPromise=M.webCryptoDecrypt(D,F.key.buffer,F.iv.buffer).then(function(nt){var it=S.push(nt,null,i);return S.decryptionPromise=null,it}),this.decryptionPromise;var B=M.softwareDecrypt(D,F.key.buffer,F.iv.buffer);if(!B)return b.executeEnd=_(),s(i);D=new Uint8Array(B)}var U=c||x,G=U.contiguous,K=U.discontinuity,H=U.trackSwitch,Y=U.accurateTimeOffset,W=U.timeOffset,q=P.audioCodec,Q=P.videoCodec,tt=P.defaultInitPts,rt=P.duration,z=P.initSegmentData;if((K||H)&&this.resetInitSegment(z,q,Q,rt),K&&this.resetInitialTimestamp(tt),G||this.resetContiguity(),this.needsProbing(D,K,H)){if(O.dataLength){var at=O.flush();D=Object(y.appendUint8Array)(at,D)}this.configureTransmuxer(D,P)}var X=this.transmux(D,F,W,Y,i),J=this.currentTransmuxState;return J.contiguous=!0,J.discontinuity=!1,J.trackSwitch=!1,b.executeEnd=_(),X},g.flush=function(v){var r=this,i=v.transmuxing;i.executeStart=_();var c=this.decrypter,S=this.cache,b=this.currentTransmuxState,D=this.decryptionPromise;if(D)return D.then(function(){return r.flush(v)});var O=[],C=b.timeOffset;if(c){var x=c.flush();x&&O.push(this.push(x,null,v))}var P=S.dataLength;S.reset();var F=this.demuxer,M=this.remuxer;if(!F||!M)return P>=a&&this.observer.emit(T.Events.ERROR,T.Events.ERROR,{type:A.ErrorTypes.MEDIA_ERROR,details:A.ErrorDetails.FRAG_PARSING_ERROR,fatal:!0,reason:"no demux matching with content found"}),i.executeEnd=_(),[s(v)];var B=F.flush(C);return u(B)?B.then(function(U){return r.flushRemux(O,U,v),O}):(this.flushRemux(O,B,v),O)},g.flushRemux=function(v,r,i){var c=r.audioTrack,S=r.avcTrack,b=r.id3Track,D=r.textTrack,O=this.currentTransmuxState,C=O.accurateTimeOffset,x=O.timeOffset;d.logger.log("[transmuxer.ts]: Flushed fragment "+i.sn+(i.part>-1?" p: "+i.part:"")+" of level "+i.level);var P=this.remuxer.remux(c,S,b,D,x,C,!0,this.id);v.push({remuxResult:P,chunkMeta:i}),i.transmuxing.executeEnd=_()},g.resetInitialTimestamp=function(v){var r=this.demuxer,i=this.remuxer;r&&i&&(r.resetTimeStamp(v),i.resetTimeStamp(v))},g.resetContiguity=function(){var v=this.demuxer,r=this.remuxer;v&&r&&(v.resetContiguity(),r.resetNextTimestamp())},g.resetInitSegment=function(v,r,i,c){var S=this.demuxer,b=this.remuxer;S&&b&&(S.resetInitSegment(r,i,c),b.resetInitSegment(v,r,i))},g.destroy=function(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)},g.transmux=function(v,r,i,c,S){return r&&r.method==="SAMPLE-AES"?this.transmuxSampleAes(v,r,i,c,S):this.transmuxUnencrypted(v,i,c,S)},g.transmuxUnencrypted=function(v,r,i,c){var S=this.demuxer.demux(v,r,!1,!this.config.progressive),b=S.audioTrack,D=S.avcTrack,O=S.id3Track,C=S.textTrack;return{remuxResult:this.remuxer.remux(b,D,O,C,r,i,!1,this.id),chunkMeta:c}},g.transmuxSampleAes=function(v,r,i,c,S){var b=this;return this.demuxer.demuxSampleAes(v,r,i).then(function(D){return{remuxResult:b.remuxer.remux(D.audioTrack,D.avcTrack,D.id3Track,D.textTrack,i,c,!1,b.id),chunkMeta:S}})},g.configureTransmuxer=function(v,r){for(var i,c=this.config,S=this.observer,b=this.typeSupported,D=this.vendor,O=r.audioCodec,C=r.defaultInitPts,x=r.duration,P=r.initSegmentData,F=r.videoCodec,M=0,B=t.length;M<B;M++)if(t[M].demux.probe(v)){i=t[M];break}i||(d.logger.warn("Failed to find demuxer by probing frag, treating as mp4 passthrough"),i={demux:k.default,remux:h.default});var U=this.demuxer,G=this.remuxer,K=i.remux,H=i.demux;G&&G instanceof K||(this.remuxer=new K(S,c,b,D)),U&&U instanceof H||(this.demuxer=new H(S,c,b),this.probe=H.probe),this.resetInitSegment(P,O,F,x),this.resetInitialTimestamp(C)},g.needsProbing=function(v,r,i){return!this.demuxer||!this.remuxer||r||i},g.getDecrypter=function(){var v=this.decrypter;return v||(v=this.decrypter=new R.default(this.observer,this.config)),v},p}(),s=function(p){return{remuxResult:{},chunkMeta:p}};function u(p){return"then"in p&&p.then instanceof Function}var n=function(p,g,v,r,i){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=p,this.videoCodec=g,this.initSegmentData=v,this.duration=r,this.defaultInitPts=i},l=function(p,g,v,r,i){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.discontinuity=p,this.contiguous=g,this.accurateTimeOffset=v,this.trackSwitch=r,this.timeOffset=i}},"./src/demux/tsdemuxer.ts":function(N,w,f){f.r(w),f.d(w,"discardEPB",function(){return u});var _=f("./src/demux/adts.ts"),T=f("./src/demux/mpegaudio.ts"),A=f("./src/demux/exp-golomb.ts"),R=f("./src/demux/id3.ts"),I=f("./src/demux/sample-aes.ts"),k=f("./src/events.ts"),o=f("./src/utils/mp4-tools.ts"),L=f("./src/utils/logger.ts"),m=f("./src/errors.ts"),h={video:1,audio:2,id3:3,text:4},E=function(){function n(p,g,v){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._duration=0,this.aacLastPTS=null,this._initPTS=null,this._initDTS=null,this._pmtId=-1,this._avcTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.avcSample=null,this.remainderData=null,this.observer=p,this.config=g,this.typeSupported=v}n.probe=function(p){var g=n.syncOffset(p);return!(g<0||(g&&L.logger.warn("MPEG2-TS detected but first sync word found @ offset "+g+", junk ahead ?"),0))},n.syncOffset=function(p){for(var g=Math.min(1e3,p.length-564),v=0;v<g;){if(p[v]===71&&p[v+188]===71&&p[v+376]===71)return v;v++}return-1},n.createTrack=function(p,g){return{container:p==="video"||p==="audio"?"video/mp2t":void 0,type:p,id:h[p],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:p==="audio"?g:void 0}};var l=n.prototype;return l.resetInitSegment=function(p,g,v){this.pmtParsed=!1,this._pmtId=-1,this._avcTrack=n.createTrack("video",v),this._audioTrack=n.createTrack("audio",v),this._id3Track=n.createTrack("id3",v),this._txtTrack=n.createTrack("text",v),this._audioTrack.isAAC=!0,this.aacOverFlow=null,this.aacLastPTS=null,this.avcSample=null,this.audioCodec=p,this.videoCodec=g,this._duration=v},l.resetTimeStamp=function(){},l.resetContiguity=function(){var p=this._audioTrack,g=this._avcTrack,v=this._id3Track;p&&(p.pesData=null),g&&(g.pesData=null),v&&(v.pesData=null),this.aacOverFlow=null,this.aacLastPTS=null},l.demux=function(p,g,v,r){var i;v===void 0&&(v=!1),r===void 0&&(r=!1),v||(this.sampleAes=null);var c=this._avcTrack,S=this._audioTrack,b=this._id3Track,D=c.pid,O=c.pesData,C=S.pid,x=b.pid,P=S.pesData,F=b.pesData,M=!1,B=this.pmtParsed,U=this._pmtId,G=p.length;if(this.remainderData&&(G=(p=Object(o.appendUint8Array)(this.remainderData,p)).length,this.remainderData=null),G<188&&!r)return this.remainderData=p,{audioTrack:S,avcTrack:c,id3Track:b,textTrack:this._txtTrack};var K=Math.max(0,n.syncOffset(p));(G-=(G+K)%188)<p.byteLength&&!r&&(this.remainderData=new Uint8Array(p.buffer,G,p.buffer.byteLength-G));for(var H=K;H<G;H+=188)if(p[H]===71){var Y=!!(64&p[H+1]),W=((31&p[H+1])<<8)+p[H+2],q=void 0;if((48&p[H+3])>>4>1){if((q=H+5+p[H+4])===H+188)continue}else q=H+4;switch(W){case D:Y&&(O&&(i=a(O))&&this.parseAVCPES(i,!1),O={data:[],size:0}),O&&(O.data.push(p.subarray(q,H+188)),O.size+=H+188-q);break;case C:Y&&(P&&(i=a(P))&&(S.isAAC?this.parseAACPES(i):this.parseMPEGPES(i)),P={data:[],size:0}),P&&(P.data.push(p.subarray(q,H+188)),P.size+=H+188-q);break;case x:Y&&(F&&(i=a(F))&&this.parseID3PES(i),F={data:[],size:0}),F&&(F.data.push(p.subarray(q,H+188)),F.size+=H+188-q);break;case 0:Y&&(q+=p[q]+1),U=this._pmtId=d(p,q);break;case U:Y&&(q+=p[q]+1);var Q=t(p,q,this.typeSupported.mpeg===!0||this.typeSupported.mp3===!0,v);(D=Q.avc)>0&&(c.pid=D),(C=Q.audio)>0&&(S.pid=C,S.isAAC=Q.isAAC),(x=Q.id3)>0&&(b.pid=x),M&&!B&&(L.logger.log("reparse from beginning"),M=!1,H=K-188),B=this.pmtParsed=!0;break;case 17:case 8191:break;default:M=!0}}else this.observer.emit(k.Events.ERROR,k.Events.ERROR,{type:m.ErrorTypes.MEDIA_ERROR,details:m.ErrorDetails.FRAG_PARSING_ERROR,fatal:!1,reason:"TS packet did not start with 0x47"});c.pesData=O,S.pesData=P,b.pesData=F;var tt={audioTrack:S,avcTrack:c,id3Track:b,textTrack:this._txtTrack};return r&&this.extractRemainingSamples(tt),tt},l.flush=function(){var p,g=this.remainderData;return this.remainderData=null,p=g?this.demux(g,-1,!1,!0):{audioTrack:this._audioTrack,avcTrack:this._avcTrack,textTrack:this._txtTrack,id3Track:this._id3Track},this.extractRemainingSamples(p),this.sampleAes?this.decrypt(p,this.sampleAes):p},l.extractRemainingSamples=function(p){var g,v=p.audioTrack,r=p.avcTrack,i=p.id3Track,c=r.pesData,S=v.pesData,b=i.pesData;c&&(g=a(c))?(this.parseAVCPES(g,!0),r.pesData=null):r.pesData=c,S&&(g=a(S))?(v.isAAC?this.parseAACPES(g):this.parseMPEGPES(g),v.pesData=null):(S!=null&&S.size&&L.logger.log("last AAC PES packet truncated,might overlap between fragments"),v.pesData=S),b&&(g=a(b))?(this.parseID3PES(g),i.pesData=null):i.pesData=b},l.demuxSampleAes=function(p,g,v){var r=this.demux(p,v,!0,!this.config.progressive),i=this.sampleAes=new I.default(this.observer,this.config,g);return this.decrypt(r,i)},l.decrypt=function(p,g){return new Promise(function(v){var r=p.audioTrack,i=p.avcTrack;r.samples&&r.isAAC?g.decryptAacSamples(r.samples,0,function(){i.samples?g.decryptAvcSamples(i.samples,0,0,function(){v(p)}):v(p)}):i.samples&&g.decryptAvcSamples(i.samples,0,0,function(){v(p)})})},l.destroy=function(){this._initPTS=this._initDTS=null,this._duration=0},l.parseAVCPES=function(p,g){var v,r=this,i=this._avcTrack,c=this.parseAVCNALu(p.data),S=this.avcSample,b=!1;p.data=null,S&&c.length&&!i.audFound&&(e(S,i),S=this.avcSample=y(!1,p.pts,p.dts,"")),c.forEach(function(D){switch(D.type){case 1:v=!0,S||(S=r.avcSample=y(!0,p.pts,p.dts,"")),S.frame=!0;var O=D.data;if(b&&O.length>4){var C=new A.default(O).readSliceType();C!==2&&C!==4&&C!==7&&C!==9||(S.key=!0)}break;case 5:v=!0,S||(S=r.avcSample=y(!0,p.pts,p.dts,"")),S.key=!0,S.frame=!0;break;case 6:v=!0;var x=new A.default(u(D.data));x.readUByte();for(var P=0,F=0,M=!1,B=0;!M&&x.bytesAvailable>1;){P=0;do P+=B=x.readUByte();while(B===255);F=0;do F+=B=x.readUByte();while(B===255);if(P===4&&x.bytesAvailable!==0){if(M=!0,x.readUByte()===181&&x.readUShort()===49&&x.readUInt()===1195456820&&x.readUByte()===3){for(var U=x.readUByte(),G=31&U,K=[U,x.readUByte()],H=0;H<G;H++)K.push(x.readUByte()),K.push(x.readUByte()),K.push(x.readUByte());s(r._txtTrack.samples,{type:3,pts:p.pts,bytes:K})}}else if(P===5&&x.bytesAvailable!==0){if(M=!0,F>16){for(var Y=[],W=0;W<16;W++)Y.push(x.readUByte().toString(16)),W!==3&&W!==5&&W!==7&&W!==9||Y.push("-");for(var q=F-16,Q=new Uint8Array(q),tt=0;tt<q;tt++)Q[tt]=x.readUByte();s(r._txtTrack.samples,{pts:p.pts,payloadType:P,uuid:Y.join(""),userData:Object(R.utf8ArrayToStr)(Q),userDataBytes:Q})}}else if(F<x.bytesAvailable)for(var rt=0;rt<F;rt++)x.readUByte()}break;case 7:if(v=!0,b=!0,!i.sps){var z=new A.default(D.data).readSPS();i.width=z.width,i.height=z.height,i.pixelRatio=z.pixelRatio,i.sps=[D.data],i.duration=r._duration;for(var at=D.data.subarray(1,4),X="avc1.",J=0;J<3;J++){var nt=at[J].toString(16);nt.length<2&&(nt="0"+nt),X+=nt}i.codec=X}break;case 8:v=!0,i.pps||(i.pps=[D.data]);break;case 9:v=!1,i.audFound=!0,S&&e(S,i),S=r.avcSample=y(!1,p.pts,p.dts,"");break;case 12:v=!1;break;default:v=!1,S&&(S.debug+="unknown NAL "+D.type+" ")}S&&v&&S.units.push(D)}),g&&S&&(e(S,i),this.avcSample=null)},l.getLastNalUnit=function(){var p,g,v=this.avcSample;if(!v||v.units.length===0){var r=this._avcTrack.samples;v=r[r.length-1]}if((p=v)!==null&&p!==void 0&&p.units){var i=v.units;g=i[i.length-1]}return g},l.parseAVCNALu=function(p){var g,v,r=p.byteLength,i=this._avcTrack,c=i.naluState||0,S=c,b=[],D=0,O=-1,C=0;for(c===-1&&(O=0,C=31&p[0],c=0,D=1);D<r;)if(g=p[D++],c)if(c!==1)if(g)if(g===1){if(O>=0){var x={data:p.subarray(O,D-c-1),type:C};b.push(x)}else{var P=this.getLastNalUnit();if(P&&(S&&D<=4-S&&P.state&&(P.data=P.data.subarray(0,P.data.byteLength-S)),(v=D-c-1)>0)){var F=new Uint8Array(P.data.byteLength+v);F.set(P.data,0),F.set(p.subarray(0,v),P.data.byteLength),P.data=F}}D<r?(O=D,C=31&p[D],c=0):c=-1}else c=0;else c=3;else c=g?0:2;else c=g?0:1;if(O>=0&&c>=0){var M={data:p.subarray(O,r),type:C,state:c};b.push(M)}if(b.length===0){var B=this.getLastNalUnit();if(B){var U=new Uint8Array(B.data.byteLength+p.byteLength);U.set(B.data,0),U.set(p,B.data.byteLength),B.data=U}}return i.naluState=c,b},l.parseAACPES=function(p){var g,v,r,i,c,S=0,b=this._audioTrack,D=this.aacOverFlow,O=p.data;if(D){this.aacOverFlow=null;var C=D.sample.unit.byteLength,x=Math.min(D.missing,C),P=C-x;D.sample.unit.set(O.subarray(0,x),P),b.samples.push(D.sample),S=D.missing}for(g=S,v=O.length;g<v-1&&!_.isHeader(O,g);g++);if(g===S||(g<v-1?(r="AAC PES did not start with ADTS header,offset:"+g,i=!1):(r="no ADTS header found in AAC PES",i=!0),L.logger.warn("parsing error:"+r),this.observer.emit(k.Events.ERROR,k.Events.ERROR,{type:m.ErrorTypes.MEDIA_ERROR,details:m.ErrorDetails.FRAG_PARSING_ERROR,fatal:i,reason:r}),!i)){if(_.initTrackConfig(b,this.observer,O,g,this.audioCodec),p.pts!==void 0)c=p.pts;else{if(!D)return void L.logger.warn("[tsdemuxer]: AAC PES unknown PTS");var F=_.getFrameDuration(b.samplerate);c=D.sample.pts+F}for(var M=0;g<v;){if(_.isHeader(O,g)){if(g+5<v){var B=_.appendFrame(b,O,g,c,M);if(B){if(!B.missing){g+=B.length,M++;continue}this.aacOverFlow=B}}break}g++}}},l.parseMPEGPES=function(p){var g=p.data,v=g.length,r=0,i=0,c=p.pts;if(c!==void 0)for(;i<v;)if(T.isHeader(g,i)){var S=T.appendFrame(this._audioTrack,g,i,c,r);if(!S)break;i+=S.length,r++}else i++;else L.logger.warn("[tsdemuxer]: MPEG PES unknown PTS")},l.parseID3PES=function(p){p.pts!==void 0?this._id3Track.samples.push(p):L.logger.warn("[tsdemuxer]: ID3 PES unknown PTS")},n}();function y(n,l,p,g){return{key:n,frame:!1,pts:l,dts:p,units:[],debug:g,length:0}}function d(n,l){return(31&n[l+10])<<8|n[l+11]}function t(n,l,p,g){var v={audio:-1,avc:-1,id3:-1,isAAC:!0},r=l+3+((15&n[l+1])<<8|n[l+2])-4;for(l+=12+((15&n[l+10])<<8|n[l+11]);l<r;){var i=(31&n[l+1])<<8|n[l+2];switch(n[l]){case 207:if(!g){L.logger.log("ADTS AAC with AES-128-CBC frame encryption found in unencrypted stream");break}case 15:v.audio===-1&&(v.audio=i);break;case 21:v.id3===-1&&(v.id3=i);break;case 219:if(!g){L.logger.log("H.264 with AES-128-CBC slice encryption found in unencrypted stream");break}case 27:v.avc===-1&&(v.avc=i);break;case 3:case 4:p?v.audio===-1&&(v.audio=i,v.isAAC=!1):L.logger.log("MPEG audio found, not supported in this browser");break;case 36:L.logger.warn("Unsupported HEVC stream type found")}l+=5+((15&n[l+3])<<8|n[l+4])}return v}function a(n){var l,p,g,v,r,i=0,c=n.data;if(!n||n.size===0)return null;for(;c[0].length<19&&c.length>1;){var S=new Uint8Array(c[0].length+c[1].length);S.set(c[0]),S.set(c[1],c[0].length),c[0]=S,c.splice(1,1)}if(((l=c[0])[0]<<16)+(l[1]<<8)+l[2]===1){if((p=(l[4]<<8)+l[5])&&p>n.size-6)return null;var b=l[7];192&b&&(v=536870912*(14&l[9])+4194304*(255&l[10])+16384*(254&l[11])+128*(255&l[12])+(254&l[13])/2,64&b?v-(r=536870912*(14&l[14])+4194304*(255&l[15])+16384*(254&l[16])+128*(255&l[17])+(254&l[18])/2)>54e5&&(L.logger.warn(Math.round((v-r)/9e4)+"s delta between PTS and DTS, align them"),v=r):r=v);var D=(g=l[8])+9;if(n.size<=D)return null;n.size-=D;for(var O=new Uint8Array(n.size),C=0,x=c.length;C<x;C++){var P=(l=c[C]).byteLength;if(D){if(D>P){D-=P;continue}l=l.subarray(D),P-=D,D=0}O.set(l,i),i+=P}return p&&(p-=g+3),{data:O,pts:v,dts:r,len:p}}return null}function e(n,l){if(n.units.length&&n.frame){if(n.pts===void 0){var p=l.samples,g=p.length;if(!g)return void l.dropped++;var v=p[g-1];n.pts=v.pts,n.dts=v.dts}l.samples.push(n)}n.debug.length&&L.logger.log(n.pts+"/"+n.dts+":"+n.debug)}function s(n,l){var p=n.length;if(p>0){if(l.pts>=n[p-1].pts)n.push(l);else for(var g=p-1;g>=0;g--)if(l.pts<n[g].pts){n.splice(g,0,l);break}}else n.push(l)}function u(n){for(var l=n.byteLength,p=[],g=1;g<l-2;)n[g]===0&&n[g+1]===0&&n[g+2]===3?(p.push(g+2),g+=2):g++;if(p.length===0)return n;var v=l-p.length,r=new Uint8Array(v),i=0;for(g=0;g<v;i++,g++)i===p[0]&&(i++,p.shift()),r[g]=n[i];return r}E.minProbeByteLength=188,w.default=E},"./src/errors.ts":function(N,w,f){var _,T,A,R;f.r(w),f.d(w,"ErrorTypes",function(){return _}),f.d(w,"ErrorDetails",function(){return A}),(T=_||(_={})).NETWORK_ERROR="networkError",T.MEDIA_ERROR="mediaError",T.KEY_SYSTEM_ERROR="keySystemError",T.MUX_ERROR="muxError",T.OTHER_ERROR="otherError",(R=A||(A={})).KEY_SYSTEM_NO_KEYS="keySystemNoKeys",R.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",R.KEY_SYSTEM_NO_SESSION="keySystemNoSession",R.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",R.KEY_SYSTEM_NO_INIT_DATA="keySystemNoInitData",R.MANIFEST_LOAD_ERROR="manifestLoadError",R.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",R.MANIFEST_PARSING_ERROR="manifestParsingError",R.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",R.LEVEL_EMPTY_ERROR="levelEmptyError",R.LEVEL_LOAD_ERROR="levelLoadError",R.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",R.LEVEL_SWITCH_ERROR="levelSwitchError",R.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",R.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",R.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",R.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",R.FRAG_LOAD_ERROR="fragLoadError",R.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",R.FRAG_DECRYPT_ERROR="fragDecryptError",R.FRAG_PARSING_ERROR="fragParsingError",R.REMUX_ALLOC_ERROR="remuxAllocError",R.KEY_LOAD_ERROR="keyLoadError",R.KEY_LOAD_TIMEOUT="keyLoadTimeOut",R.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",R.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",R.BUFFER_APPEND_ERROR="bufferAppendError",R.BUFFER_APPENDING_ERROR="bufferAppendingError",R.BUFFER_STALLED_ERROR="bufferStalledError",R.BUFFER_FULL_ERROR="bufferFullError",R.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",R.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",R.INTERNAL_EXCEPTION="internalException",R.INTERNAL_ABORTED="aborted",R.UNKNOWN="unknown"},"./src/events.ts":function(N,w,f){var _,T;f.r(w),f.d(w,"Events",function(){return _}),(T=_||(_={})).MEDIA_ATTACHING="hlsMediaAttaching",T.MEDIA_ATTACHED="hlsMediaAttached",T.MEDIA_DETACHING="hlsMediaDetaching",T.MEDIA_DETACHED="hlsMediaDetached",T.BUFFER_RESET="hlsBufferReset",T.BUFFER_CODECS="hlsBufferCodecs",T.BUFFER_CREATED="hlsBufferCreated",T.BUFFER_APPENDING="hlsBufferAppending",T.BUFFER_APPENDED="hlsBufferAppended",T.BUFFER_EOS="hlsBufferEos",T.BUFFER_FLUSHING="hlsBufferFlushing",T.BUFFER_FLUSHED="hlsBufferFlushed",T.MANIFEST_LOADING="hlsManifestLoading",T.MANIFEST_LOADED="hlsManifestLoaded",T.MANIFEST_PARSED="hlsManifestParsed",T.LEVEL_SWITCHING="hlsLevelSwitching",T.LEVEL_SWITCHED="hlsLevelSwitched",T.LEVEL_LOADING="hlsLevelLoading",T.LEVEL_LOADED="hlsLevelLoaded",T.LEVEL_UPDATED="hlsLevelUpdated",T.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",T.LEVELS_UPDATED="hlsLevelsUpdated",T.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",T.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",T.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",T.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",T.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",T.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",T.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",T.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",T.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",T.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",T.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",T.CUES_PARSED="hlsCuesParsed",T.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",T.INIT_PTS_FOUND="hlsInitPtsFound",T.FRAG_LOADING="hlsFragLoading",T.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",T.FRAG_LOADED="hlsFragLoaded",T.FRAG_DECRYPTED="hlsFragDecrypted",T.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",T.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",T.FRAG_PARSING_METADATA="hlsFragParsingMetadata",T.FRAG_PARSED="hlsFragParsed",T.FRAG_BUFFERED="hlsFragBuffered",T.FRAG_CHANGED="hlsFragChanged",T.FPS_DROP="hlsFpsDrop",T.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",T.ERROR="hlsError",T.DESTROYING="hlsDestroying",T.KEY_LOADING="hlsKeyLoading",T.KEY_LOADED="hlsKeyLoaded",T.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",T.BACK_BUFFER_REACHED="hlsBackBufferReached"},"./src/hls.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return e});var _=f("./node_modules/url-toolkit/src/url-toolkit.js"),T=f("./src/loader/playlist-loader.ts"),A=f("./src/loader/key-loader.ts"),R=f("./src/controller/id3-track-controller.ts"),I=f("./src/controller/latency-controller.ts"),k=f("./src/controller/level-controller.ts"),o=f("./src/controller/fragment-tracker.ts"),L=f("./src/controller/stream-controller.ts"),m=f("./src/is-supported.ts"),h=f("./src/utils/logger.ts"),E=f("./src/config.ts"),y=f("./node_modules/eventemitter3/index.js"),d=f("./src/events.ts"),t=f("./src/errors.ts");function a(s,u){for(var n=0;n<u.length;n++){var l=u[n];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(s,l.key,l)}}var e=function(){function s(g){g===void 0&&(g={}),this.config=void 0,this.userConfig=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this._emitter=new y.EventEmitter,this._autoLevelCapping=void 0,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.emeController=void 0,this._media=null,this.url=null;var v=this.config=Object(E.mergeConfig)(s.DefaultConfig,g);this.userConfig=g,Object(h.enableLogs)(v.debug),this._autoLevelCapping=-1,v.progressive&&Object(E.enableStreamingMode)(v);var r=v.abrController,i=v.bufferController,c=v.capLevelController,S=v.fpsController,b=this.abrController=new r(this),D=this.bufferController=new i(this),O=this.capLevelController=new c(this),C=new S(this),x=new T.default(this),P=new A.default(this),F=new R.default(this),M=this.levelController=new k.default(this),B=new o.FragmentTracker(this),U=this.streamController=new L.default(this,B);O.setStreamController(U),C.setStreamController(U);var G=[M,U];this.networkControllers=G;var K=[x,P,b,D,O,C,F,B];this.audioTrackController=this.createController(v.audioTrackController,null,G),this.createController(v.audioStreamController,B,G),this.subtitleTrackController=this.createController(v.subtitleTrackController,null,G),this.createController(v.subtitleStreamController,B,G),this.createController(v.timelineController,null,K),this.emeController=this.createController(v.emeController,null,K),this.latencyController=this.createController(I.default,null,K),this.coreComponents=K}s.isSupported=function(){return Object(m.isSupported)()};var u,n,l,p=s.prototype;return p.createController=function(g,v,r){if(g){var i=v?new g(this,v):new g(this);return r&&r.push(i),i}return null},p.on=function(g,v,r){r===void 0&&(r=this),this._emitter.on(g,v,r)},p.once=function(g,v,r){r===void 0&&(r=this),this._emitter.once(g,v,r)},p.removeAllListeners=function(g){this._emitter.removeAllListeners(g)},p.off=function(g,v,r,i){r===void 0&&(r=this),this._emitter.off(g,v,r,i)},p.listeners=function(g){return this._emitter.listeners(g)},p.emit=function(g,v,r){return this._emitter.emit(g,v,r)},p.trigger=function(g,v){if(this.config.debug)return this.emit(g,g,v);try{return this.emit(g,g,v)}catch(r){h.logger.error("An internal error happened while handling event "+g+'. Error message: "'+r.message+'". Here is a stacktrace:',r),this.trigger(d.Events.ERROR,{type:t.ErrorTypes.OTHER_ERROR,details:t.ErrorDetails.INTERNAL_EXCEPTION,fatal:!1,event:g,error:r})}return!1},p.listenerCount=function(g){return this._emitter.listenerCount(g)},p.destroy=function(){h.logger.log("destroy"),this.trigger(d.Events.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this.url=null,this.networkControllers.forEach(function(g){return g.destroy()}),this.networkControllers.length=0,this.coreComponents.forEach(function(g){return g.destroy()}),this.coreComponents.length=0},p.attachMedia=function(g){h.logger.log("attachMedia"),this._media=g,this.trigger(d.Events.MEDIA_ATTACHING,{media:g})},p.detachMedia=function(){h.logger.log("detachMedia"),this.trigger(d.Events.MEDIA_DETACHING,void 0),this._media=null},p.loadSource=function(g){this.stopLoad();var v=this.media,r=this.url,i=this.url=_.buildAbsoluteURL(self.location.href,g,{alwaysNormalize:!0});h.logger.log("loadSource:"+i),v&&r&&r!==i&&this.bufferController.hasSourceTypes()&&(this.detachMedia(),this.attachMedia(v)),this.trigger(d.Events.MANIFEST_LOADING,{url:g})},p.startLoad=function(g){g===void 0&&(g=-1),h.logger.log("startLoad("+g+")"),this.networkControllers.forEach(function(v){v.startLoad(g)})},p.stopLoad=function(){h.logger.log("stopLoad"),this.networkControllers.forEach(function(g){g.stopLoad()})},p.swapAudioCodec=function(){h.logger.log("swapAudioCodec"),this.streamController.swapAudioCodec()},p.recoverMediaError=function(){h.logger.log("recoverMediaError");var g=this._media;this.detachMedia(),g&&this.attachMedia(g)},p.removeLevel=function(g,v){v===void 0&&(v=0),this.levelController.removeLevel(g,v)},u=s,l=[{key:"version",get:function(){return"1.0.10"}},{key:"Events",get:function(){return d.Events}},{key:"ErrorTypes",get:function(){return t.ErrorTypes}},{key:"ErrorDetails",get:function(){return t.ErrorDetails}},{key:"DefaultConfig",get:function(){return s.defaultConfig?s.defaultConfig:E.hlsDefaultConfig},set:function(g){s.defaultConfig=g}}],(n=[{key:"levels",get:function(){var g=this.levelController.levels;return g||[]}},{key:"currentLevel",get:function(){return this.streamController.currentLevel},set:function(g){h.logger.log("set currentLevel:"+g),this.loadLevel=g,this.abrController.clearTimer(),this.streamController.immediateLevelSwitch()}},{key:"nextLevel",get:function(){return this.streamController.nextLevel},set:function(g){h.logger.log("set nextLevel:"+g),this.levelController.manualLevel=g,this.streamController.nextLevelSwitch()}},{key:"loadLevel",get:function(){return this.levelController.level},set:function(g){h.logger.log("set loadLevel:"+g),this.levelController.manualLevel=g}},{key:"nextLoadLevel",get:function(){return this.levelController.nextLoadLevel},set:function(g){this.levelController.nextLoadLevel=g}},{key:"firstLevel",get:function(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)},set:function(g){h.logger.log("set firstLevel:"+g),this.levelController.firstLevel=g}},{key:"startLevel",get:function(){return this.levelController.startLevel},set:function(g){h.logger.log("set startLevel:"+g),g!==-1&&(g=Math.max(g,this.minAutoLevel)),this.levelController.startLevel=g}},{key:"capLevelToPlayerSize",get:function(){return this.config.capLevelToPlayerSize},set:function(g){var v=!!g;v!==this.config.capLevelToPlayerSize&&(v?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=v)}},{key:"autoLevelCapping",get:function(){return this._autoLevelCapping},set:function(g){this._autoLevelCapping!==g&&(h.logger.log("set autoLevelCapping:"+g),this._autoLevelCapping=g)}},{key:"bandwidthEstimate",get:function(){var g=this.abrController.bwEstimator;return g?g.getEstimate():NaN}},{key:"autoLevelEnabled",get:function(){return this.levelController.manualLevel===-1}},{key:"manualLevel",get:function(){return this.levelController.manualLevel}},{key:"minAutoLevel",get:function(){var g=this.levels,v=this.config.minAutoBitrate;if(!g)return 0;for(var r=g.length,i=0;i<r;i++)if(g[i].maxBitrate>v)return i;return 0}},{key:"maxAutoLevel",get:function(){var g=this.levels,v=this.autoLevelCapping;return v===-1&&g&&g.length?g.length-1:v}},{key:"nextAutoLevel",get:function(){return Math.min(Math.max(this.abrController.nextAutoLevel,this.minAutoLevel),this.maxAutoLevel)},set:function(g){this.abrController.nextAutoLevel=Math.max(this.minAutoLevel,g)}},{key:"audioTracks",get:function(){var g=this.audioTrackController;return g?g.audioTracks:[]}},{key:"audioTrack",get:function(){var g=this.audioTrackController;return g?g.audioTrack:-1},set:function(g){var v=this.audioTrackController;v&&(v.audioTrack=g)}},{key:"subtitleTracks",get:function(){var g=this.subtitleTrackController;return g?g.subtitleTracks:[]}},{key:"subtitleTrack",get:function(){var g=this.subtitleTrackController;return g?g.subtitleTrack:-1},set:function(g){var v=this.subtitleTrackController;v&&(v.subtitleTrack=g)}},{key:"media",get:function(){return this._media}},{key:"subtitleDisplay",get:function(){var g=this.subtitleTrackController;return!!g&&g.subtitleDisplay},set:function(g){var v=this.subtitleTrackController;v&&(v.subtitleDisplay=g)}},{key:"lowLatencyMode",get:function(){return this.config.lowLatencyMode},set:function(g){this.config.lowLatencyMode=g}},{key:"liveSyncPosition",get:function(){return this.latencyController.liveSyncPosition}},{key:"latency",get:function(){return this.latencyController.latency}},{key:"maxLatency",get:function(){return this.latencyController.maxLatency}},{key:"targetLatency",get:function(){return this.latencyController.targetLatency}},{key:"drift",get:function(){return this.latencyController.drift}},{key:"forceStartLoad",get:function(){return this.streamController.forceStartLoad}}])&&a(u.prototype,n),l&&a(u,l),s}();e.defaultConfig=void 0},"./src/is-supported.ts":function(N,w,f){f.r(w),f.d(w,"isSupported",function(){return A}),f.d(w,"changeTypeSupported",function(){return R});var _=f("./src/utils/mediasource-helper.ts");function T(){return self.SourceBuffer||self.WebKitSourceBuffer}function A(){var I=Object(_.getMediaSource)();if(!I)return!1;var k=T(),o=I&&typeof I.isTypeSupported=="function"&&I.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),L=!k||k.prototype&&typeof k.prototype.appendBuffer=="function"&&typeof k.prototype.remove=="function";return!!o&&!!L}function R(){var I,k=T();return typeof(k==null||(I=k.prototype)===null||I===void 0?void 0:I.changeType)=="function"}},"./src/loader/fragment-loader.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return m}),f.d(w,"LoadError",function(){return E});var _=f("./src/polyfills/number.ts"),T=f("./src/errors.ts");function A(y){var d=typeof Map=="function"?new Map:void 0;return(A=function(t){if(t===null||(a=t,Function.toString.call(a).indexOf("[native code]")===-1))return t;var a;if(typeof t!="function")throw new TypeError("Super expression must either be null or a function");if(d!==void 0){if(d.has(t))return d.get(t);d.set(t,e)}function e(){return R(t,arguments,o(this).constructor)}return e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),k(e,t)})(y)}function R(y,d,t){return(R=I()?Reflect.construct:function(a,e,s){var u=[null];u.push.apply(u,e);var n=new(Function.bind.apply(a,u));return s&&k(n,s.prototype),n}).apply(null,arguments)}function I(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function k(y,d){return(k=Object.setPrototypeOf||function(t,a){return t.__proto__=a,t})(y,d)}function o(y){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(d){return d.__proto__||Object.getPrototypeOf(d)})(y)}var L=Math.pow(2,17),m=function(){function y(t){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=t}var d=y.prototype;return d.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},d.abort=function(){this.loader&&this.loader.abort()},d.load=function(t,a){var e=this,s=t.url;if(!s)return Promise.reject(new E({type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:t,networkDetails:null},"Fragment does not have a "+(s?"part list":"url")));this.abort();var u=this.config,n=u.fLoader,l=u.loader;return new Promise(function(p,g){e.loader&&e.loader.destroy();var v=e.loader=t.loader=n?new n(u):new l(u),r=h(t),i={timeout:u.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:u.fragLoadingMaxRetryTimeout,highWaterMark:L};t.stats=v.stats,v.load(r,i,{onSuccess:function(c,S,b,D){e.resetLoader(t,v),p({frag:t,part:null,payload:c.data,networkDetails:D})},onError:function(c,S,b){e.resetLoader(t,v),g(new E({type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:t,response:c,networkDetails:b}))},onAbort:function(c,S,b){e.resetLoader(t,v),g(new E({type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.INTERNAL_ABORTED,fatal:!1,frag:t,networkDetails:b}))},onTimeout:function(c,S,b){e.resetLoader(t,v),g(new E({type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,networkDetails:b}))},onProgress:function(c,S,b,D){a&&a({frag:t,part:null,payload:b,networkDetails:D})}})})},d.loadPart=function(t,a,e){var s=this;this.abort();var u=this.config,n=u.fLoader,l=u.loader;return new Promise(function(p,g){s.loader&&s.loader.destroy();var v=s.loader=t.loader=n?new n(u):new l(u),r=h(t,a),i={timeout:u.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:u.fragLoadingMaxRetryTimeout,highWaterMark:L};a.stats=v.stats,v.load(r,i,{onSuccess:function(c,S,b,D){s.resetLoader(t,v),s.updateStatsFromPart(t,a);var O={frag:t,part:a,payload:c.data,networkDetails:D};e(O),p(O)},onError:function(c,S,b){s.resetLoader(t,v),g(new E({type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:t,part:a,response:c,networkDetails:b}))},onAbort:function(c,S,b){t.stats.aborted=a.stats.aborted,s.resetLoader(t,v),g(new E({type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.INTERNAL_ABORTED,fatal:!1,frag:t,part:a,networkDetails:b}))},onTimeout:function(c,S,b){s.resetLoader(t,v),g(new E({type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,part:a,networkDetails:b}))}})})},d.updateStatsFromPart=function(t,a){var e=t.stats,s=a.stats,u=s.total;if(e.loaded+=s.loaded,u){var n=Math.round(t.duration/a.duration),l=Math.min(Math.round(e.loaded/u),n),p=(n-l)*Math.round(e.loaded/l);e.total=e.loaded+p}else e.total=Math.max(e.loaded,e.total);var g=e.loading,v=s.loading;g.start?g.first+=v.first-v.start:(g.start=v.start,g.first=v.first),g.end=v.end},d.resetLoader=function(t,a){t.loader=null,this.loader===a&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),a.destroy()},y}();function h(y,d){d===void 0&&(d=null);var t=d||y,a={frag:y,part:d,responseType:"arraybuffer",url:t.url,rangeStart:0,rangeEnd:0},e=t.byteRangeStartOffset,s=t.byteRangeEndOffset;return Object(_.isFiniteNumber)(e)&&Object(_.isFiniteNumber)(s)&&(a.rangeStart=e,a.rangeEnd=s),a}var E=function(y){var d,t;function a(e){for(var s,u=arguments.length,n=new Array(u>1?u-1:0),l=1;l<u;l++)n[l-1]=arguments[l];return(s=y.call.apply(y,[this].concat(n))||this).data=void 0,s.data=e,s}return t=y,(d=a).prototype=Object.create(t.prototype),d.prototype.constructor=d,k(d,t),a}(A(Error))},"./src/loader/fragment.ts":function(N,w,f){f.r(w),f.d(w,"ElementaryStreamTypes",function(){return _}),f.d(w,"BaseSegment",function(){return y}),f.d(w,"Fragment",function(){return d}),f.d(w,"Part",function(){return t});var _,T,A=f("./src/polyfills/number.ts"),R=f("./node_modules/url-toolkit/src/url-toolkit.js"),I=f("./src/utils/logger.ts"),k=f("./src/loader/level-key.ts"),o=f("./src/loader/load-stats.ts");function L(a,e){a.prototype=Object.create(e.prototype),a.prototype.constructor=a,m(a,e)}function m(a,e){return(m=Object.setPrototypeOf||function(s,u){return s.__proto__=u,s})(a,e)}function h(a,e){for(var s=0;s<e.length;s++){var u=e[s];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(a,u.key,u)}}function E(a,e,s){return e&&h(a.prototype,e),s&&h(a,s),a}(T=_||(_={})).AUDIO="audio",T.VIDEO="video",T.AUDIOVIDEO="audiovideo";var y=function(){function a(e){var s;this._byteRange=null,this._url=null,this.baseurl=void 0,this.relurl=void 0,this.elementaryStreams=((s={})[_.AUDIO]=null,s[_.VIDEO]=null,s[_.AUDIOVIDEO]=null,s),this.baseurl=e}return a.prototype.setByteRange=function(e,s){var u=e.split("@",2),n=[];u.length===1?n[0]=s?s.byteRangeEndOffset:0:n[0]=parseInt(u[1]),n[1]=parseInt(u[0])+n[0],this._byteRange=n},E(a,[{key:"byteRange",get:function(){return this._byteRange?this._byteRange:[]}},{key:"byteRangeStartOffset",get:function(){return this.byteRange[0]}},{key:"byteRangeEndOffset",get:function(){return this.byteRange[1]}},{key:"url",get:function(){return!this._url&&this.baseurl&&this.relurl&&(this._url=Object(R.buildAbsoluteURL)(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""},set:function(e){this._url=e}}]),a}(),d=function(a){function e(u,n){var l;return(l=a.call(this,n)||this)._decryptdata=null,l.rawProgramDateTime=null,l.programDateTime=null,l.tagList=[],l.duration=0,l.sn=0,l.levelkey=void 0,l.type=void 0,l.loader=null,l.level=-1,l.cc=0,l.startPTS=void 0,l.endPTS=void 0,l.appendedPTS=void 0,l.startDTS=void 0,l.endDTS=void 0,l.start=0,l.deltaPTS=void 0,l.maxStartPTS=void 0,l.minEndPTS=void 0,l.stats=new o.LoadStats,l.urlId=0,l.data=void 0,l.bitrateTest=!1,l.title=null,l.initSegment=null,l.type=u,l}L(e,a);var s=e.prototype;return s.createInitializationVector=function(u){for(var n=new Uint8Array(16),l=12;l<16;l++)n[l]=u>>8*(15-l)&255;return n},s.setDecryptDataFromLevelKey=function(u,n){var l=u;return(u==null?void 0:u.method)==="AES-128"&&u.uri&&!u.iv&&((l=k.LevelKey.fromURI(u.uri)).method=u.method,l.iv=this.createInitializationVector(n),l.keyFormat="identity"),l},s.setElementaryStreamInfo=function(u,n,l,p,g,v){v===void 0&&(v=!1);var r=this.elementaryStreams,i=r[u];i?(i.startPTS=Math.min(i.startPTS,n),i.endPTS=Math.max(i.endPTS,l),i.startDTS=Math.min(i.startDTS,p),i.endDTS=Math.max(i.endDTS,g)):r[u]={startPTS:n,endPTS:l,startDTS:p,endDTS:g,partial:v}},s.clearElementaryStreamInfo=function(){var u=this.elementaryStreams;u[_.AUDIO]=null,u[_.VIDEO]=null,u[_.AUDIOVIDEO]=null},E(e,[{key:"decryptdata",get:function(){if(!this.levelkey&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkey){var u=this.sn;typeof u!="number"&&(this.levelkey&&this.levelkey.method==="AES-128"&&!this.levelkey.iv&&I.logger.warn('missing IV for initialization segment with method="'+this.levelkey.method+'" - compliance issue'),u=0),this._decryptdata=this.setDecryptDataFromLevelKey(this.levelkey,u)}return this._decryptdata}},{key:"end",get:function(){return this.start+this.duration}},{key:"endProgramDateTime",get:function(){if(this.programDateTime===null||!Object(A.isFiniteNumber)(this.programDateTime))return null;var u=Object(A.isFiniteNumber)(this.duration)?this.duration:0;return this.programDateTime+1e3*u}},{key:"encrypted",get:function(){var u;return!((u=this.decryptdata)===null||u===void 0||!u.keyFormat||!this.decryptdata.uri)}}]),e}(y),t=function(a){function e(s,u,n,l,p){var g;(g=a.call(this,n)||this).fragOffset=0,g.duration=0,g.gap=!1,g.independent=!1,g.relurl=void 0,g.fragment=void 0,g.index=void 0,g.stats=new o.LoadStats,g.duration=s.decimalFloatingPoint("DURATION"),g.gap=s.bool("GAP"),g.independent=s.bool("INDEPENDENT"),g.relurl=s.enumeratedString("URI"),g.fragment=u,g.index=l;var v=s.enumeratedString("BYTERANGE");return v&&g.setByteRange(v,p),p&&(g.fragOffset=p.fragOffset+p.duration),g}return L(e,a),E(e,[{key:"start",get:function(){return this.fragment.start+this.fragOffset}},{key:"end",get:function(){return this.start+this.duration}},{key:"loaded",get:function(){var s=this.elementaryStreams;return!!(s.audio||s.video||s.audiovideo)}}]),e}(y)},"./src/loader/key-loader.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return R});var _=f("./src/events.ts"),T=f("./src/errors.ts"),A=f("./src/utils/logger.ts"),R=function(){function I(o){this.hls=void 0,this.loaders={},this.decryptkey=null,this.decrypturl=null,this.hls=o,this._registerListeners()}var k=I.prototype;return k._registerListeners=function(){this.hls.on(_.Events.KEY_LOADING,this.onKeyLoading,this)},k._unregisterListeners=function(){this.hls.off(_.Events.KEY_LOADING,this.onKeyLoading)},k.destroy=function(){for(var o in this._unregisterListeners(),this.loaders){var L=this.loaders[o];L&&L.destroy()}this.loaders={}},k.onKeyLoading=function(o,L){var m=L.frag,h=m.type,E=this.loaders[h];if(m.decryptdata){var y=m.decryptdata.uri;if(y!==this.decrypturl||this.decryptkey===null){var d=this.hls.config;if(E&&(A.logger.warn("abort previous key loader for type:"+h),E.abort()),!y)return void A.logger.warn("key uri is falsy");var t=d.loader,a=m.loader=this.loaders[h]=new t(d);this.decrypturl=y,this.decryptkey=null;var e={url:y,frag:m,responseType:"arraybuffer"},s={timeout:d.fragLoadingTimeOut,maxRetry:0,retryDelay:d.fragLoadingRetryDelay,maxRetryDelay:d.fragLoadingMaxRetryTimeout,highWaterMark:0},u={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};a.load(e,s,u)}else this.decryptkey&&(m.decryptdata.key=this.decryptkey,this.hls.trigger(_.Events.KEY_LOADED,{frag:m}))}else A.logger.warn("Missing decryption data on fragment in onKeyLoading")},k.loadsuccess=function(o,L,m){var h=m.frag;h.decryptdata?(this.decryptkey=h.decryptdata.key=new Uint8Array(o.data),h.loader=null,delete this.loaders[h.type],this.hls.trigger(_.Events.KEY_LOADED,{frag:h})):A.logger.error("after key load, decryptdata unset")},k.loaderror=function(o,L){var m=L.frag,h=m.loader;h&&h.abort(),delete this.loaders[m.type],this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.KEY_LOAD_ERROR,fatal:!1,frag:m,response:o})},k.loadtimeout=function(o,L){var m=L.frag,h=m.loader;h&&h.abort(),delete this.loaders[m.type],this.hls.trigger(_.Events.ERROR,{type:T.ErrorTypes.NETWORK_ERROR,details:T.ErrorDetails.KEY_LOAD_TIMEOUT,fatal:!1,frag:m})},I}()},"./src/loader/level-details.ts":function(N,w,f){f.r(w),f.d(w,"LevelDetails",function(){return A});var _=f("./src/polyfills/number.ts");function T(R,I){for(var k=0;k<I.length;k++){var o=I[k];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(R,o.key,o)}}var A=function(){function R(o){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.live=!0,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.availabilityDelay=void 0,this.misses=0,this.needSidxRanges=!1,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.fragments=[],this.url=o}var I,k;return R.prototype.reloaded=function(o){if(!o)return this.advanced=!0,void(this.updated=!0);var L=this.lastPartSn-o.lastPartSn,m=this.lastPartIndex-o.lastPartIndex;this.updated=this.endSN!==o.endSN||!!m||!!L,this.advanced=this.endSN>o.endSN||L>0||L===0&&m>0,this.updated||this.advanced?this.misses=Math.floor(.6*o.misses):this.misses=o.misses+1,this.availabilityDelay=o.availabilityDelay},I=R,(k=[{key:"hasProgramDateTime",get:function(){return!!this.fragments.length&&Object(_.isFiniteNumber)(this.fragments[this.fragments.length-1].programDateTime)}},{key:"levelTargetDuration",get:function(){return this.averagetargetduration||this.targetduration||10}},{key:"drift",get:function(){var o=this.driftEndTime-this.driftStartTime;return o>0?1e3*(this.driftEnd-this.driftStart)/o:1}},{key:"edge",get:function(){return this.partEnd||this.fragmentEnd}},{key:"partEnd",get:function(){var o;return(o=this.partList)!==null&&o!==void 0&&o.length?this.partList[this.partList.length-1].end:this.fragmentEnd}},{key:"fragmentEnd",get:function(){var o;return(o=this.fragments)!==null&&o!==void 0&&o.length?this.fragments[this.fragments.length-1].end:0}},{key:"age",get:function(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}},{key:"lastPartIndex",get:function(){var o;return(o=this.partList)!==null&&o!==void 0&&o.length?this.partList[this.partList.length-1].index:-1}},{key:"lastPartSn",get:function(){var o;return(o=this.partList)!==null&&o!==void 0&&o.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}}])&&T(I.prototype,k),R}()},"./src/loader/level-key.ts":function(N,w,f){f.r(w),f.d(w,"LevelKey",function(){return A});var _=f("./node_modules/url-toolkit/src/url-toolkit.js");function T(R,I){for(var k=0;k<I.length;k++){var o=I[k];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(R,o.key,o)}}var A=function(){function R(o,L){this._uri=null,this.method=null,this.keyFormat=null,this.keyFormatVersions=null,this.keyID=null,this.key=null,this.iv=null,this._uri=L?Object(_.buildAbsoluteURL)(o,L,{alwaysNormalize:!0}):o}var I,k;return R.fromURL=function(o,L){return new R(o,L)},R.fromURI=function(o){return new R(o)},I=R,(k=[{key:"uri",get:function(){return this._uri}}])&&T(I.prototype,k),R}()},"./src/loader/load-stats.ts":function(N,w,f){f.r(w),f.d(w,"LoadStats",function(){return _});var _=function(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}}},"./src/loader/m3u8-parser.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return t});var _=f("./src/polyfills/number.ts"),T=f("./node_modules/url-toolkit/src/url-toolkit.js"),A=f("./src/loader/fragment.ts"),R=f("./src/loader/level-details.ts"),I=f("./src/loader/level-key.ts"),k=f("./src/utils/attr-list.ts"),o=f("./src/utils/logger.ts"),L=f("./src/utils/codecs.ts"),m=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-SESSION-DATA:([^\r\n]*)[\r\n]+/g,h=/#EXT-X-MEDIA:(.*)/g,E=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[\S ]*)/.source,/#EXT-X-BYTERANGE:*(.+)/.source,/#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/#.*/.source].join("|"),"g"),y=new RegExp([/#(EXTM3U)/.source,/#EXT-X-(PLAYLIST-TYPE):(.+)/.source,/#EXT-X-(MEDIA-SEQUENCE): *(\d+)/.source,/#EXT-X-(SKIP):(.+)/.source,/#EXT-X-(TARGETDURATION): *(\d+)/.source,/#EXT-X-(KEY):(.+)/.source,/#EXT-X-(START):(.+)/.source,/#EXT-X-(ENDLIST)/.source,/#EXT-X-(DISCONTINUITY-SEQ)UENCE: *(\d+)/.source,/#EXT-X-(DIS)CONTINUITY/.source,/#EXT-X-(VERSION):(\d+)/.source,/#EXT-X-(MAP):(.+)/.source,/#EXT-X-(SERVER-CONTROL):(.+)/.source,/#EXT-X-(PART-INF):(.+)/.source,/#EXT-X-(GAP)/.source,/#EXT-X-(BITRATE):\s*(\d+)/.source,/#EXT-X-(PART):(.+)/.source,/#EXT-X-(PRELOAD-HINT):(.+)/.source,/#EXT-X-(RENDITION-REPORT):(.+)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|")),d=/\.(mp4|m4s|m4v|m4a)$/i,t=function(){function u(){}return u.findGroup=function(n,l){for(var p=0;p<n.length;p++){var g=n[p];if(g.id===l)return g}},u.convertAVC1ToAVCOTI=function(n){var l=n.split(".");if(l.length>2){var p=l.shift()+".";return p+=parseInt(l.shift()).toString(16),p+=("000"+parseInt(l.shift()).toString(16)).substr(-4)}return n},u.resolve=function(n,l){return T.buildAbsoluteURL(l,n,{alwaysNormalize:!0})},u.parseMasterPlaylist=function(n,l){var p,g=[],v={},r=!1;for(m.lastIndex=0;(p=m.exec(n))!=null;)if(p[1]){var i=new k.AttrList(p[1]),c={attrs:i,bitrate:i.decimalInteger("AVERAGE-BANDWIDTH")||i.decimalInteger("BANDWIDTH"),name:i.NAME,url:u.resolve(p[2],l)},S=i.decimalResolution("RESOLUTION");S&&(c.width=S.width,c.height=S.height),a((i.CODECS||"").split(/[ ,]+/).filter(function(D){return D}),c),c.videoCodec&&c.videoCodec.indexOf("avc1")!==-1&&(c.videoCodec=u.convertAVC1ToAVCOTI(c.videoCodec)),g.push(c)}else if(p[3]){var b=new k.AttrList(p[3]);b["DATA-ID"]&&(r=!0,v[b["DATA-ID"]]=b)}return{levels:g,sessionData:r?v:null}},u.parseMasterPlaylistMedia=function(n,l,p,g){var v;g===void 0&&(g=[]);var r=[],i=0;for(h.lastIndex=0;(v=h.exec(n))!==null;){var c=new k.AttrList(v[1]);if(c.TYPE===p){var S={attrs:c,bitrate:0,id:i++,groupId:c["GROUP-ID"],instreamId:c["INSTREAM-ID"],name:c.NAME||c.LANGUAGE||"",type:p,default:c.bool("DEFAULT"),autoselect:c.bool("AUTOSELECT"),forced:c.bool("FORCED"),lang:c.LANGUAGE,url:c.URI?u.resolve(c.URI,l):""};if(g.length){var b=u.findGroup(g,S.groupId)||g[0];e(S,b,"audioCodec"),e(S,b,"textCodec")}r.push(S)}}return r},u.parseLevelPlaylist=function(n,l,p,g,v){var r,i,c,S=new R.LevelDetails(l),b=S.fragments,D=null,O=0,C=0,x=0,P=0,F=null,M=new A.Fragment(g,l),B=-1,U=!1;for(E.lastIndex=0,S.m3u8=n;(r=E.exec(n))!==null;){U&&(U=!1,(M=new A.Fragment(g,l)).start=x,M.sn=O,M.cc=P,M.level=p,D&&(M.initSegment=D,M.rawProgramDateTime=D.rawProgramDateTime));var G=r[1];if(G){M.duration=parseFloat(G);var K=(" "+r[2]).slice(1);M.title=K||null,M.tagList.push(K?["INF",G,K]:["INF",G])}else if(r[3])Object(_.isFiniteNumber)(M.duration)&&(M.start=x,c&&(M.levelkey=c),M.sn=O,M.level=p,M.cc=P,M.urlId=v,b.push(M),M.relurl=(" "+r[3]).slice(1),s(M,F),F=M,x+=M.duration,O++,C=0,U=!0);else if(r[4]){var H=(" "+r[4]).slice(1);F?M.setByteRange(H,F):M.setByteRange(H)}else if(r[5])M.rawProgramDateTime=(" "+r[5]).slice(1),M.tagList.push(["PROGRAM-DATE-TIME",M.rawProgramDateTime]),B===-1&&(B=b.length);else{if(!(r=r[0].match(y))){o.logger.warn("No matches on slow regex match for level playlist!");continue}for(i=1;i<r.length&&r[i]===void 0;i++);var Y=(" "+r[i]).slice(1),W=(" "+r[i+1]).slice(1),q=r[i+2]?(" "+r[i+2]).slice(1):"";switch(Y){case"PLAYLIST-TYPE":S.type=W.toUpperCase();break;case"MEDIA-SEQUENCE":O=S.startSN=parseInt(W);break;case"SKIP":var Q=new k.AttrList(W),tt=Q.decimalInteger("SKIPPED-SEGMENTS");if(Object(_.isFiniteNumber)(tt)){S.skippedSegments=tt;for(var rt=tt;rt--;)b.unshift(null);O+=tt}var z=Q.enumeratedString("RECENTLY-REMOVED-DATERANGES");z&&(S.recentlyRemovedDateranges=z.split("	"));break;case"TARGETDURATION":S.targetduration=parseFloat(W);break;case"VERSION":S.version=parseInt(W);break;case"EXTM3U":break;case"ENDLIST":S.live=!1;break;case"#":(W||q)&&M.tagList.push(q?[W,q]:[W]);break;case"DIS":P++;case"GAP":M.tagList.push([Y]);break;case"BITRATE":M.tagList.push([Y,W]);break;case"DISCONTINUITY-SEQ":P=parseInt(W);break;case"KEY":var at,X=new k.AttrList(W),J=X.enumeratedString("METHOD"),nt=X.URI,it=X.hexadecimalInteger("IV"),dt=X.enumeratedString("KEYFORMATVERSIONS"),gt=X.enumeratedString("KEYID"),vt=(at=X.enumeratedString("KEYFORMAT"))!=null?at:"identity";if(["com.apple.streamingkeydelivery","com.microsoft.playready","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed","com.widevine"].indexOf(vt)>-1){o.logger.warn("Keyformat "+vt+" is not supported from the manifest");continue}if(vt!=="identity")continue;J&&(c=I.LevelKey.fromURL(l,nt),nt&&["AES-128","SAMPLE-AES","SAMPLE-AES-CENC"].indexOf(J)>=0&&(c.method=J,c.keyFormat=vt,gt&&(c.keyID=gt),dt&&(c.keyFormatVersions=dt),c.iv=it));break;case"START":var mt=new k.AttrList(W).decimalFloatingPoint("TIME-OFFSET");Object(_.isFiniteNumber)(mt)&&(S.startTimeOffset=mt);break;case"MAP":var lt=new k.AttrList(W);M.relurl=lt.URI,lt.BYTERANGE&&M.setByteRange(lt.BYTERANGE),M.level=p,M.sn="initSegment",c&&(M.levelkey=c),M.initSegment=null,D=M,U=!0;break;case"SERVER-CONTROL":var ot=new k.AttrList(W);S.canBlockReload=ot.bool("CAN-BLOCK-RELOAD"),S.canSkipUntil=ot.optionalFloat("CAN-SKIP-UNTIL",0),S.canSkipDateRanges=S.canSkipUntil>0&&ot.bool("CAN-SKIP-DATERANGES"),S.partHoldBack=ot.optionalFloat("PART-HOLD-BACK",0),S.holdBack=ot.optionalFloat("HOLD-BACK",0);break;case"PART-INF":var Mt=new k.AttrList(W);S.partTarget=Mt.decimalFloatingPoint("PART-TARGET");break;case"PART":var yt=S.partList;yt||(yt=S.partList=[]);var Lt=C>0?yt[yt.length-1]:void 0,Nt=C++,kt=new A.Part(new k.AttrList(W),M,l,Nt,Lt);yt.push(kt),M.duration+=kt.duration;break;case"PRELOAD-HINT":var St=new k.AttrList(W);S.preloadHint=St;break;case"RENDITION-REPORT":var Kt=new k.AttrList(W);S.renditionReports=S.renditionReports||[],S.renditionReports.push(Kt);break;default:o.logger.warn("line parsed but not handled: "+r)}}}F&&!F.relurl?(b.pop(),x-=F.duration,S.partList&&(S.fragmentHint=F)):S.partList&&(s(M,F),M.cc=P,S.fragmentHint=M);var Bt=b.length,bt=b[0],Vt=b[Bt-1];if((x+=S.skippedSegments*S.targetduration)>0&&Bt&&Vt){S.averagetargetduration=x/Bt;var Ut=Vt.sn;S.endSN=Ut!=="initSegment"?Ut:0,bt&&(S.startCC=bt.cc,bt.initSegment||S.fragments.every(function(Rt){return Rt.relurl&&(At=Rt.relurl,d.test((_t=(Et=T.parseURL(At))===null||Et===void 0?void 0:Et.path)!=null?_t:""));var At,_t,Et})&&(o.logger.warn("MP4 fragments found but no init segment (probably no MAP, incomplete M3U8), trying to fetch SIDX"),(M=new A.Fragment(g,l)).relurl=Vt.relurl,M.level=p,M.sn="initSegment",bt.initSegment=M,S.needSidxRanges=!0))}else S.endSN=0,S.startCC=0;return S.fragmentHint&&(x+=S.fragmentHint.duration),S.totalduration=x,S.endCC=P,B>0&&function(Rt,At){for(var _t=Rt[At],Et=At;Et--;){var It=Rt[Et];if(!It)return;It.programDateTime=_t.programDateTime-1e3*It.duration,_t=It}}(b,B),S},u}();function a(u,n){["video","audio","text"].forEach(function(l){var p=u.filter(function(v){return Object(L.isCodecType)(v,l)});if(p.length){var g=p.filter(function(v){return v.lastIndexOf("avc1",0)===0||v.lastIndexOf("mp4a",0)===0});n[l+"Codec"]=g.length>0?g[0]:p[0],u=u.filter(function(v){return p.indexOf(v)===-1})}}),n.unknownCodecs=u}function e(u,n,l){var p=n[l];p&&(u[l]=p)}function s(u,n){u.rawProgramDateTime?u.programDateTime=Date.parse(u.rawProgramDateTime):n!=null&&n.programDateTime&&(u.programDateTime=n.endProgramDateTime),Object(_.isFiniteNumber)(u.programDateTime)||(u.programDateTime=null,u.rawProgramDateTime=null)}},"./src/loader/playlist-loader.ts":function(N,w,f){f.r(w);var _=f("./src/polyfills/number.ts"),T=f("./src/events.ts"),A=f("./src/errors.ts"),R=f("./src/utils/logger.ts"),I=f("./src/utils/mp4-tools.ts"),k=f("./src/loader/m3u8-parser.ts"),o=f("./src/types/loader.ts"),L=f("./src/utils/attr-list.ts");function m(E,y){var d=E.url;return d!==void 0&&d.indexOf("data:")!==0||(d=y.url),d}var h=function(){function E(d){this.hls=void 0,this.loaders=Object.create(null),this.hls=d,this.registerListeners()}var y=E.prototype;return y.registerListeners=function(){var d=this.hls;d.on(T.Events.MANIFEST_LOADING,this.onManifestLoading,this),d.on(T.Events.LEVEL_LOADING,this.onLevelLoading,this),d.on(T.Events.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),d.on(T.Events.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},y.unregisterListeners=function(){var d=this.hls;d.off(T.Events.MANIFEST_LOADING,this.onManifestLoading,this),d.off(T.Events.LEVEL_LOADING,this.onLevelLoading,this),d.off(T.Events.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),d.off(T.Events.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},y.createInternalLoader=function(d){var t=this.hls.config,a=t.pLoader,e=t.loader,s=new(a||e)(t);return d.loader=s,this.loaders[d.type]=s,s},y.getInternalLoader=function(d){return this.loaders[d.type]},y.resetInternalLoader=function(d){this.loaders[d]&&delete this.loaders[d]},y.destroyInternalLoaders=function(){for(var d in this.loaders){var t=this.loaders[d];t&&t.destroy(),this.resetInternalLoader(d)}},y.destroy=function(){this.unregisterListeners(),this.destroyInternalLoaders()},y.onManifestLoading=function(d,t){var a=t.url;this.load({id:null,groupId:null,level:0,responseType:"text",type:o.PlaylistContextType.MANIFEST,url:a,deliveryDirectives:null})},y.onLevelLoading=function(d,t){var a=t.id,e=t.level,s=t.url,u=t.deliveryDirectives;this.load({id:a,groupId:null,level:e,responseType:"text",type:o.PlaylistContextType.LEVEL,url:s,deliveryDirectives:u})},y.onAudioTrackLoading=function(d,t){var a=t.id,e=t.groupId,s=t.url,u=t.deliveryDirectives;this.load({id:a,groupId:e,level:null,responseType:"text",type:o.PlaylistContextType.AUDIO_TRACK,url:s,deliveryDirectives:u})},y.onSubtitleTrackLoading=function(d,t){var a=t.id,e=t.groupId,s=t.url,u=t.deliveryDirectives;this.load({id:a,groupId:e,level:null,responseType:"text",type:o.PlaylistContextType.SUBTITLE_TRACK,url:s,deliveryDirectives:u})},y.load=function(d){var t,a,e,s,u,n,l=this.hls.config,p=this.getInternalLoader(d);if(p){var g=p.context;if(g&&g.url===d.url)return void R.logger.trace("[playlist-loader]: playlist request ongoing");R.logger.log("[playlist-loader]: aborting previous loader for type: "+d.type),p.abort()}switch(d.type){case o.PlaylistContextType.MANIFEST:a=l.manifestLoadingMaxRetry,e=l.manifestLoadingTimeOut,s=l.manifestLoadingRetryDelay,u=l.manifestLoadingMaxRetryTimeout;break;case o.PlaylistContextType.LEVEL:case o.PlaylistContextType.AUDIO_TRACK:case o.PlaylistContextType.SUBTITLE_TRACK:a=0,e=l.levelLoadingTimeOut;break;default:a=l.levelLoadingMaxRetry,e=l.levelLoadingTimeOut,s=l.levelLoadingRetryDelay,u=l.levelLoadingMaxRetryTimeout}if(p=this.createInternalLoader(d),(t=d.deliveryDirectives)!==null&&t!==void 0&&t.part&&(d.type===o.PlaylistContextType.LEVEL&&d.level!==null?n=this.hls.levels[d.level].details:d.type===o.PlaylistContextType.AUDIO_TRACK&&d.id!==null?n=this.hls.audioTracks[d.id].details:d.type===o.PlaylistContextType.SUBTITLE_TRACK&&d.id!==null&&(n=this.hls.subtitleTracks[d.id].details),n)){var v=n.partTarget,r=n.targetduration;v&&r&&(e=Math.min(1e3*Math.max(3*v,.8*r),e))}var i={timeout:e,maxRetry:a,retryDelay:s,maxRetryDelay:u,highWaterMark:0},c={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};p.load(d,i,c)},y.loadsuccess=function(d,t,a,e){if(e===void 0&&(e=null),a.isSidxRequest)return this.handleSidxRequest(d,a),void this.handlePlaylistLoaded(d,t,a,e);this.resetInternalLoader(a.type);var s=d.data;s.indexOf("#EXTM3U")===0?(t.parsing.start=performance.now(),s.indexOf("#EXTINF:")>0||s.indexOf("#EXT-X-TARGETDURATION:")>0?this.handleTrackOrLevelPlaylist(d,t,a,e):this.handleMasterPlaylist(d,t,a,e)):this.handleManifestParsingError(d,a,"no EXTM3U delimiter",e)},y.loaderror=function(d,t,a){a===void 0&&(a=null),this.handleNetworkError(t,a,!1,d)},y.loadtimeout=function(d,t,a){a===void 0&&(a=null),this.handleNetworkError(t,a,!0)},y.handleMasterPlaylist=function(d,t,a,e){var s=this.hls,u=d.data,n=m(d,a),l=k.default.parseMasterPlaylist(u,n),p=l.levels,g=l.sessionData;if(p.length){var v=p.map(function(b){return{id:b.attrs.AUDIO,audioCodec:b.audioCodec}}),r=p.map(function(b){return{id:b.attrs.SUBTITLES,textCodec:b.textCodec}}),i=k.default.parseMasterPlaylistMedia(u,n,"AUDIO",v),c=k.default.parseMasterPlaylistMedia(u,n,"SUBTITLES",r),S=k.default.parseMasterPlaylistMedia(u,n,"CLOSED-CAPTIONS");i.length&&(i.some(function(b){return!b.url})||!p[0].audioCodec||p[0].attrs.AUDIO||(R.logger.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),i.unshift({type:"main",name:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new L.AttrList({}),bitrate:0,url:""}))),s.trigger(T.Events.MANIFEST_LOADED,{levels:p,audioTracks:i,subtitles:c,captions:S,url:n,stats:t,networkDetails:e,sessionData:g})}else this.handleManifestParsingError(d,a,"no level found in manifest",e)},y.handleTrackOrLevelPlaylist=function(d,t,a,e){var s=this.hls,u=a.id,n=a.level,l=a.type,p=m(d,a),g=Object(_.isFiniteNumber)(u)?u:0,v=Object(_.isFiniteNumber)(n)?n:g,r=function(D){switch(D.type){case o.PlaylistContextType.AUDIO_TRACK:return o.PlaylistLevelType.AUDIO;case o.PlaylistContextType.SUBTITLE_TRACK:return o.PlaylistLevelType.SUBTITLE;default:return o.PlaylistLevelType.MAIN}}(a),i=k.default.parseLevelPlaylist(d.data,p,v,r,g);if(i.fragments.length){if(l===o.PlaylistContextType.MANIFEST){var c={attrs:new L.AttrList({}),bitrate:0,details:i,name:"",url:p};s.trigger(T.Events.MANIFEST_LOADED,{levels:[c],audioTracks:[],url:p,stats:t,networkDetails:e,sessionData:null})}if(t.parsing.end=performance.now(),i.needSidxRanges){var S,b=(S=i.fragments[0].initSegment)===null||S===void 0?void 0:S.url;this.load({url:b,isSidxRequest:!0,type:l,level:n,levelDetails:i,id:u,groupId:null,rangeStart:0,rangeEnd:2048,responseType:"arraybuffer",deliveryDirectives:null})}else a.levelDetails=i,this.handlePlaylistLoaded(d,t,a,e)}else s.trigger(T.Events.ERROR,{type:A.ErrorTypes.NETWORK_ERROR,details:A.ErrorDetails.LEVEL_EMPTY_ERROR,fatal:!1,url:p,reason:"no fragments found in level",level:typeof a.level=="number"?a.level:void 0})},y.handleSidxRequest=function(d,t){var a=Object(I.parseSegmentIndex)(new Uint8Array(d.data));if(a){var e=a.references,s=t.levelDetails;e.forEach(function(u,n){var l=u.info,p=s.fragments[n];p.byteRange.length===0&&p.setByteRange(String(1+l.end-l.start)+"@"+String(l.start)),p.initSegment&&p.initSegment.setByteRange(String(a.moovEndOffset)+"@0")})}},y.handleManifestParsingError=function(d,t,a,e){this.hls.trigger(T.Events.ERROR,{type:A.ErrorTypes.NETWORK_ERROR,details:A.ErrorDetails.MANIFEST_PARSING_ERROR,fatal:t.type===o.PlaylistContextType.MANIFEST,url:d.url,reason:a,response:d,context:t,networkDetails:e})},y.handleNetworkError=function(d,t,a,e){a===void 0&&(a=!1),R.logger.warn("[playlist-loader]: A network "+(a?"timeout":"error")+" occurred while loading "+d.type+" level: "+d.level+" id: "+d.id+' group-id: "'+d.groupId+'"');var s=A.ErrorDetails.UNKNOWN,u=!1,n=this.getInternalLoader(d);switch(d.type){case o.PlaylistContextType.MANIFEST:s=a?A.ErrorDetails.MANIFEST_LOAD_TIMEOUT:A.ErrorDetails.MANIFEST_LOAD_ERROR,u=!0;break;case o.PlaylistContextType.LEVEL:s=a?A.ErrorDetails.LEVEL_LOAD_TIMEOUT:A.ErrorDetails.LEVEL_LOAD_ERROR,u=!1;break;case o.PlaylistContextType.AUDIO_TRACK:s=a?A.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT:A.ErrorDetails.AUDIO_TRACK_LOAD_ERROR,u=!1;break;case o.PlaylistContextType.SUBTITLE_TRACK:s=a?A.ErrorDetails.SUBTITLE_TRACK_LOAD_TIMEOUT:A.ErrorDetails.SUBTITLE_LOAD_ERROR,u=!1}n&&this.resetInternalLoader(d.type);var l={type:A.ErrorTypes.NETWORK_ERROR,details:s,fatal:u,url:d.url,loader:n,context:d,networkDetails:t};e&&(l.response=e),this.hls.trigger(T.Events.ERROR,l)},y.handlePlaylistLoaded=function(d,t,a,e){var s=a.type,u=a.level,n=a.id,l=a.groupId,p=a.loader,g=a.levelDetails,v=a.deliveryDirectives;if(g!=null&&g.targetduration){if(p)switch(g.live&&(p.getCacheAge&&(g.ageHeader=p.getCacheAge()||0),p.getCacheAge&&!isNaN(g.ageHeader)||(g.ageHeader=0)),s){case o.PlaylistContextType.MANIFEST:case o.PlaylistContextType.LEVEL:this.hls.trigger(T.Events.LEVEL_LOADED,{details:g,level:u||0,id:n||0,stats:t,networkDetails:e,deliveryDirectives:v});break;case o.PlaylistContextType.AUDIO_TRACK:this.hls.trigger(T.Events.AUDIO_TRACK_LOADED,{details:g,id:n||0,groupId:l||"",stats:t,networkDetails:e,deliveryDirectives:v});break;case o.PlaylistContextType.SUBTITLE_TRACK:this.hls.trigger(T.Events.SUBTITLE_TRACK_LOADED,{details:g,id:n||0,groupId:l||"",stats:t,networkDetails:e,deliveryDirectives:v})}}else this.handleManifestParsingError(d,a,"invalid target duration",e)},E}();w.default=h},"./src/polyfills/number.ts":function(N,w,f){f.r(w),f.d(w,"isFiniteNumber",function(){return _}),f.d(w,"MAX_SAFE_INTEGER",function(){return T});var _=Number.isFinite||function(A){return typeof A=="number"&&isFinite(A)},T=Number.MAX_SAFE_INTEGER||9007199254740991},"./src/remux/aac-helper.ts":function(N,w,f){f.r(w);var _=function(){function T(){}return T.getSilentFrame=function(A,R){switch(A){case"mp4a.40.2":if(R===1)return new Uint8Array([0,200,0,128,35,128]);if(R===2)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(R===3)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(R===4)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(R===5)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(R===6)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(R===1)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(R===2)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(R===3)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}},T}();w.default=_},"./src/remux/mp4-generator.ts":function(N,w,f){f.r(w);var _=Math.pow(2,32)-1,T=function(){function A(){}return A.init=function(){var R;for(R in A.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},A.types)A.types.hasOwnProperty(R)&&(A.types[R]=[R.charCodeAt(0),R.charCodeAt(1),R.charCodeAt(2),R.charCodeAt(3)]);var I=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),k=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);A.HDLR_TYPES={video:I,audio:k};var o=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),L=new Uint8Array([0,0,0,0,0,0,0,0]);A.STTS=A.STSC=A.STCO=L,A.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),A.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),A.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),A.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var m=new Uint8Array([105,115,111,109]),h=new Uint8Array([97,118,99,49]),E=new Uint8Array([0,0,0,1]);A.FTYP=A.box(A.types.ftyp,m,E,m,h),A.DINF=A.box(A.types.dinf,A.box(A.types.dref,o))},A.box=function(R){for(var I=8,k=arguments.length,o=new Array(k>1?k-1:0),L=1;L<k;L++)o[L-1]=arguments[L];for(var m=o.length,h=m;m--;)I+=o[m].byteLength;var E=new Uint8Array(I);for(E[0]=I>>24&255,E[1]=I>>16&255,E[2]=I>>8&255,E[3]=255&I,E.set(R,4),m=0,I=8;m<h;m++)E.set(o[m],I),I+=o[m].byteLength;return E},A.hdlr=function(R){return A.box(A.types.hdlr,A.HDLR_TYPES[R])},A.mdat=function(R){return A.box(A.types.mdat,R)},A.mdhd=function(R,I){I*=R;var k=Math.floor(I/(_+1)),o=Math.floor(I%(_+1));return A.box(A.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,R>>24&255,R>>16&255,R>>8&255,255&R,k>>24,k>>16&255,k>>8&255,255&k,o>>24,o>>16&255,o>>8&255,255&o,85,196,0,0]))},A.mdia=function(R){return A.box(A.types.mdia,A.mdhd(R.timescale,R.duration),A.hdlr(R.type),A.minf(R))},A.mfhd=function(R){return A.box(A.types.mfhd,new Uint8Array([0,0,0,0,R>>24,R>>16&255,R>>8&255,255&R]))},A.minf=function(R){return R.type==="audio"?A.box(A.types.minf,A.box(A.types.smhd,A.SMHD),A.DINF,A.stbl(R)):A.box(A.types.minf,A.box(A.types.vmhd,A.VMHD),A.DINF,A.stbl(R))},A.moof=function(R,I,k){return A.box(A.types.moof,A.mfhd(R),A.traf(k,I))},A.moov=function(R){for(var I=R.length,k=[];I--;)k[I]=A.trak(R[I]);return A.box.apply(null,[A.types.moov,A.mvhd(R[0].timescale,R[0].duration)].concat(k).concat(A.mvex(R)))},A.mvex=function(R){for(var I=R.length,k=[];I--;)k[I]=A.trex(R[I]);return A.box.apply(null,[A.types.mvex].concat(k))},A.mvhd=function(R,I){I*=R;var k=Math.floor(I/(_+1)),o=Math.floor(I%(_+1)),L=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,R>>24&255,R>>16&255,R>>8&255,255&R,k>>24,k>>16&255,k>>8&255,255&k,o>>24,o>>16&255,o>>8&255,255&o,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return A.box(A.types.mvhd,L)},A.sdtp=function(R){var I,k,o=R.samples||[],L=new Uint8Array(4+o.length);for(I=0;I<o.length;I++)k=o[I].flags,L[I+4]=k.dependsOn<<4|k.isDependedOn<<2|k.hasRedundancy;return A.box(A.types.sdtp,L)},A.stbl=function(R){return A.box(A.types.stbl,A.stsd(R),A.box(A.types.stts,A.STTS),A.box(A.types.stsc,A.STSC),A.box(A.types.stsz,A.STSZ),A.box(A.types.stco,A.STCO))},A.avc1=function(R){var I,k,o,L=[],m=[];for(I=0;I<R.sps.length;I++)o=(k=R.sps[I]).byteLength,L.push(o>>>8&255),L.push(255&o),L=L.concat(Array.prototype.slice.call(k));for(I=0;I<R.pps.length;I++)o=(k=R.pps[I]).byteLength,m.push(o>>>8&255),m.push(255&o),m=m.concat(Array.prototype.slice.call(k));var h=A.box(A.types.avcC,new Uint8Array([1,L[3],L[4],L[5],255,224|R.sps.length].concat(L).concat([R.pps.length]).concat(m))),E=R.width,y=R.height,d=R.pixelRatio[0],t=R.pixelRatio[1];return A.box(A.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,E>>8&255,255&E,y>>8&255,255&y,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),h,A.box(A.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),A.box(A.types.pasp,new Uint8Array([d>>24,d>>16&255,d>>8&255,255&d,t>>24,t>>16&255,t>>8&255,255&t])))},A.esds=function(R){var I=R.config.length;return new Uint8Array([0,0,0,0,3,23+I,0,1,0,4,15+I,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([I]).concat(R.config).concat([6,1,2]))},A.mp4a=function(R){var I=R.samplerate;return A.box(A.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,R.channelCount,0,16,0,0,0,0,I>>8&255,255&I,0,0]),A.box(A.types.esds,A.esds(R)))},A.mp3=function(R){var I=R.samplerate;return A.box(A.types[".mp3"],new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,R.channelCount,0,16,0,0,0,0,I>>8&255,255&I,0,0]))},A.stsd=function(R){return R.type==="audio"?R.isAAC||R.codec!=="mp3"?A.box(A.types.stsd,A.STSD,A.mp4a(R)):A.box(A.types.stsd,A.STSD,A.mp3(R)):A.box(A.types.stsd,A.STSD,A.avc1(R))},A.tkhd=function(R){var I=R.id,k=R.duration*R.timescale,o=R.width,L=R.height,m=Math.floor(k/(_+1)),h=Math.floor(k%(_+1));return A.box(A.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,I>>24&255,I>>16&255,I>>8&255,255&I,0,0,0,0,m>>24,m>>16&255,m>>8&255,255&m,h>>24,h>>16&255,h>>8&255,255&h,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,o>>8&255,255&o,0,0,L>>8&255,255&L,0,0]))},A.traf=function(R,I){var k=A.sdtp(R),o=R.id,L=Math.floor(I/(_+1)),m=Math.floor(I%(_+1));return A.box(A.types.traf,A.box(A.types.tfhd,new Uint8Array([0,0,0,0,o>>24,o>>16&255,o>>8&255,255&o])),A.box(A.types.tfdt,new Uint8Array([1,0,0,0,L>>24,L>>16&255,L>>8&255,255&L,m>>24,m>>16&255,m>>8&255,255&m])),A.trun(R,k.length+16+20+8+16+8+8),k)},A.trak=function(R){return R.duration=R.duration||4294967295,A.box(A.types.trak,A.tkhd(R),A.mdia(R))},A.trex=function(R){var I=R.id;return A.box(A.types.trex,new Uint8Array([0,0,0,0,I>>24,I>>16&255,I>>8&255,255&I,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},A.trun=function(R,I){var k,o,L,m,h,E,y=R.samples||[],d=y.length,t=12+16*d,a=new Uint8Array(t);for(I+=8+t,a.set([0,0,15,1,d>>>24&255,d>>>16&255,d>>>8&255,255&d,I>>>24&255,I>>>16&255,I>>>8&255,255&I],0),k=0;k<d;k++)L=(o=y[k]).duration,m=o.size,h=o.flags,E=o.cts,a.set([L>>>24&255,L>>>16&255,L>>>8&255,255&L,m>>>24&255,m>>>16&255,m>>>8&255,255&m,h.isLeading<<2|h.dependsOn,h.isDependedOn<<6|h.hasRedundancy<<4|h.paddingValue<<1|h.isNonSync,61440&h.degradPrio,15&h.degradPrio,E>>>24&255,E>>>16&255,E>>>8&255,255&E],12+16*k);return A.box(A.types.trun,a)},A.initSegment=function(R){A.types||A.init();var I=A.moov(R),k=new Uint8Array(A.FTYP.byteLength+I.byteLength);return k.set(A.FTYP),k.set(I,A.FTYP.byteLength),k},A}();T.types=void 0,T.HDLR_TYPES=void 0,T.STTS=void 0,T.STSC=void 0,T.STCO=void 0,T.STSZ=void 0,T.VMHD=void 0,T.SMHD=void 0,T.STSD=void 0,T.FTYP=void 0,T.DINF=void 0,w.default=T},"./src/remux/mp4-remuxer.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return d}),f.d(w,"normalizePts",function(){return t});var _=f("./src/polyfills/number.ts"),T=f("./src/remux/aac-helper.ts"),A=f("./src/remux/mp4-generator.ts"),R=f("./src/events.ts"),I=f("./src/errors.ts"),k=f("./src/utils/logger.ts"),o=f("./src/types/loader.ts"),L=f("./src/utils/timescale-conversion.ts");function m(){return(m=Object.assign||function(s){for(var u=1;u<arguments.length;u++){var n=arguments[u];for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(s[l]=n[l])}return s}).apply(this,arguments)}var h=null,E=null,y=!1,d=function(){function s(n,l,p,g){if(this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.ISGenerated=!1,this._initPTS=void 0,this._initDTS=void 0,this.nextAvcDts=null,this.nextAudioPts=null,this.isAudioContiguous=!1,this.isVideoContiguous=!1,this.observer=n,this.config=l,this.typeSupported=p,this.ISGenerated=!1,h===null){var v=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);h=v?parseInt(v[1]):0}if(E===null){var r=navigator.userAgent.match(/Safari\/(\d+)/i);E=r?parseInt(r[1]):0}y=!!h&&h<75||!!E&&E<600}var u=s.prototype;return u.destroy=function(){},u.resetTimeStamp=function(n){k.logger.log("[mp4-remuxer]: initPTS & initDTS reset"),this._initPTS=this._initDTS=n},u.resetNextTimestamp=function(){k.logger.log("[mp4-remuxer]: reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1},u.resetInitSegment=function(){k.logger.log("[mp4-remuxer]: ISGenerated flag reset"),this.ISGenerated=!1},u.getVideoStartPts=function(n){var l=!1,p=n.reduce(function(g,v){var r=v.pts-g;return r<-4294967296?(l=!0,t(g,v.pts)):r>0?g:v.pts},n[0].pts);return l&&k.logger.debug("PTS rollover detected"),p},u.remux=function(n,l,p,g,v,r,i,c){var S,b,D,O,C,x,P=v,F=v,M=n.pid>-1,B=l.pid>-1,U=l.samples.length,G=n.samples.length>0,K=U>1;if((!M||G)&&(!B||K)||this.ISGenerated||i){this.ISGenerated||(D=this.generateIS(n,l,v));var H=this.isVideoContiguous,Y=-1;if(K&&(Y=function(rt){for(var z=0;z<rt.length;z++)if(rt[z].key)return z;return-1}(l.samples),!H&&this.config.forceKeyFrameOnDiscontinuity))if(x=!0,Y>0){k.logger.warn("[mp4-remuxer]: Dropped "+Y+" out of "+U+" video samples due to a missing keyframe");var W=this.getVideoStartPts(l.samples);l.samples=l.samples.slice(Y),l.dropped+=Y,F+=(l.samples[0].pts-W)/(l.timescale||9e4)}else Y===-1&&(k.logger.warn("[mp4-remuxer]: No keyframe found out of "+U+" video samples"),x=!1);if(this.ISGenerated){if(G&&K){var q=this.getVideoStartPts(l.samples),Q=(t(n.samples[0].pts,q)-q)/l.inputTimeScale;P+=Math.max(0,Q),F+=Math.max(0,-Q)}if(G){if(n.samplerate||(k.logger.warn("[mp4-remuxer]: regenerate InitSegment as audio detected"),D=this.generateIS(n,l,v)),b=this.remuxAudio(n,P,this.isAudioContiguous,r,B||K||c===o.PlaylistLevelType.AUDIO?F:void 0),K){var tt=b?b.endPTS-b.startPTS:0;l.inputTimeScale||(k.logger.warn("[mp4-remuxer]: regenerate InitSegment as video detected"),D=this.generateIS(n,l,v)),S=this.remuxVideo(l,F,H,tt)}}else K&&(S=this.remuxVideo(l,F,H,0));S&&(S.firstKeyFrame=Y,S.independent=Y!==-1)}}return this.ISGenerated&&(p.samples.length&&(C=this.remuxID3(p,v)),g.samples.length&&(O=this.remuxText(g,v))),{audio:b,video:S,initSegment:D,independent:x,text:O,id3:C}},u.generateIS=function(n,l,p){var g,v,r,i=n.samples,c=l.samples,S=this.typeSupported,b={},D=!Object(_.isFiniteNumber)(this._initPTS),O="audio/mp4";if(D&&(g=v=1/0),n.config&&i.length&&(n.timescale=n.samplerate,n.isAAC||(S.mpeg?(O="audio/mpeg",n.codec=""):S.mp3&&(n.codec="mp3")),b.audio={id:"audio",container:O,codec:n.codec,initSegment:!n.isAAC&&S.mpeg?new Uint8Array(0):A.default.initSegment([n]),metadata:{channelCount:n.channelCount}},D&&(r=n.inputTimeScale,g=v=i[0].pts-Math.round(r*p))),l.sps&&l.pps&&c.length&&(l.timescale=l.inputTimeScale,b.video={id:"main",container:"video/mp4",codec:l.codec,initSegment:A.default.initSegment([l]),metadata:{width:l.width,height:l.height}},D)){r=l.inputTimeScale;var C=this.getVideoStartPts(c),x=Math.round(r*p);v=Math.min(v,t(c[0].dts,C)-x),g=Math.min(g,C-x)}if(Object.keys(b).length)return this.ISGenerated=!0,D&&(this._initPTS=g,this._initDTS=v),{tracks:b,initPTS:g,timescale:r}},u.remuxVideo=function(n,l,p,g){var v,r,i,c=n.inputTimeScale,S=n.samples,b=[],D=S.length,O=this._initPTS,C=this.nextAvcDts,x=8,P=Number.POSITIVE_INFINITY,F=Number.NEGATIVE_INFINITY,M=0,B=!1;p&&C!==null||(C=l*c-(S[0].pts-t(S[0].dts,S[0].pts)));for(var U=0;U<D;U++){var G=S[U];G.pts=t(G.pts-O,C),G.dts=t(G.dts-O,C),G.dts>G.pts&&(M=Math.max(Math.min(M,G.pts-G.dts),-18e3)),G.dts<S[U>0?U-1:U].dts&&(B=!0)}B&&S.sort(function(Et,It){var me=Et.dts-It.dts,ye=Et.pts-It.pts;return me||ye}),r=S[0].dts,i=S[S.length-1].dts;var K=Math.round((i-r)/(D-1));if(M<0){if(M<-2*K){k.logger.warn("PTS < DTS detected in video samples, offsetting DTS from PTS by "+Object(L.toMsFromMpegTsClock)(-K,!0)+" ms");for(var H=M,Y=0;Y<D;Y++)S[Y].dts=H=Math.max(H,S[Y].pts-K),S[Y].pts=Math.max(H,S[Y].pts)}else{k.logger.warn("PTS < DTS detected in video samples, shifting DTS by "+Object(L.toMsFromMpegTsClock)(M,!0)+" ms to overcome this issue");for(var W=0;W<D;W++)S[W].dts=S[W].dts+M}r=S[0].dts}if(p){var q=r-C,Q=q>K;if(Q||q<-1){Q?k.logger.warn("AVC: "+Object(L.toMsFromMpegTsClock)(q,!0)+" ms ("+q+"dts) hole between fragments detected, filling it"):k.logger.warn("AVC: "+Object(L.toMsFromMpegTsClock)(-q,!0)+" ms ("+q+"dts) overlapping between fragments detected"),r=C;var tt=S[0].pts-q;S[0].dts=r,S[0].pts=tt,k.logger.log("Video: First PTS/DTS adjusted: "+Object(L.toMsFromMpegTsClock)(tt,!0)+"/"+Object(L.toMsFromMpegTsClock)(r,!0)+", delta: "+Object(L.toMsFromMpegTsClock)(q,!0)+" ms")}}y&&(r=Math.max(0,r));for(var rt=0,z=0,at=0;at<D;at++){for(var X=S[at],J=X.units,nt=J.length,it=0,dt=0;dt<nt;dt++)it+=J[dt].data.length;z+=it,rt+=nt,X.length=it,X.dts=Math.max(X.dts,r),X.pts=Math.max(X.pts,X.dts,0),P=Math.min(X.pts,P),F=Math.max(X.pts,F)}i=S[D-1].dts;var gt,vt=z+4*rt+8;try{gt=new Uint8Array(vt)}catch{return void this.observer.emit(R.Events.ERROR,R.Events.ERROR,{type:I.ErrorTypes.MUX_ERROR,details:I.ErrorDetails.REMUX_ALLOC_ERROR,fatal:!1,bytes:vt,reason:"fail allocating video mdat "+vt})}var mt=new DataView(gt.buffer);mt.setUint32(0,vt),gt.set(A.default.types.mdat,4);for(var lt=0;lt<D;lt++){for(var ot=S[lt],Mt=ot.units,yt=0,Lt=0,Nt=Mt.length;Lt<Nt;Lt++){var kt=Mt[Lt],St=kt.data,Kt=kt.data.byteLength;mt.setUint32(x,Kt),x+=4,gt.set(St,x),x+=Kt,yt+=4+Kt}if(lt<D-1)v=S[lt+1].dts-ot.dts;else{var Bt=this.config,bt=ot.dts-S[lt>0?lt-1:lt].dts;if(Bt.stretchShortVideoTrack&&this.nextAudioPts!==null){var Vt=Math.floor(Bt.maxBufferHole*c),Ut=(g?P+g*c:this.nextAudioPts)-ot.pts;Ut>Vt?((v=Ut-bt)<0&&(v=bt),k.logger.log("[mp4-remuxer]: It is approximately "+Ut/90+" ms to the next segment; using duration "+v/90+" ms for the last video frame.")):v=bt}else v=bt}var Rt=Math.round(ot.pts-ot.dts);b.push(new a(ot.key,v,yt,Rt))}if(b.length&&h&&h<70){var At=b[0].flags;At.dependsOn=2,At.isNonSync=0}console.assert(v!==void 0,"mp4SampleDuration must be computed"),this.nextAvcDts=C=i+v,this.isVideoContiguous=!0;var _t={data1:A.default.moof(n.sequenceNumber++,r,m({},n,{samples:b})),data2:gt,startPTS:P/c,endPTS:(F+v)/c,startDTS:r/c,endDTS:C/c,type:"video",hasAudio:!1,hasVideo:!0,nb:b.length,dropped:n.dropped};return n.samples=[],n.dropped=0,console.assert(gt.length,"MDAT length must not be zero"),_t},u.remuxAudio=function(n,l,p,g,v){var r=n.inputTimeScale,i=r/(n.samplerate?n.samplerate:r),c=n.isAAC?1024:1152,S=c*i,b=this._initPTS,D=!n.isAAC&&this.typeSupported.mpeg,O=[],C=n.samples,x=D?0:8,P=this.nextAudioPts||-1,F=l*r;if(this.isAudioContiguous=p=p||C.length&&P>0&&(g&&Math.abs(F-P)<9e3||Math.abs(t(C[0].pts-b,F)-P)<20*S),C.forEach(function(St){St.pts=t(St.pts-b,F)}),!p||P<0){if(!(C=C.filter(function(St){return St.pts>=0})).length)return;P=v===0?0:g?Math.max(0,F):C[0].pts}if(n.isAAC)for(var M=v!==void 0,B=this.config.maxAudioFramesDrift,U=0,G=P;U<C.length;U++){var K=C[U],H=K.pts,Y=H-G,W=Math.abs(1e3*Y/r);if(Y<=-B*S&&M)U===0&&(k.logger.warn("Audio frame @ "+(H/r).toFixed(3)+"s overlaps nextAudioPts by "+Math.round(1e3*Y/r)+" ms."),this.nextAudioPts=P=G=H);else if(Y>=B*S&&W<1e4&&M){var q=Math.round(Y/S);(G=H-q*S)<0&&(q--,G+=S),U===0&&(this.nextAudioPts=P=G),k.logger.warn("[mp4-remuxer]: Injecting "+q+" audio frame @ "+(G/r).toFixed(3)+"s due to "+Math.round(1e3*Y/r)+" ms gap.");for(var Q=0;Q<q;Q++){var tt=Math.max(G,0),rt=T.default.getSilentFrame(n.manifestCodec||n.codec,n.channelCount);rt||(k.logger.log("[mp4-remuxer]: Unable to get silent frame for given audio codec; duplicating last frame instead."),rt=K.unit.subarray()),C.splice(U,0,{unit:rt,pts:tt}),G+=S,U++}}K.pts=G,G+=S}for(var z,at=null,X=null,J=0,nt=C.length;nt--;)J+=C[nt].unit.byteLength;for(var it=0,dt=C.length;it<dt;it++){var gt=C[it],vt=gt.unit,mt=gt.pts;if(X!==null)O[it-1].duration=Math.round((mt-X)/i);else{if(p&&n.isAAC&&(mt=P),at=mt,!(J>0))return;J+=x;try{z=new Uint8Array(J)}catch{return void this.observer.emit(R.Events.ERROR,R.Events.ERROR,{type:I.ErrorTypes.MUX_ERROR,details:I.ErrorDetails.REMUX_ALLOC_ERROR,fatal:!1,bytes:J,reason:"fail allocating audio mdat "+J})}D||(new DataView(z.buffer).setUint32(0,J),z.set(A.default.types.mdat,4))}z.set(vt,x);var lt=vt.byteLength;x+=lt,O.push(new a(!0,c,lt,0)),X=mt}var ot=O.length;if(ot){var Mt=O[O.length-1];this.nextAudioPts=P=X+i*Mt.duration;var yt=D?new Uint8Array(0):A.default.moof(n.sequenceNumber++,at/i,m({},n,{samples:O}));n.samples=[];var Lt=at/r,Nt=P/r,kt={data1:yt,data2:z,startPTS:Lt,endPTS:Nt,startDTS:Lt,endDTS:Nt,type:"audio",hasAudio:!0,hasVideo:!1,nb:ot};return this.isAudioContiguous=!0,console.assert(z.length,"MDAT length must not be zero"),kt}},u.remuxEmptyAudio=function(n,l,p,g){var v=n.inputTimeScale,r=v/(n.samplerate?n.samplerate:v),i=this.nextAudioPts,c=(i!==null?i:g.startDTS*v)+this._initDTS,S=g.endDTS*v+this._initDTS,b=1024*r,D=Math.ceil((S-c)/b),O=T.default.getSilentFrame(n.manifestCodec||n.codec,n.channelCount);if(k.logger.warn("[mp4-remuxer]: remux empty Audio"),O){for(var C=[],x=0;x<D;x++){var P=c+x*b;C.push({unit:O,pts:P,dts:P})}return n.samples=C,this.remuxAudio(n,l,p,!1)}k.logger.trace("[mp4-remuxer]: Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec")},u.remuxID3=function(n,l){var p=n.samples.length;if(p){for(var g=n.inputTimeScale,v=this._initPTS,r=this._initDTS,i=0;i<p;i++){var c=n.samples[i];c.pts=t(c.pts-v,l*g)/g,c.dts=t(c.dts-r,l*g)/g}var S=n.samples;return n.samples=[],{samples:S}}},u.remuxText=function(n,l){var p=n.samples.length;if(p){for(var g=n.inputTimeScale,v=this._initPTS,r=0;r<p;r++){var i=n.samples[r];i.pts=t(i.pts-v,l*g)/g}n.samples.sort(function(S,b){return S.pts-b.pts});var c=n.samples;return n.samples=[],{samples:c}}},s}();function t(s,u){var n;if(u===null)return s;for(n=u<s?-8589934592:8589934592;Math.abs(s-u)>4294967296;)s+=n;return s}var a=function(s,u,n,l){this.size=void 0,this.duration=void 0,this.cts=void 0,this.flags=void 0,this.duration=u,this.size=n,this.cts=l,this.flags=new e(s)},e=function(s){this.isLeading=0,this.isDependedOn=0,this.hasRedundancy=0,this.degradPrio=0,this.dependsOn=1,this.isNonSync=1,this.dependsOn=s?2:1,this.isNonSync=s?0:1}},"./src/remux/passthrough-remuxer.ts":function(N,w,f){f.r(w);var _=f("./src/polyfills/number.ts"),T=f("./src/utils/mp4-tools.ts"),A=f("./src/loader/fragment.ts"),R=f("./src/utils/logger.ts"),I=function(){function L(){this.emitInitSegment=!1,this.audioCodec=void 0,this.videoCodec=void 0,this.initData=void 0,this.initPTS=void 0,this.initTracks=void 0,this.lastEndDTS=null}var m=L.prototype;return m.destroy=function(){},m.resetTimeStamp=function(h){this.initPTS=h,this.lastEndDTS=null},m.resetNextTimestamp=function(){this.lastEndDTS=null},m.resetInitSegment=function(h,E,y){this.audioCodec=E,this.videoCodec=y,this.generateInitSegment(h),this.emitInitSegment=!0},m.generateInitSegment=function(h){var E=this.audioCodec,y=this.videoCodec;if(!h||!h.byteLength)return this.initTracks=void 0,void(this.initData=void 0);var d=this.initData=Object(T.parseInitSegment)(h);E||(E=o(d.audio,A.ElementaryStreamTypes.AUDIO)),y||(y=o(d.video,A.ElementaryStreamTypes.VIDEO));var t={};d.audio&&d.video?t.audiovideo={container:"video/mp4",codec:E+","+y,initSegment:h,id:"main"}:d.audio?t.audio={container:"audio/mp4",codec:E,initSegment:h,id:"audio"}:d.video?t.video={container:"video/mp4",codec:y,initSegment:h,id:"main"}:R.logger.warn("[passthrough-remuxer.ts]: initSegment does not contain moov or trak boxes."),this.initTracks=t},m.remux=function(h,E,y,d,t){var a=this.initPTS,e=this.lastEndDTS,s={audio:void 0,video:void 0,text:d,id3:y,initSegment:void 0};Object(_.isFiniteNumber)(e)||(e=this.lastEndDTS=t||0);var u=E.samples;if(!u||!u.length)return s;var n={initPTS:void 0,timescale:1},l=this.initData;if(l&&l.length||(this.generateInitSegment(u),l=this.initData),!l||!l.length)return R.logger.warn("[passthrough-remuxer.ts]: Failed to generate initSegment."),s;this.emitInitSegment&&(n.tracks=this.initTracks,this.emitInitSegment=!1),Object(_.isFiniteNumber)(a)||(this.initPTS=n.initPTS=a=k(l,u,e));var p=Object(T.getDuration)(u,l),g=e,v=p+g;Object(T.offsetStartDTS)(l,u,a),p>0?this.lastEndDTS=v:(R.logger.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());var r=!!l.audio,i=!!l.video,c="";r&&(c+="audio"),i&&(c+="video");var S={data1:u,startPTS:g,startDTS:g,endPTS:v,endDTS:v,type:c,hasAudio:r,hasVideo:i,nb:1,dropped:0};return s.audio=S.type==="audio"?S:void 0,s.video=S.type!=="audio"?S:void 0,s.text=d,s.id3=y,s.initSegment=n,s},L}(),k=function(L,m,h){return Object(T.getStartDTS)(L,m)-h};function o(L,m){var h=L==null?void 0:L.codec;return h&&h.length>4?h:h==="hvc1"?"hvc1.1.c.L120.90":h==="av01"?"av01.0.04M.08":h==="avc1"||m===A.ElementaryStreamTypes.VIDEO?"avc1.42e01e":"mp4a.40.5"}w.default=I},"./src/task-loop.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return _});var _=function(){function T(){this._boundTick=void 0,this._tickTimer=null,this._tickInterval=null,this._tickCallCount=0,this._boundTick=this.tick.bind(this)}var A=T.prototype;return A.destroy=function(){this.onHandlerDestroying(),this.onHandlerDestroyed()},A.onHandlerDestroying=function(){this.clearNextTick(),this.clearInterval()},A.onHandlerDestroyed=function(){},A.hasInterval=function(){return!!this._tickInterval},A.hasNextTick=function(){return!!this._tickTimer},A.setInterval=function(R){return!this._tickInterval&&(this._tickInterval=self.setInterval(this._boundTick,R),!0)},A.clearInterval=function(){return!!this._tickInterval&&(self.clearInterval(this._tickInterval),this._tickInterval=null,!0)},A.clearNextTick=function(){return!!this._tickTimer&&(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0)},A.tick=function(){this._tickCallCount++,this._tickCallCount===1&&(this.doTick(),this._tickCallCount>1&&this.tickImmediate(),this._tickCallCount=0)},A.tickImmediate=function(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)},A.doTick=function(){},T}()},"./src/types/level.ts":function(N,w,f){function _(o,L){for(var m=0;m<L.length;m++){var h=L[m];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(o,h.key,h)}}var T,A;function R(o,L){var m=o.canSkipUntil,h=o.canSkipDateRanges,E=o.endSN;return m&&(L!==void 0?L-E:0)<m?h?T.v2:T.Yes:T.No}f.r(w),f.d(w,"HlsSkip",function(){return T}),f.d(w,"getSkipValue",function(){return R}),f.d(w,"HlsUrlParameters",function(){return I}),f.d(w,"Level",function(){return k}),(A=T||(T={})).No="",A.Yes="YES",A.v2="v2";var I=function(){function o(L,m,h){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=L,this.part=m,this.skip=h}return o.prototype.addDirectives=function(L){var m=new self.URL(L);return this.msn!==void 0&&m.searchParams.set("_HLS_msn",this.msn.toString()),this.part!==void 0&&m.searchParams.set("_HLS_part",this.part.toString()),this.skip&&m.searchParams.set("_HLS_skip",this.skip),m.toString()},o}(),k=function(){function o(h){this.attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.videoCodec=void 0,this.width=void 0,this.unknownCodecs=void 0,this.audioGroupIds=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.textGroupIds=void 0,this.url=void 0,this._urlId=0,this.url=[h.url],this.attrs=h.attrs,this.bitrate=h.bitrate,h.details&&(this.details=h.details),this.id=h.id||0,this.name=h.name,this.width=h.width||0,this.height=h.height||0,this.audioCodec=h.audioCodec,this.videoCodec=h.videoCodec,this.unknownCodecs=h.unknownCodecs,this.codecSet=[h.videoCodec,h.audioCodec].filter(function(E){return E}).join(",").replace(/\.[^.,]+/g,"")}var L,m;return L=o,(m=[{key:"maxBitrate",get:function(){return Math.max(this.realBitrate,this.bitrate)}},{key:"uri",get:function(){return this.url[this._urlId]||""}},{key:"urlId",get:function(){return this._urlId},set:function(h){var E=h%this.url.length;this._urlId!==E&&(this.details=void 0,this._urlId=E)}}])&&_(L.prototype,m),o}()},"./src/types/loader.ts":function(N,w,f){var _,T,A,R;f.r(w),f.d(w,"PlaylistContextType",function(){return _}),f.d(w,"PlaylistLevelType",function(){return A}),(T=_||(_={})).MANIFEST="manifest",T.LEVEL="level",T.AUDIO_TRACK="audioTrack",T.SUBTITLE_TRACK="subtitleTrack",(R=A||(A={})).MAIN="main",R.AUDIO="audio",R.SUBTITLE="subtitle"},"./src/types/transmuxer.ts":function(N,w,f){f.r(w),f.d(w,"ChunkMetadata",function(){return _});var _=function(T,A,R,I,k,o){I===void 0&&(I=0),k===void 0&&(k=-1),o===void 0&&(o=!1),this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing={start:0,executeStart:0,executeEnd:0,end:0},this.buffering={audio:{start:0,executeStart:0,executeEnd:0,end:0},video:{start:0,executeStart:0,executeEnd:0,end:0},audiovideo:{start:0,executeStart:0,executeEnd:0,end:0}},this.level=T,this.sn=A,this.id=R,this.size=I,this.part=k,this.partial=o}},"./src/utils/attr-list.ts":function(N,w,f){f.r(w),f.d(w,"AttrList",function(){return A});var _=/^(\d+)x(\d+)$/,T=/\s*(.+?)\s*=((?:\".*?\")|.*?)(?:,|$)/g,A=function(){function R(k){for(var o in typeof k=="string"&&(k=R.parseAttrList(k)),k)k.hasOwnProperty(o)&&(this[o]=k[o])}var I=R.prototype;return I.decimalInteger=function(k){var o=parseInt(this[k],10);return o>Number.MAX_SAFE_INTEGER?1/0:o},I.hexadecimalInteger=function(k){if(this[k]){var o=(this[k]||"0x").slice(2);o=(1&o.length?"0":"")+o;for(var L=new Uint8Array(o.length/2),m=0;m<o.length/2;m++)L[m]=parseInt(o.slice(2*m,2*m+2),16);return L}return null},I.hexadecimalIntegerAsNumber=function(k){var o=parseInt(this[k],16);return o>Number.MAX_SAFE_INTEGER?1/0:o},I.decimalFloatingPoint=function(k){return parseFloat(this[k])},I.optionalFloat=function(k,o){var L=this[k];return L?parseFloat(L):o},I.enumeratedString=function(k){return this[k]},I.bool=function(k){return this[k]==="YES"},I.decimalResolution=function(k){var o=_.exec(this[k]);if(o!==null)return{width:parseInt(o[1],10),height:parseInt(o[2],10)}},R.parseAttrList=function(k){var o,L={};for(T.lastIndex=0;(o=T.exec(k))!==null;){var m=o[2];m.indexOf('"')===0&&m.lastIndexOf('"')===m.length-1&&(m=m.slice(1,-1)),L[o[1]]=m}return L},R}()},"./src/utils/binary-search.ts":function(N,w,f){f.r(w),w.default={search:function(_,T){for(var A=0,R=_.length-1,I=null,k=null;A<=R;){var o=T(k=_[I=(A+R)/2|0]);if(o>0)A=I+1;else{if(!(o<0))return k;R=I-1}}return null}}},"./src/utils/buffer-helper.ts":function(N,w,f){f.r(w),f.d(w,"BufferHelper",function(){return A});var _=f("./src/utils/logger.ts"),T={length:0,start:function(){return 0},end:function(){return 0}},A=function(){function R(){}return R.isBuffered=function(I,k){try{if(I){for(var o=R.getBuffered(I),L=0;L<o.length;L++)if(k>=o.start(L)&&k<=o.end(L))return!0}}catch{}return!1},R.bufferInfo=function(I,k,o){try{if(I){var L,m=R.getBuffered(I),h=[];for(L=0;L<m.length;L++)h.push({start:m.start(L),end:m.end(L)});return this.bufferedInfo(h,k,o)}}catch{}return{len:0,start:k,end:k,nextStart:void 0}},R.bufferedInfo=function(I,k,o){k=Math.max(0,k),I.sort(function(n,l){var p=n.start-l.start;return p||l.end-n.end});var L=[];if(o)for(var m=0;m<I.length;m++){var h=L.length;if(h){var E=L[h-1].end;I[m].start-E<o?I[m].end>E&&(L[h-1].end=I[m].end):L.push(I[m])}else L.push(I[m])}else L=I;for(var y,d=0,t=k,a=k,e=0;e<L.length;e++){var s=L[e].start,u=L[e].end;if(k+o>=s&&k<u)t=s,d=(a=u)-k;else if(k+o<s){y=s;break}}return{len:d,start:t||0,end:a||0,nextStart:y}},R.getBuffered=function(I){try{return I.buffered}catch(k){return _.logger.log("failed to get media.buffered",k),T}},R}()},"./src/utils/cea-608-parser.ts":function(N,w,f){f.r(w),f.d(w,"Row",function(){return s}),f.d(w,"CaptionScreen",function(){return u});var _,T,A=f("./src/utils/logger.ts"),R={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},I=function(v){var r=v;return R.hasOwnProperty(v)&&(r=R[v]),String.fromCharCode(r)},k=15,o=100,L={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},m={17:2,18:4,21:6,22:8,23:10,19:13,20:15},h={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},E={25:2,26:4,29:6,30:8,31:10,27:13,28:15},y=["white","green","blue","cyan","red","yellow","magenta","black","transparent"];(T=_||(_={}))[T.ERROR=0]="ERROR",T[T.TEXT=1]="TEXT",T[T.WARNING=2]="WARNING",T[T.INFO=2]="INFO",T[T.DEBUG=3]="DEBUG",T[T.DATA=3]="DATA";var d=function(){function v(){this.time=null,this.verboseLevel=_.ERROR}return v.prototype.log=function(r,i){this.verboseLevel>=r&&A.logger.log(this.time+" ["+r+"] "+i)},v}(),t=function(v){for(var r=[],i=0;i<v.length;i++)r.push(v[i].toString(16));return r},a=function(){function v(i,c,S,b,D){this.foreground=void 0,this.underline=void 0,this.italics=void 0,this.background=void 0,this.flash=void 0,this.foreground=i||"white",this.underline=c||!1,this.italics=S||!1,this.background=b||"black",this.flash=D||!1}var r=v.prototype;return r.reset=function(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1},r.setStyles=function(i){for(var c=["foreground","underline","italics","background","flash"],S=0;S<c.length;S++){var b=c[S];i.hasOwnProperty(b)&&(this[b]=i[b])}},r.isDefault=function(){return this.foreground==="white"&&!this.underline&&!this.italics&&this.background==="black"&&!this.flash},r.equals=function(i){return this.foreground===i.foreground&&this.underline===i.underline&&this.italics===i.italics&&this.background===i.background&&this.flash===i.flash},r.copy=function(i){this.foreground=i.foreground,this.underline=i.underline,this.italics=i.italics,this.background=i.background,this.flash=i.flash},r.toString=function(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash},v}(),e=function(){function v(i,c,S,b,D,O){this.uchar=void 0,this.penState=void 0,this.uchar=i||" ",this.penState=new a(c,S,b,D,O)}var r=v.prototype;return r.reset=function(){this.uchar=" ",this.penState.reset()},r.setChar=function(i,c){this.uchar=i,this.penState.copy(c)},r.setPenState=function(i){this.penState.copy(i)},r.equals=function(i){return this.uchar===i.uchar&&this.penState.equals(i.penState)},r.copy=function(i){this.uchar=i.uchar,this.penState.copy(i.penState)},r.isEmpty=function(){return this.uchar===" "&&this.penState.isDefault()},v}(),s=function(){function v(i){this.chars=void 0,this.pos=void 0,this.currPenState=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chars=[];for(var c=0;c<o;c++)this.chars.push(new e);this.logger=i,this.pos=0,this.currPenState=new a}var r=v.prototype;return r.equals=function(i){for(var c=!0,S=0;S<o;S++)if(!this.chars[S].equals(i.chars[S])){c=!1;break}return c},r.copy=function(i){for(var c=0;c<o;c++)this.chars[c].copy(i.chars[c])},r.isEmpty=function(){for(var i=!0,c=0;c<o;c++)if(!this.chars[c].isEmpty()){i=!1;break}return i},r.setCursor=function(i){this.pos!==i&&(this.pos=i),this.pos<0?(this.logger.log(_.DEBUG,"Negative cursor position "+this.pos),this.pos=0):this.pos>o&&(this.logger.log(_.DEBUG,"Too large cursor position "+this.pos),this.pos=o)},r.moveCursor=function(i){var c=this.pos+i;if(i>1)for(var S=this.pos+1;S<c+1;S++)this.chars[S].setPenState(this.currPenState);this.setCursor(c)},r.backSpace=function(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)},r.insertChar=function(i){i>=144&&this.backSpace();var c=I(i);this.pos>=o?this.logger.log(_.ERROR,"Cannot insert "+i.toString(16)+" ("+c+") at position "+this.pos+". Skipping it!"):(this.chars[this.pos].setChar(c,this.currPenState),this.moveCursor(1))},r.clearFromPos=function(i){var c;for(c=i;c<o;c++)this.chars[c].reset()},r.clear=function(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()},r.clearToEndOfRow=function(){this.clearFromPos(this.pos)},r.getTextString=function(){for(var i=[],c=!0,S=0;S<o;S++){var b=this.chars[S].uchar;b!==" "&&(c=!1),i.push(b)}return c?"":i.join("")},r.setPenStyles=function(i){this.currPenState.setStyles(i),this.chars[this.pos].setPenState(this.currPenState)},v}(),u=function(){function v(i){this.rows=void 0,this.currRow=void 0,this.nrRollUpRows=void 0,this.lastOutputScreen=void 0,this.logger=void 0,this.rows=[];for(var c=0;c<k;c++)this.rows.push(new s(i));this.logger=i,this.currRow=14,this.nrRollUpRows=null,this.lastOutputScreen=null,this.reset()}var r=v.prototype;return r.reset=function(){for(var i=0;i<k;i++)this.rows[i].clear();this.currRow=14},r.equals=function(i){for(var c=!0,S=0;S<k;S++)if(!this.rows[S].equals(i.rows[S])){c=!1;break}return c},r.copy=function(i){for(var c=0;c<k;c++)this.rows[c].copy(i.rows[c])},r.isEmpty=function(){for(var i=!0,c=0;c<k;c++)if(!this.rows[c].isEmpty()){i=!1;break}return i},r.backSpace=function(){this.rows[this.currRow].backSpace()},r.clearToEndOfRow=function(){this.rows[this.currRow].clearToEndOfRow()},r.insertChar=function(i){this.rows[this.currRow].insertChar(i)},r.setPen=function(i){this.rows[this.currRow].setPenStyles(i)},r.moveCursor=function(i){this.rows[this.currRow].moveCursor(i)},r.setCursor=function(i){this.logger.log(_.INFO,"setCursor: "+i),this.rows[this.currRow].setCursor(i)},r.setPAC=function(i){this.logger.log(_.INFO,"pacData = "+JSON.stringify(i));var c=i.row-1;if(this.nrRollUpRows&&c<this.nrRollUpRows-1&&(c=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==c){for(var S=0;S<k;S++)this.rows[S].clear();var b=this.currRow+1-this.nrRollUpRows,D=this.lastOutputScreen;if(D){var O=D.rows[b].cueStartTime,C=this.logger.time;if(O&&C!==null&&O<C)for(var x=0;x<this.nrRollUpRows;x++)this.rows[c-this.nrRollUpRows+x+1].copy(D.rows[b+x])}}this.currRow=c;var P=this.rows[this.currRow];if(i.indent!==null){var F=i.indent,M=Math.max(F-1,0);P.setCursor(i.indent),i.color=P.chars[M].penState.foreground}var B={foreground:i.color,underline:i.underline,italics:i.italics,background:"black",flash:!1};this.setPen(B)},r.setBkgData=function(i){this.logger.log(_.INFO,"bkgData = "+JSON.stringify(i)),this.backSpace(),this.setPen(i),this.insertChar(32)},r.setRollUpRows=function(i){this.nrRollUpRows=i},r.rollUp=function(){if(this.nrRollUpRows!==null){this.logger.log(_.TEXT,this.getDisplayText());var i=this.currRow+1-this.nrRollUpRows,c=this.rows.splice(i,1)[0];c.clear(),this.rows.splice(this.currRow,0,c),this.logger.log(_.INFO,"Rolling up")}else this.logger.log(_.DEBUG,"roll_up but nrRollUpRows not set yet")},r.getDisplayText=function(i){i=i||!1;for(var c=[],S="",b=-1,D=0;D<k;D++){var O=this.rows[D].getTextString();O&&(b=D+1,i?c.push("Row "+b+": '"+O+"'"):c.push(O.trim()))}return c.length>0&&(S=i?"["+c.join(" | ")+"]":c.join(`
`)),S},r.getTextAndFormat=function(){return this.rows},v}(),n=function(){function v(i,c,S){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chNr=i,this.outputFilter=c,this.mode=null,this.verbose=0,this.displayedMemory=new u(S),this.nonDisplayedMemory=new u(S),this.lastOutputScreen=new u(S),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.logger=S}var r=v.prototype;return r.reset=function(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.outputFilter.reset(),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null},r.getHandler=function(){return this.outputFilter},r.setHandler=function(i){this.outputFilter=i},r.setPAC=function(i){this.writeScreen.setPAC(i)},r.setBkgData=function(i){this.writeScreen.setBkgData(i)},r.setMode=function(i){i!==this.mode&&(this.mode=i,this.logger.log(_.INFO,"MODE="+i),this.mode==="MODE_POP-ON"?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),this.mode!=="MODE_ROLL-UP"&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=i)},r.insertChars=function(i){for(var c=0;c<i.length;c++)this.writeScreen.insertChar(i[c]);var S=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";this.logger.log(_.INFO,S+": "+this.writeScreen.getDisplayText(!0)),this.mode!=="MODE_PAINT-ON"&&this.mode!=="MODE_ROLL-UP"||(this.logger.log(_.TEXT,"DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())},r.ccRCL=function(){this.logger.log(_.INFO,"RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")},r.ccBS=function(){this.logger.log(_.INFO,"BS - BackSpace"),this.mode!=="MODE_TEXT"&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())},r.ccAOF=function(){},r.ccAON=function(){},r.ccDER=function(){this.logger.log(_.INFO,"DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()},r.ccRU=function(i){this.logger.log(_.INFO,"RU("+i+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(i)},r.ccFON=function(){this.logger.log(_.INFO,"FON - Flash On"),this.writeScreen.setPen({flash:!0})},r.ccRDC=function(){this.logger.log(_.INFO,"RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")},r.ccTR=function(){this.logger.log(_.INFO,"TR"),this.setMode("MODE_TEXT")},r.ccRTD=function(){this.logger.log(_.INFO,"RTD"),this.setMode("MODE_TEXT")},r.ccEDM=function(){this.logger.log(_.INFO,"EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)},r.ccCR=function(){this.logger.log(_.INFO,"CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)},r.ccENM=function(){this.logger.log(_.INFO,"ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()},r.ccEOC=function(){if(this.logger.log(_.INFO,"EOC - End Of Caption"),this.mode==="MODE_POP-ON"){var i=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=i,this.writeScreen=this.nonDisplayedMemory,this.logger.log(_.TEXT,"DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate(!0)},r.ccTO=function(i){this.logger.log(_.INFO,"TO("+i+") - Tab Offset"),this.writeScreen.moveCursor(i)},r.ccMIDROW=function(i){var c={flash:!1};if(c.underline=i%2==1,c.italics=i>=46,c.italics)c.foreground="white";else{var S=Math.floor(i/2)-16;c.foreground=["white","green","blue","cyan","red","yellow","magenta"][S]}this.logger.log(_.INFO,"MIDROW: "+JSON.stringify(c)),this.writeScreen.setPen(c)},r.outputDataUpdate=function(i){i===void 0&&(i=!1);var c=this.logger.time;c!==null&&this.outputFilter&&(this.cueStartTime!==null||this.displayedMemory.isEmpty()?this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,c,this.lastOutputScreen),i&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:c):this.cueStartTime=c,this.lastOutputScreen.copy(this.displayedMemory))},r.cueSplitAtTime=function(i){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,i,this.displayedMemory),this.cueStartTime=i))},v}(),l=function(){function v(i,c,S){this.channels=void 0,this.currentChannel=0,this.cmdHistory=void 0,this.logger=void 0;var b=new d;this.channels=[null,new n(i,c,b),new n(i+1,S,b)],this.cmdHistory={a:null,b:null},this.logger=b}var r=v.prototype;return r.getHandler=function(i){return this.channels[i].getHandler()},r.setHandler=function(i,c){this.channels[i].setHandler(c)},r.addData=function(i,c){var S,b,D,O=!1;this.logger.time=i;for(var C=0;C<c.length;C+=2)if(b=127&c[C],D=127&c[C+1],b!==0||D!==0){if(this.logger.log(_.DATA,"["+t([c[C],c[C+1]])+"] -> ("+t([b,D])+")"),(S=this.parseCmd(b,D))||(S=this.parseMidrow(b,D)),S||(S=this.parsePAC(b,D)),S||(S=this.parseBackgroundAttributes(b,D)),!S&&(O=this.parseChars(b,D))){var x=this.currentChannel;x&&x>0?this.channels[x].insertChars(O):this.logger.log(_.WARNING,"No channel found yet. TEXT-MODE?")}S||O||this.logger.log(_.WARNING,"Couldn't parse cleaned data "+t([b,D])+" orig: "+t([c[C],c[C+1]]))}},r.parseCmd=function(i,c){var S=this.cmdHistory;if(!((i===20||i===28||i===21||i===29)&&c>=32&&c<=47||(i===23||i===31)&&c>=33&&c<=35))return!1;if(g(i,c,S))return p(null,null,S),this.logger.log(_.DEBUG,"Repeated command ("+t([i,c])+") is dropped"),!0;var b=i===20||i===21||i===23?1:2,D=this.channels[b];return i===20||i===21||i===28||i===29?c===32?D.ccRCL():c===33?D.ccBS():c===34?D.ccAOF():c===35?D.ccAON():c===36?D.ccDER():c===37?D.ccRU(2):c===38?D.ccRU(3):c===39?D.ccRU(4):c===40?D.ccFON():c===41?D.ccRDC():c===42?D.ccTR():c===43?D.ccRTD():c===44?D.ccEDM():c===45?D.ccCR():c===46?D.ccENM():c===47&&D.ccEOC():D.ccTO(c-32),p(i,c,S),this.currentChannel=b,!0},r.parseMidrow=function(i,c){var S=0;if((i===17||i===25)&&c>=32&&c<=47){if((S=i===17?1:2)!==this.currentChannel)return this.logger.log(_.ERROR,"Mismatch channel in midrow parsing"),!1;var b=this.channels[S];return!!b&&(b.ccMIDROW(c),this.logger.log(_.DEBUG,"MIDROW ("+t([i,c])+")"),!0)}return!1},r.parsePAC=function(i,c){var S,b=this.cmdHistory;if(!((i>=17&&i<=23||i>=25&&i<=31)&&c>=64&&c<=127||(i===16||i===24)&&c>=64&&c<=95))return!1;if(g(i,c,b))return p(null,null,b),!0;var D=i<=23?1:2;S=c>=64&&c<=95?D===1?L[i]:h[i]:D===1?m[i]:E[i];var O=this.channels[D];return!!O&&(O.setPAC(this.interpretPAC(S,c)),p(i,c,b),this.currentChannel=D,!0)},r.interpretPAC=function(i,c){var S,b={color:null,italics:!1,indent:null,underline:!1,row:i};return S=c>95?c-96:c-64,b.underline=(1&S)==1,S<=13?b.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(S/2)]:S<=15?(b.italics=!0,b.color="white"):b.indent=4*Math.floor((S-16)/2),b},r.parseChars=function(i,c){var S,b,D=null,O=null;if(i>=25?(S=2,O=i-8):(S=1,O=i),O>=17&&O<=19?(b=O===17?c+80:O===18?c+112:c+144,this.logger.log(_.INFO,"Special char '"+I(b)+"' in channel "+S),D=[b]):i>=32&&i<=127&&(D=c===0?[i]:[i,c]),D){var C=t(D);this.logger.log(_.DEBUG,"Char codes =  "+C.join(",")),p(i,c,this.cmdHistory)}return D},r.parseBackgroundAttributes=function(i,c){var S;if(!((i===16||i===24)&&c>=32&&c<=47||(i===23||i===31)&&c>=45&&c<=47))return!1;var b={};i===16||i===24?(S=Math.floor((c-32)/2),b.background=y[S],c%2==1&&(b.background=b.background+"_semi")):c===45?b.background="transparent":(b.foreground="black",c===47&&(b.underline=!0));var D=i<=23?1:2;return this.channels[D].setBkgData(b),p(i,c,this.cmdHistory),!0},r.reset=function(){for(var i=0;i<Object.keys(this.channels).length;i++){var c=this.channels[i];c&&c.reset()}this.cmdHistory={a:null,b:null}},r.cueSplitAtTime=function(i){for(var c=0;c<this.channels.length;c++){var S=this.channels[c];S&&S.cueSplitAtTime(i)}},v}();function p(v,r,i){i.a=v,i.b=r}function g(v,r,i){return i.a===v&&i.b===r}w.default=l},"./src/utils/codecs.ts":function(N,w,f){f.r(w),f.d(w,"isCodecType",function(){return T}),f.d(w,"isCodecSupportedInMp4",function(){return A});var _={audio:{a3ds:!0,"ac-3":!0,"ac-4":!0,alac:!0,alaw:!0,dra1:!0,"dts+":!0,"dts-":!0,dtsc:!0,dtse:!0,dtsh:!0,"ec-3":!0,enca:!0,g719:!0,g726:!0,m4ae:!0,mha1:!0,mha2:!0,mhm1:!0,mhm2:!0,mlpa:!0,mp4a:!0,"raw ":!0,Opus:!0,samr:!0,sawb:!0,sawp:!0,sevc:!0,sqcp:!0,ssmv:!0,twos:!0,ulaw:!0},video:{avc1:!0,avc2:!0,avc3:!0,avc4:!0,avcp:!0,av01:!0,drac:!0,dvav:!0,dvhe:!0,encv:!0,hev1:!0,hvc1:!0,mjp2:!0,mp4v:!0,mvc1:!0,mvc2:!0,mvc3:!0,mvc4:!0,resv:!0,rv60:!0,s263:!0,svc1:!0,svc2:!0,"vc-1":!0,vp08:!0,vp09:!0},text:{stpp:!0,wvtt:!0}};function T(R,I){var k=_[I];return!!k&&k[R.slice(0,4)]===!0}function A(R,I){return MediaSource.isTypeSupported((I||"video")+'/mp4;codecs="'+R+'"')}},"./src/utils/cues.ts":function(N,w,f){f.r(w);var _=f("./src/utils/vttparser.ts"),T=f("./src/utils/webvtt-parser.ts"),A=f("./src/utils/texttrack-utils.ts"),R=/\s/,I={newCue:function(k,o,L,m){for(var h,E,y,d,t,a=[],e=self.VTTCue||self.TextTrackCue,s=0;s<m.rows.length;s++)if(y=!0,d=0,t="",!(h=m.rows[s]).isEmpty()){for(var u=0;u<h.chars.length;u++)R.test(h.chars[u].uchar)&&y?d++:(t+=h.chars[u].uchar,y=!1);h.cueStartTime=o,o===L&&(L+=1e-4),d>=16?d--:d++;var n=Object(_.fixLineBreaks)(t.trim()),l=Object(T.generateCueId)(o,L,n);k&&k.cues&&k.cues.getCueById(l)||((E=new e(o,L,n)).id=l,E.line=s+1,E.align="left",E.position=10+Math.min(80,10*Math.floor(8*d/32)),a.push(E))}return k&&a.length&&(a.sort(function(p,g){return p.line==="auto"||g.line==="auto"?0:p.line>8&&g.line>8?g.line-p.line:p.line-g.line}),a.forEach(function(p){return Object(A.addCueToTrack)(k,p)})),a}};w.default=I},"./src/utils/discontinuities.ts":function(N,w,f){f.r(w),f.d(w,"findFirstFragWithCC",function(){return R}),f.d(w,"shouldAlignOnDiscontinuities",function(){return I}),f.d(w,"findDiscontinuousReferenceFrag",function(){return k}),f.d(w,"adjustSlidingStart",function(){return L}),f.d(w,"alignStream",function(){return m}),f.d(w,"alignPDT",function(){return h});var _=f("./src/polyfills/number.ts"),T=f("./src/utils/logger.ts"),A=f("./src/controller/level-helper.ts");function R(E,y){for(var d=null,t=0,a=E.length;t<a;t++){var e=E[t];if(e&&e.cc===y){d=e;break}}return d}function I(E,y,d){return!(!y.details||!(d.endCC>d.startCC||E&&E.cc<d.startCC))}function k(E,y){var d=E.fragments,t=y.fragments;if(t.length&&d.length){var a=R(d,t[0].cc);if(a&&(!a||a.startPTS))return a;T.logger.log("No frag in previous level to align on")}else T.logger.log("No fragments to align")}function o(E,y){if(E){var d=E.start+y;E.start=E.startPTS=d,E.endPTS=d+E.duration}}function L(E,y){for(var d=y.fragments,t=0,a=d.length;t<a;t++)o(d[t],E);y.fragmentHint&&o(y.fragmentHint,E),y.alignedSliding=!0}function m(E,y,d){y&&(function(t,a,e){if(I(t,e,a)){var s=k(e.details,a);s&&Object(_.isFiniteNumber)(s.start)&&(T.logger.log("Adjusting PTS using last level due to CC increase within current level "+a.url),L(s.start,a))}}(E,d,y),!d.alignedSliding&&y.details&&h(d,y.details),d.alignedSliding||!y.details||d.skippedSegments||Object(A.adjustSliding)(y.details,d))}function h(E,y){if(y.fragments.length&&E.hasProgramDateTime&&y.hasProgramDateTime){var d=y.fragments[0].programDateTime,t=E.fragments[0].programDateTime,a=(t-d)/1e3+y.fragments[0].start;a&&Object(_.isFiniteNumber)(a)&&(T.logger.log("Adjusting PTS using programDateTime delta "+(t-d)+"ms, sliding:"+a.toFixed(3)+" "+E.url+" "),L(a,E))}}},"./src/utils/ewma-bandwidth-estimator.ts":function(N,w,f){f.r(w);var _=f("./src/utils/ewma.ts"),T=function(){function A(I,k,o){this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultEstimate_=o,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new _.default(I),this.fast_=new _.default(k)}var R=A.prototype;return R.update=function(I,k){var o=this.slow_,L=this.fast_;this.slow_.halfLife!==I&&(this.slow_=new _.default(I,o.getEstimate(),o.getTotalWeight())),this.fast_.halfLife!==k&&(this.fast_=new _.default(k,L.getEstimate(),L.getTotalWeight()))},R.sample=function(I,k){var o=(I=Math.max(I,this.minDelayMs_))/1e3,L=8*k/o;this.fast_.sample(o,L),this.slow_.sample(o,L)},R.canEstimate=function(){var I=this.fast_;return I&&I.getTotalWeight()>=this.minWeight_},R.getEstimate=function(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_},R.destroy=function(){},A}();w.default=T},"./src/utils/ewma.ts":function(N,w,f){f.r(w);var _=function(){function T(R,I,k){I===void 0&&(I=0),k===void 0&&(k=0),this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=R,this.alpha_=R?Math.exp(Math.log(.5)/R):0,this.estimate_=I,this.totalWeight_=k}var A=T.prototype;return A.sample=function(R,I){var k=Math.pow(this.alpha_,R);this.estimate_=I*(1-k)+k*this.estimate_,this.totalWeight_+=R},A.getTotalWeight=function(){return this.totalWeight_},A.getEstimate=function(){if(this.alpha_){var R=1-Math.pow(this.alpha_,this.totalWeight_);if(R)return this.estimate_/R}return this.estimate_},T}();w.default=_},"./src/utils/fetch-loader.ts":function(N,w,f){f.r(w),f.d(w,"fetchSupported",function(){return m});var _=f("./src/polyfills/number.ts"),T=f("./src/loader/load-stats.ts"),A=f("./src/demux/chunk-cache.ts");function R(d){var t=typeof Map=="function"?new Map:void 0;return(R=function(a){if(a===null||(e=a,Function.toString.call(e).indexOf("[native code]")===-1))return a;var e;if(typeof a!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(a))return t.get(a);t.set(a,s)}function s(){return I(a,arguments,L(this).constructor)}return s.prototype=Object.create(a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),o(s,a)})(d)}function I(d,t,a){return(I=k()?Reflect.construct:function(e,s,u){var n=[null];n.push.apply(n,s);var l=new(Function.bind.apply(e,n));return u&&o(l,u.prototype),l}).apply(null,arguments)}function k(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function o(d,t){return(o=Object.setPrototypeOf||function(a,e){return a.__proto__=e,a})(d,t)}function L(d){return(L=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(d)}function m(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),!0}catch{}return!1}var h=function(){function d(a){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=void 0,this.response=void 0,this.controller=void 0,this.context=void 0,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=a.fetchSetup||E,this.controller=new self.AbortController,this.stats=new T.LoadStats}var t=d.prototype;return t.destroy=function(){this.loader=this.callbacks=null,this.abortInternal()},t.abortInternal=function(){var a=this.response;a&&a.ok||(this.stats.aborted=!0,this.controller.abort())},t.abort=function(){var a;this.abortInternal(),(a=this.callbacks)!==null&&a!==void 0&&a.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)},t.load=function(a,e,s){var u=this,n=this.stats;if(n.loading.start)throw new Error("Loader can only be used once.");n.loading.start=self.performance.now();var l=function(r,i){var c={method:"GET",mode:"cors",credentials:"same-origin",signal:i};return r.rangeEnd&&(c.headers=new self.Headers({Range:"bytes="+r.rangeStart+"-"+String(r.rangeEnd-1)})),c}(a,this.controller.signal),p=s.onProgress,g=a.responseType==="arraybuffer",v=g?"byteLength":"length";this.context=a,this.config=e,this.callbacks=s,this.request=this.fetchSetup(a,l),self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(function(){u.abortInternal(),s.onTimeout(n,a,u.response)},e.timeout),self.fetch(this.request).then(function(r){if(u.response=u.loader=r,!r.ok){var i=r.status,c=r.statusText;throw new y(c||"fetch, bad network response",i,r)}return n.loading.first=Math.max(self.performance.now(),n.loading.start),n.total=parseInt(r.headers.get("Content-Length")||"0"),p&&Object(_.isFiniteNumber)(e.highWaterMark)?u.loadProgressively(r,n,a,e.highWaterMark,p):g?r.arrayBuffer():r.text()}).then(function(r){var i=u.response;self.clearTimeout(u.requestTimeout),n.loading.end=Math.max(self.performance.now(),n.loading.first),n.loaded=n.total=r[v];var c={url:i.url,data:r};p&&!Object(_.isFiniteNumber)(e.highWaterMark)&&p(n,a,r,i),s.onSuccess(c,n,a,i)}).catch(function(r){if(self.clearTimeout(u.requestTimeout),!n.aborted){var i=r.code||0;s.onError({code:i,text:r.message},a,r.details)}})},t.getCacheAge=function(){var a=null;if(this.response){var e=this.response.headers.get("age");a=e?parseFloat(e):null}return a},t.loadProgressively=function(a,e,s,u,n){u===void 0&&(u=0);var l=new A.default,p=a.body.getReader();return function g(){return p.read().then(function(v){if(v.done)return l.dataLength&&n(e,s,l.flush(),a),Promise.resolve(new ArrayBuffer(0));var r=v.value,i=r.length;return e.loaded+=i,i<u||l.dataLength?(l.push(r),l.dataLength>=u&&n(e,s,l.flush(),a)):n(e,s,r,a),g()}).catch(function(){return Promise.reject()})}()},d}();function E(d,t){return new self.Request(d.url,t)}var y=function(d){var t,a;function e(s,u,n){var l;return(l=d.call(this,s)||this).code=void 0,l.details=void 0,l.code=u,l.details=n,l}return a=d,(t=e).prototype=Object.create(a.prototype),t.prototype.constructor=t,o(t,a),e}(R(Error));w.default=h},"./src/utils/imsc1-ttml-parser.ts":function(N,w,f){f.r(w),f.d(w,"IMSC1_CODEC",function(){return L}),f.d(w,"parseIMSC1",function(){return y});var _=f("./src/utils/mp4-tools.ts"),T=f("./src/utils/vttparser.ts"),A=f("./src/utils/vttcue.ts"),R=f("./src/demux/id3.ts"),I=f("./src/utils/timescale-conversion.ts"),k=f("./src/utils/webvtt-parser.ts");function o(){return(o=Object.assign||function(n){for(var l=1;l<arguments.length;l++){var p=arguments[l];for(var g in p)Object.prototype.hasOwnProperty.call(p,g)&&(n[g]=p[g])}return n}).apply(this,arguments)}var L="stpp.ttml.im1t",m=/^(\d{2,}):(\d{2}):(\d{2}):(\d{2})\.?(\d+)?$/,h=/^(\d*(?:\.\d*)?)(h|m|s|ms|f|t)$/,E={left:"start",center:"center",right:"end",start:"start",end:"end"};function y(n,l,p,g,v){var r=Object(_.findBox)(new Uint8Array(n),["mdat"]);if(r.length!==0){var i=r[0],c=Object(R.utf8ArrayToStr)(new Uint8Array(n,i.start,i.end-i.start)),S=Object(I.toTimescaleFromScale)(l,1,p);try{g(function(b,D){var O=new DOMParser().parseFromString(b,"text/xml").getElementsByTagName("tt")[0];if(!O)throw new Error("Invalid ttml");var C={frameRate:30,subFrameRate:1,frameRateMultiplier:0,tickRate:0},x=Object.keys(C).reduce(function(U,G){return U[G]=O.getAttribute("ttp:"+G)||C[G],U},{}),P=O.getAttribute("xml:space")!=="preserve",F=t(d(O,"styling","style")),M=t(d(O,"layout","region")),B=d(O,"body","[begin]");return[].map.call(B,function(U){var G=a(U,P);if(!G||!U.hasAttribute("begin"))return null;var K=u(U.getAttribute("begin"),x),H=u(U.getAttribute("dur"),x),Y=u(U.getAttribute("end"),x);if(K===null)throw s(U);if(Y===null){if(H===null)throw s(U);Y=K+H}var W=new A.default(K-D,Y-D,G);W.id=Object(k.generateCueId)(W.startTime,W.endTime,W.text);var q=M[U.getAttribute("region")],Q=F[U.getAttribute("style")];W.position=10,W.size=80;var tt=function(at,X){var J="http://www.w3.org/ns/ttml#styling";return["displayAlign","textAlign","color","backgroundColor","fontSize","fontFamily"].reduce(function(nt,it){var dt=e(X,J,it)||e(at,J,it);return dt&&(nt[it]=dt),nt},{})}(q,Q),rt=tt.textAlign;if(rt){var z=E[rt];z&&(W.lineAlign=z),W.align=rt}return o(W,tt),W}).filter(function(U){return U!==null})}(c,S))}catch(b){v(b)}}else v(new Error("Could not parse IMSC1 mdat"))}function d(n,l,p){var g=n.getElementsByTagName(l)[0];return g?[].slice.call(g.querySelectorAll(p)):[]}function t(n){return n.reduce(function(l,p){var g=p.getAttribute("xml:id");return g&&(l[g]=p),l},{})}function a(n,l){return[].slice.call(n.childNodes).reduce(function(p,g,v){var r;return g.nodeName==="br"&&v?p+`
`:(r=g.childNodes)!==null&&r!==void 0&&r.length?a(g,l):l?p+g.textContent.trim().replace(/\s+/g," "):p+g.textContent},"")}function e(n,l,p){return n.hasAttributeNS(l,p)?n.getAttributeNS(l,p):null}function s(n){return new Error("Could not parse ttml timestamp "+n)}function u(n,l){if(!n)return null;var p=Object(T.parseTimeStamp)(n);return p===null&&(m.test(n)?p=function(g,v){var r=m.exec(g),i=(0|r[4])+(0|r[5])/v.subFrameRate;return 3600*(0|r[1])+60*(0|r[2])+(0|r[3])+i/v.frameRate}(n,l):h.test(n)&&(p=function(g,v){var r=h.exec(g),i=Number(r[1]);switch(r[2]){case"h":return 3600*i;case"m":return 60*i;case"ms":return 1e3*i;case"f":return i/v.frameRate;case"t":return i/v.tickRate}return i}(n,l))),p}},"./src/utils/logger.ts":function(N,w,f){f.r(w),f.d(w,"enableLogs",function(){return I}),f.d(w,"logger",function(){return k});var _=function(){},T={trace:_,debug:_,log:_,warn:_,info:_,error:_},A=T;function R(o){var L=self.console[o];return L?L.bind(self.console,"["+o+"] >"):_}function I(o){if(self.console&&o===!0||typeof o=="object"){(function(L){for(var m=arguments.length,h=new Array(m>1?m-1:0),E=1;E<m;E++)h[E-1]=arguments[E];h.forEach(function(y){A[y]=L[y]?L[y].bind(L):R(y)})})(o,"debug","log","info","warn","error");try{A.log()}catch{A=T}}else A=T}var k=T},"./src/utils/mediakeys-helper.ts":function(N,w,f){var _,T;f.r(w),f.d(w,"KeySystems",function(){return _}),f.d(w,"requestMediaKeySystemAccess",function(){return A}),(T=_||(_={})).WIDEVINE="com.widevine.alpha",T.PLAYREADY="com.microsoft.playready";var A=typeof self<"u"&&self.navigator&&self.navigator.requestMediaKeySystemAccess?self.navigator.requestMediaKeySystemAccess.bind(self.navigator):null},"./src/utils/mediasource-helper.ts":function(N,w,f){function _(){return self.MediaSource||self.WebKitMediaSource}f.r(w),f.d(w,"getMediaSource",function(){return _})},"./src/utils/mp4-tools.ts":function(N,w,f){f.r(w),f.d(w,"bin2str",function(){return I}),f.d(w,"readUint16",function(){return k}),f.d(w,"readUint32",function(){return o}),f.d(w,"writeUint32",function(){return L}),f.d(w,"findBox",function(){return m}),f.d(w,"parseSegmentIndex",function(){return h}),f.d(w,"parseInitSegment",function(){return E}),f.d(w,"getStartDTS",function(){return y}),f.d(w,"getDuration",function(){return d}),f.d(w,"computeRawDurationFromSamples",function(){return t}),f.d(w,"offsetStartDTS",function(){return a}),f.d(w,"segmentValidRange",function(){return e}),f.d(w,"appendUint8Array",function(){return s});var _=f("./src/utils/typed-array.ts"),T=f("./src/loader/fragment.ts"),A=Math.pow(2,32)-1,R=[].push;function I(u){return String.fromCharCode.apply(null,u)}function k(u,n){"data"in u&&(n+=u.start,u=u.data);var l=u[n]<<8|u[n+1];return l<0?65536+l:l}function o(u,n){"data"in u&&(n+=u.start,u=u.data);var l=u[n]<<24|u[n+1]<<16|u[n+2]<<8|u[n+3];return l<0?4294967296+l:l}function L(u,n,l){"data"in u&&(n+=u.start,u=u.data),u[n]=l>>24,u[n+1]=l>>16&255,u[n+2]=l>>8&255,u[n+3]=255&l}function m(u,n){var l,p,g,v=[];if(!n.length)return v;"data"in u?(l=u.data,p=u.start,g=u.end):(p=0,g=(l=u).byteLength);for(var r=p;r<g;){var i=o(l,r),c=i>1?r+i:g;if(I(l.subarray(r+4,r+8))===n[0])if(n.length===1)v.push({data:l,start:r+8,end:c});else{var S=m({data:l,start:r+8,end:c},n.slice(1));S.length&&R.apply(v,S)}r=c}return v}function h(u){var n=m(u,["moov"])[0],l=n?n.end:null,p=m(u,["sidx"]);if(!p||!p[0])return null;var g=[],v=p[0],r=v.data[0],i=r===0?8:16,c=o(v,i);i+=4,i+=r===0?8:16,i+=2;var S=v.end+0,b=k(v,i);i+=2;for(var D=0;D<b;D++){var O=i,C=o(v,O);O+=4;var x=2147483647&C;if((2147483648&C)>>>31==1)return console.warn("SIDX has hierarchical references (not supported)"),null;var P=o(v,O);O+=4,g.push({referenceSize:x,subsegmentDuration:P,info:{duration:P/c,start:S,end:S+x-1}}),S+=x,i=O+=4}return{earliestPresentationTime:0,timescale:c,version:r,referencesCount:b,references:g,moovEndOffset:l}}function E(u){for(var n=[],l=m(u,["moov","trak"]),p=0;p<l.length;p++){var g=l[p],v=m(g,["tkhd"])[0];if(v){var r=v.data[v.start],i=r===0?12:20,c=o(v,i),S=m(g,["mdia","mdhd"])[0];if(S){var b=o(S,i=(r=S.data[S.start])===0?12:20),D=m(g,["mdia","hdlr"])[0];if(D){var O=I(D.data.subarray(D.start+8,D.start+12)),C={soun:T.ElementaryStreamTypes.AUDIO,vide:T.ElementaryStreamTypes.VIDEO}[O];if(C){var x=m(g,["mdia","minf","stbl","stsd"])[0],P=void 0;x&&(P=I(x.data.subarray(x.start+12,x.start+16))),n[c]={timescale:b,type:C},n[C]={timescale:b,id:c,codec:P}}}}}}return m(u,["moov","mvex","trex"]).forEach(function(F){var M=o(F,4),B=n[M];B&&(B.default={duration:o(F,12),flags:o(F,20)})}),n}function y(u,n){return m(n,["moof","traf"]).reduce(function(l,p){var g=m(p,["tfdt"])[0],v=g.data[g.start],r=m(p,["tfhd"]).reduce(function(i,c){var S=o(c,4),b=u[S];if(b){var D=o(g,4);v===1&&(D*=Math.pow(2,32),D+=o(g,8));var O=D/(b.timescale||9e4);if(isFinite(O)&&(i===null||O<i))return O}return i},null);return r!==null&&isFinite(r)&&(l===null||r<l)?r:l},null)||0}function d(u,n){for(var l=0,p=0,g=0,v=m(u,["moof","traf"]),r=0;r<v.length;r++){var i=v[r],c=m(i,["tfhd"])[0],S=n[o(c,4)];if(S){var b=S.default,D=o(c,0)|(b==null?void 0:b.flags),O=b==null?void 0:b.duration;8&D&&(O=o(c,2&D?12:8));for(var C=S.timescale||9e4,x=m(i,["trun"]),P=0;P<x.length;P++)l=O?O*o(x[P],4):t(x[P]),S.type===T.ElementaryStreamTypes.VIDEO?p+=l/C:S.type===T.ElementaryStreamTypes.AUDIO&&(g+=l/C)}}if(p===0&&g===0){var F=h(u);if(F!=null&&F.references)return F.references.reduce(function(M,B){return M+B.info.duration||0},0)}return p||g}function t(u){var n=o(u,0),l=8;1&n&&(l+=4),4&n&&(l+=4);for(var p=0,g=o(u,4),v=0;v<g;v++)256&n&&(p+=o(u,l),l+=4),512&n&&(l+=4),1024&n&&(l+=4),2048&n&&(l+=4);return p}function a(u,n,l){m(n,["moof","traf"]).forEach(function(p){m(p,["tfhd"]).forEach(function(g){var v=o(g,4),r=u[v];if(r){var i=r.timescale||9e4;m(p,["tfdt"]).forEach(function(c){var S=c.data[c.start],b=o(c,4);if(S===0)L(c,4,b-l*i);else{b*=Math.pow(2,32),b+=o(c,8),b-=l*i,b=Math.max(b,0);var D=Math.floor(b/(A+1)),O=Math.floor(b%(A+1));L(c,4,D),L(c,8,O)}})}})})}function e(u){var n={valid:null,remainder:null},l=m(u,["moof"]);if(!l)return n;if(l.length<2)return n.remainder=u,n;var p=l[l.length-1];return n.valid=Object(_.sliceUint8)(u,0,p.start-8),n.remainder=Object(_.sliceUint8)(u,p.start-8),n}function s(u,n){var l=new Uint8Array(u.length+n.length);return l.set(u),l.set(n,u.length),l}},"./src/utils/output-filter.ts":function(N,w,f){f.r(w),f.d(w,"default",function(){return _});var _=function(){function T(R,I){this.timelineController=void 0,this.cueRanges=[],this.trackName=void 0,this.startTime=null,this.endTime=null,this.screen=null,this.timelineController=R,this.trackName=I}var A=T.prototype;return A.dispatchCue=function(){this.startTime!==null&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen,this.cueRanges),this.startTime=null)},A.newCue=function(R,I,k){(this.startTime===null||this.startTime>R)&&(this.startTime=R),this.endTime=I,this.screen=k,this.timelineController.createCaptionsTrack(this.trackName)},A.reset=function(){this.cueRanges=[]},T}()},"./src/utils/texttrack-utils.ts":function(N,w,f){f.r(w),f.d(w,"sendAddTrackEvent",function(){return T}),f.d(w,"addCueToTrack",function(){return A}),f.d(w,"clearCurrentCues",function(){return R}),f.d(w,"removeCuesInRange",function(){return I}),f.d(w,"getCuesInRange",function(){return k});var _=f("./src/utils/logger.ts");function T(o,L){var m;try{m=new Event("addtrack")}catch{(m=document.createEvent("Event")).initEvent("addtrack",!1,!1)}m.track=o,L.dispatchEvent(m)}function A(o,L){var m=o.mode;if(m==="disabled"&&(o.mode="hidden"),o.cues&&!o.cues.getCueById(L.id))try{if(o.addCue(L),!o.cues.getCueById(L.id))throw new Error("addCue is failed for: "+L)}catch(E){_.logger.debug("[texttrack-utils]: "+E);var h=new self.TextTrackCue(L.startTime,L.endTime,L.text);h.id=L.id,o.addCue(h)}m==="disabled"&&(o.mode=m)}function R(o){var L=o.mode;if(L==="disabled"&&(o.mode="hidden"),o.cues)for(var m=o.cues.length;m--;)o.removeCue(o.cues[m]);L==="disabled"&&(o.mode=L)}function I(o,L,m){var h=o.mode;if(h==="disabled"&&(o.mode="hidden"),o.cues&&o.cues.length>0)for(var E=k(o.cues,L,m),y=0;y<E.length;y++)o.removeCue(E[y]);h==="disabled"&&(o.mode=h)}function k(o,L,m){var h=[],E=function(a,e){if(e<a[0].startTime)return 0;var s=a.length-1;if(e>a[s].endTime)return-1;for(var u=0,n=s;u<=n;){var l=Math.floor((n+u)/2);if(e<a[l].startTime)n=l-1;else{if(!(e>a[l].startTime&&u<s))return l;u=l+1}}return a[u].startTime-e<e-a[n].startTime?u:n}(o,L);if(E>-1)for(var y=E,d=o.length;y<d;y++){var t=o[y];if(t.startTime>=L&&t.endTime<=m)h.push(t);else if(t.startTime>m)return h}return h}},"./src/utils/time-ranges.ts":function(N,w,f){f.r(w),w.default={toString:function(_){for(var T="",A=_.length,R=0;R<A;R++)T+="["+_.start(R).toFixed(3)+","+_.end(R).toFixed(3)+"]";return T}}},"./src/utils/timescale-conversion.ts":function(N,w,f){function _(I,k,o,L){o===void 0&&(o=1),L===void 0&&(L=!1);var m=I*k*o;return L?Math.round(m):m}function T(I,k,o,L){return o===void 0&&(o=1),L===void 0&&(L=!1),_(I,k,1/o,L)}function A(I,k){return k===void 0&&(k=!1),_(I,1e3,1/9e4,k)}function R(I,k){return k===void 0&&(k=1),_(I,9e4,1/k)}f.r(w),f.d(w,"toTimescaleFromBase",function(){return _}),f.d(w,"toTimescaleFromScale",function(){return T}),f.d(w,"toMsFromMpegTsClock",function(){return A}),f.d(w,"toMpegTsClockFromTimescale",function(){return R})},"./src/utils/typed-array.ts":function(N,w,f){function _(T,A,R){return Uint8Array.prototype.slice?T.slice(A,R):new Uint8Array(Array.prototype.slice.call(T,A,R))}f.r(w),f.d(w,"sliceUint8",function(){return _})},"./src/utils/vttcue.ts":function(N,w,f){f.r(w),w.default=function(){if(typeof self<"u"&&self.VTTCue)return self.VTTCue;var _=["","lr","rl"],T=["start","middle","end","left","right"];function A(o,L){if(typeof L!="string"||!Array.isArray(o))return!1;var m=L.toLowerCase();return!!~o.indexOf(m)&&m}function R(o){return A(T,o)}function I(o){for(var L=arguments.length,m=new Array(L>1?L-1:0),h=1;h<L;h++)m[h-1]=arguments[h];for(var E=1;E<arguments.length;E++){var y=arguments[E];for(var d in y)o[d]=y[d]}return o}function k(o,L,m){var h=this,E={enumerable:!0};h.hasBeenReset=!1;var y="",d=!1,t=o,a=L,e=m,s=null,u="",n=!0,l="auto",p="start",g=50,v="middle",r=50,i="middle";Object.defineProperty(h,"id",I({},E,{get:function(){return y},set:function(c){y=""+c}})),Object.defineProperty(h,"pauseOnExit",I({},E,{get:function(){return d},set:function(c){d=!!c}})),Object.defineProperty(h,"startTime",I({},E,{get:function(){return t},set:function(c){if(typeof c!="number")throw new TypeError("Start time must be set to a number.");t=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"endTime",I({},E,{get:function(){return a},set:function(c){if(typeof c!="number")throw new TypeError("End time must be set to a number.");a=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"text",I({},E,{get:function(){return e},set:function(c){e=""+c,this.hasBeenReset=!0}})),Object.defineProperty(h,"region",I({},E,{get:function(){return s},set:function(c){s=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"vertical",I({},E,{get:function(){return u},set:function(c){var S=function(b){return A(_,b)}(c);if(S===!1)throw new SyntaxError("An invalid or illegal string was specified.");u=S,this.hasBeenReset=!0}})),Object.defineProperty(h,"snapToLines",I({},E,{get:function(){return n},set:function(c){n=!!c,this.hasBeenReset=!0}})),Object.defineProperty(h,"line",I({},E,{get:function(){return l},set:function(c){if(typeof c!="number"&&c!=="auto")throw new SyntaxError("An invalid number or illegal string was specified.");l=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"lineAlign",I({},E,{get:function(){return p},set:function(c){var S=R(c);if(!S)throw new SyntaxError("An invalid or illegal string was specified.");p=S,this.hasBeenReset=!0}})),Object.defineProperty(h,"position",I({},E,{get:function(){return g},set:function(c){if(c<0||c>100)throw new Error("Position must be between 0 and 100.");g=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"positionAlign",I({},E,{get:function(){return v},set:function(c){var S=R(c);if(!S)throw new SyntaxError("An invalid or illegal string was specified.");v=S,this.hasBeenReset=!0}})),Object.defineProperty(h,"size",I({},E,{get:function(){return r},set:function(c){if(c<0||c>100)throw new Error("Size must be between 0 and 100.");r=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"align",I({},E,{get:function(){return i},set:function(c){var S=R(c);if(!S)throw new SyntaxError("An invalid or illegal string was specified.");i=S,this.hasBeenReset=!0}})),h.displayState=void 0}return k.prototype.getCueAsHTML=function(){return self.WebVTT.convertCueToDOMTree(self,this.text)},k}()},"./src/utils/vttparser.ts":function(N,w,f){f.r(w),f.d(w,"parseTimeStamp",function(){return A}),f.d(w,"fixLineBreaks",function(){return m}),f.d(w,"VTTParser",function(){return h});var _=f("./src/utils/vttcue.ts"),T=function(){function E(){}return E.prototype.decode=function(y,d){if(!y)return"";if(typeof y!="string")throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(y))},E}();function A(E){function y(t,a,e,s){return 3600*(0|t)+60*(0|a)+(0|e)+parseFloat(s||0)}var d=E.match(/^(?:(\d+):)?(\d{2}):(\d{2})(\.\d+)?/);return d?parseFloat(d[2])>59?y(d[2],d[3],0,d[4]):y(d[1],d[2],d[3],d[4]):null}var R=function(){function E(){this.values=Object.create(null)}var y=E.prototype;return y.set=function(d,t){this.get(d)||t===""||(this.values[d]=t)},y.get=function(d,t,a){return a?this.has(d)?this.values[d]:t[a]:this.has(d)?this.values[d]:t},y.has=function(d){return d in this.values},y.alt=function(d,t,a){for(var e=0;e<a.length;++e)if(t===a[e]){this.set(d,t);break}},y.integer=function(d,t){/^-?\d+$/.test(t)&&this.set(d,parseInt(t,10))},y.percent=function(d,t){if(/^([\d]{1,3})(\.[\d]*)?%$/.test(t)){var a=parseFloat(t);if(a>=0&&a<=100)return this.set(d,a),!0}return!1},E}();function I(E,y,d,t){var a=t?E.split(t):[E];for(var e in a)if(typeof a[e]=="string"){var s=a[e].split(d);s.length===2&&y(s[0],s[1])}}var k=new _.default(0,0,""),o=k.align==="middle"?"middle":"center";function L(E,y,d){var t=E;function a(){var s=A(E);if(s===null)throw new Error("Malformed timestamp: "+t);return E=E.replace(/^[^\sa-zA-Z-]+/,""),s}function e(){E=E.replace(/^\s+/,"")}if(e(),y.startTime=a(),e(),E.substr(0,3)!=="-->")throw new Error("Malformed time stamp (time stamps must be separated by '-->'): "+t);E=E.substr(3),e(),y.endTime=a(),e(),function(s,u){var n=new R;I(s,function(g,v){var r;switch(g){case"region":for(var i=d.length-1;i>=0;i--)if(d[i].id===v){n.set(g,d[i].region);break}break;case"vertical":n.alt(g,v,["rl","lr"]);break;case"line":r=v.split(","),n.integer(g,r[0]),n.percent(g,r[0])&&n.set("snapToLines",!1),n.alt(g,r[0],["auto"]),r.length===2&&n.alt("lineAlign",r[1],["start",o,"end"]);break;case"position":r=v.split(","),n.percent(g,r[0]),r.length===2&&n.alt("positionAlign",r[1],["start",o,"end","line-left","line-right","auto"]);break;case"size":n.percent(g,v);break;case"align":n.alt(g,v,["start",o,"end","left","right"])}},/:/,/\s/),u.region=n.get("region",null),u.vertical=n.get("vertical","");var l=n.get("line","auto");l==="auto"&&k.line===-1&&(l=-1),u.line=l,u.lineAlign=n.get("lineAlign","start"),u.snapToLines=n.get("snapToLines",!0),u.size=n.get("size",100),u.align=n.get("align",o);var p=n.get("position","auto");p==="auto"&&k.position===50&&(p=u.align==="start"||u.align==="left"?0:u.align==="end"||u.align==="right"?100:50),u.position=p}(E,y)}function m(E){return E.replace(/<br(?: \/)?>/gi,`
`)}var h=function(){function E(){this.state="INITIAL",this.buffer="",this.decoder=new T,this.regionList=[],this.cue=null,this.oncue=void 0,this.onparsingerror=void 0,this.onflush=void 0}var y=E.prototype;return y.parse=function(d){var t=this;function a(){var l=t.buffer,p=0;for(l=m(l);p<l.length&&l[p]!=="\r"&&l[p]!==`
`;)++p;var g=l.substr(0,p);return l[p]==="\r"&&++p,l[p]===`
`&&++p,t.buffer=l.substr(p),g}d&&(t.buffer+=t.decoder.decode(d,{stream:!0}));try{var e="";if(t.state==="INITIAL"){if(!/\r\n|\n/.test(t.buffer))return this;var s=(e=a()).match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(!s||!s[0])throw new Error("Malformed WebVTT signature.");t.state="HEADER"}for(var u=!1;t.buffer;){if(!/\r\n|\n/.test(t.buffer))return this;switch(u?u=!1:e=a(),t.state){case"HEADER":/:/.test(e)?I(e,function(l,p){},/:/):e||(t.state="ID");continue;case"NOTE":e||(t.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(e)){t.state="NOTE";break}if(!e)continue;if(t.cue=new _.default(0,0,""),t.state="CUE",e.indexOf("-->")===-1){t.cue.id=e;continue}case"CUE":if(!t.cue){t.state="BADCUE";continue}try{L(e,t.cue,t.regionList)}catch{t.cue=null,t.state="BADCUE";continue}t.state="CUETEXT";continue;case"CUETEXT":var n=e.indexOf("-->")!==-1;if(!e||n&&(u=!0)){t.oncue&&t.cue&&t.oncue(t.cue),t.cue=null,t.state="ID";continue}if(t.cue===null)continue;t.cue.text&&(t.cue.text+=`
`),t.cue.text+=e;continue;case"BADCUE":e||(t.state="ID")}}}catch{t.state==="CUETEXT"&&t.cue&&t.oncue&&t.oncue(t.cue),t.cue=null,t.state=t.state==="INITIAL"?"BADWEBVTT":"BADCUE"}return this},y.flush=function(){var d=this;try{if((d.cue||d.state==="HEADER")&&(d.buffer+=`

`,d.parse()),d.state==="INITIAL"||d.state==="BADWEBVTT")throw new Error("Malformed WebVTT signature.")}catch(t){d.onparsingerror&&d.onparsingerror(t)}return d.onflush&&d.onflush(),this},E}()},"./src/utils/webvtt-parser.ts":function(N,w,f){f.r(w),f.d(w,"generateCueId",function(){return m}),f.d(w,"parseWebVTT",function(){return h});var _=f("./src/polyfills/number.ts"),T=f("./src/utils/vttparser.ts"),A=f("./src/demux/id3.ts"),R=f("./src/utils/timescale-conversion.ts"),I=f("./src/remux/mp4-remuxer.ts"),k=/\r\n|\n\r|\n|\r/g,o=function(E,y,d){return d===void 0&&(d=0),E.substr(d,y.length)===y},L=function(E){for(var y=5381,d=E.length;d;)y=33*y^E.charCodeAt(--d);return(y>>>0).toString()};function m(E,y,d){return L(E.toString())+L(y.toString())+L(d)}function h(E,y,d,t,a,e,s,u){var n,l=new T.VTTParser,p=Object(A.utf8ArrayToStr)(new Uint8Array(E)).trim().replace(k,`
`).split(`
`),g=[],v=Object(R.toMpegTsClockFromTimescale)(y,d),r="00:00.000",i=0,c=0,S=!0,b=!1;l.oncue=function(D){var O=t[a],C=t.ccOffset,x=(i-v)/9e4;if(O!=null&&O.new&&(c!==void 0?C=t.ccOffset=O.start:function(B,U,G){var K=B[U],H=B[K.prevCC];if(!H||!H.new&&K.new)return B.ccOffset=B.presentationOffset=K.start,void(K.new=!1);for(;(Y=H)!==null&&Y!==void 0&&Y.new;){var Y;B.ccOffset+=K.start-H.start,K.new=!1,H=B[(K=H).prevCC]}B.presentationOffset=G}(t,a,x)),x&&(C=x-t.presentationOffset),b){var P=D.endTime-D.startTime,F=Object(I.normalizePts)(9e4*(D.startTime+C-c),9e4*e)/9e4;D.startTime=F,D.endTime=F+P}var M=D.text.trim();D.text=decodeURIComponent(encodeURIComponent(M)),D.id||(D.id=m(D.startTime,D.endTime,M)),D.endTime>0&&g.push(D)},l.onparsingerror=function(D){n=D},l.onflush=function(){n?u(n):s(g)},p.forEach(function(D){if(S){if(o(D,"X-TIMESTAMP-MAP=")){S=!1,b=!0,D.substr(16).split(",").forEach(function(O){o(O,"LOCAL:")?r=O.substr(6):o(O,"MPEGTS:")&&(i=parseInt(O.substr(7)))});try{c=function(O){var C=parseInt(O.substr(-3)),x=parseInt(O.substr(-6,2)),P=parseInt(O.substr(-9,2)),F=O.length>9?parseInt(O.substr(0,O.indexOf(":"))):0;if(!(Object(_.isFiniteNumber)(C)&&Object(_.isFiniteNumber)(x)&&Object(_.isFiniteNumber)(P)&&Object(_.isFiniteNumber)(F)))throw Error("Malformed X-TIMESTAMP-MAP: Local:"+O);return C+=1e3*x,(C+=6e4*P)+36e5*F}(r)/1e3}catch(O){b=!1,n=O}return}D===""&&(S=!1)}l.parse(D+`
`)}),l.flush()}},"./src/utils/xhr-loader.ts":function(N,w,f){f.r(w);var _=f("./src/utils/logger.ts"),T=f("./src/loader/load-stats.ts"),A=/^age:\s*[\d.]+\s*$/m,R=function(){function I(o){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=void 0,this.loader=null,this.stats=void 0,this.xhrSetup=o?o.xhrSetup:null,this.stats=new T.LoadStats,this.retryDelay=0}var k=I.prototype;return k.destroy=function(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null},k.abortInternal=function(){var o=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),o&&(o.onreadystatechange=null,o.onprogress=null,o.readyState!==4&&(this.stats.aborted=!0,o.abort()))},k.abort=function(){var o;this.abortInternal(),(o=this.callbacks)!==null&&o!==void 0&&o.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)},k.load=function(o,L,m){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=o,this.config=L,this.callbacks=m,this.retryDelay=L.retryDelay,this.loadInternal()},k.loadInternal=function(){var o=this.config,L=this.context;if(o){var m=this.loader=new self.XMLHttpRequest,h=this.stats;h.loading.first=0,h.loaded=0;var E=this.xhrSetup;try{if(E)try{E(m,L.url)}catch{m.open("GET",L.url,!0),E(m,L.url)}m.readyState||m.open("GET",L.url,!0)}catch(y){return void this.callbacks.onError({code:m.status,text:y.message},L,m)}L.rangeEnd&&m.setRequestHeader("Range","bytes="+L.rangeStart+"-"+(L.rangeEnd-1)),m.onreadystatechange=this.readystatechange.bind(this),m.onprogress=this.loadprogress.bind(this),m.responseType=L.responseType,self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),o.timeout),m.send()}},k.readystatechange=function(){var o=this.context,L=this.loader,m=this.stats;if(o&&L){var h=L.readyState,E=this.config;if(!m.aborted&&h>=2)if(self.clearTimeout(this.requestTimeout),m.loading.first===0&&(m.loading.first=Math.max(self.performance.now(),m.loading.start)),h===4){L.onreadystatechange=null,L.onprogress=null;var y=L.status;if(y>=200&&y<300){var d,t;if(m.loading.end=Math.max(self.performance.now(),m.loading.first),t=o.responseType==="arraybuffer"?(d=L.response).byteLength:(d=L.responseText).length,m.loaded=m.total=t,!this.callbacks)return;var a=this.callbacks.onProgress;if(a&&a(m,o,d,L),!this.callbacks)return;var e={url:L.responseURL,data:d};this.callbacks.onSuccess(e,m,o,L)}else m.retry>=E.maxRetry||y>=400&&y<499?(_.logger.error(y+" while loading "+o.url),this.callbacks.onError({code:y,text:L.statusText},o,L)):(_.logger.warn(y+" while loading "+o.url+", retrying in "+this.retryDelay+"..."),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay),this.retryDelay=Math.min(2*this.retryDelay,E.maxRetryDelay),m.retry++)}else self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),E.timeout)}},k.loadtimeout=function(){_.logger.warn("timeout while loading "+this.context.url);var o=this.callbacks;o&&(this.abortInternal(),o.onTimeout(this.stats,this.context,this.loader))},k.loadprogress=function(o){var L=this.stats;L.loaded=o.loaded,o.lengthComputable&&(L.total=o.total)},k.getCacheAge=function(){var o=null;if(this.loader&&A.test(this.loader.getAllResponseHeaders())){var L=this.loader.getResponseHeader("age");o=L?parseFloat(L):null}return o},I}();w.default=R}}).default);var Qt=Ie(fe.exports);const Ce={name:"d-icon"},pt=Object.assign(Ce,{props:{icon:String,size:[Number,String]},setup:function(N){const w=N,f=xt(()=>({fontSize:/^\d+$/.test(w.size)?w.size+"px":w.size}));return(_,T)=>($(),Z("i",{class:Ct(["d-icon iconfont",N.icon]),style:wt(j(f))},null,6))}});pt.__scopeId="data-v-0c690e66";Pt("data-v-4cb76d59");const we={class:"d-player-top"},Oe={class:"top-title"},xe={class:"top-title"};Ft();const Pe={props:{title:{default:""}},setup(N){Date.prototype.format=function(_){let T={"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds()};for(var A in T)new RegExp("("+A+")").test(_)&&(_=_.replace(RegExp.$1,RegExp.$1.length==1?T[A]:("00"+T[A]).substr((""+T[A]).length)));return _};let w=Dt("00:00:00");w.value=new Date().format("hh:mm:ss");let f=null;return f=setInterval(()=>{w.value=new Date().format("hh:mm:ss")},1e3),ce(()=>{clearInterval(f)}),(_,T)=>($(),Z("div",we,[V("p",Oe,st(N.title||""),1),V("p",xe,st(j(w)),1)]))},__scopeId:"data-v-4cb76d59"};Pt("data-v-ac2469ec");const Fe={class:"d-status"},Me={class:"d-flex-center"},Ne={class:"d-flex-center"},Be=Tt("5X\u901F\u64AD\u653E\u4E2D ");Ft();var ge=jt({props:["state"],setup:N=>(w,f)=>ht(($(),Z("div",Fe,[ht(V("li",Me,[et(pt,{size:"18",class:"d-status-icon",icon:"icon-volume-"+(N.state.volume==0?"mute":N.state.volume>.5?"up":"down")},null,8,["icon"]),Tt(" "+st(~~(100*N.state.volume))+"% ",1)],512),[[ft,N.state.handleType=="volume"]]),ht(V("li",Ne,[et(pt,{size:"12",icon:"icon-play"}),et(pt,{size:"12",icon:"icon-play",style:{"margin-right":"5px"}}),Be],512),[[ft,N.state.handleType=="playbackRate"||N.state.isMultiplesPlay]])],512)),[[ft,N.state.handleType||N.state.isMultiplesPlay]])});ge.__scopeId="data-v-ac2469ec",Pt("data-v-385f7870");const Ue=["checked","true-value","false-value"],Ge=V("span",{class:"d-switch_action"},null,-1);Ft();var Yt=jt({props:{modelValue:{type:[Number,String,Boolean]},width:{type:String,default:"40px"},trueValue:{type:[Number,String,Boolean],default:!0},falseValue:{type:[Number,String,Boolean],default:!0},activeColor:{type:[String],default:"#409EFF"}},emits:["update:modelValue","change"],setup(N,{emit:w}){const f=N;ie(R=>({"014e5dc0":N.width,e4e32852:N.activeColor}));const _=Dt(null),T=xt(()=>f.modelValue===f.trueValue),A=()=>{he(()=>{const R=_.value.checked;w("update:modelValue",R),w("change",R)})};return(R,I)=>($(),Z("div",{class:Ct(["d-switch",{"is-checked":j(T)}])},[V("input",{class:"d-switch__input",ref:_,type:"checkbox",checked:j(T),onChange:A,"true-value":N.trueValue,"false-value":N.falseValue},null,40,Ue),Ge],2))}});Yt.__scopeId="data-v-385f7870",Pt("data-v-b2384226");const je={key:0},Ke=V("i",{class:"rotating iconfont icon-loading f50"},null,-1),Ve=[V("i",{class:"rotating iconfont icon-loading f50"},null,-1),V("p",null,"\u6B63\u5728\u7F13\u51B2...",-1)],He=[V("i",{class:"iconfont icon-replay f24 mr5"},null,-1),Tt("\u91CD\u65B0\u64AD\u653E ")],We=[V("i",{class:"iconfont icon-replay f24 mr5"},null,-1),Tt("\u8BF7\u6C42\u9519\u8BEF ")];Ft();var ve=jt({props:{loadType:String,text:{type:String,default:""}},setup(N){const w=N,{proxy:f}=Le(),_=["loadstart","waiting","ended","error","stalled"],T=()=>{f.$parent.play()},A=xt(()=>{let R="background: rgba(0, 0, 0, .1);z-index:1";return w.loadType=="loadstart"&&(R="background: rgba(0, 0, 0, 1);;z-index:3"),R});return(R,I)=>ht(($(),Z("div",{class:"d-loading",style:wt(j(A))},[V("div",null,[N.loadType=="loadstart"?($(),Z("span",je,[Ke,V("p",null,st(N.text),1)])):ut("",!0),ht(V("span",null,Ve,512),[[ft,N.loadType=="waiting"]]),ht(V("span",null,[V("p",{onClick:T,class:"d-flex-x d-pointer"},He)],512),[[ft,N.loadType=="ended"]]),ht(V("span",null,[V("p",{onClick:T,class:"d-flex-x d-pointer"},We)],512),[[ft,N.loadType=="error"||N.loadType=="stalled"]])])],4)),[[ft,_.includes(N.loadType)]])}});ve.__scopeId="data-v-b2384226";const Ot=function(N,w,f,_=!1){N&&w&&f&&N.addEventListener(w,f,_)},ct=function(N,w,f,_=!1){N&&w&&f&&N.removeEventListener(w,f,_)};Pt("data-v-5a794390");const Ye=["onMousedown"];Ft();var Gt=jt(re(ee({},{name:"DSlider"}),{props:{modelValue:{required:!0,type:Number,default:0},disabled:{type:Boolean,default:!1},vertical:{type:Boolean,default:!1},hover:{type:Boolean,default:!0},hoverText:{type:String,default:""},preload:{type:Number,default:0},size:{type:String,default:"10px"}},emits:["update:modelValue","change","onMousemove"],setup:function(N,{emit:w}){const f=N;ie(t=>({"5242b67b":N.size}));const _=Dt(null),T=Dt(null),A=qt({dragging:!1,hoverPosition:0,hoverTipsLeft:"50%"}),R=xt(()=>{let t=f.modelValue<0?0:f.modelValue>1?1:f.modelValue;return f.vertical?`height:${100*t}%`:`width:${100*t}%`}),I=xt(()=>{let t=f.preload<0?0:f.preload>1?1:f.preload;return f.vertical?`height:${100*t}%`:`width:${100*t}%`}),k=xt(()=>{let t=A.hoverPosition<0?0:A.hoverPosition>1?1:A.hoverPosition;return f.vertical?`bottom:${100*t}%`:`left:${100*t}%`}),o=t=>{t.preventDefault()},L=t=>{f.disabled||(t.preventDefault(),A.dragging=!0,h(t),Ot(window,"mousemove",y),Ot(window,"touchmove",y),Ot(window,"mouseup",d),Ot(window,"touchend",d))},m=t=>{if(!f.hover)return;let a=E(t);if(w("onMousemove",t,a),A.hoverPosition=a,f.vertical)return;let e=_.value,s=T.value.clientWidth/2,u=t.clientX-e.getBoundingClientRect().left;u<s?A.hoverTipsLeft=s-u+"px":e.clientWidth-u<s?A.hoverTipsLeft=e.clientWidth-u-s+"px":A.hoverTipsLeft="50%"},h=t=>{let a=E(t);w("update:modelValue",a),w("change",t,a)},E=t=>{let a=_.value,e=0;if(f.vertical){let s=a.clientHeight;e=(a.getBoundingClientRect().bottom-t.clientY)/s}else e=(t.clientX-a.getBoundingClientRect().left)/a.clientWidth;return e<0?0:e>1?1:e},y=t=>{h(t)},d=t=>{A.dragging&&(ct(window,"mousemove",y),ct(window,"touchmove",y),ct(window,"mouseup",d),ct(window,"touchend",d),ct(window,"contextmenu",d),setTimeout(()=>{A.dragging=!1},0))};return(t,a)=>($(),Z("div",{ref:_,class:Ct(["d-slider",{"is-vertical":f.vertical}]),onMousedown:Ee(L,["stop"]),onContextmenu:o},[V("div",{class:"d-slider__runway",onMousemove:m},[ht(V("div",{class:"d-slider__cursor",style:wt(j(k))},[ht(V("div",{class:"d-slider__tips",ref:T,style:wt({left:j(A).hoverTipsLeft})},st(f.hoverText),5),[[ft,f.hoverText]])],4),[[ft,f.hover]]),V("div",{class:"d-slider__preload",style:wt(j(I))},null,4),V("div",{class:"d-slider__bar",style:wt(j(R))},null,4)],32)],42,Ye))}}));Gt.__scopeId="data-v-5a794390";Pt("data-v-570fa0d1");const qe={key:0,class:"d-player-dialog"},Xe={class:"d-player-dialog-body"},ze={class:"d-player-dialog-title"},$e={class:"d-player-hotkey-panel"},Qe={class:"d-player-filter-panel"},Ze={class:"d-player-filter-panel-item"},Je=V("span",null,"\u9971\u548C\u5EA6",-1),tr={class:"d-player-filter-panel-item"},er=V("span",null,"\u4EAE\u5EA6",-1),rr={class:"d-player-filter-panel-item"},ir=V("span",null,"\u5BF9\u6BD4\u5EA6",-1),nr={key:0,class:"d-player-contextmenu"},ar=["dplayerKeyCode"],sr=V("input",{class:"d-player-copyText"},null,-1);Ft();var pe=jt({setup(N){const w=qt({show:!1,dialogType:"",dialogTitle:"",version:"1.3.1-beta.6",mouseX:0,mouseY:0}),f=[{label:"\u89C6\u9891\u8272\u5F69\u8C03\u6574",key:"filter"},{label:"\u5FEB\u6377\u952E\u8BF4\u660E",key:"hotkey"},{label:"\u590D\u5236\u89C6\u9891\u7F51\u5740",key:"copy"},{label:"\u7248\u672C\uFF1A1.3.1-beta.6",key:"version"}],_=[{key:"Space",label:"\u64AD\u653E/\u6682\u505C"},{key:"\u2192",label:"\u5355\u6B21\u5FEB\u8FDB10s\uFF0C\u957F\u63095\u500D\u901F\u64AD\u653E"},{key:"\u2190",label:"\u5FEB\u90005s"},{key:"\u2191",label:"\u97F3\u91CF\u589E\u52A010%"},{key:"\u2193",label:"\u97F3\u91CF\u589E\u52A0\u964D\u4F4E10%"},{key:"Esc",label:"\u9000\u51FA\u5168\u5C4F/\u9000\u51FA\u7F51\u9875\u5168\u5C4F"},{key:"F",label:"\u5168\u5C4F/\u9000\u51FA\u5168\u5C4F"}],T=qt({saturate:.392,brightness:.392,contrast:.392}),A=xt(()=>({left:w.mouseX+"px",top:w.mouseY+"px"}));ue(T,L=>{let m=document.querySelector("#dPlayerVideo"),h=(2.55*L.saturate).toFixed(2),E=(2.55*L.brightness).toFixed(2),y=(2.55*L.contrast).toFixed(2);m.style.filter=`saturate(${h}) brightness(${E}) contrast(${y})`});const R=()=>{T.saturate=.392,T.brightness=.392,T.contrast=.392},I=L=>{L.key=="Escape"&&o(0)},k=L=>{L.preventDefault(),Ot(window,"keydown",I),Ot(window,"click",o);let m=document.querySelector("#refPlayerWrap"),h=m.clientWidth;m.clientHeight,w.mouseX=L.clientX-m.getBoundingClientRect().left,h-w.mouseX<130&&(w.mouseX=w.mouseX+(h-w.mouseX-130)),w.mouseY=L.clientY-m.getBoundingClientRect().top,w.show=!0},o=L=>{let m=L.path[0].tagName=="LI",h=L.path[0].attributes.dplayerKeyCode&&L.path[0].attributes.dplayerKeyCode.value,E=f.map(y=>y.key);if(m&&E.includes(h))if(w.dialogTitle=L.path[0].innerText,w.dialogType=h,h=="copy"){let y=document.querySelector(".d-player-copyText");y.value=window.location.href,y.select(),document.execCommand("copy"),w.dialogType=""}else h=="version"&&(w.dialogType="");w.show=!1,ct(window,"keydown",I),ct(window,"click",o)};return de(()=>{let L=document.querySelector("#refPlayerWrap");ct(window,"keydown",I),ct(window,"click",o),ct(L,"contextmenu",k),Ot(L,"contextmenu",k)}),ce(()=>{let L=document.querySelector("#refPlayerWrap");ct(window,"keydown",I),ct(window,"click",o),ct(L,"contextmenu",k)}),(L,m)=>($(),Z("div",null,[et(te,{name:"d-fade-in"},{default:Jt(()=>[j(w).dialogType?($(),Z("div",qe,[V("div",Xe,[V("h5",ze,[Tt(st(j(w).dialogTitle)+" ",1),V("i",{onClick:m[0]||(m[0]=h=>j(w).dialogType=!1),class:"icon icon-close"},"X")]),ht(V("ul",$e,[($(),Z(Xt,null,zt(_,h=>V("li",{class:"d-player-hotkey-panel-item",key:h.key},[V("span",null,st(h.key),1),V("span",null,st(h.label),1)])),64))],512),[[ft,j(w).dialogType=="hotkey"]]),ht(V("ul",Qe,[V("li",Ze,[Je,et(Gt,{class:"filter-panel-slider",size:"5px",modelValue:j(T).saturate,"onUpdate:modelValue":m[1]||(m[1]=h=>j(T).saturate=h)},null,8,["modelValue"]),V("span",null,st(Math.round(255*j(T).saturate)),1)]),V("li",tr,[er,et(Gt,{class:"filter-panel-slider",size:"5px",modelValue:j(T).brightness,"onUpdate:modelValue":m[2]||(m[2]=h=>j(T).brightness=h)},null,8,["modelValue"]),V("span",null,st(Math.round(255*j(T).brightness)),1)]),V("li",rr,[ir,et(Gt,{class:"filter-panel-slider",size:"5px",modelValue:j(T).contrast,"onUpdate:modelValue":m[3]||(m[3]=h=>j(T).contrast=h)},null,8,["modelValue"]),V("span",null,st(Math.round(255*j(T).contrast)),1)]),V("span",{onClick:R,title:"\u91CD\u7F6E","aria-label":"\u91CD\u7F6E",class:"d-player-filter-reset"},"\u91CD\u7F6E")],512),[[ft,j(w).dialogType=="filter"]])])])):ut("",!0)]),_:1}),j(w).show?($(),Z("div",nr,[V("ul",{class:"d-player-contextmenu-body",style:wt(j(A))},[($(),Z(Xt,null,zt(f,h=>V("li",{dplayerKeyCode:h.key,key:h.key},st(h.label),9,ar)),64))],4),sr])):ut("",!0)]))}});pe.__scopeId="data-v-570fa0d1";const Zt=N=>{let w=~~(N/3600),f=~~(N%3600/60),_=~~(N%60);return w=w<10?"0"+w:w,f=f<10?"0"+f:f,_=_<10?"0"+_:_,`${w}:${f}:${_}`},Wt="ontouchstart"in window,le=["loadstart","play","pause","playing","seeking","seeked","waiting","durationchange","progress","canplay","timeupdate","ended","error","stalled"],or={width:{type:String,default:"800px"},height:{type:String,default:"450px"},color:{type:String,default:"#409eff"},src:{required:!0,type:String,default:""},title:{type:String,default:""},type:{type:String,default:"video/mp4"},poster:{type:String,default:""},webFullScreen:{type:Boolean,default:!1},speed:{type:Boolean,default:!0},currentTime:{type:Number,default:0},playsinline:{type:Boolean,default:!1},muted:{type:Boolean,default:!1},speedRate:{type:Array,default:()=>["2.0","1.5","1.25","1.0","0.75","0.5"]},autoPlay:{type:Boolean,default:!1},loop:{type:Boolean,default:!1},mirror:{type:Boolean,default:!1},ligthOff:{type:Boolean,default:!1},volume:{type:[String,Number],default:.3},control:{type:Boolean,default:!0},controlBtns:{type:Array,default:["audioTrack","quality","speedRate","volume","setting","pip","pageFullScreen","fullScreen"]},preload:{type:String,default:"auto"}};Pt("data-v-01791e9e");const lr={class:"d-player-video",id:"dPlayerVideo"},ur=["controls","webkit-playsinline","playsinline","volume","muted","loop","preload","src","poster"],dr={class:"d-player-lightoff"},cr={key:1,class:"d-player-state"},hr={class:"d-play-btn"},fr=["onKeyup","onKeydown"],gr={class:"d-control-progress"},vr={class:"d-tool-bar"},pr={key:0,class:"d-tool-item d-tool-time audioTrack-btn"},mr=V("span",{style:{margin:"0 3px"}},"/",-1),yr={class:"total-time"},Er={class:"d-tool-bar"},Tr={key:0,class:"d-tool-item quality-btn"},Sr={class:"d-tool-item-main"},br={class:"speed-main",style:{"text-align":"center"}},Lr=["onClick"],Ar={key:1,class:"d-tool-item speedRate-btn"},Dr={class:"d-tool-item-main"},kr={class:"speed-main"},Rr=["onClick"],_r={key:2,class:"d-tool-item volume-btn"},Ir={class:"d-tool-item-main volume-box",style:{width:"52px"}},Cr={class:"volume-text-size"},wr={key:3,class:"d-tool-item setting-btn"},Or={class:"d-tool-item-main"},xr={class:"speed-main"},Pr=Tt(" \u955C\u50CF\u753B\u9762 "),Fr=Tt(" \u5FAA\u73AF\u64AD\u653E "),Mr=Tt(" \u5173\u706F\u6A21\u5F0F "),Nr=V("div",{class:"d-tool-item-main"},"\u753B\u4E2D\u753B",-1),Br=V("div",{class:"d-tool-item-main"},"\u7F51\u9875\u5168\u5C4F",-1),Ur=V("div",{class:"d-tool-item-main"},"\u5168\u5C4F",-1);Ft();var $t=jt(re(ee({},{name:"vue3VideoPlay",inheritAttrs:!1}),{props:or,emits:[...le,"mirrorChange","loopChange","lightOffChange"],setup:function(N,{expose:w,emit:f}){const _=N;ie(C=>({"51d4439c":j(E),"77e758a6":C.width,b8a1afc0:C.height}));const T=new Qt({fragLoadingTimeOut:2e3}),A=Dt(null),R=Dt(null),I=Dt(null),k=Dt(null),o=qt(re(ee({dVideo:null},_),{muted:_.muted,playBtnState:_.autoPlay?"pause":"play",loadStateType:"loadstart",fullScreen:!1,handleType:"",currentTime:"00:00:00",preloadBar:0,totalTime:"00:00:00",isVideoHovering:!0,speedActive:"1.0",playProgress:0,isMultiplesPlay:!1,longPressTimeout:null,progressCursorTime:"00:00:00",qualityLevels:[],currentLevel:0})),L=(...C)=>x=>C.reverse().reduce((P,F)=>F(P),x),m=le.reduce((C,x)=>{var P;return C[`on${P=x,P.charAt(0).toUpperCase()+P.slice(1)}`]=F=>{o.loadStateType=x,f(x,F)},C},{});m.onCanplay=L(m.onCanplay,()=>{o.playBtnState!="play"&&o.dVideo.play(),o.autoPlay&&(o.dVideo.play(),o.playBtnState="pause")}),m.onEnded=L(m.onEnded,()=>{o.playBtnState="replay"}),m.onDurationchange=C=>{f("durationchange",C),_.currentTime!=0&&(o.dVideo.currentTime=_.currentTime),m.onTimeupdate(C)},m.onProgress=C=>{console.log("\u7F13\u51B2\u4E2D..."),f("progress",C);let x=C.target.duration,P=C.target.buffered,F=C.target.buffered.length&&C.target.buffered.end(P-1);o.preloadBar=F/x},m.onTimeupdate=C=>{f("timeupdate",C);let x=C.duration||C.target.duration||0,P=C.currentTime||C.target.currentTime;o.playProgress=P/x,o.currentTime=Zt(P),o.totalTime=Zt(x)},m.onError=L(m.onError,()=>{o.playBtnState="replay"});let h=Te();for(let C in h)m[C]=h[C];const E=(y=o.color,`${parseInt("0x"+y.slice(1,3))},${parseInt("0x"+y.slice(3,5))},${parseInt("0x"+y.slice(5,7))}`);var y;const d=oe(500,()=>{o.handleType=""}),t=C=>{C.preventDefault(),C.code=="ArrowUp"?o.volume=o.volume+.1>1?1:o.volume+.1:o.volume=o.volume-.1<0?0:o.volume-.1,o.muted=!1,o.handleType="volume",d()},a=C=>{_.speed&&(o.dVideo.currentTime=o.dVideo.currentTime<10?.1:o.dVideo.currentTime-10,m.onTimeupdate(o.dVideo),u())},e=C=>{C.preventDefault();let x=C.type;if(C.key=="ArrowRight"){if(u(),x=="keyup"){if(clearTimeout(o.longPressTimeout),!_.speed&&!o.longPressTimeout)return;o.isMultiplesPlay?(o.dVideo.playbackRate=o.speedActive,o.isMultiplesPlay=!1):(o.dVideo.currentTime=o.dVideo.currentTime+10,m.onTimeupdate(o.dVideo))}else if(x=="keydown"){if(!_.speed)return;o.isMultiplesPlay&&clearTimeout(o.longPressTimeout),o.longPressTimeout=setTimeout(()=>{o.isMultiplesPlay=!0,o.dVideo.playbackRate=5,o.handleType="playbackRate",d()},500)}}},s=()=>{Wt||k.value.focus()},u=()=>{o.loadStateType="play",o.dVideo.play().catch(()=>{setTimeout(()=>{o.playBtnState="replay",o.loadStateType="error"},500)}),o.playBtnState="pause"},n=()=>{o.dVideo.pause(),o.playBtnState="play"},l=C=>{C&&C.preventDefault(),o.playBtnState=="play"||o.playBtnState=="replay"?u():o.playBtnState=="pause"&&n()},p=()=>{o.muted=!o.muted,o.volume==0&&(o.volume=.05)},g=(C,x)=>{let P=o.dVideo.duration||o.dVideo.target.duration;o.dVideo.currentTime=P*x,o.playBtnState=="play"&&(o.dVideo.play(),o.playBtnState="pause")},v=(C,x)=>{o.progressCursorTime=Zt(o.dVideo.duration*x)},r=oe(2500,()=>{o.isVideoHovering=!1}),i=C=>{o.isVideoHovering=!0,r()},c=C=>{f("mirrorChange",C,o.dVideo)},S=C=>{f("loopChange",C,o.dVideo)},b=C=>{f("lightOffChange",C,o.dVideo)},D=()=>{var C;C=o.dVideo,document.pictureInPictureElement?document.exitPictureInPicture().catch(x=>{console.log(x,"Video failed to leave Picture-in-Picture mode.")}):C.requestPictureInPicture().catch(x=>{console.log(x,"Video failed to enter Picture-in-Picture mode.")})},O=()=>{o.fullScreen=(C=>{let x=document,P=x.webkitIsFullScreen||x.fullscreen;return P?(document.exitFullscreen||x.webkitExitFullScreen).call(x):(C.requestFullscreen||C.webkitRequestFullScreen).call(C),!P})(A.value)};return ue(()=>_.src,()=>{he(()=>{o.dVideo.canPlayType(_.type)||o.dVideo.canPlayType("application/vnd.apple.mpegurl")?o.muted=_.autoPlay:Qt.isSupported()&&(T.detachMedia(),T.attachMedia(o.dVideo),T.on(Qt.Events.MEDIA_ATTACHED,()=>{T.loadSource(_.src),T.on("hlsManifestParsed",(C,x)=>{console.log(x),o.currentLevel=x.level,o.qualityLevels=x.levels||[]})}),T.on("hlsLevelSwitching",(C,x)=>{console.log(x),console.log("LEVEL_SWITCHING")}),T.on("hlsLevelSwitched",(C,x)=>{o.currentLevel=x.level,console.log("LEVEL_SWITCHED")}))})},{immediate:!0}),de(()=>{o.dVideo=R,s()}),w({play:u,pause:n,togglePlay:l}),(C,x)=>($(),Z("div",{ref:A,id:"refPlayerWrap",class:Ct(["d-player-wrap",{"web-full-screen":j(o).webFullScreen,"is-lightoff":j(o).lightOff,"d-player-wrap-hover":j(o).playBtnState=="play"||j(o).isVideoHovering}]),onMousemove:i},[V("div",lr,[V("video",Se({ref:R,class:["d-player-video-main",{"video-mirror":j(o).mirror}],id:"dPlayerVideoMain",controls:!(!j(Wt)||!j(o).speed),"webkit-playsinline":_.playsinline,playsinline:_.playsinline},j(m),{volume:j(o).volume,muted:j(o).muted,loop:j(o).loop,preload:C.preload,width:"100%",height:"100%",src:_.src,poster:_.poster}),"\u60A8\u7684\u6D4F\u89C8\u5668\u4E0D\u652F\u6301Video\u6807\u7B7E\u3002",16,ur)]),et(te,{name:"d-fade-in"},{default:Jt(()=>[ht(V("div",dr,null,512),[[ft,j(o).lightOff]])]),_:1}),j(o).fullScreen?($(),be(Pe,{key:0,title:_.title},null,8,["title"])):ut("",!0),j(Wt)?ut("",!0):($(),Z("div",cr,[et(te,{name:"d-scale-out"},{default:Jt(()=>[ht(V("div",hr,[et(pt,{icon:"icon-play",size:40})],512),[[ft,j(o).playBtnState=="play"]])]),_:1}),et(ge,{state:j(o)},null,8,["state"])])),j(Wt)?ut("",!0):($(),Z("input",{key:2,type:"input",readonly:"readonly",ref:k,onDblclick:O,onKeyup:[Ht(O,["f"]),x[0]||(x[0]=Ht(P=>j(o).webFullScreen=!1,["esc"])),e],onClick:l,onKeydown:[Ht(l,["space"]),Ht(a,["arrow-left"]),Ht(t,["arrow-up","arrow-down"]),e],class:"d-player-input",maxlength:"0"},null,40,fr)),et(ve,{loadType:j(o).loadStateType},null,8,["loadType"]),et(pe),!j(Wt)&&j(o).control?($(),Z("div",{key:3,class:"d-player-control",ref:I},[V("div",gr,[et(Gt,{class:"d-progress-bar",onOnMousemove:v,onChange:g,disabled:!j(o).speed,hoverText:j(o).progressCursorTime,modelValue:j(o).playProgress,"onUpdate:modelValue":x[1]||(x[1]=P=>j(o).playProgress=P),preload:j(o).preloadBar},null,8,["disabled","hoverText","modelValue","preload"])]),V("div",{class:"d-control-tool",onClick:s},[V("div",vr,[V("div",{class:"d-tool-item",onClick:l},[et(pt,{size:"24",icon:`icon-${j(o).playBtnState}`},null,8,["icon"])]),_.controlBtns.includes("audioTrack")?($(),Z("div",pr,[V("span",null,st(j(o).currentTime),1),mr,V("span",yr,st(j(o).totalTime),1)])):ut("",!0)]),V("div",Er,[j(o).qualityLevels.length&&_.controlBtns.includes("quality")?($(),Z("div",Tr,[Tt(st(j(o).qualityLevels.length&&(j(o).qualityLevels[j(o).currentLevel]||{}).height)+"P ",1),V("div",Sr,[V("ul",br,[($(!0),Z(Xt,null,zt(j(o).qualityLevels,(P,F)=>($(),Z("li",{class:Ct({"speed-active":j(o).currentLevel==F}),onClick:M=>((B,U)=>{T.currentLevel=U,o.currentLevel=U})(0,F),key:P},st(P.height)+"P",11,Lr))),128))])])])):ut("",!0),_.controlBtns.includes("speedRate")?($(),Z("div",Ar,[Tt(st(j(o).speedActive=="1.0"?"\u500D\u901F":j(o).speedActive+"x")+" ",1),V("div",Dr,[V("ul",kr,[($(!0),Z(Xt,null,zt(j(o).speedRate,P=>($(),Z("li",{class:Ct({"speed-active":j(o).speedActive==P}),onClick:F=>(M=>{o.speedActive=M,o.dVideo.playbackRate=M})(P),key:P},st(P)+"x",11,Rr))),128))])])])):ut("",!0),_.controlBtns.includes("volume")?($(),Z("div",_r,[V("div",Ir,[V("div",{class:Ct(["volume-main",{"is-muted":j(o).muted}])},[V("span",Cr,st(j(o).muted?0:~~(100*j(o).volume))+"%",1),et(Gt,{onChange:x[2]||(x[2]=P=>j(o).muted=!1),hover:!1,size:"5px",vertical:!0,modelValue:j(o).volume,"onUpdate:modelValue":x[3]||(x[3]=P=>j(o).volume=P)},null,8,["modelValue"])],2)]),V("span",{onClick:p,style:{display:"flex"}},[et(pt,{size:"20",icon:"icon-volume-"+(j(o).volume==0||j(o).muted?"mute":j(o).volume>.5?"up":"down")},null,8,["icon"])])])):ut("",!0),_.controlBtns.includes("setting")?($(),Z("div",wr,[et(pt,{size:"20",class:"rotateHover",icon:"icon-settings"}),V("div",Or,[V("ul",xr,[V("li",null,[Pr,et(Yt,{onChange:c,modelValue:j(o).mirror,"onUpdate:modelValue":x[4]||(x[4]=P=>j(o).mirror=P)},null,8,["modelValue"])]),V("li",null,[Fr,et(Yt,{onChange:S,modelValue:j(o).loop,"onUpdate:modelValue":x[5]||(x[5]=P=>j(o).loop=P)},null,8,["modelValue"])]),V("li",null,[Mr,et(Yt,{onChange:b,modelValue:j(o).lightOff,"onUpdate:modelValue":x[6]||(x[6]=P=>j(o).lightOff=P)},null,8,["modelValue"])])])])])):ut("",!0),_.controlBtns.includes("pip")?($(),Z("div",{key:4,class:"d-tool-item pip-btn",onClick:D},[et(pt,{size:"20",icon:"icon-pip"}),Nr])):ut("",!0),_.controlBtns.includes("pageFullScreen")?($(),Z("div",{key:5,class:"d-tool-item pip-btn",onClick:x[7]||(x[7]=P=>j(o).webFullScreen=!j(o).webFullScreen)},[et(pt,{size:"20",icon:"icon-web-screen"}),Br])):ut("",!0),_.controlBtns.includes("fullScreen")?($(),Z("div",{key:6,class:"d-tool-item fullScreen-btn",onClick:O},[Ur,et(pt,{size:"20",icon:"icon-screen"})])):ut("",!0)])])],512)):ut("",!0)],34))}}));function Gr(N){N.component($t.name,$t)}$t.__scopeId="data-v-01791e9e",$t.install=Gr;export{$t as g};
