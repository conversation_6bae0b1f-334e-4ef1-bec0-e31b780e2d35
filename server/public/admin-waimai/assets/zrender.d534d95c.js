import{_ as k}from"./tslib.60310f1a.js";var Ms=function(){function e(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return e}(),Ls=function(){function e(){this.browser=new Ms,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return e}(),kt=new Ls;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(kt.wxa=!0,kt.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?kt.worker=!0:!kt.hasGlobalWindow||"Deno"in window?(kt.node=!0,kt.svgSupported=!0):Ss(navigator.userAgent,kt);function Ss(e,t){var r=t.browser,i=e.match(/Firefox\/([\d.]+)/),a=e.match(/MSIE\s([\d.]+)/)||e.match(/Trident\/.+?rv:(([\d.]+))/),n=e.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(e);i&&(r.firefox=!0,r.version=i[1]),a&&(r.ie=!0,r.version=a[1]),n&&(r.edge=!0,r.version=n[1],r.newEdge=+n[1].split(".")[0]>18),o&&(r.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,t.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&+r.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||r.ie&&+r.version>=9}const K=kt;var La=12,Ps="sans-serif",dr=La+"px "+Ps,Rs=20,Ds=100,xs="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function As(e){var t={};if(typeof JSON>"u")return t;for(var r=0;r<e.length;r++){var i=String.fromCharCode(r+32),a=(e.charCodeAt(r)-Rs)/Ds;t[i]=a}return t}var Fs=As(xs),le={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var e,t;return function(r,i){if(!e){var a=le.createCanvas();e=a&&a.getContext("2d")}if(e)return t!==i&&(t=e.font=i||dr),e.measureText(r);r=r||"",i=i||dr;var n=/((?:\d+)?\.?\d*)px/.exec(i),o=n&&+n[1]||La,s=0;if(i.indexOf("mono")>=0)s=o*r.length;else for(var f=0;f<r.length;f++){var h=Fs[r[f]];s+=h==null?o:h*o}return{width:s}}}(),loadImage:function(e,t,r){var i=new Image;return i.onload=t,i.onerror=r,i.src=e,i}},so=oi(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(e,t){return e["[object "+t+"]"]=!0,e},{}),fo=oi(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(e,t){return e["[object "+t+"Array]"]=!0,e},{}),ce=Object.prototype.toString,ni=Array.prototype,Es=ni.forEach,Is=ni.filter,Sa=ni.slice,Os=ni.map,Oa=function(){}.constructor,_e=Oa?Oa.prototype:null,Pa="__proto__",Hs=2311;function ho(){return Hs++}function Ra(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];typeof console<"u"&&console.error.apply(console,e)}function zr(e){if(e==null||typeof e!="object")return e;var t=e,r=ce.call(e);if(r==="[object Array]"){if(!re(e)){t=[];for(var i=0,a=e.length;i<a;i++)t[i]=zr(e[i])}}else if(fo[r]){if(!re(e)){var n=e.constructor;if(n.from)t=n.from(e);else{t=new n(e.length);for(var i=0,a=e.length;i<a;i++)t[i]=e[i]}}}else if(!so[r]&&!re(e)&&!Ji(e)){t={};for(var o in e)e.hasOwnProperty(o)&&o!==Pa&&(t[o]=zr(e[o]))}return t}function Pr(e,t,r){if(!Yt(t)||!Yt(e))return r?zr(t):e;for(var i in t)if(t.hasOwnProperty(i)&&i!==Pa){var a=e[i],n=t[i];Yt(n)&&Yt(a)&&!oe(n)&&!oe(a)&&!Ji(n)&&!Ji(a)&&!Ba(n)&&!Ba(a)&&!re(n)&&!re(a)?Pr(a,n,r):(r||!(i in e))&&(e[i]=zr(t[i]))}return e}function Xv(e,t){for(var r=e[0],i=1,a=e.length;i<a;i++)r=Pr(r,e[i],t);return r}function Y(e,t){if(Object.assign)Object.assign(e,t);else for(var r in t)t.hasOwnProperty(r)&&r!==Pa&&(e[r]=t[r]);return e}function _t(e,t,r){for(var i=tt(t),a=0,n=i.length;a<n;a++){var o=i[a];(r?t[o]!=null:e[o]==null)&&(e[o]=t[o])}return e}le.createCanvas;function Rt(e,t){if(e){if(e.indexOf)return e.indexOf(t);for(var r=0,i=e.length;r<i;r++)if(e[r]===t)return r}return-1}function Gv(e,t){var r=e.prototype;function i(){}i.prototype=t.prototype,e.prototype=new i;for(var a in r)r.hasOwnProperty(a)&&(e.prototype[a]=r[a]);e.prototype.constructor=e,e.superClass=t}function uo(e,t,r){if(e="prototype"in e?e.prototype:e,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),a=0;a<i.length;a++){var n=i[a];n!=="constructor"&&(r?t[n]!=null:e[n]==null)&&(e[n]=t[n])}else _t(e,t,r)}function At(e){return!e||typeof e=="string"?!1:typeof e.length=="number"}function j(e,t,r){if(!!(e&&t))if(e.forEach&&e.forEach===Es)e.forEach(t,r);else if(e.length===+e.length)for(var i=0,a=e.length;i<a;i++)t.call(r,e[i],i,e);else for(var n in e)e.hasOwnProperty(n)&&t.call(r,e[n],n,e)}function Tt(e,t,r){if(!e)return[];if(!t)return vo(e);if(e.map&&e.map===Os)return e.map(t,r);for(var i=[],a=0,n=e.length;a<n;a++)i.push(t.call(r,e[a],a,e));return i}function oi(e,t,r,i){if(!!(e&&t)){for(var a=0,n=e.length;a<n;a++)r=t.call(i,r,e[a],a,e);return r}}function Ha(e,t,r){if(!e)return[];if(!t)return vo(e);if(e.filter&&e.filter===Is)return e.filter(t,r);for(var i=[],a=0,n=e.length;a<n;a++)t.call(r,e[a],a,e)&&i.push(e[a]);return i}function qv(e,t,r){if(!!(e&&t)){for(var i=0,a=e.length;i<a;i++)if(t.call(r,e[i],i,e))return e[i]}}function tt(e){if(!e)return[];if(Object.keys)return Object.keys(e);var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}function Bs(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return e.apply(t,r.concat(Sa.call(arguments)))}}var Uv=_e&&si(_e.bind)?_e.call.bind(_e.bind):Bs;function Zv(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return function(){return e.apply(this,t.concat(Sa.call(arguments)))}}function oe(e){return Array.isArray?Array.isArray(e):ce.call(e)==="[object Array]"}function si(e){return typeof e=="function"}function se(e){return typeof e=="string"}function Nv(e){return ce.call(e)==="[object String]"}function Be(e){return typeof e=="number"}function Yt(e){var t=typeof e;return t==="function"||!!e&&t==="object"}function Ba(e){return!!so[ce.call(e)]}function ks(e){return!!fo[ce.call(e)]}function Ji(e){return typeof e=="object"&&typeof e.nodeType=="number"&&typeof e.ownerDocument=="object"}function fi(e){return e.colorStops!=null}function zs(e){return e.image!=null}function Ys(e){return e!==e}function Vv(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=0,i=e.length;r<i;r++)if(e[r]!=null)return e[r]}function ut(e,t){return e!=null?e:t}function ke(e,t,r){return e!=null?e:t!=null?t:r}function vo(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return Sa.apply(e,t)}function $s(e){if(typeof e=="number")return[e,e,e,e];var t=e.length;return t===2?[e[0],e[1],e[0],e[1]]:t===3?[e[0],e[1],e[2],e[1]]:e}function Qv(e,t){if(!e)throw new Error(t)}function Rr(e){return e==null?null:typeof e.trim=="function"?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var lo="__ec_primitive__";function Kv(e){e[lo]=!0}function re(e){return e[lo]}var Ws=function(){function e(){this.data={}}return e.prototype.delete=function(t){var r=this.has(t);return r&&delete this.data[t],r},e.prototype.has=function(t){return this.data.hasOwnProperty(t)},e.prototype.get=function(t){return this.data[t]},e.prototype.set=function(t,r){return this.data[t]=r,this},e.prototype.keys=function(){return tt(this.data)},e.prototype.forEach=function(t){var r=this.data;for(var i in r)r.hasOwnProperty(i)&&t(r[i],i)},e}(),co=typeof Map=="function";function Xs(){return co?new Map:new Ws}var Gs=function(){function e(t){var r=oe(t);this.data=Xs();var i=this;t instanceof e?t.each(a):t&&j(t,a);function a(n,o){r?i.set(n,o):i.set(o,n)}}return e.prototype.hasKey=function(t){return this.data.has(t)},e.prototype.get=function(t){return this.data.get(t)},e.prototype.set=function(t,r){return this.data.set(t,r),r},e.prototype.each=function(t,r){this.data.forEach(function(i,a){t.call(r,i,a)})},e.prototype.keys=function(){var t=this.data.keys();return co?Array.from(t):t},e.prototype.removeKey=function(t){this.data.delete(t)},e}();function Jv(e){return new Gs(e)}function jv(e,t){for(var r=new e.constructor(e.length+t.length),i=0;i<e.length;i++)r[i]=e[i];for(var a=e.length,i=0;i<t.length;i++)r[i+a]=t[i];return r}function hi(e,t){var r;if(Object.create)r=Object.create(e);else{var i=function(){};i.prototype=e,r=new i}return t&&Y(r,t),r}function po(e){var t=e.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function qe(e,t){return e.hasOwnProperty(t)}function Or(){}var qs=180/Math.PI;function Yr(e,t){return e==null&&(e=0),t==null&&(t=0),[e,t]}function tl(e,t){return e[0]=t[0],e[1]=t[1],e}function Us(e){return[e[0],e[1]]}function ka(e,t,r){return e[0]=t[0]+r[0],e[1]=t[1]+r[1],e}function Zs(e,t,r){return e[0]=t[0]-r[0],e[1]=t[1]-r[1],e}function Ns(e){return Math.sqrt(Vs(e))}function Vs(e){return e[0]*e[0]+e[1]*e[1]}function di(e,t,r){return e[0]=t[0]*r,e[1]=t[1]*r,e}function Qs(e,t){var r=Ns(t);return r===0?(e[0]=0,e[1]=0):(e[0]=t[0]/r,e[1]=t[1]/r),e}function ji(e,t){return Math.sqrt((e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1]))}var Ks=ji;function Js(e,t){return(e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1])}var Hr=Js;function js(e,t,r,i){return e[0]=t[0]+i*(r[0]-t[0]),e[1]=t[1]+i*(r[1]-t[1]),e}function ee(e,t,r){var i=t[0],a=t[1];return e[0]=r[0]*i+r[2]*a+r[4],e[1]=r[1]*i+r[3]*a+r[5],e}function Dr(e,t,r){return e[0]=Math.min(t[0],r[0]),e[1]=Math.min(t[1],r[1]),e}function xr(e,t,r){return e[0]=Math.max(t[0],r[0]),e[1]=Math.max(t[1],r[1]),e}var _r=function(){function e(t,r){this.target=t,this.topTarget=r&&r.topTarget}return e}(),tf=function(){function e(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return e.prototype._dragStart=function(t){for(var r=t.target;r&&!r.draggable;)r=r.parent||r.__hostTarget;r&&(this._draggingTarget=r,r.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new _r(r,t),"dragstart",t.event))},e.prototype._drag=function(t){var r=this._draggingTarget;if(r){var i=t.offsetX,a=t.offsetY,n=i-this._x,o=a-this._y;this._x=i,this._y=a,r.drift(n,o,t),this.handler.dispatchToElement(new _r(r,t),"drag",t.event);var s=this.handler.findHover(i,a,r).target,f=this._dropTarget;this._dropTarget=s,r!==s&&(f&&s!==f&&this.handler.dispatchToElement(new _r(f,t),"dragleave",t.event),s&&s!==f&&this.handler.dispatchToElement(new _r(s,t),"dragenter",t.event))}},e.prototype._dragEnd=function(t){var r=this._draggingTarget;r&&(r.dragging=!1),this.handler.dispatchToElement(new _r(r,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new _r(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},e}();const rf=tf;var ef=function(){function e(t){t&&(this._$eventProcessor=t)}return e.prototype.on=function(t,r,i,a){this._$handlers||(this._$handlers={});var n=this._$handlers;if(typeof r=="function"&&(a=i,i=r,r=null),!i||!t)return this;var o=this._$eventProcessor;r!=null&&o&&o.normalizeQuery&&(r=o.normalizeQuery(r)),n[t]||(n[t]=[]);for(var s=0;s<n[t].length;s++)if(n[t][s].h===i)return this;var f={h:i,query:r,ctx:a||this,callAtLast:i.zrEventfulCallAtLast},h=n[t].length-1,u=n[t][h];return u&&u.callAtLast?n[t].splice(h,0,f):n[t].push(f),this},e.prototype.isSilent=function(t){var r=this._$handlers;return!r||!r[t]||!r[t].length},e.prototype.off=function(t,r){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(r){if(i[t]){for(var a=[],n=0,o=i[t].length;n<o;n++)i[t][n].h!==r&&a.push(i[t][n]);i[t]=a}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},e.prototype.trigger=function(t){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];if(!this._$handlers)return this;var a=this._$handlers[t],n=this._$eventProcessor;if(a)for(var o=r.length,s=a.length,f=0;f<s;f++){var h=a[f];if(!(n&&n.filter&&h.query!=null&&!n.filter(t,h.query)))switch(o){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,r[0]);break;case 2:h.h.call(h.ctx,r[0],r[1]);break;default:h.h.apply(h.ctx,r);break}}return n&&n.afterTrigger&&n.afterTrigger(t),this},e.prototype.triggerWithContext=function(t){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];if(!this._$handlers)return this;var a=this._$handlers[t],n=this._$eventProcessor;if(a)for(var o=r.length,s=r[o-1],f=a.length,h=0;h<f;h++){var u=a[h];if(!(n&&n.filter&&u.query!=null&&!n.filter(t,u.query)))switch(o){case 0:u.h.call(s);break;case 1:u.h.call(s,r[0]);break;case 2:u.h.call(s,r[0],r[1]);break;default:u.h.apply(s,r.slice(1,o-1));break}}return n&&n.afterTrigger&&n.afterTrigger(t),this},e}();const $r=ef;var af=Math.log(2);function ta(e,t,r,i,a,n){var o=i+"-"+a,s=e.length;if(n.hasOwnProperty(o))return n[o];if(t===1){var f=Math.round(Math.log((1<<s)-1&~a)/af);return e[r][f]}for(var h=i|1<<r,u=r+1;i&1<<u;)u++;for(var v=0,l=0,c=0;l<s;l++){var _=1<<l;_&a||(v+=(c%2?-1:1)*e[r][l]*ta(e,t-1,u,h,a|_,n),c++)}return n[o]=v,v}function za(e,t){var r=[[e[0],e[1],1,0,0,0,-t[0]*e[0],-t[0]*e[1]],[0,0,0,e[0],e[1],1,-t[1]*e[0],-t[1]*e[1]],[e[2],e[3],1,0,0,0,-t[2]*e[2],-t[2]*e[3]],[0,0,0,e[2],e[3],1,-t[3]*e[2],-t[3]*e[3]],[e[4],e[5],1,0,0,0,-t[4]*e[4],-t[4]*e[5]],[0,0,0,e[4],e[5],1,-t[5]*e[4],-t[5]*e[5]],[e[6],e[7],1,0,0,0,-t[6]*e[6],-t[6]*e[7]],[0,0,0,e[6],e[7],1,-t[7]*e[6],-t[7]*e[7]]],i={},a=ta(r,8,0,0,0,i);if(a!==0){for(var n=[],o=0;o<8;o++)for(var s=0;s<8;s++)n[s]==null&&(n[s]=0),n[s]+=((o+s)%2?-1:1)*ta(r,7,o===0?1:0,1<<o,1<<s,i)/a*t[o];return function(f,h,u){var v=h*n[6]+u*n[7]+1;f[0]=(h*n[0]+u*n[1]+n[2])/v,f[1]=(h*n[3]+u*n[4]+n[5])/v}}}var Ya="___zrEVENTSAVED",pi=[];function rl(e,t,r,i,a){return ra(pi,t,i,a,!0)&&ra(e,r,pi[0],pi[1])}function ra(e,t,r,i,a){if(t.getBoundingClientRect&&K.domSupported&&!_o(t)){var n=t[Ya]||(t[Ya]={}),o=nf(t,n),s=of(o,n,a);if(s)return s(e,r,i),!0}return!1}function nf(e,t){var r=t.markers;if(r)return r;r=t.markers=[];for(var i=["left","right"],a=["top","bottom"],n=0;n<4;n++){var o=document.createElement("div"),s=o.style,f=n%2,h=(n>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[f]+":0",a[h]+":0",i[1-f]+":auto",a[1-h]+":auto",""].join("!important;"),e.appendChild(o),r.push(o)}return r}function of(e,t,r){for(var i=r?"invTrans":"trans",a=t[i],n=t.srcCoords,o=[],s=[],f=!0,h=0;h<4;h++){var u=e[h].getBoundingClientRect(),v=2*h,l=u.left,c=u.top;o.push(l,c),f=f&&n&&l===n[v]&&c===n[v+1],s.push(e[h].offsetLeft,e[h].offsetTop)}return f&&a?a:(t.srcCoords=o,t[i]=r?za(s,o):za(o,s))}function _o(e){return e.nodeName.toUpperCase()==="CANVAS"}var sf=/([&<>"'])/g,ff={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function el(e){return e==null?"":(e+"").replace(sf,function(t,r){return ff[r]})}var hf=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,_i=[],uf=K.browser.firefox&&+K.browser.version.split(".")[0]<39;function ea(e,t,r,i){return r=r||{},i?$a(e,t,r):uf&&t.layerX!=null&&t.layerX!==t.offsetX?(r.zrX=t.layerX,r.zrY=t.layerY):t.offsetX!=null?(r.zrX=t.offsetX,r.zrY=t.offsetY):$a(e,t,r),r}function $a(e,t,r){if(K.domSupported&&e.getBoundingClientRect){var i=t.clientX,a=t.clientY;if(_o(e)){var n=e.getBoundingClientRect();r.zrX=i-n.left,r.zrY=a-n.top;return}else if(ra(_i,e,i,a)){r.zrX=_i[0],r.zrY=_i[1];return}}r.zrX=r.zrY=0}function Da(e){return e||window.event}function yt(e,t,r){if(t=Da(t),t.zrX!=null)return t;var i=t.type,a=i&&i.indexOf("touch")>=0;if(a){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&ea(e,o,t,r)}else{ea(e,t,t,r);var n=vf(t);t.zrDelta=n?n/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&hf.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function vf(e){var t=e.wheelDelta;if(t)return t;var r=e.deltaX,i=e.deltaY;if(r==null||i==null)return t;var a=Math.abs(i!==0?i:r),n=i>0?-1:i<0?1:r>0?-1:1;return 3*a*n}function lf(e,t,r,i){e.addEventListener(t,r,i)}function cf(e,t,r,i){e.removeEventListener(t,r,i)}var df=function(e){e.preventDefault(),e.stopPropagation(),e.cancelBubble=!0};function il(e){return e.which===2||e.which===3}var pf=function(){function e(){this._track=[]}return e.prototype.recognize=function(t,r,i){return this._doTrack(t,r,i),this._recognize(t)},e.prototype.clear=function(){return this._track.length=0,this},e.prototype._doTrack=function(t,r,i){var a=t.touches;if(!!a){for(var n={points:[],touches:[],target:r,event:t},o=0,s=a.length;o<s;o++){var f=a[o],h=ea(i,f,{});n.points.push([h.zrX,h.zrY]),n.touches.push(f)}this._track.push(n)}},e.prototype._recognize=function(t){for(var r in gi)if(gi.hasOwnProperty(r)){var i=gi[r](this._track,t);if(i)return i}},e}();function Wa(e){var t=e[1][0]-e[0][0],r=e[1][1]-e[0][1];return Math.sqrt(t*t+r*r)}function _f(e){return[(e[0][0]+e[1][0])/2,(e[0][1]+e[1][1])/2]}var gi={pinch:function(e,t){var r=e.length;if(!!r){var i=(e[r-1]||{}).points,a=(e[r-2]||{}).points||i;if(a&&a.length>1&&i&&i.length>1){var n=Wa(i)/Wa(a);!isFinite(n)&&(n=1),t.pinchScale=n;var o=_f(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:e[0].target,event:t}}}}};function Br(){return[1,0,0,1,0,0]}function gf(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=1,e[4]=0,e[5]=0,e}function yf(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e}function ie(e,t,r){var i=t[0]*r[0]+t[2]*r[1],a=t[1]*r[0]+t[3]*r[1],n=t[0]*r[2]+t[2]*r[3],o=t[1]*r[2]+t[3]*r[3],s=t[0]*r[4]+t[2]*r[5]+t[4],f=t[1]*r[4]+t[3]*r[5]+t[5];return e[0]=i,e[1]=a,e[2]=n,e[3]=o,e[4]=s,e[5]=f,e}function ia(e,t,r){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4]+r[0],e[5]=t[5]+r[1],e}function go(e,t,r,i){i===void 0&&(i=[0,0]);var a=t[0],n=t[2],o=t[4],s=t[1],f=t[3],h=t[5],u=Math.sin(r),v=Math.cos(r);return e[0]=a*v+s*u,e[1]=-a*u+s*v,e[2]=n*v+f*u,e[3]=-n*u+v*f,e[4]=v*(o-i[0])+u*(h-i[1])+i[0],e[5]=v*(h-i[1])-u*(o-i[0])+i[1],e}function yo(e,t,r){var i=r[0],a=r[1];return e[0]=t[0]*i,e[1]=t[1]*a,e[2]=t[2]*i,e[3]=t[3]*a,e[4]=t[4]*i,e[5]=t[5]*a,e}function mf(e,t){var r=t[0],i=t[2],a=t[4],n=t[1],o=t[3],s=t[5],f=r*o-n*i;return f?(f=1/f,e[0]=o*f,e[1]=-n*f,e[2]=-i*f,e[3]=r*f,e[4]=(i*s-o*a)*f,e[5]=(n*a-r*s)*f,e):null}var wf=function(){function e(t,r){this.x=t||0,this.y=r||0}return e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,r){return this.x=t,this.y=r,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,r){this.x+=t.x*r,this.y+=t.y*r},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var r=this.x-t.x,i=this.y-t.y;return Math.sqrt(r*r+i*i)},e.prototype.distanceSquare=function(t){var r=this.x-t.x,i=this.y-t.y;return r*r+i*i},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){if(!!t){var r=this.x,i=this.y;return this.x=t[0]*r+t[2]*i+t[4],this.y=t[1]*r+t[3]*i+t[5],this}},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,r,i){t.x=r,t.y=i},e.copy=function(t,r){t.x=r.x,t.y=r.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,r){return t.x*r.x+t.y*r.y},e.add=function(t,r,i){t.x=r.x+i.x,t.y=r.y+i.y},e.sub=function(t,r,i){t.x=r.x-i.x,t.y=r.y-i.y},e.scale=function(t,r,i){t.x=r.x*i,t.y=r.y*i},e.scaleAndAdd=function(t,r,i,a){t.x=r.x+i.x*a,t.y=r.y+i.y*a},e.lerp=function(t,r,i,a){var n=1-a;t.x=n*r.x+a*i.x,t.y=n*r.y+a*i.y},e}();const I=wf;var ge=Math.min,ye=Math.max,Nt=new I,Vt=new I,Qt=new I,Kt=new I,Xr=new I,Gr=new I,Tf=function(){function e(t,r,i,a){i<0&&(t=t+i,i=-i),a<0&&(r=r+a,a=-a),this.x=t,this.y=r,this.width=i,this.height=a}return e.prototype.union=function(t){var r=ge(t.x,this.x),i=ge(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=ye(t.x+t.width,this.x+this.width)-r:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=ye(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=r,this.y=i},e.prototype.applyTransform=function(t){e.applyTransform(this,this,t)},e.prototype.calculateTransform=function(t){var r=this,i=t.width/r.width,a=t.height/r.height,n=Br();return ia(n,n,[-r.x,-r.y]),yo(n,n,[i,a]),ia(n,n,[t.x,t.y]),n},e.prototype.intersect=function(t,r){if(!t)return!1;t instanceof e||(t=e.create(t));var i=this,a=i.x,n=i.x+i.width,o=i.y,s=i.y+i.height,f=t.x,h=t.x+t.width,u=t.y,v=t.y+t.height,l=!(n<f||h<a||s<u||v<o);if(r){var c=1/0,_=0,g=Math.abs(n-f),d=Math.abs(h-a),p=Math.abs(s-u),y=Math.abs(v-o),m=Math.min(g,d),w=Math.min(p,y);n<f||h<a?m>_&&(_=m,g<d?I.set(Gr,-g,0):I.set(Gr,d,0)):m<c&&(c=m,g<d?I.set(Xr,g,0):I.set(Xr,-d,0)),s<u||v<o?w>_&&(_=w,p<y?I.set(Gr,0,-p):I.set(Gr,0,y)):m<c&&(c=m,p<y?I.set(Xr,0,p):I.set(Xr,0,-y))}return r&&I.copy(r,l?Xr:Gr),l},e.prototype.contain=function(t,r){var i=this;return t>=i.x&&t<=i.x+i.width&&r>=i.y&&r<=i.y+i.height},e.prototype.clone=function(){return new e(this.x,this.y,this.width,this.height)},e.prototype.copy=function(t){e.copy(this,t)},e.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},e.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},e.prototype.isZero=function(){return this.width===0||this.height===0},e.create=function(t){return new e(t.x,t.y,t.width,t.height)},e.copy=function(t,r){t.x=r.x,t.y=r.y,t.width=r.width,t.height=r.height},e.applyTransform=function(t,r,i){if(!i){t!==r&&e.copy(t,r);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var a=i[0],n=i[3],o=i[4],s=i[5];t.x=r.x*a+o,t.y=r.y*n+s,t.width=r.width*a,t.height=r.height*n,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}Nt.x=Qt.x=r.x,Nt.y=Kt.y=r.y,Vt.x=Kt.x=r.x+r.width,Vt.y=Qt.y=r.y+r.height,Nt.transform(i),Kt.transform(i),Vt.transform(i),Qt.transform(i),t.x=ge(Nt.x,Vt.x,Qt.x,Kt.x),t.y=ge(Nt.y,Vt.y,Qt.y,Kt.y);var f=ye(Nt.x,Vt.x,Qt.x,Kt.x),h=ye(Nt.y,Vt.y,Qt.y,Kt.y);t.width=f-t.x,t.height=h-t.y},e}();const $=Tf;var mo="silent";function bf(e,t,r){return{type:e,event:r,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:Cf}}function Cf(){df(this.event)}var Mf=function(e){k(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.handler=null,r}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}($r),qr=function(){function e(t,r){this.x=t,this.y=r}return e}(),Lf=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],yi=new $(0,0,0,0),wo=function(e){k(t,e);function t(r,i,a,n,o){var s=e.call(this)||this;return s._hovered=new qr(0,0),s.storage=r,s.painter=i,s.painterRoot=n,s._pointerSize=o,a=a||new Mf,s.proxy=null,s.setHandlerProxy(a),s._draggingMgr=new rf(s),s}return t.prototype.setHandlerProxy=function(r){this.proxy&&this.proxy.dispose(),r&&(j(Lf,function(i){r.on&&r.on(i,this[i],this)},this),r.handler=this),this.proxy=r},t.prototype.mousemove=function(r){var i=r.zrX,a=r.zrY,n=To(this,i,a),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var f=this._hovered=n?new qr(i,a):this.findHover(i,a),h=f.target,u=this.proxy;u.setCursor&&u.setCursor(h?h.cursor:"default"),s&&h!==s&&this.dispatchToElement(o,"mouseout",r),this.dispatchToElement(f,"mousemove",r),h&&h!==s&&this.dispatchToElement(f,"mouseover",r)},t.prototype.mouseout=function(r){var i=r.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",r),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:r})},t.prototype.resize=function(){this._hovered=new qr(0,0)},t.prototype.dispatch=function(r,i){var a=this[r];a&&a.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(r){var i=this.proxy;i.setCursor&&i.setCursor(r)},t.prototype.dispatchToElement=function(r,i,a){r=r||{};var n=r.target;if(!(n&&n.silent)){for(var o="on"+i,s=bf(i,r,a);n&&(n[o]&&(s.cancelBubble=!!n[o].call(n,s)),n.trigger(i,s),n=n.__hostTarget?n.__hostTarget:n.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(f){typeof f[o]=="function"&&f[o].call(f,s),f.trigger&&f.trigger(i,s)}))}},t.prototype.findHover=function(r,i,a){var n=this.storage.getDisplayList(),o=new qr(r,i);if(Xa(n,o,r,i,a),this._pointerSize&&!o.target){for(var s=[],f=this._pointerSize,h=f/2,u=new $(r-h,i-h,f,f),v=n.length-1;v>=0;v--){var l=n[v];l!==a&&!l.ignore&&!l.ignoreCoarsePointer&&(!l.parent||!l.parent.ignoreCoarsePointer)&&(yi.copy(l.getBoundingRect()),l.transform&&yi.applyTransform(l.transform),yi.intersect(u)&&s.push(l))}if(s.length)for(var c=4,_=Math.PI/12,g=Math.PI*2,d=0;d<h;d+=c)for(var p=0;p<g;p+=_){var y=r+d*Math.cos(p),m=i+d*Math.sin(p);if(Xa(s,o,y,m,a),o.target)return o}}return o},t.prototype.processGesture=function(r,i){this._gestureMgr||(this._gestureMgr=new pf);var a=this._gestureMgr;i==="start"&&a.clear();var n=a.recognize(r,this.findHover(r.zrX,r.zrY,null).target,this.proxy.dom);if(i==="end"&&a.clear(),n){var o=n.type;r.gestureEvent=o;var s=new qr;s.target=n.target,this.dispatchToElement(s,o,n.event)}},t}($r);j(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(e){wo.prototype[e]=function(t){var r=t.zrX,i=t.zrY,a=To(this,r,i),n,o;if((e!=="mouseup"||!a)&&(n=this.findHover(r,i),o=n.target),e==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(e==="mouseup")this._upEl=o;else if(e==="click"){if(this._downEl!==this._upEl||!this._downPoint||Ks(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,e,t)}});function Sf(e,t,r){if(e[e.rectHover?"rectContain":"contain"](t,r)){for(var i=e,a=void 0,n=!1;i;){if(i.ignoreClip&&(n=!0),!n){var o=i.getClipPath();if(o&&!o.contain(t,r))return!1}i.silent&&(a=!0);var s=i.__hostTarget;i=s||i.parent}return a?mo:!0}return!1}function Xa(e,t,r,i,a){for(var n=e.length-1;n>=0;n--){var o=e[n],s=void 0;if(o!==a&&!o.ignore&&(s=Sf(o,r,i))&&(!t.topTarget&&(t.topTarget=o),s!==mo)){t.target=o;break}}}function To(e,t,r){var i=e.painter;return t<0||t>i.getWidth()||r<0||r>i.getHeight()}const Pf=wo;var bo=32,Ur=7;function Rf(e){for(var t=0;e>=bo;)t|=e&1,e>>=1;return e+t}function Ga(e,t,r,i){var a=t+1;if(a===r)return 1;if(i(e[a++],e[t])<0){for(;a<r&&i(e[a],e[a-1])<0;)a++;Df(e,t,a)}else for(;a<r&&i(e[a],e[a-1])>=0;)a++;return a-t}function Df(e,t,r){for(r--;t<r;){var i=e[t];e[t++]=e[r],e[r--]=i}}function qa(e,t,r,i,a){for(i===t&&i++;i<r;i++){for(var n=e[i],o=t,s=i,f;o<s;)f=o+s>>>1,a(n,e[f])<0?s=f:o=f+1;var h=i-o;switch(h){case 3:e[o+3]=e[o+2];case 2:e[o+2]=e[o+1];case 1:e[o+1]=e[o];break;default:for(;h>0;)e[o+h]=e[o+h-1],h--}e[o]=n}}function mi(e,t,r,i,a,n){var o=0,s=0,f=1;if(n(e,t[r+a])>0){for(s=i-a;f<s&&n(e,t[r+a+f])>0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s),o+=a,f+=a}else{for(s=a+1;f<s&&n(e,t[r+a-f])<=0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s);var h=o;o=a-f,f=a-h}for(o++;o<f;){var u=o+(f-o>>>1);n(e,t[r+u])>0?o=u+1:f=u}return f}function wi(e,t,r,i,a,n){var o=0,s=0,f=1;if(n(e,t[r+a])<0){for(s=a+1;f<s&&n(e,t[r+a-f])<0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s);var h=o;o=a-f,f=a-h}else{for(s=i-a;f<s&&n(e,t[r+a+f])>=0;)o=f,f=(f<<1)+1,f<=0&&(f=s);f>s&&(f=s),o+=a,f+=a}for(o++;o<f;){var u=o+(f-o>>>1);n(e,t[r+u])<0?f=u:o=u+1}return f}function xf(e,t){var r=Ur,i,a,n=0,o=[];i=[],a=[];function s(c,_){i[n]=c,a[n]=_,n+=1}function f(){for(;n>1;){var c=n-2;if(c>=1&&a[c-1]<=a[c]+a[c+1]||c>=2&&a[c-2]<=a[c]+a[c-1])a[c-1]<a[c+1]&&c--;else if(a[c]>a[c+1])break;u(c)}}function h(){for(;n>1;){var c=n-2;c>0&&a[c-1]<a[c+1]&&c--,u(c)}}function u(c){var _=i[c],g=a[c],d=i[c+1],p=a[c+1];a[c]=g+p,c===n-3&&(i[c+1]=i[c+2],a[c+1]=a[c+2]),n--;var y=wi(e[d],e,_,g,0,t);_+=y,g-=y,g!==0&&(p=mi(e[_+g-1],e,d,p,p-1,t),p!==0&&(g<=p?v(_,g,d,p):l(_,g,d,p)))}function v(c,_,g,d){var p=0;for(p=0;p<_;p++)o[p]=e[c+p];var y=0,m=g,w=c;if(e[w++]=e[m++],--d===0){for(p=0;p<_;p++)e[w+p]=o[y+p];return}if(_===1){for(p=0;p<d;p++)e[w+p]=e[m+p];e[w+d]=o[y];return}for(var C=r,T,b,M;;){T=0,b=0,M=!1;do if(t(e[m],o[y])<0){if(e[w++]=e[m++],b++,T=0,--d===0){M=!0;break}}else if(e[w++]=o[y++],T++,b=0,--_===1){M=!0;break}while((T|b)<C);if(M)break;do{if(T=wi(e[m],o,y,_,0,t),T!==0){for(p=0;p<T;p++)e[w+p]=o[y+p];if(w+=T,y+=T,_-=T,_<=1){M=!0;break}}if(e[w++]=e[m++],--d===0){M=!0;break}if(b=mi(o[y],e,m,d,0,t),b!==0){for(p=0;p<b;p++)e[w+p]=e[m+p];if(w+=b,m+=b,d-=b,d===0){M=!0;break}}if(e[w++]=o[y++],--_===1){M=!0;break}C--}while(T>=Ur||b>=Ur);if(M)break;C<0&&(C=0),C+=2}if(r=C,r<1&&(r=1),_===1){for(p=0;p<d;p++)e[w+p]=e[m+p];e[w+d]=o[y]}else{if(_===0)throw new Error;for(p=0;p<_;p++)e[w+p]=o[y+p]}}function l(c,_,g,d){var p=0;for(p=0;p<d;p++)o[p]=e[g+p];var y=c+_-1,m=d-1,w=g+d-1,C=0,T=0;if(e[w--]=e[y--],--_===0){for(C=w-(d-1),p=0;p<d;p++)e[C+p]=o[p];return}if(d===1){for(w-=_,y-=_,T=w+1,C=y+1,p=_-1;p>=0;p--)e[T+p]=e[C+p];e[w]=o[m];return}for(var b=r;;){var M=0,L=0,S=!1;do if(t(o[m],e[y])<0){if(e[w--]=e[y--],M++,L=0,--_===0){S=!0;break}}else if(e[w--]=o[m--],L++,M=0,--d===1){S=!0;break}while((M|L)<b);if(S)break;do{if(M=_-wi(o[m],e,c,_,_-1,t),M!==0){for(w-=M,y-=M,_-=M,T=w+1,C=y+1,p=M-1;p>=0;p--)e[T+p]=e[C+p];if(_===0){S=!0;break}}if(e[w--]=o[m--],--d===1){S=!0;break}if(L=d-mi(e[y],o,0,d,d-1,t),L!==0){for(w-=L,m-=L,d-=L,T=w+1,C=m+1,p=0;p<L;p++)e[T+p]=o[C+p];if(d<=1){S=!0;break}}if(e[w--]=e[y--],--_===0){S=!0;break}b--}while(M>=Ur||L>=Ur);if(S)break;b<0&&(b=0),b+=2}if(r=b,r<1&&(r=1),d===1){for(w-=_,y-=_,T=w+1,C=y+1,p=_-1;p>=0;p--)e[T+p]=e[C+p];e[w]=o[m]}else{if(d===0)throw new Error;for(C=w-(d-1),p=0;p<d;p++)e[C+p]=o[p]}}return{mergeRuns:f,forceMergeRuns:h,pushRun:s}}function Af(e,t,r,i){r||(r=0),i||(i=e.length);var a=i-r;if(!(a<2)){var n=0;if(a<bo){n=Ga(e,r,i,t),qa(e,r,i,r+n,t);return}var o=xf(e,t),s=Rf(a);do{if(n=Ga(e,r,i,t),n<s){var f=a;f>s&&(f=s),qa(e,r,r+f,r+n,t),n=f}o.pushRun(r,n),o.mergeRuns(),a-=n,r+=n}while(a!==0);o.forceMergeRuns()}}var ht=1,Kr=2,Mr=4,Ua=!1;function Ti(){Ua||(Ua=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Za(e,t){return e.zlevel===t.zlevel?e.z===t.z?e.z2-t.z2:e.z-t.z:e.zlevel-t.zlevel}var Ff=function(){function e(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Za}return e.prototype.traverse=function(t,r){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,r)},e.prototype.getDisplayList=function(t,r){r=r||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(r),i},e.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var r=this._roots,i=this._displayList,a=0,n=r.length;a<n;a++)this._updateAndAddDisplayable(r[a],null,t);i.length=this._displayListLen,Af(i,Za)},e.prototype._updateAndAddDisplayable=function(t,r,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var a=t.getClipPath();if(t.ignoreClip)r=null;else if(a){r?r=r.slice():r=[];for(var n=a,o=t;n;)n.parent=o,n.updateTransform(),r.push(n),o=n,n=n.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),f=0;f<s.length;f++){var h=s[f];t.__dirty&&(h.__dirty|=ht),this._updateAndAddDisplayable(h,r,i)}t.__dirty=0}else{var u=t;r&&r.length?u.__clipPaths=r:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(Ti(),u.z=0),isNaN(u.z2)&&(Ti(),u.z2=0),isNaN(u.zlevel)&&(Ti(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var v=t.getDecalElement&&t.getDecalElement();v&&this._updateAndAddDisplayable(v,r,i);var l=t.getTextGuideLine();l&&this._updateAndAddDisplayable(l,r,i);var c=t.getTextContent();c&&this._updateAndAddDisplayable(c,r,i)}},e.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},e.prototype.delRoot=function(t){if(t instanceof Array){for(var r=0,i=t.length;r<i;r++)this.delRoot(t[r]);return}var a=Rt(this._roots,t);a>=0&&this._roots.splice(a,1)},e.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},e.prototype.getRoots=function(){return this._roots},e.prototype.dispose=function(){this._displayList=null,this._roots=null},e}();const Ef=Ff;var Co;Co=K.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(e){return setTimeout(e,16)};const aa=Co;var ze={linear:function(e){return e},quadraticIn:function(e){return e*e},quadraticOut:function(e){return e*(2-e)},quadraticInOut:function(e){return(e*=2)<1?.5*e*e:-.5*(--e*(e-2)-1)},cubicIn:function(e){return e*e*e},cubicOut:function(e){return--e*e*e+1},cubicInOut:function(e){return(e*=2)<1?.5*e*e*e:.5*((e-=2)*e*e+2)},quarticIn:function(e){return e*e*e*e},quarticOut:function(e){return 1- --e*e*e*e},quarticInOut:function(e){return(e*=2)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2)},quinticIn:function(e){return e*e*e*e*e},quinticOut:function(e){return--e*e*e*e*e+1},quinticInOut:function(e){return(e*=2)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2)},sinusoidalIn:function(e){return 1-Math.cos(e*Math.PI/2)},sinusoidalOut:function(e){return Math.sin(e*Math.PI/2)},sinusoidalInOut:function(e){return .5*(1-Math.cos(Math.PI*e))},exponentialIn:function(e){return e===0?0:Math.pow(1024,e-1)},exponentialOut:function(e){return e===1?1:1-Math.pow(2,-10*e)},exponentialInOut:function(e){return e===0?0:e===1?1:(e*=2)<1?.5*Math.pow(1024,e-1):.5*(-Math.pow(2,-10*(e-1))+2)},circularIn:function(e){return 1-Math.sqrt(1-e*e)},circularOut:function(e){return Math.sqrt(1- --e*e)},circularInOut:function(e){return(e*=2)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1)},elasticIn:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),-(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)))},elasticOut:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*e)*Math.sin((e-t)*(2*Math.PI)/i)+1)},elasticInOut:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),(e*=2)<1?-.5*(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)):r*Math.pow(2,-10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)*.5+1)},backIn:function(e){var t=1.70158;return e*e*((t+1)*e-t)},backOut:function(e){var t=1.70158;return--e*e*((t+1)*e+t)+1},backInOut:function(e){var t=2.5949095;return(e*=2)<1?.5*(e*e*((t+1)*e-t)):.5*((e-=2)*e*((t+1)*e+t)+2)},bounceIn:function(e){return 1-ze.bounceOut(1-e)},bounceOut:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},bounceInOut:function(e){return e<.5?ze.bounceIn(e*2)*.5:ze.bounceOut(e*2-1)*.5+.5}};const Mo=ze;var me=Math.pow,Xt=Math.sqrt,Ue=1e-8,Lo=1e-4,Na=Xt(3),we=1/3,Dt=Yr(),pt=Yr(),kr=Yr();function $t(e){return e>-Ue&&e<Ue}function So(e){return e>Ue||e<-Ue}function N(e,t,r,i,a){var n=1-a;return n*n*(n*e+3*a*t)+a*a*(a*i+3*n*r)}function Va(e,t,r,i,a){var n=1-a;return 3*(((t-e)*n+2*(r-t)*a)*n+(i-r)*a*a)}function Po(e,t,r,i,a,n){var o=i+3*(t-r)-e,s=3*(r-t*2+e),f=3*(t-e),h=e-a,u=s*s-3*o*f,v=s*f-9*o*h,l=f*f-3*s*h,c=0;if($t(u)&&$t(v))if($t(s))n[0]=0;else{var _=-f/s;_>=0&&_<=1&&(n[c++]=_)}else{var g=v*v-4*u*l;if($t(g)){var d=v/u,_=-s/o+d,p=-d/2;_>=0&&_<=1&&(n[c++]=_),p>=0&&p<=1&&(n[c++]=p)}else if(g>0){var y=Xt(g),m=u*s+1.5*o*(-v+y),w=u*s+1.5*o*(-v-y);m<0?m=-me(-m,we):m=me(m,we),w<0?w=-me(-w,we):w=me(w,we);var _=(-s-(m+w))/(3*o);_>=0&&_<=1&&(n[c++]=_)}else{var C=(2*u*s-3*o*v)/(2*Xt(u*u*u)),T=Math.acos(C)/3,b=Xt(u),M=Math.cos(T),_=(-s-2*b*M)/(3*o),p=(-s+b*(M+Na*Math.sin(T)))/(3*o),L=(-s+b*(M-Na*Math.sin(T)))/(3*o);_>=0&&_<=1&&(n[c++]=_),p>=0&&p<=1&&(n[c++]=p),L>=0&&L<=1&&(n[c++]=L)}}return c}function Ro(e,t,r,i,a){var n=6*r-12*t+6*e,o=9*t+3*i-3*e-9*r,s=3*t-3*e,f=0;if($t(o)){if(So(n)){var h=-s/n;h>=0&&h<=1&&(a[f++]=h)}}else{var u=n*n-4*o*s;if($t(u))a[0]=-n/(2*o);else if(u>0){var v=Xt(u),h=(-n+v)/(2*o),l=(-n-v)/(2*o);h>=0&&h<=1&&(a[f++]=h),l>=0&&l<=1&&(a[f++]=l)}}return f}function Gt(e,t,r,i,a,n){var o=(t-e)*a+e,s=(r-t)*a+t,f=(i-r)*a+r,h=(s-o)*a+o,u=(f-s)*a+s,v=(u-h)*a+h;n[0]=e,n[1]=o,n[2]=h,n[3]=v,n[4]=v,n[5]=u,n[6]=f,n[7]=i}function If(e,t,r,i,a,n,o,s,f,h,u){var v,l=.005,c=1/0,_,g,d,p;Dt[0]=f,Dt[1]=h;for(var y=0;y<1;y+=.05)pt[0]=N(e,r,a,o,y),pt[1]=N(t,i,n,s,y),d=Hr(Dt,pt),d<c&&(v=y,c=d);c=1/0;for(var m=0;m<32&&!(l<Lo);m++)_=v-l,g=v+l,pt[0]=N(e,r,a,o,_),pt[1]=N(t,i,n,s,_),d=Hr(pt,Dt),_>=0&&d<c?(v=_,c=d):(kr[0]=N(e,r,a,o,g),kr[1]=N(t,i,n,s,g),p=Hr(kr,Dt),g<=1&&p<c?(v=g,c=p):l*=.5);return u&&(u[0]=N(e,r,a,o,v),u[1]=N(t,i,n,s,v)),Xt(c)}function Of(e,t,r,i,a,n,o,s,f){for(var h=e,u=t,v=0,l=1/f,c=1;c<=f;c++){var _=c*l,g=N(e,r,a,o,_),d=N(t,i,n,s,_),p=g-h,y=d-u;v+=Math.sqrt(p*p+y*y),h=g,u=d}return v}function Q(e,t,r,i){var a=1-i;return a*(a*e+2*i*t)+i*i*r}function Qa(e,t,r,i){return 2*((1-i)*(t-e)+i*(r-t))}function Hf(e,t,r,i,a){var n=e-2*t+r,o=2*(t-e),s=e-i,f=0;if($t(n)){if(So(o)){var h=-s/o;h>=0&&h<=1&&(a[f++]=h)}}else{var u=o*o-4*n*s;if($t(u)){var h=-o/(2*n);h>=0&&h<=1&&(a[f++]=h)}else if(u>0){var v=Xt(u),h=(-o+v)/(2*n),l=(-o-v)/(2*n);h>=0&&h<=1&&(a[f++]=h),l>=0&&l<=1&&(a[f++]=l)}}return f}function Do(e,t,r){var i=e+r-2*t;return i===0?.5:(e-t)/i}function Ze(e,t,r,i,a){var n=(t-e)*i+e,o=(r-t)*i+t,s=(o-n)*i+n;a[0]=e,a[1]=n,a[2]=s,a[3]=s,a[4]=o,a[5]=r}function Bf(e,t,r,i,a,n,o,s,f){var h,u=.005,v=1/0;Dt[0]=o,Dt[1]=s;for(var l=0;l<1;l+=.05){pt[0]=Q(e,r,a,l),pt[1]=Q(t,i,n,l);var c=Hr(Dt,pt);c<v&&(h=l,v=c)}v=1/0;for(var _=0;_<32&&!(u<Lo);_++){var g=h-u,d=h+u;pt[0]=Q(e,r,a,g),pt[1]=Q(t,i,n,g);var c=Hr(pt,Dt);if(g>=0&&c<v)h=g,v=c;else{kr[0]=Q(e,r,a,d),kr[1]=Q(t,i,n,d);var p=Hr(kr,Dt);d<=1&&p<v?(h=d,v=p):u*=.5}}return f&&(f[0]=Q(e,r,a,h),f[1]=Q(t,i,n,h)),Xt(v)}function kf(e,t,r,i,a,n,o){for(var s=e,f=t,h=0,u=1/o,v=1;v<=o;v++){var l=v*u,c=Q(e,r,a,l),_=Q(t,i,n,l),g=c-s,d=_-f;h+=Math.sqrt(g*g+d*d),s=c,f=_}return h}var zf=/cubic-bezier\(([0-9,\.e ]+)\)/;function xo(e){var t=e&&zf.exec(e);if(t){var r=t[1].split(","),i=+Rr(r[0]),a=+Rr(r[1]),n=+Rr(r[2]),o=+Rr(r[3]);if(isNaN(i+a+n+o))return;var s=[];return function(f){return f<=0?0:f>=1?1:Po(0,i,n,1,f,s)&&N(0,a,o,1,s[0])}}}var Yf=function(){function e(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Or,this.ondestroy=t.ondestroy||Or,this.onrestart=t.onrestart||Or,t.easing&&this.setEasing(t.easing)}return e.prototype.step=function(t,r){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=r;return}var i=this._life,a=t-this._startTime-this._pausedTime,n=a/i;n<0&&(n=0),n=Math.min(n,1);var o=this.easingFunc,s=o?o(n):n;if(this.onframe(s),n===1)if(this.loop){var f=a%i;this._startTime=t-f,this._pausedTime=0,this.onrestart()}else return!0;return!1},e.prototype.pause=function(){this._paused=!0},e.prototype.resume=function(){this._paused=!1},e.prototype.setEasing=function(t){this.easing=t,this.easingFunc=si(t)?t:Mo[t]||xo(t)},e}();const $f=Yf;var Ao=function(){function e(t){this.value=t}return e}(),Wf=function(){function e(){this._len=0}return e.prototype.insert=function(t){var r=new Ao(t);return this.insertEntry(r),r},e.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},e.prototype.remove=function(t){var r=t.prev,i=t.next;r?r.next=i:this.head=i,i?i.prev=r:this.tail=r,t.next=t.prev=null,this._len--},e.prototype.len=function(){return this._len},e.prototype.clear=function(){this.head=this.tail=null,this._len=0},e}(),Xf=function(){function e(t){this._list=new Wf,this._maxSize=10,this._map={},this._maxSize=t}return e.prototype.put=function(t,r){var i=this._list,a=this._map,n=null;if(a[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var f=i.head;i.remove(f),delete a[f.key],n=f.value,this._lastRemovedEntry=f}s?s.value=r:s=new Ao(r),s.key=t,i.insertEntry(s),a[t]=s}return n},e.prototype.get=function(t){var r=this._map[t],i=this._list;if(r!=null)return r!==i.tail&&(i.remove(r),i.insertEntry(r)),r.value},e.prototype.clear=function(){this._list.clear(),this._map={}},e.prototype.len=function(){return this._list.len()},e}();const ui=Xf;var Ka={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function wt(e){return e=Math.round(e),e<0?0:e>255?255:e}function Gf(e){return e=Math.round(e),e<0?0:e>360?360:e}function fe(e){return e<0?0:e>1?1:e}function bi(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?wt(parseFloat(t)/100*255):wt(parseInt(t,10))}function lr(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?fe(parseFloat(t)/100):fe(parseFloat(t))}function Ci(e,t,r){return r<0?r+=1:r>1&&(r-=1),r*6<1?e+(t-e)*r*6:r*2<1?t:r*3<2?e+(t-e)*(2/3-r)*6:e}function Wt(e,t,r){return e+(t-e)*r}function ct(e,t,r,i,a){return e[0]=t,e[1]=r,e[2]=i,e[3]=a,e}function na(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e}var Fo=new ui(20),Te=null;function gr(e,t){Te&&na(Te,t),Te=Fo.put(e,Te||t.slice())}function xt(e,t){if(!!e){t=t||[];var r=Fo.get(e);if(r)return na(t,r);e=e+"";var i=e.replace(/ /g,"").toLowerCase();if(i in Ka)return na(t,Ka[i]),gr(e,t),t;var a=i.length;if(i.charAt(0)==="#"){if(a===4||a===5){var n=parseInt(i.slice(1,4),16);if(!(n>=0&&n<=4095)){ct(t,0,0,0,1);return}return ct(t,(n&3840)>>4|(n&3840)>>8,n&240|(n&240)>>4,n&15|(n&15)<<4,a===5?parseInt(i.slice(4),16)/15:1),gr(e,t),t}else if(a===7||a===9){var n=parseInt(i.slice(1,7),16);if(!(n>=0&&n<=16777215)){ct(t,0,0,0,1);return}return ct(t,(n&16711680)>>16,(n&65280)>>8,n&255,a===9?parseInt(i.slice(7),16)/255:1),gr(e,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===a){var f=i.substr(0,o),h=i.substr(o+1,s-(o+1)).split(","),u=1;switch(f){case"rgba":if(h.length!==4)return h.length===3?ct(t,+h[0],+h[1],+h[2],1):ct(t,0,0,0,1);u=lr(h.pop());case"rgb":if(h.length>=3)return ct(t,bi(h[0]),bi(h[1]),bi(h[2]),h.length===3?u:lr(h[3])),gr(e,t),t;ct(t,0,0,0,1);return;case"hsla":if(h.length!==4){ct(t,0,0,0,1);return}return h[3]=lr(h[3]),oa(h,t),gr(e,t),t;case"hsl":if(h.length!==3){ct(t,0,0,0,1);return}return oa(h,t),gr(e,t),t;default:return}}ct(t,0,0,0,1)}}function oa(e,t){var r=(parseFloat(e[0])%360+360)%360/360,i=lr(e[1]),a=lr(e[2]),n=a<=.5?a*(i+1):a+i-a*i,o=a*2-n;return t=t||[],ct(t,wt(Ci(o,n,r+1/3)*255),wt(Ci(o,n,r)*255),wt(Ci(o,n,r-1/3)*255),1),e.length===4&&(t[3]=e[3]),t}function qf(e){if(!!e){var t=e[0]/255,r=e[1]/255,i=e[2]/255,a=Math.min(t,r,i),n=Math.max(t,r,i),o=n-a,s=(n+a)/2,f,h;if(o===0)f=0,h=0;else{s<.5?h=o/(n+a):h=o/(2-n-a);var u=((n-t)/6+o/2)/o,v=((n-r)/6+o/2)/o,l=((n-i)/6+o/2)/o;t===n?f=l-v:r===n?f=1/3+u-l:i===n&&(f=2/3+v-u),f<0&&(f+=1),f>1&&(f-=1)}var c=[f*360,h,s];return e[3]!=null&&c.push(e[3]),c}}function Ja(e,t){var r=xt(e);if(r){for(var i=0;i<3;i++)t<0?r[i]=r[i]*(1-t)|0:r[i]=(255-r[i])*t+r[i]|0,r[i]>255?r[i]=255:r[i]<0&&(r[i]=0);return de(r,r.length===4?"rgba":"rgb")}}function al(e,t,r){if(!(!(t&&t.length)||!(e>=0&&e<=1))){r=r||[];var i=e*(t.length-1),a=Math.floor(i),n=Math.ceil(i),o=t[a],s=t[n],f=i-a;return r[0]=wt(Wt(o[0],s[0],f)),r[1]=wt(Wt(o[1],s[1],f)),r[2]=wt(Wt(o[2],s[2],f)),r[3]=fe(Wt(o[3],s[3],f)),r}}function nl(e,t,r){if(!(!(t&&t.length)||!(e>=0&&e<=1))){var i=e*(t.length-1),a=Math.floor(i),n=Math.ceil(i),o=xt(t[a]),s=xt(t[n]),f=i-a,h=de([wt(Wt(o[0],s[0],f)),wt(Wt(o[1],s[1],f)),wt(Wt(o[2],s[2],f)),fe(Wt(o[3],s[3],f))],"rgba");return r?{color:h,leftIndex:a,rightIndex:n,value:i}:h}}function ol(e,t,r,i){var a=xt(e);if(e)return a=qf(a),t!=null&&(a[0]=Gf(t)),r!=null&&(a[1]=lr(r)),i!=null&&(a[2]=lr(i)),de(oa(a),"rgba")}function sl(e,t){var r=xt(e);if(r&&t!=null)return r[3]=fe(t),de(r,"rgba")}function de(e,t){if(!(!e||!e.length)){var r=e[0]+","+e[1]+","+e[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(r+=","+e[3]),t+"("+r+")"}}function Ne(e,t){var r=xt(e);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*t:0}var ja=new ui(100);function fl(e){if(se(e)){var t=ja.get(e);return t||(t=Ja(e,-.1),ja.put(e,t)),t}else if(fi(e)){var r=Y({},e);return r.colorStops=Tt(e.colorStops,function(i){return{offset:i.offset,color:Ja(i.color,-.1)}}),r}return e}function Uf(e){return e.type==="linear"}function Zf(e){return e.type==="radial"}(function(){return K.hasGlobalWindow&&si(window.btoa)?function(e){return window.btoa(unescape(encodeURIComponent(e)))}:typeof Buffer<"u"?function(e){return Buffer.from(e).toString("base64")}:function(e){return null}})();var sa=Array.prototype.slice;function It(e,t,r){return(t-e)*r+e}function Mi(e,t,r,i){for(var a=t.length,n=0;n<a;n++)e[n]=It(t[n],r[n],i);return e}function Nf(e,t,r,i){for(var a=t.length,n=a&&t[0].length,o=0;o<a;o++){e[o]||(e[o]=[]);for(var s=0;s<n;s++)e[o][s]=It(t[o][s],r[o][s],i)}return e}function be(e,t,r,i){for(var a=t.length,n=0;n<a;n++)e[n]=t[n]+r[n]*i;return e}function tn(e,t,r,i){for(var a=t.length,n=a&&t[0].length,o=0;o<a;o++){e[o]||(e[o]=[]);for(var s=0;s<n;s++)e[o][s]=t[o][s]+r[o][s]*i}return e}function Vf(e,t){for(var r=e.length,i=t.length,a=r>i?t:e,n=Math.min(r,i),o=a[n-1]||{color:[0,0,0,0],offset:0},s=n;s<Math.max(r,i);s++)a.push({offset:o.offset,color:o.color.slice()})}function Qf(e,t,r){var i=e,a=t;if(!(!i.push||!a.push)){var n=i.length,o=a.length;if(n!==o){var s=n>o;if(s)i.length=o;else for(var f=n;f<o;f++)i.push(r===1?a[f]:sa.call(a[f]))}for(var h=i[0]&&i[0].length,f=0;f<i.length;f++)if(r===1)isNaN(i[f])&&(i[f]=a[f]);else for(var u=0;u<h;u++)isNaN(i[f][u])&&(i[f][u]=a[f][u])}}function Ye(e){if(At(e)){var t=e.length;if(At(e[0])){for(var r=[],i=0;i<t;i++)r.push(sa.call(e[i]));return r}return sa.call(e)}return e}function $e(e){return e[0]=Math.floor(e[0])||0,e[1]=Math.floor(e[1])||0,e[2]=Math.floor(e[2])||0,e[3]=e[3]==null?1:e[3],"rgba("+e.join(",")+")"}function Kf(e){return At(e&&e[0])?2:1}var Ce=0,We=1,Eo=2,Jr=3,fa=4,ha=5,rn=6;function en(e){return e===fa||e===ha}function Me(e){return e===We||e===Eo}var Zr=[0,0,0,0],Jf=function(){function e(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return e.prototype.isFinished=function(){return this._finished},e.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},e.prototype.needsAnimate=function(){return this.keyframes.length>=1},e.prototype.getAdditiveTrack=function(){return this._additiveTrack},e.prototype.addKeyframe=function(t,r,i){this._needsSort=!0;var a=this.keyframes,n=a.length,o=!1,s=rn,f=r;if(At(r)){var h=Kf(r);s=h,(h===1&&!Be(r[0])||h===2&&!Be(r[0][0]))&&(o=!0)}else if(Be(r)&&!Ys(r))s=Ce;else if(se(r))if(!isNaN(+r))s=Ce;else{var u=xt(r);u&&(f=u,s=Jr)}else if(fi(r)){var v=Y({},f);v.colorStops=Tt(r.colorStops,function(c){return{offset:c.offset,color:xt(c.color)}}),Uf(r)?s=fa:Zf(r)&&(s=ha),f=v}n===0?this.valType=s:(s!==this.valType||s===rn)&&(o=!0),this.discrete=this.discrete||o;var l={time:t,value:f,rawValue:r,percent:0};return i&&(l.easing=i,l.easingFunc=si(i)?i:Mo[i]||xo(i)),a.push(l),l},e.prototype.prepare=function(t,r){var i=this.keyframes;this._needsSort&&i.sort(function(g,d){return g.time-d.time});for(var a=this.valType,n=i.length,o=i[n-1],s=this.discrete,f=Me(a),h=en(a),u=0;u<n;u++){var v=i[u],l=v.value,c=o.value;v.percent=v.time/t,s||(f&&u!==n-1?Qf(l,c,a):h&&Vf(l.colorStops,c.colorStops))}if(!s&&a!==ha&&r&&this.needsAnimate()&&r.needsAnimate()&&a===r.valType&&!r._finished){this._additiveTrack=r;for(var _=i[0].value,u=0;u<n;u++)a===Ce?i[u].additiveValue=i[u].value-_:a===Jr?i[u].additiveValue=be([],i[u].value,_,-1):Me(a)&&(i[u].additiveValue=a===We?be([],i[u].value,_,-1):tn([],i[u].value,_,-1))}},e.prototype.step=function(t,r){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,a=i?"additiveValue":"value",n=this.valType,o=this.keyframes,s=o.length,f=this.propName,h=n===Jr,u,v=this._lastFr,l=Math.min,c,_;if(s===1)c=_=o[0];else{if(r<0)u=0;else if(r<this._lastFrP){var g=l(v+1,s-1);for(u=g;u>=0&&!(o[u].percent<=r);u--);u=l(u,s-2)}else{for(u=v;u<s&&!(o[u].percent>r);u++);u=l(u-1,s-2)}_=o[u+1],c=o[u]}if(!!(c&&_)){this._lastFr=u,this._lastFrP=r;var d=_.percent-c.percent,p=d===0?1:l((r-c.percent)/d,1);_.easingFunc&&(p=_.easingFunc(p));var y=i?this._additiveValue:h?Zr:t[f];if((Me(n)||h)&&!y&&(y=this._additiveValue=[]),this.discrete)t[f]=p<1?c.rawValue:_.rawValue;else if(Me(n))n===We?Mi(y,c[a],_[a],p):Nf(y,c[a],_[a],p);else if(en(n)){var m=c[a],w=_[a],C=n===fa;t[f]={type:C?"linear":"radial",x:It(m.x,w.x,p),y:It(m.y,w.y,p),colorStops:Tt(m.colorStops,function(b,M){var L=w.colorStops[M];return{offset:It(b.offset,L.offset,p),color:$e(Mi([],b.color,L.color,p))}}),global:w.global},C?(t[f].x2=It(m.x2,w.x2,p),t[f].y2=It(m.y2,w.y2,p)):t[f].r=It(m.r,w.r,p)}else if(h)Mi(y,c[a],_[a],p),i||(t[f]=$e(y));else{var T=It(c[a],_[a],p);i?this._additiveValue=T:t[f]=T}i&&this._addToTarget(t)}}},e.prototype._addToTarget=function(t){var r=this.valType,i=this.propName,a=this._additiveValue;r===Ce?t[i]=t[i]+a:r===Jr?(xt(t[i],Zr),be(Zr,Zr,a,1),t[i]=$e(Zr)):r===We?be(t[i],t[i],a,1):r===Eo&&tn(t[i],t[i],a,1)},e}(),xa=function(){function e(t,r,i,a){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=r,r&&a){Ra("Can' use additive animation on looped animation.");return}this._additiveAnimators=a,this._allowDiscrete=i}return e.prototype.getMaxTime=function(){return this._maxTime},e.prototype.getDelay=function(){return this._delay},e.prototype.getLoop=function(){return this._loop},e.prototype.getTarget=function(){return this._target},e.prototype.changeTarget=function(t){this._target=t},e.prototype.when=function(t,r,i){return this.whenWithKeys(t,r,tt(r),i)},e.prototype.whenWithKeys=function(t,r,i,a){for(var n=this._tracks,o=0;o<i.length;o++){var s=i[o],f=n[s];if(!f){f=n[s]=new Jf(s);var h=void 0,u=this._getAdditiveTrack(s);if(u){var v=u.keyframes,l=v[v.length-1];h=l&&l.value,u.valType===Jr&&h&&(h=$e(h))}else h=this._target[s];if(h==null)continue;t>0&&f.addKeyframe(0,Ye(h),a),this._trackKeys.push(s)}f.addKeyframe(t,Ye(r[s]),a)}return this._maxTime=Math.max(this._maxTime,t),this},e.prototype.pause=function(){this._clip.pause(),this._paused=!0},e.prototype.resume=function(){this._clip.resume(),this._paused=!1},e.prototype.isPaused=function(){return!!this._paused},e.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},e.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var r=t.length,i=0;i<r;i++)t[i].call(this)},e.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,r=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,r)for(var i=0;i<r.length;i++)r[i].call(this)},e.prototype._setTracksFinished=function(){for(var t=this._tracks,r=this._trackKeys,i=0;i<r.length;i++)t[r[i]].setFinished()},e.prototype._getAdditiveTrack=function(t){var r,i=this._additiveAnimators;if(i)for(var a=0;a<i.length;a++){var n=i[a].getTrack(t);n&&(r=n)}return r},e.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var r=this,i=[],a=this._maxTime||0,n=0;n<this._trackKeys.length;n++){var o=this._trackKeys[n],s=this._tracks[o],f=this._getAdditiveTrack(o),h=s.keyframes,u=h.length;if(s.prepare(a,f),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var v=h[u-1];v&&(r._target[s.propName]=v.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var l=new $f({life:a,loop:this._loop,delay:this._delay||0,onframe:function(c){r._started=2;var _=r._additiveAnimators;if(_){for(var g=!1,d=0;d<_.length;d++)if(_[d]._clip){g=!0;break}g||(r._additiveAnimators=null)}for(var d=0;d<i.length;d++)i[d].step(r._target,c);var p=r._onframeCbs;if(p)for(var d=0;d<p.length;d++)p[d](r._target,c)},ondestroy:function(){r._doneCallback()}});this._clip=l,this.animation&&this.animation.addClip(l),t&&l.setEasing(t)}else this._doneCallback();return this}},e.prototype.stop=function(t){if(!!this._clip){var r=this._clip;t&&r.onframe(1),this._abortedCallback()}},e.prototype.delay=function(t){return this._delay=t,this},e.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},e.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},e.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},e.prototype.getClip=function(){return this._clip},e.prototype.getTrack=function(t){return this._tracks[t]},e.prototype.getTracks=function(){var t=this;return Tt(this._trackKeys,function(r){return t._tracks[r]})},e.prototype.stopTracks=function(t,r){if(!t.length||!this._clip)return!0;for(var i=this._tracks,a=this._trackKeys,n=0;n<t.length;n++){var o=i[t[n]];o&&!o.isFinished()&&(r?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,n=0;n<a.length;n++)if(!i[a[n]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},e.prototype.saveTo=function(t,r,i){if(!!t){r=r||this._trackKeys;for(var a=0;a<r.length;a++){var n=r[a],o=this._tracks[n];if(!(!o||o.isFinished())){var s=o.keyframes,f=s[i?0:s.length-1];f&&(t[n]=Ye(f.rawValue))}}}},e.prototype.__changeFinalValue=function(t,r){r=r||tt(t);for(var i=0;i<r.length;i++){var a=r[i],n=this._tracks[a];if(!!n){var o=n.keyframes;if(o.length>1){var s=o.pop();n.addKeyframe(s.time,t[a]),n.prepare(this._maxTime,n.getAdditiveTrack())}}}},e}();function Ar(){return new Date().getTime()}var jf=function(e){k(t,e);function t(r){var i=e.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,r=r||{},i.stage=r.stage||{},i}return t.prototype.addClip=function(r){r.animation&&this.removeClip(r),this._head?(this._tail.next=r,r.prev=this._tail,r.next=null,this._tail=r):this._head=this._tail=r,r.animation=this},t.prototype.addAnimator=function(r){r.animation=this;var i=r.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(r){if(!!r.animation){var i=r.prev,a=r.next;i?i.next=a:this._head=a,a?a.prev=i:this._tail=i,r.next=r.prev=r.animation=null}},t.prototype.removeAnimator=function(r){var i=r.getClip();i&&this.removeClip(i),r.animation=null},t.prototype.update=function(r){for(var i=Ar()-this._pausedTime,a=i-this._time,n=this._head;n;){var o=n.next,s=n.step(i,a);s&&(n.ondestroy(),this.removeClip(n)),n=o}this._time=i,r||(this.trigger("frame",a),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var r=this;this._running=!0;function i(){r._running&&(aa(i),!r._paused&&r.update())}aa(i)},t.prototype.start=function(){this._running||(this._time=Ar(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Ar(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Ar()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var r=this._head;r;){var i=r.next;r.prev=r.next=r.animation=null,r=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(r,i){i=i||{},this.start();var a=new xa(r,i.loop);return this.addAnimator(a),a},t}($r);const th=jf;var rh=300,Li=K.domSupported,Si=function(){var e=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],r={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=Tt(e,function(a){var n=a.replace("mouse","pointer");return r.hasOwnProperty(n)?n:a});return{mouse:e,touch:t,pointer:i}}(),an={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},nn=!1;function ua(e){var t=e.pointerType;return t==="pen"||t==="touch"}function eh(e){e.touching=!0,e.touchTimer!=null&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)}function Pi(e){e&&(e.zrByTouch=!0)}function ih(e,t){return yt(e.dom,new ah(e,t),!0)}function Io(e,t){for(var r=t,i=!1;r&&r.nodeType!==9&&!(i=r.domBelongToZr||r!==t&&r===e.painterRoot);)r=r.parentNode;return i}var ah=function(){function e(t,r){this.stopPropagation=Or,this.stopImmediatePropagation=Or,this.preventDefault=Or,this.type=r.type,this.target=this.currentTarget=t.dom,this.pointerType=r.pointerType,this.clientX=r.clientX,this.clientY=r.clientY}return e}(),mt={mousedown:function(e){e=yt(this.dom,e),this.__mayPointerCapture=[e.zrX,e.zrY],this.trigger("mousedown",e)},mousemove:function(e){e=yt(this.dom,e);var t=this.__mayPointerCapture;t&&(e.zrX!==t[0]||e.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",e)},mouseup:function(e){e=yt(this.dom,e),this.__togglePointerCapture(!1),this.trigger("mouseup",e)},mouseout:function(e){e=yt(this.dom,e);var t=e.toElement||e.relatedTarget;Io(this,t)||(this.__pointerCapturing&&(e.zrEventControl="no_globalout"),this.trigger("mouseout",e))},wheel:function(e){nn=!0,e=yt(this.dom,e),this.trigger("mousewheel",e)},mousewheel:function(e){nn||(e=yt(this.dom,e),this.trigger("mousewheel",e))},touchstart:function(e){e=yt(this.dom,e),Pi(e),this.__lastTouchMoment=new Date,this.handler.processGesture(e,"start"),mt.mousemove.call(this,e),mt.mousedown.call(this,e)},touchmove:function(e){e=yt(this.dom,e),Pi(e),this.handler.processGesture(e,"change"),mt.mousemove.call(this,e)},touchend:function(e){e=yt(this.dom,e),Pi(e),this.handler.processGesture(e,"end"),mt.mouseup.call(this,e),+new Date-+this.__lastTouchMoment<rh&&mt.click.call(this,e)},pointerdown:function(e){mt.mousedown.call(this,e)},pointermove:function(e){ua(e)||mt.mousemove.call(this,e)},pointerup:function(e){mt.mouseup.call(this,e)},pointerout:function(e){ua(e)||mt.mouseout.call(this,e)}};j(["click","dblclick","contextmenu"],function(e){mt[e]=function(t){t=yt(this.dom,t),this.trigger(e,t)}});var va={pointermove:function(e){ua(e)||va.mousemove.call(this,e)},pointerup:function(e){va.mouseup.call(this,e)},mousemove:function(e){this.trigger("mousemove",e)},mouseup:function(e){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",e),t&&(e.zrEventControl="only_globalout",this.trigger("mouseout",e))}};function nh(e,t){var r=t.domHandlers;K.pointerEventsSupported?j(Si.pointer,function(i){Xe(t,i,function(a){r[i].call(e,a)})}):(K.touchEventsSupported&&j(Si.touch,function(i){Xe(t,i,function(a){r[i].call(e,a),eh(t)})}),j(Si.mouse,function(i){Xe(t,i,function(a){a=Da(a),t.touching||r[i].call(e,a)})}))}function oh(e,t){K.pointerEventsSupported?j(an.pointer,r):K.touchEventsSupported||j(an.mouse,r);function r(i){function a(n){n=Da(n),Io(e,n.target)||(n=ih(e,n),t.domHandlers[i].call(e,n))}Xe(t,i,a,{capture:!0})}}function Xe(e,t,r,i){e.mounted[t]=r,e.listenerOpts[t]=i,lf(e.domTarget,t,r,i)}function Ri(e){var t=e.mounted;for(var r in t)t.hasOwnProperty(r)&&cf(e.domTarget,r,t[r],e.listenerOpts[r]);e.mounted={}}var on=function(){function e(t,r){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=r}return e}(),sh=function(e){k(t,e);function t(r,i){var a=e.call(this)||this;return a.__pointerCapturing=!1,a.dom=r,a.painterRoot=i,a._localHandlerScope=new on(r,mt),Li&&(a._globalHandlerScope=new on(document,va)),nh(a,a._localHandlerScope),a}return t.prototype.dispose=function(){Ri(this._localHandlerScope),Li&&Ri(this._globalHandlerScope)},t.prototype.setCursor=function(r){this.dom.style&&(this.dom.style.cursor=r||"default")},t.prototype.__togglePointerCapture=function(r){if(this.__mayPointerCapture=null,Li&&+this.__pointerCapturing^+r){this.__pointerCapturing=r;var i=this._globalHandlerScope;r?oh(this,i):Ri(i)}},t}($r);const fh=sh;var Oo=1;K.hasGlobalWindow&&(Oo=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Ve=Oo,la=.4,ca="#333",da="#ccc",hh="#eee",sn=gf,fn=5e-5;function Jt(e){return e>fn||e<-fn}var jt=[],yr=[],Di=Br(),xi=Math.abs,uh=function(){function e(){}return e.prototype.getLocalTransform=function(t){return e.getLocalTransform(this,t)},e.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},e.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},e.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},e.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},e.prototype.needLocalTransform=function(){return Jt(this.rotation)||Jt(this.x)||Jt(this.y)||Jt(this.scaleX-1)||Jt(this.scaleY-1)||Jt(this.skewX)||Jt(this.skewY)},e.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,r=this.needLocalTransform(),i=this.transform;if(!(r||t)){i&&(sn(i),this.invTransform=null);return}i=i||Br(),r?this.getLocalTransform(i):sn(i),t&&(r?ie(i,t,i):yf(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},e.prototype._resolveGlobalScaleRatio=function(t){var r=this.globalScaleRatio;if(r!=null&&r!==1){this.getGlobalScale(jt);var i=jt[0]<0?-1:1,a=jt[1]<0?-1:1,n=((jt[0]-i)*r+i)/jt[0]||0,o=((jt[1]-a)*r+a)/jt[1]||0;t[0]*=n,t[1]*=n,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||Br(),mf(this.invTransform,t)},e.prototype.getComputedTransform=function(){for(var t=this,r=[];t;)r.push(t),t=t.parent;for(;t=r.pop();)t.updateTransform();return this.transform},e.prototype.setLocalTransform=function(t){if(!!t){var r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],a=Math.atan2(t[1],t[0]),n=Math.PI/2+a-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-a,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0}},e.prototype.decomposeTransform=function(){if(!!this.transform){var t=this.parent,r=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||Br(),ie(yr,t.invTransform,r),r=yr);var i=this.originX,a=this.originY;(i||a)&&(Di[4]=i,Di[5]=a,ie(yr,r,Di),yr[4]-=i,yr[5]-=a,r=yr),this.setLocalTransform(r)}},e.prototype.getGlobalScale=function(t){var r=this.transform;return t=t||[],r?(t[0]=Math.sqrt(r[0]*r[0]+r[1]*r[1]),t[1]=Math.sqrt(r[2]*r[2]+r[3]*r[3]),r[0]<0&&(t[0]=-t[0]),r[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},e.prototype.transformCoordToLocal=function(t,r){var i=[t,r],a=this.invTransform;return a&&ee(i,i,a),i},e.prototype.transformCoordToGlobal=function(t,r){var i=[t,r],a=this.transform;return a&&ee(i,i,a),i},e.prototype.getLineScale=function(){var t=this.transform;return t&&xi(t[0]-1)>1e-10&&xi(t[3]-1)>1e-10?Math.sqrt(xi(t[0]*t[3]-t[2]*t[1])):1},e.prototype.copyTransform=function(t){vh(this,t)},e.getLocalTransform=function(t,r){r=r||[];var i=t.originX||0,a=t.originY||0,n=t.scaleX,o=t.scaleY,s=t.anchorX,f=t.anchorY,h=t.rotation||0,u=t.x,v=t.y,l=t.skewX?Math.tan(t.skewX):0,c=t.skewY?Math.tan(-t.skewY):0;if(i||a||s||f){var _=i+s,g=a+f;r[4]=-_*n-l*g*o,r[5]=-g*o-c*_*n}else r[4]=r[5]=0;return r[0]=n,r[3]=o,r[1]=c*n,r[2]=l*o,h&&go(r,r,h),r[4]+=i+u,r[5]+=a+v,r},e.initDefaultProps=function(){var t=e.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),e}(),he=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function vh(e,t){for(var r=0;r<he.length;r++){var i=he[r];e[i]=t[i]}}const Aa=uh;var hn={};function vt(e,t){t=t||dr;var r=hn[t];r||(r=hn[t]=new ui(500));var i=r.get(e);return i==null&&(i=le.measureText(e,t).width,r.put(e,i)),i}function un(e,t,r,i){var a=vt(e,t),n=Fa(t),o=jr(0,a,r),s=Lr(0,n,i),f=new $(o,s,a,n);return f}function lh(e,t,r,i){var a=((e||"")+"").split(`
`),n=a.length;if(n===1)return un(a[0],t,r,i);for(var o=new $(0,0,0,0),s=0;s<a.length;s++){var f=un(a[s],t,r,i);s===0?o.copy(f):o.union(f)}return o}function jr(e,t,r){return r==="right"?e-=t:r==="center"&&(e-=t/2),e}function Lr(e,t,r){return r==="middle"?e-=t/2:r==="bottom"&&(e-=t),e}function Fa(e){return vt("\u56FD",e)}function ue(e,t){return typeof e=="string"?e.lastIndexOf("%")>=0?parseFloat(e)/100*t:parseFloat(e):e}function ch(e,t,r){var i=t.position||"inside",a=t.distance!=null?t.distance:5,n=r.height,o=r.width,s=n/2,f=r.x,h=r.y,u="left",v="top";if(i instanceof Array)f+=ue(i[0],r.width),h+=ue(i[1],r.height),u=null,v=null;else switch(i){case"left":f-=a,h+=s,u="right",v="middle";break;case"right":f+=a+o,h+=s,v="middle";break;case"top":f+=o/2,h-=a,u="center",v="bottom";break;case"bottom":f+=o/2,h+=n+a,u="center";break;case"inside":f+=o/2,h+=s,u="center",v="middle";break;case"insideLeft":f+=a,h+=s,v="middle";break;case"insideRight":f+=o-a,h+=s,u="right",v="middle";break;case"insideTop":f+=o/2,h+=a,u="center";break;case"insideBottom":f+=o/2,h+=n-a,u="center",v="bottom";break;case"insideTopLeft":f+=a,h+=a;break;case"insideTopRight":f+=o-a,h+=a,u="right";break;case"insideBottomLeft":f+=a,h+=n-a,v="bottom";break;case"insideBottomRight":f+=o-a,h+=n-a,u="right",v="bottom";break}return e=e||{},e.x=f,e.y=h,e.align=u,e.verticalAlign=v,e}var Ai="__zr_normal__",Fi=he.concat(["ignore"]),dh=oi(he,function(e,t){return e[t]=!0,e},{ignore:!1}),mr={},ph=new $(0,0,0,0),Ea=function(){function e(t){this.id=ho(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return e.prototype._init=function(t){this.attr(t)},e.prototype.drift=function(t,r,i){switch(this.draggable){case"horizontal":r=0;break;case"vertical":t=0;break}var a=this.transform;a||(a=this.transform=[1,0,0,1,0,0]),a[4]+=t,a[5]+=r,this.decomposeTransform(),this.markRedraw()},e.prototype.beforeUpdate=function(){},e.prototype.afterUpdate=function(){},e.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},e.prototype.updateInnerText=function(t){var r=this._textContent;if(r&&(!r.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,a=i.local,n=r.innerTransformable,o=void 0,s=void 0,f=!1;n.parent=a?this:null;var h=!1;if(n.copyTransform(r),i.position!=null){var u=ph;i.layoutRect?u.copy(i.layoutRect):u.copy(this.getBoundingRect()),a||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(mr,i,u):ch(mr,i,u),n.x=mr.x,n.y=mr.y,o=mr.align,s=mr.verticalAlign;var v=i.origin;if(v&&i.rotation!=null){var l=void 0,c=void 0;v==="center"?(l=u.width*.5,c=u.height*.5):(l=ue(v[0],u.width),c=ue(v[1],u.height)),h=!0,n.originX=-n.x+l+(a?0:u.x),n.originY=-n.y+c+(a?0:u.y)}}i.rotation!=null&&(n.rotation=i.rotation);var _=i.offset;_&&(n.x+=_[0],n.y+=_[1],h||(n.originX=-_[0],n.originY=-_[1]));var g=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,d=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),p=void 0,y=void 0,m=void 0;g&&this.canBeInsideText()?(p=i.insideFill,y=i.insideStroke,(p==null||p==="auto")&&(p=this.getInsideTextFill()),(y==null||y==="auto")&&(y=this.getInsideTextStroke(p),m=!0)):(p=i.outsideFill,y=i.outsideStroke,(p==null||p==="auto")&&(p=this.getOutsideFill()),(y==null||y==="auto")&&(y=this.getOutsideStroke(p),m=!0)),p=p||"#000",(p!==d.fill||y!==d.stroke||m!==d.autoStroke||o!==d.align||s!==d.verticalAlign)&&(f=!0,d.fill=p,d.stroke=y,d.autoStroke=m,d.align=o,d.verticalAlign=s,r.setDefaultTextStyle(d)),r.__dirty|=ht,f&&r.dirtyStyle(!0)}},e.prototype.canBeInsideText=function(){return!0},e.prototype.getInsideTextFill=function(){return"#fff"},e.prototype.getInsideTextStroke=function(t){return"#000"},e.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?da:ca},e.prototype.getOutsideStroke=function(t){var r=this.__zr&&this.__zr.getBackgroundColor(),i=typeof r=="string"&&xt(r);i||(i=[255,255,255,1]);for(var a=i[3],n=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*a+(n?0:255)*(1-a);return i[3]=1,de(i,"rgba")},e.prototype.traverse=function(t,r){},e.prototype.attrKV=function(t,r){t==="textConfig"?this.setTextConfig(r):t==="textContent"?this.setTextContent(r):t==="clipPath"?this.setClipPath(r):t==="extra"?(this.extra=this.extra||{},Y(this.extra,r)):this[t]=r},e.prototype.hide=function(){this.ignore=!0,this.markRedraw()},e.prototype.show=function(){this.ignore=!1,this.markRedraw()},e.prototype.attr=function(t,r){if(typeof t=="string")this.attrKV(t,r);else if(Yt(t))for(var i=t,a=tt(i),n=0;n<a.length;n++){var o=a[n];this.attrKV(o,t[o])}return this.markRedraw(),this},e.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var r=this._normalState,i=0;i<this.animators.length;i++){var a=this.animators[i],n=a.__fromStateTransition;if(!(a.getLoop()||n&&n!==Ai)){var o=a.targetName,s=o?r[o]:r;a.saveTo(s)}}},e.prototype._innerSaveToNormal=function(t){var r=this._normalState;r||(r=this._normalState={}),t.textConfig&&!r.textConfig&&(r.textConfig=this.textConfig),this._savePrimaryToNormal(t,r,Fi)},e.prototype._savePrimaryToNormal=function(t,r,i){for(var a=0;a<i.length;a++){var n=i[a];t[n]!=null&&!(n in r)&&(r[n]=this[n])}},e.prototype.hasState=function(){return this.currentStates.length>0},e.prototype.getState=function(t){return this.states[t]},e.prototype.ensureState=function(t){var r=this.states;return r[t]||(r[t]={}),r[t]},e.prototype.clearStates=function(t){this.useState(Ai,!1,t)},e.prototype.useState=function(t,r,i,a){var n=t===Ai,o=this.hasState();if(!(!o&&n)){var s=this.currentStates,f=this.stateTransition;if(!(Rt(s,t)>=0&&(r||s.length===1))){var h;if(this.stateProxy&&!n&&(h=this.stateProxy(t)),h||(h=this.states&&this.states[t]),!h&&!n){Ra("State "+t+" not exists.");return}n||this.saveCurrentToNormalState(h);var u=!!(h&&h.hoverLayer||a);u&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,h,this._normalState,r,!i&&!this.__inHover&&f&&f.duration>0,f);var v=this._textContent,l=this._textGuide;return v&&v.useState(t,r,i,u),l&&l.useState(t,r,i,u),n?(this.currentStates=[],this._normalState={}):r?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!u&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ht),h}}},e.prototype.useStates=function(t,r,i){if(!t.length)this.clearStates();else{var a=[],n=this.currentStates,o=t.length,s=o===n.length;if(s){for(var f=0;f<o;f++)if(t[f]!==n[f]){s=!1;break}}if(s)return;for(var f=0;f<o;f++){var h=t[f],u=void 0;this.stateProxy&&(u=this.stateProxy(h,t)),u||(u=this.states[h]),u&&a.push(u)}var v=a[o-1],l=!!(v&&v.hoverLayer||i);l&&this._toggleHoverLayerFlag(!0);var c=this._mergeStates(a),_=this.stateTransition;this.saveCurrentToNormalState(c),this._applyStateObj(t.join(","),c,this._normalState,!1,!r&&!this.__inHover&&_&&_.duration>0,_);var g=this._textContent,d=this._textGuide;g&&g.useStates(t,r,l),d&&d.useStates(t,r,l),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ht)}},e.prototype.isSilent=function(){for(var t=this.silent,r=this.parent;!t&&r;){if(r.silent){t=!0;break}r=r.parent}return t},e.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var r=this.animators[t];r.targetName&&r.changeTarget(this[r.targetName])}},e.prototype.removeState=function(t){var r=Rt(this.currentStates,t);if(r>=0){var i=this.currentStates.slice();i.splice(r,1),this.useStates(i)}},e.prototype.replaceState=function(t,r,i){var a=this.currentStates.slice(),n=Rt(a,t),o=Rt(a,r)>=0;n>=0?o?a.splice(n,1):a[n]=r:i&&!o&&a.push(r),this.useStates(a)},e.prototype.toggleState=function(t,r){r?this.useState(t,!0):this.removeState(t)},e.prototype._mergeStates=function(t){for(var r={},i,a=0;a<t.length;a++){var n=t[a];Y(r,n),n.textConfig&&(i=i||{},Y(i,n.textConfig))}return i&&(r.textConfig=i),r},e.prototype._applyStateObj=function(t,r,i,a,n,o){var s=!(r&&a);r&&r.textConfig?(this.textConfig=Y({},a?this.textConfig:i.textConfig),Y(this.textConfig,r.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var f={},h=!1,u=0;u<Fi.length;u++){var v=Fi[u],l=n&&dh[v];r&&r[v]!=null?l?(h=!0,f[v]=r[v]):this[v]=r[v]:s&&i[v]!=null&&(l?(h=!0,f[v]=i[v]):this[v]=i[v])}if(!n)for(var u=0;u<this.animators.length;u++){var c=this.animators[u],_=c.targetName;c.getLoop()||c.__changeFinalValue(_?(r||i)[_]:r||i)}h&&this._transitionState(t,f,o)},e.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var r=this.__zr;r&&t.addSelfToZr(r),t.__zr=r,t.__hostTarget=this}},e.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},e.prototype.getClipPath=function(){return this._clipPath},e.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},e.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},e.prototype.getTextContent=function(){return this._textContent},e.prototype.setTextContent=function(t){var r=this._textContent;r!==t&&(r&&r!==t&&this.removeTextContent(),t.innerTransformable=new Aa,this._attachComponent(t),this._textContent=t,this.markRedraw())},e.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),Y(this.textConfig,t),this.markRedraw()},e.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},e.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},e.prototype.getTextGuideLine=function(){return this._textGuide},e.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},e.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},e.prototype.markRedraw=function(){this.__dirty|=ht;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},e.prototype.dirty=function(){this.markRedraw()},e.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var r=this._textContent,i=this._textGuide;r&&(r.__inHover=t),i&&(i.__inHover=t)},e.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var r=this.animators;if(r)for(var i=0;i<r.length;i++)t.animation.addAnimator(r[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},e.prototype.removeSelfFromZr=function(t){if(!!this.__zr){this.__zr=null;var r=this.animators;if(r)for(var i=0;i<r.length;i++)t.animation.removeAnimator(r[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},e.prototype.animate=function(t,r,i){var a=t?this[t]:this,n=new xa(a,r,i);return t&&(n.targetName=t),this.addAnimator(n,t),n},e.prototype.addAnimator=function(t,r){var i=this.__zr,a=this;t.during(function(){a.updateDuringAnimation(r)}).done(function(){var n=a.animators,o=Rt(n,t);o>=0&&n.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},e.prototype.updateDuringAnimation=function(t){this.markRedraw()},e.prototype.stopAnimation=function(t,r){for(var i=this.animators,a=i.length,n=[],o=0;o<a;o++){var s=i[o];!t||t===s.scope?s.stop(r):n.push(s)}return this.animators=n,this},e.prototype.animateTo=function(t,r,i){Ei(this,t,r,i)},e.prototype.animateFrom=function(t,r,i){Ei(this,t,r,i,!0)},e.prototype._transitionState=function(t,r,i,a){for(var n=Ei(this,r,i,a),o=0;o<n.length;o++)n[o].__fromStateTransition=t},e.prototype.getBoundingRect=function(){return null},e.prototype.getPaintRect=function(){return null},e.initDefaultProps=function(){var t=e.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=ht;function r(i,a,n,o){Object.defineProperty(t,i,{get:function(){if(!this[a]){var f=this[a]=[];s(this,f)}return this[a]},set:function(f){this[n]=f[0],this[o]=f[1],this[a]=f,s(this,f)}});function s(f,h){Object.defineProperty(h,0,{get:function(){return f[n]},set:function(u){f[n]=u}}),Object.defineProperty(h,1,{get:function(){return f[o]},set:function(u){f[o]=u}})}}Object.defineProperty&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),e}();uo(Ea,$r);uo(Ea,Aa);function Ei(e,t,r,i,a){r=r||{};var n=[];Ho(e,"",e,t,r,i,n,a);var o=n.length,s=!1,f=r.done,h=r.aborted,u=function(){s=!0,o--,o<=0&&(s?f&&f():h&&h())},v=function(){o--,o<=0&&(s?f&&f():h&&h())};o||f&&f(),n.length>0&&r.during&&n[0].during(function(_,g){r.during(g)});for(var l=0;l<n.length;l++){var c=n[l];u&&c.done(u),v&&c.aborted(v),r.force&&c.duration(r.duration),c.start(r.easing)}return n}function Ii(e,t,r){for(var i=0;i<r;i++)e[i]=t[i]}function _h(e){return At(e[0])}function gh(e,t,r){if(At(t[r]))if(At(e[r])||(e[r]=[]),ks(t[r])){var i=t[r].length;e[r].length!==i&&(e[r]=new t[r].constructor(i),Ii(e[r],t[r],i))}else{var a=t[r],n=e[r],o=a.length;if(_h(a))for(var s=a[0].length,f=0;f<o;f++)n[f]?Ii(n[f],a[f],s):n[f]=Array.prototype.slice.call(a[f]);else Ii(n,a,o);n.length=a.length}else e[r]=t[r]}function yh(e,t){return e===t||At(e)&&At(t)&&mh(e,t)}function mh(e,t){var r=e.length;if(r!==t.length)return!1;for(var i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function Ho(e,t,r,i,a,n,o,s){for(var f=tt(i),h=a.duration,u=a.delay,v=a.additive,l=a.setToFinal,c=!Yt(n),_=e.animators,g=[],d=0;d<f.length;d++){var p=f[d],y=i[p];if(y!=null&&r[p]!=null&&(c||n[p]))if(Yt(y)&&!At(y)&&!fi(y)){if(t){s||(r[p]=y,e.updateDuringAnimation(t));continue}Ho(e,p,r[p],y,a,n&&n[p],o,s)}else g.push(p);else s||(r[p]=y,e.updateDuringAnimation(t),g.push(p))}var m=g.length;if(!v&&m)for(var w=0;w<_.length;w++){var C=_[w];if(C.targetName===t){var T=C.stopTracks(g);if(T){var b=Rt(_,C);_.splice(b,1)}}}if(a.force||(g=Ha(g,function(P){return!yh(i[P],r[P])}),m=g.length),m>0||a.force&&!o.length){var M=void 0,L=void 0,S=void 0;if(s){L={},l&&(M={});for(var w=0;w<m;w++){var p=g[w];L[p]=r[p],l?M[p]=i[p]:r[p]=i[p]}}else if(l){S={};for(var w=0;w<m;w++){var p=g[w];S[p]=Ye(r[p]),gh(r,i,p)}}var C=new xa(r,!1,!1,v?Ha(_,function(D){return D.targetName===t}):null);C.targetName=t,a.scope&&(C.scope=a.scope),l&&M&&C.whenWithKeys(0,M,g),S&&C.whenWithKeys(0,S,g),C.whenWithKeys(h==null?500:h,s?L:i,g).delay(u||0),e.addAnimator(C,t),o.push(C)}}const Bo=Ea;var ko=function(e){k(t,e);function t(r){var i=e.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(r),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(r){return this._children[r]},t.prototype.childOfName=function(r){for(var i=this._children,a=0;a<i.length;a++)if(i[a].name===r)return i[a]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(r){return r&&r!==this&&r.parent!==this&&(this._children.push(r),this._doAdd(r)),this},t.prototype.addBefore=function(r,i){if(r&&r!==this&&r.parent!==this&&i&&i.parent===this){var a=this._children,n=a.indexOf(i);n>=0&&(a.splice(n,0,r),this._doAdd(r))}return this},t.prototype.replace=function(r,i){var a=Rt(this._children,r);return a>=0&&this.replaceAt(i,a),this},t.prototype.replaceAt=function(r,i){var a=this._children,n=a[i];if(r&&r!==this&&r.parent!==this&&r!==n){a[i]=r,n.parent=null;var o=this.__zr;o&&n.removeSelfFromZr(o),this._doAdd(r)}return this},t.prototype._doAdd=function(r){r.parent&&r.parent.remove(r),r.parent=this;var i=this.__zr;i&&i!==r.__zr&&r.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(r){var i=this.__zr,a=this._children,n=Rt(a,r);return n<0?this:(a.splice(n,1),r.parent=null,i&&r.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var r=this._children,i=this.__zr,a=0;a<r.length;a++){var n=r[a];i&&n.removeSelfFromZr(i),n.parent=null}return r.length=0,this},t.prototype.eachChild=function(r,i){for(var a=this._children,n=0;n<a.length;n++){var o=a[n];r.call(i,o,n)}return this},t.prototype.traverse=function(r,i){for(var a=0;a<this._children.length;a++){var n=this._children[a],o=r.call(i,n);n.isGroup&&!o&&n.traverse(r,i)}return this},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var i=0;i<this._children.length;i++){var a=this._children[i];a.addSelfToZr(r)}},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var i=0;i<this._children.length;i++){var a=this._children[i];a.removeSelfFromZr(r)}},t.prototype.getBoundingRect=function(r){for(var i=new $(0,0,0,0),a=r||this._children,n=[],o=null,s=0;s<a.length;s++){var f=a[s];if(!(f.ignore||f.invisible)){var h=f.getBoundingRect(),u=f.getLocalTransform(n);u?($.applyTransform(i,h,u),o=o||i.clone(),o.union(i)):(o=o||h.clone(),o.union(h))}}return o||i},t}(Bo);ko.prototype.type="group";const Sr=ko;/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Ge={},zo={};function wh(e){delete zo[e]}function Th(e){if(!e)return!1;if(typeof e=="string")return Ne(e,1)<la;if(e.colorStops){for(var t=e.colorStops,r=0,i=t.length,a=0;a<i;a++)r+=Ne(t[a].color,1);return r/=i,r<la}return!1}var bh=function(){function e(t,r,i){var a=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=r,this.id=t;var n=new Ef,o=i.renderer||"canvas";Ge[o]||(o=tt(Ge)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Ge[o](r,n,i,t),f=i.ssr||s.ssrOnly;this.storage=n,this.painter=s;var h=!K.node&&!K.worker&&!f?new fh(s.getViewportRoot(),s.root):null,u=i.useCoarsePointer,v=u==null||u==="auto"?K.touchEventsSupported:!!u,l=44,c;v&&(c=ut(i.pointerSize,l)),this.handler=new Pf(n,s,h,s.root,c),this.animation=new th({stage:{update:f?null:function(){return a._flush(!0)}}}),f||this.animation.start()}return e.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},e.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},e.prototype.configLayer=function(t,r){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,r),this.refresh())},e.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=Th(t))},e.prototype.getBackgroundColor=function(){return this._backgroundColor},e.prototype.setDarkMode=function(t){this._darkMode=t},e.prototype.isDarkMode=function(){return this._darkMode},e.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},e.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},e.prototype.flush=function(){this._disposed||this._flush(!1)},e.prototype._flush=function(t){var r,i=Ar();this._needsRefresh&&(r=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(r=!0,this.refreshHoverImmediately());var a=Ar();r?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:a-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},e.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},e.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},e.prototype.refreshHover=function(){this._needsRefreshHover=!0},e.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},e.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},e.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},e.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},e.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},e.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},e.prototype.findHover=function(t,r){if(!this._disposed)return this.handler.findHover(t,r)},e.prototype.on=function(t,r,i){return this._disposed||this.handler.on(t,r,i),this},e.prototype.off=function(t,r){this._disposed||this.handler.off(t,r)},e.prototype.trigger=function(t,r){this._disposed||this.handler.trigger(t,r)},e.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),r=0;r<t.length;r++)t[r]instanceof Sr&&t[r].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},e.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,wh(this.id))},e}();function hl(e,t){var r=new bh(ho(),e,t);return zo[r.id]=r,r}function ul(e,t){Ge[e]=t}var pa=new ui(50);function Ch(e){if(typeof e=="string"){var t=pa.get(e);return t&&t.image}else return e}function Yo(e,t,r,i,a){if(e)if(typeof e=="string"){if(t&&t.__zrImageSrc===e||!r)return t;var n=pa.get(e),o={hostEl:r,cb:i,cbPayload:a};return n?(t=n.image,!vi(t)&&n.pending.push(o)):(t=le.loadImage(e,vn,vn),t.__zrImageSrc=e,pa.put(e,t.__cachedImgObj={image:t,pending:[o]})),t}else return e;else return t}function vn(){var e=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<e.pending.length;t++){var r=e.pending[t],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}e.pending.length=0}function vi(e){return e&&e.width&&e.height}var Oi=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Mh(e,t,r,i,a,n){if(!r){e.text="",e.isTruncated=!1;return}var o=(t+"").split(`
`);n=$o(r,i,a,n);for(var s=!1,f={},h=0,u=o.length;h<u;h++)Wo(f,o[h],n),o[h]=f.textLine,s=s||f.isTruncated;e.text=o.join(`
`),e.isTruncated=s}function $o(e,t,r,i){i=i||{};var a=Y({},i);a.font=t,r=ut(r,"..."),a.maxIterations=ut(i.maxIterations,2);var n=a.minChar=ut(i.minChar,0);a.cnCharWidth=vt("\u56FD",t);var o=a.ascCharWidth=vt("a",t);a.placeholder=ut(i.placeholder,"");for(var s=e=Math.max(0,e-1),f=0;f<n&&s>=o;f++)s-=o;var h=vt(r,t);return h>s&&(r="",h=0),s=e-h,a.ellipsis=r,a.ellipsisWidth=h,a.contentWidth=s,a.containerWidth=e,a}function Wo(e,t,r){var i=r.containerWidth,a=r.font,n=r.contentWidth;if(!i){e.textLine="",e.isTruncated=!1;return}var o=vt(t,a);if(o<=i){e.textLine=t,e.isTruncated=!1;return}for(var s=0;;s++){if(o<=n||s>=r.maxIterations){t+=r.ellipsis;break}var f=s===0?Lh(t,n,r.ascCharWidth,r.cnCharWidth):o>0?Math.floor(t.length*n/o):0;t=t.substr(0,f),o=vt(t,a)}t===""&&(t=r.placeholder),e.textLine=t,e.isTruncated=!0}function Lh(e,t,r,i){for(var a=0,n=0,o=e.length;n<o&&a<t;n++){var s=e.charCodeAt(n);a+=0<=s&&s<=127?r:i}return n}function Sh(e,t){e!=null&&(e+="");var r=t.overflow,i=t.padding,a=t.font,n=r==="truncate",o=Fa(a),s=ut(t.lineHeight,o),f=!!t.backgroundColor,h=t.lineOverflow==="truncate",u=!1,v=t.width,l;v!=null&&(r==="break"||r==="breakAll")?l=e?Xo(e,t.font,v,r==="breakAll",0).lines:[]:l=e?e.split(`
`):[];var c=l.length*s,_=ut(t.height,c);if(c>_&&h){var g=Math.floor(_/s);u=u||l.length>g,l=l.slice(0,g)}if(e&&n&&v!=null)for(var d=$o(v,a,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),p={},y=0;y<l.length;y++)Wo(p,l[y],d),l[y]=p.textLine,u=u||p.isTruncated;for(var m=_,w=0,y=0;y<l.length;y++)w=Math.max(vt(l[y],a),w);v==null&&(v=w);var C=w;return i&&(m+=i[0]+i[2],C+=i[1]+i[3],v+=i[1]+i[3]),f&&(C=v),{lines:l,height:_,outerWidth:C,outerHeight:m,lineHeight:s,calculatedLineHeight:o,contentWidth:w,contentHeight:c,width:v,isTruncated:u}}var Ph=function(){function e(){}return e}(),ln=function(){function e(t){this.tokens=[],t&&(this.tokens=t)}return e}(),Rh=function(){function e(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return e}();function Dh(e,t){var r=new Rh;if(e!=null&&(e+=""),!e)return r;for(var i=t.width,a=t.height,n=t.overflow,o=(n==="break"||n==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:n==="breakAll"}:null,s=Oi.lastIndex=0,f;(f=Oi.exec(e))!=null;){var h=f.index;h>s&&Hi(r,e.substring(s,h),t,o),Hi(r,f[2],t,o,f[1]),s=Oi.lastIndex}s<e.length&&Hi(r,e.substring(s,e.length),t,o);var u=[],v=0,l=0,c=t.padding,_=n==="truncate",g=t.lineOverflow==="truncate",d={};function p(U,G,W){U.width=G,U.lineHeight=W,v+=W,l=Math.max(l,G)}t:for(var y=0;y<r.lines.length;y++){for(var m=r.lines[y],w=0,C=0,T=0;T<m.tokens.length;T++){var b=m.tokens[T],M=b.styleName&&t.rich[b.styleName]||{},L=b.textPadding=M.padding,S=L?L[1]+L[3]:0,P=b.font=M.font||t.font;b.contentHeight=Fa(P);var D=ut(M.height,b.contentHeight);if(b.innerHeight=D,L&&(D+=L[0]+L[2]),b.height=D,b.lineHeight=ke(M.lineHeight,t.lineHeight,D),b.align=M&&M.align||t.align,b.verticalAlign=M&&M.verticalAlign||"middle",g&&a!=null&&v+b.lineHeight>a){var A=r.lines.length;T>0?(m.tokens=m.tokens.slice(0,T),p(m,C,w),r.lines=r.lines.slice(0,y+1)):r.lines=r.lines.slice(0,y),r.isTruncated=r.isTruncated||r.lines.length<A;break t}var R=M.width,E=R==null||R==="auto";if(typeof R=="string"&&R.charAt(R.length-1)==="%")b.percentWidth=R,u.push(b),b.contentWidth=vt(b.text,P);else{if(E){var x=M.backgroundColor,H=x&&x.image;H&&(H=Ch(H),vi(H)&&(b.width=Math.max(b.width,H.width*D/H.height)))}var z=_&&i!=null?i-C:null;z!=null&&z<b.width?!E||z<S?(b.text="",b.width=b.contentWidth=0):(Mh(d,b.text,z-S,P,t.ellipsis,{minChar:t.truncateMinChar}),b.text=d.text,r.isTruncated=r.isTruncated||d.isTruncated,b.width=b.contentWidth=vt(b.text,P)):b.contentWidth=vt(b.text,P)}b.width+=S,C+=b.width,M&&(w=Math.max(w,b.lineHeight))}p(m,C,w)}r.outerWidth=r.width=ut(i,l),r.outerHeight=r.height=ut(a,v),r.contentHeight=v,r.contentWidth=l,c&&(r.outerWidth+=c[1]+c[3],r.outerHeight+=c[0]+c[2]);for(var y=0;y<u.length;y++){var b=u[y],X=b.percentWidth;b.width=parseInt(X,10)/100*r.width}return r}function Hi(e,t,r,i,a){var n=t==="",o=a&&r.rich[a]||{},s=e.lines,f=o.font||r.font,h=!1,u,v;if(i){var l=o.padding,c=l?l[1]+l[3]:0;if(o.width!=null&&o.width!=="auto"){var _=ue(o.width,i.width)+c;s.length>0&&_+i.accumWidth>i.width&&(u=t.split(`
`),h=!0),i.accumWidth=_}else{var g=Xo(t,f,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+c,v=g.linesWidths,u=g.lines}}else u=t.split(`
`);for(var d=0;d<u.length;d++){var p=u[d],y=new Ph;if(y.styleName=a,y.text=p,y.isLineHolder=!p&&!n,typeof o.width=="number"?y.width=o.width:y.width=v?v[d]:vt(p,f),!d&&!h){var m=(s[s.length-1]||(s[0]=new ln)).tokens,w=m.length;w===1&&m[0].isLineHolder?m[0]=y:(p||!w||n)&&m.push(y)}else s.push(new ln([y]))}}function xh(e){var t=e.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var Ah=oi(",&?/;] ".split(""),function(e,t){return e[t]=!0,e},{});function Fh(e){return xh(e)?!!Ah[e]:!0}function Xo(e,t,r,i,a){for(var n=[],o=[],s="",f="",h=0,u=0,v=0;v<e.length;v++){var l=e.charAt(v);if(l===`
`){f&&(s+=f,u+=h),n.push(s),o.push(u),s="",f="",h=0,u=0;continue}var c=vt(l,t),_=i?!1:!Fh(l);if(n.length?u+c>r:a+u+c>r){u?(s||f)&&(_?(s||(s=f,f="",h=0,u=h),n.push(s),o.push(u-h),f+=l,h+=c,s="",u=h):(f&&(s+=f,f="",h=0),n.push(s),o.push(u),s=l,u=c)):_?(n.push(f),o.push(h),f=l,h=c):(n.push(l),o.push(c));continue}u+=c,_?(f+=l,h+=c):(f&&(s+=f,f="",h=0),s+=l)}return!n.length&&!s&&(s=e,f="",h=0),f&&(s+=f),s&&(n.push(s),o.push(u)),n.length===1&&(u+=a),{accumWidth:u,lines:n,linesWidths:o}}var _a="__zr_style_"+Math.round(Math.random()*10),cr={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},li={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};cr[_a]=!0;var cn=["z","z2","invisible"],Eh=["invisible"],Ih=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype._init=function(r){for(var i=tt(r),a=0;a<i.length;a++){var n=i[a];n==="style"?this.useStyle(r[n]):e.prototype.attrKV.call(this,n,r[n])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(r,i,a,n){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&Oh(this,r,i)||o&&!o[0]&&!o[3])return!1;if(a&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(n&&this.parent)for(var f=this.parent;f;){if(f.ignore)return!1;f=f.parent}return!0},t.prototype.contain=function(r,i){return this.rectContain(r,i)},t.prototype.traverse=function(r,i){r.call(i,this)},t.prototype.rectContain=function(r,i){var a=this.transformCoordToLocal(r,i),n=this.getBoundingRect();return n.contain(a[0],a[1])},t.prototype.getPaintRect=function(){var r=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,a=this.getBoundingRect(),n=this.style,o=n.shadowBlur||0,s=n.shadowOffsetX||0,f=n.shadowOffsetY||0;r=this._paintRect||(this._paintRect=new $(0,0,0,0)),i?$.applyTransform(r,a,i):r.copy(a),(o||s||f)&&(r.width+=o*2+Math.abs(s),r.height+=o*2+Math.abs(f),r.x=Math.min(r.x,r.x+s-o),r.y=Math.min(r.y,r.y+f-o));var h=this.dirtyRectTolerance;r.isZero()||(r.x=Math.floor(r.x-h),r.y=Math.floor(r.y-h),r.width=Math.ceil(r.width+1+h*2),r.height=Math.ceil(r.height+1+h*2))}return r},t.prototype.setPrevPaintRect=function(r){r?(this._prevPaintRect=this._prevPaintRect||new $(0,0,0,0),this._prevPaintRect.copy(r)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(r){return this.animate("style",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(r,i){r!=="style"?e.prototype.attrKV.call(this,r,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(r,i){return typeof r=="string"?this.style[r]=i:Y(this.style,r),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(r){r||this.markRedraw(),this.__dirty|=Kr,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&Kr)},t.prototype.styleUpdated=function(){this.__dirty&=~Kr},t.prototype.createStyle=function(r){return hi(cr,r)},t.prototype.useStyle=function(r){r[_a]||(r=this.createStyle(r)),this.__inHover?this.__hoverStyle=r:this.style=r,this.dirtyStyle()},t.prototype.isStyleObject=function(r){return r[_a]},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var i=this._normalState;r.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(r,i,cn)},t.prototype._applyStateObj=function(r,i,a,n,o,s){e.prototype._applyStateObj.call(this,r,i,a,n,o,s);var f=!(i&&n),h;if(i&&i.style?o?n?h=i.style:(h=this._mergeStyle(this.createStyle(),a.style),this._mergeStyle(h,i.style)):(h=this._mergeStyle(this.createStyle(),n?this.style:a.style),this._mergeStyle(h,i.style)):f&&(h=a.style),h)if(o){var u=this.style;if(this.style=this.createStyle(f?{}:u),f)for(var v=tt(u),l=0;l<v.length;l++){var c=v[l];c in h&&(h[c]=h[c],this.style[c]=u[c])}for(var _=tt(h),l=0;l<_.length;l++){var c=_[l];this.style[c]=this.style[c]}this._transitionState(r,{style:h},s,this.getAnimationStyleProps())}else this.useStyle(h);for(var g=this.__inHover?Eh:cn,l=0;l<g.length;l++){var c=g[l];i&&i[c]!=null?this[c]=i[c]:f&&a[c]!=null&&(this[c]=a[c])}},t.prototype._mergeStates=function(r){for(var i=e.prototype._mergeStates.call(this,r),a,n=0;n<r.length;n++){var o=r[n];o.style&&(a=a||{},this._mergeStyle(a,o.style))}return a&&(i.style=a),i},t.prototype._mergeStyle=function(r,i){return Y(r,i),r},t.prototype.getAnimationStyleProps=function(){return li},t.initDefaultProps=function(){var r=t.prototype;r.type="displayable",r.invisible=!1,r.z=0,r.z2=0,r.zlevel=0,r.culling=!1,r.cursor="pointer",r.rectHover=!1,r.incremental=!1,r._rect=null,r.dirtyRectTolerance=0,r.__dirty=ht|Kr}(),t}(Bo),Bi=new $(0,0,0,0),ki=new $(0,0,0,0);function Oh(e,t,r){return Bi.copy(e.getBoundingRect()),e.transform&&Bi.applyTransform(e.transform),ki.width=t,ki.height=r,!Bi.intersect(ki)}const pe=Ih;var it=Math.min,at=Math.max,zi=Math.sin,Yi=Math.cos,tr=Math.PI*2,Le=Yr(),Se=Yr(),Pe=Yr();function Go(e,t,r){if(e.length!==0){for(var i=e[0],a=i[0],n=i[0],o=i[1],s=i[1],f=1;f<e.length;f++)i=e[f],a=it(a,i[0]),n=at(n,i[0]),o=it(o,i[1]),s=at(s,i[1]);t[0]=a,t[1]=o,r[0]=n,r[1]=s}}function dn(e,t,r,i,a,n){a[0]=it(e,r),a[1]=it(t,i),n[0]=at(e,r),n[1]=at(t,i)}var pn=[],_n=[];function Hh(e,t,r,i,a,n,o,s,f,h){var u=Ro,v=N,l=u(e,r,a,o,pn);f[0]=1/0,f[1]=1/0,h[0]=-1/0,h[1]=-1/0;for(var c=0;c<l;c++){var _=v(e,r,a,o,pn[c]);f[0]=it(_,f[0]),h[0]=at(_,h[0])}l=u(t,i,n,s,_n);for(var c=0;c<l;c++){var g=v(t,i,n,s,_n[c]);f[1]=it(g,f[1]),h[1]=at(g,h[1])}f[0]=it(e,f[0]),h[0]=at(e,h[0]),f[0]=it(o,f[0]),h[0]=at(o,h[0]),f[1]=it(t,f[1]),h[1]=at(t,h[1]),f[1]=it(s,f[1]),h[1]=at(s,h[1])}function Bh(e,t,r,i,a,n,o,s){var f=Do,h=Q,u=at(it(f(e,r,a),1),0),v=at(it(f(t,i,n),1),0),l=h(e,r,a,u),c=h(t,i,n,v);o[0]=it(e,a,l),o[1]=it(t,n,c),s[0]=at(e,a,l),s[1]=at(t,n,c)}function kh(e,t,r,i,a,n,o,s,f){var h=Dr,u=xr,v=Math.abs(a-n);if(v%tr<1e-4&&v>1e-4){s[0]=e-r,s[1]=t-i,f[0]=e+r,f[1]=t+i;return}if(Le[0]=Yi(a)*r+e,Le[1]=zi(a)*i+t,Se[0]=Yi(n)*r+e,Se[1]=zi(n)*i+t,h(s,Le,Se),u(f,Le,Se),a=a%tr,a<0&&(a=a+tr),n=n%tr,n<0&&(n=n+tr),a>n&&!o?n+=tr:a<n&&o&&(a+=tr),o){var l=n;n=a,a=l}for(var c=0;c<n;c+=Math.PI/2)c>a&&(Pe[0]=Yi(c)*r+e,Pe[1]=zi(c)*i+t,h(s,Pe,s),u(f,Pe,f))}var B={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},rr=[],er=[],Ct=[],Ht=[],Mt=[],Lt=[],$i=Math.min,Wi=Math.max,ir=Math.cos,ar=Math.sin,Et=Math.abs,ga=Math.PI,zt=ga*2,Xi=typeof Float32Array<"u",Nr=[];function Gi(e){var t=Math.round(e/ga*1e8)/1e8;return t%2*ga}function zh(e,t){var r=Gi(e[0]);r<0&&(r+=zt);var i=r-e[0],a=e[1];a+=i,!t&&a-r>=zt?a=r+zt:t&&r-a>=zt?a=r-zt:!t&&r>a?a=r+(zt-Gi(r-a)):t&&r<a&&(a=r-(zt-Gi(a-r))),e[0]=r,e[1]=a}var pr=function(){function e(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return e.prototype.increaseVersion=function(){this._version++},e.prototype.getVersion=function(){return this._version},e.prototype.setScale=function(t,r,i){i=i||0,i>0&&(this._ux=Et(i/Ve/t)||0,this._uy=Et(i/Ve/r)||0)},e.prototype.setDPR=function(t){this.dpr=t},e.prototype.setContext=function(t){this._ctx=t},e.prototype.getContext=function(){return this._ctx},e.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},e.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},e.prototype.moveTo=function(t,r){return this._drawPendingPt(),this.addData(B.M,t,r),this._ctx&&this._ctx.moveTo(t,r),this._x0=t,this._y0=r,this._xi=t,this._yi=r,this},e.prototype.lineTo=function(t,r){var i=Et(t-this._xi),a=Et(r-this._yi),n=i>this._ux||a>this._uy;if(this.addData(B.L,t,r),this._ctx&&n&&this._ctx.lineTo(t,r),n)this._xi=t,this._yi=r,this._pendingPtDist=0;else{var o=i*i+a*a;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=r,this._pendingPtDist=o)}return this},e.prototype.bezierCurveTo=function(t,r,i,a,n,o){return this._drawPendingPt(),this.addData(B.C,t,r,i,a,n,o),this._ctx&&this._ctx.bezierCurveTo(t,r,i,a,n,o),this._xi=n,this._yi=o,this},e.prototype.quadraticCurveTo=function(t,r,i,a){return this._drawPendingPt(),this.addData(B.Q,t,r,i,a),this._ctx&&this._ctx.quadraticCurveTo(t,r,i,a),this._xi=i,this._yi=a,this},e.prototype.arc=function(t,r,i,a,n,o){this._drawPendingPt(),Nr[0]=a,Nr[1]=n,zh(Nr,o),a=Nr[0],n=Nr[1];var s=n-a;return this.addData(B.A,t,r,i,i,a,s,0,o?0:1),this._ctx&&this._ctx.arc(t,r,i,a,n,o),this._xi=ir(n)*i+t,this._yi=ar(n)*i+r,this},e.prototype.arcTo=function(t,r,i,a,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,r,i,a,n),this},e.prototype.rect=function(t,r,i,a){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,r,i,a),this.addData(B.R,t,r,i,a),this},e.prototype.closePath=function(){this._drawPendingPt(),this.addData(B.Z);var t=this._ctx,r=this._x0,i=this._y0;return t&&t.closePath(),this._xi=r,this._yi=i,this},e.prototype.fill=function(t){t&&t.fill(),this.toStatic()},e.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},e.prototype.len=function(){return this._len},e.prototype.setData=function(t){var r=t.length;!(this.data&&this.data.length===r)&&Xi&&(this.data=new Float32Array(r));for(var i=0;i<r;i++)this.data[i]=t[i];this._len=r},e.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var r=t.length,i=0,a=this._len,n=0;n<r;n++)i+=t[n].len();Xi&&this.data instanceof Float32Array&&(this.data=new Float32Array(a+i));for(var n=0;n<r;n++)for(var o=t[n].data,s=0;s<o.length;s++)this.data[a++]=o[s];this._len=a},e.prototype.addData=function(t,r,i,a,n,o,s,f,h){if(!!this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var v=0;v<arguments.length;v++)u[this._len++]=arguments[v]}},e.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},e.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],r=0;r<this._len;r++)t[r]=this.data[r];this.data=t}},e.prototype.toStatic=function(){if(!!this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Xi&&this._len>11&&(this.data=new Float32Array(t)))}},e.prototype.getBoundingRect=function(){Ct[0]=Ct[1]=Mt[0]=Mt[1]=Number.MAX_VALUE,Ht[0]=Ht[1]=Lt[0]=Lt[1]=-Number.MAX_VALUE;var t=this.data,r=0,i=0,a=0,n=0,o;for(o=0;o<this._len;){var s=t[o++],f=o===1;switch(f&&(r=t[o],i=t[o+1],a=r,n=i),s){case B.M:r=a=t[o++],i=n=t[o++],Mt[0]=a,Mt[1]=n,Lt[0]=a,Lt[1]=n;break;case B.L:dn(r,i,t[o],t[o+1],Mt,Lt),r=t[o++],i=t[o++];break;case B.C:Hh(r,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],Mt,Lt),r=t[o++],i=t[o++];break;case B.Q:Bh(r,i,t[o++],t[o++],t[o],t[o+1],Mt,Lt),r=t[o++],i=t[o++];break;case B.A:var h=t[o++],u=t[o++],v=t[o++],l=t[o++],c=t[o++],_=t[o++]+c;o+=1;var g=!t[o++];f&&(a=ir(c)*v+h,n=ar(c)*l+u),kh(h,u,v,l,c,_,g,Mt,Lt),r=ir(_)*v+h,i=ar(_)*l+u;break;case B.R:a=r=t[o++],n=i=t[o++];var d=t[o++],p=t[o++];dn(a,n,a+d,n+p,Mt,Lt);break;case B.Z:r=a,i=n;break}Dr(Ct,Ct,Mt),xr(Ht,Ht,Lt)}return o===0&&(Ct[0]=Ct[1]=Ht[0]=Ht[1]=0),new $(Ct[0],Ct[1],Ht[0]-Ct[0],Ht[1]-Ct[1])},e.prototype._calculateLength=function(){var t=this.data,r=this._len,i=this._ux,a=this._uy,n=0,o=0,s=0,f=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,u=0,v=0,l=0;l<r;){var c=t[l++],_=l===1;_&&(n=t[l],o=t[l+1],s=n,f=o);var g=-1;switch(c){case B.M:n=s=t[l++],o=f=t[l++];break;case B.L:{var d=t[l++],p=t[l++],y=d-n,m=p-o;(Et(y)>i||Et(m)>a||l===r-1)&&(g=Math.sqrt(y*y+m*m),n=d,o=p);break}case B.C:{var w=t[l++],C=t[l++],d=t[l++],p=t[l++],T=t[l++],b=t[l++];g=Of(n,o,w,C,d,p,T,b,10),n=T,o=b;break}case B.Q:{var w=t[l++],C=t[l++],d=t[l++],p=t[l++];g=kf(n,o,w,C,d,p,10),n=d,o=p;break}case B.A:var M=t[l++],L=t[l++],S=t[l++],P=t[l++],D=t[l++],A=t[l++],R=A+D;l+=1,_&&(s=ir(D)*S+M,f=ar(D)*P+L),g=Wi(S,P)*$i(zt,Math.abs(A)),n=ir(R)*S+M,o=ar(R)*P+L;break;case B.R:{s=n=t[l++],f=o=t[l++];var E=t[l++],x=t[l++];g=E*2+x*2;break}case B.Z:{var y=s-n,m=f-o;g=Math.sqrt(y*y+m*m),n=s,o=f;break}}g>=0&&(h[v++]=g,u+=g)}return this._pathLen=u,u},e.prototype.rebuildPath=function(t,r){var i=this.data,a=this._ux,n=this._uy,o=this._len,s,f,h,u,v,l,c=r<1,_,g,d=0,p=0,y,m=0,w,C;if(c&&(this._pathSegLen||this._calculateLength(),_=this._pathSegLen,g=this._pathLen,y=r*g,!y))return;t:for(var T=0;T<o;){var b=i[T++],M=T===1;switch(M&&(h=i[T],u=i[T+1],s=h,f=u),b!==B.L&&m>0&&(t.lineTo(w,C),m=0),b){case B.M:s=h=i[T++],f=u=i[T++],t.moveTo(h,u);break;case B.L:{v=i[T++],l=i[T++];var L=Et(v-h),S=Et(l-u);if(L>a||S>n){if(c){var P=_[p++];if(d+P>y){var D=(y-d)/P;t.lineTo(h*(1-D)+v*D,u*(1-D)+l*D);break t}d+=P}t.lineTo(v,l),h=v,u=l,m=0}else{var A=L*L+S*S;A>m&&(w=v,C=l,m=A)}break}case B.C:{var R=i[T++],E=i[T++],x=i[T++],H=i[T++],z=i[T++],X=i[T++];if(c){var P=_[p++];if(d+P>y){var D=(y-d)/P;Gt(h,R,x,z,D,rr),Gt(u,E,H,X,D,er),t.bezierCurveTo(rr[1],er[1],rr[2],er[2],rr[3],er[3]);break t}d+=P}t.bezierCurveTo(R,E,x,H,z,X),h=z,u=X;break}case B.Q:{var R=i[T++],E=i[T++],x=i[T++],H=i[T++];if(c){var P=_[p++];if(d+P>y){var D=(y-d)/P;Ze(h,R,x,D,rr),Ze(u,E,H,D,er),t.quadraticCurveTo(rr[1],er[1],rr[2],er[2]);break t}d+=P}t.quadraticCurveTo(R,E,x,H),h=x,u=H;break}case B.A:var U=i[T++],G=i[T++],W=i[T++],ot=i[T++],st=i[T++],Ft=i[T++],qt=i[T++],Ut=!i[T++],Zt=W>ot?W:ot,rt=Et(W-ot)>.001,Z=st+Ft,F=!1;if(c){var P=_[p++];d+P>y&&(Z=st+Ft*(y-d)/P,F=!0),d+=P}if(rt&&t.ellipse?t.ellipse(U,G,W,ot,qt,st,Z,Ut):t.arc(U,G,Zt,st,Z,Ut),F)break t;M&&(s=ir(st)*W+U,f=ar(st)*ot+G),h=ir(Z)*W+U,u=ar(Z)*ot+G;break;case B.R:s=h=i[T],f=u=i[T+1],v=i[T++],l=i[T++];var O=i[T++],Wr=i[T++];if(c){var P=_[p++];if(d+P>y){var bt=y-d;t.moveTo(v,l),t.lineTo(v+$i(bt,O),l),bt-=O,bt>0&&t.lineTo(v+O,l+$i(bt,Wr)),bt-=Wr,bt>0&&t.lineTo(v+Wi(O-bt,0),l+Wr),bt-=O,bt>0&&t.lineTo(v,l+Wi(Wr-bt,0));break t}d+=P}t.rect(v,l,O,Wr);break;case B.Z:if(c){var P=_[p++];if(d+P>y){var D=(y-d)/P;t.lineTo(h*(1-D)+s*D,u*(1-D)+f*D);break t}d+=P}t.closePath(),h=s,u=f}}},e.prototype.clone=function(){var t=new e,r=this.data;return t.data=r.slice?r.slice():Array.prototype.slice.call(r),t._len=this._len,t},e.CMD=B,e.initDefaultProps=function(){var t=e.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),e}();function wr(e,t,r,i,a,n,o){if(a===0)return!1;var s=a,f=0,h=e;if(o>t+s&&o>i+s||o<t-s&&o<i-s||n>e+s&&n>r+s||n<e-s&&n<r-s)return!1;if(e!==r)f=(t-i)/(e-r),h=(e*i-r*t)/(e-r);else return Math.abs(n-e)<=s/2;var u=f*n-o+h,v=u*u/(f*f+1);return v<=s/2*s/2}function Yh(e,t,r,i,a,n,o,s,f,h,u){if(f===0)return!1;var v=f;if(u>t+v&&u>i+v&&u>n+v&&u>s+v||u<t-v&&u<i-v&&u<n-v&&u<s-v||h>e+v&&h>r+v&&h>a+v&&h>o+v||h<e-v&&h<r-v&&h<a-v&&h<o-v)return!1;var l=If(e,t,r,i,a,n,o,s,h,u,null);return l<=v/2}function $h(e,t,r,i,a,n,o,s,f){if(o===0)return!1;var h=o;if(f>t+h&&f>i+h&&f>n+h||f<t-h&&f<i-h&&f<n-h||s>e+h&&s>r+h&&s>a+h||s<e-h&&s<r-h&&s<a-h)return!1;var u=Bf(e,t,r,i,a,n,s,f,null);return u<=h/2}var gn=Math.PI*2;function Re(e){return e%=gn,e<0&&(e+=gn),e}var Vr=Math.PI*2;function Wh(e,t,r,i,a,n,o,s,f){if(o===0)return!1;var h=o;s-=e,f-=t;var u=Math.sqrt(s*s+f*f);if(u-h>r||u+h<r)return!1;if(Math.abs(i-a)%Vr<1e-4)return!0;if(n){var v=i;i=Re(a),a=Re(v)}else i=Re(i),a=Re(a);i>a&&(a+=Vr);var l=Math.atan2(f,s);return l<0&&(l+=Vr),l>=i&&l<=a||l+Vr>=i&&l+Vr<=a}function Ot(e,t,r,i,a,n){if(n>t&&n>i||n<t&&n<i||i===t)return 0;var o=(n-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var f=o*(r-e)+e;return f===a?1/0:f>a?s:0}var Bt=pr.CMD,nr=Math.PI*2,Xh=1e-4;function Gh(e,t){return Math.abs(e-t)<Xh}var J=[-1,-1,-1],dt=[-1,-1];function qh(){var e=dt[0];dt[0]=dt[1],dt[1]=e}function Uh(e,t,r,i,a,n,o,s,f,h){if(h>t&&h>i&&h>n&&h>s||h<t&&h<i&&h<n&&h<s)return 0;var u=Po(t,i,n,s,h,J);if(u===0)return 0;for(var v=0,l=-1,c=void 0,_=void 0,g=0;g<u;g++){var d=J[g],p=d===0||d===1?.5:1,y=N(e,r,a,o,d);y<f||(l<0&&(l=Ro(t,i,n,s,dt),dt[1]<dt[0]&&l>1&&qh(),c=N(t,i,n,s,dt[0]),l>1&&(_=N(t,i,n,s,dt[1]))),l===2?d<dt[0]?v+=c<t?p:-p:d<dt[1]?v+=_<c?p:-p:v+=s<_?p:-p:d<dt[0]?v+=c<t?p:-p:v+=s<c?p:-p)}return v}function Zh(e,t,r,i,a,n,o,s){if(s>t&&s>i&&s>n||s<t&&s<i&&s<n)return 0;var f=Hf(t,i,n,s,J);if(f===0)return 0;var h=Do(t,i,n);if(h>=0&&h<=1){for(var u=0,v=Q(t,i,n,h),l=0;l<f;l++){var c=J[l]===0||J[l]===1?.5:1,_=Q(e,r,a,J[l]);_<o||(J[l]<h?u+=v<t?c:-c:u+=n<v?c:-c)}return u}else{var c=J[0]===0||J[0]===1?.5:1,_=Q(e,r,a,J[0]);return _<o?0:n<t?c:-c}}function Nh(e,t,r,i,a,n,o,s){if(s-=t,s>r||s<-r)return 0;var f=Math.sqrt(r*r-s*s);J[0]=-f,J[1]=f;var h=Math.abs(i-a);if(h<1e-4)return 0;if(h>=nr-1e-4){i=0,a=nr;var u=n?1:-1;return o>=J[0]+e&&o<=J[1]+e?u:0}if(i>a){var v=i;i=a,a=v}i<0&&(i+=nr,a+=nr);for(var l=0,c=0;c<2;c++){var _=J[c];if(_+e>o){var g=Math.atan2(s,_),u=n?1:-1;g<0&&(g=nr+g),(g>=i&&g<=a||g+nr>=i&&g+nr<=a)&&(g>Math.PI/2&&g<Math.PI*1.5&&(u=-u),l+=u)}}return l}function qo(e,t,r,i,a){for(var n=e.data,o=e.len(),s=0,f=0,h=0,u=0,v=0,l,c,_=0;_<o;){var g=n[_++],d=_===1;switch(g===Bt.M&&_>1&&(r||(s+=Ot(f,h,u,v,i,a))),d&&(f=n[_],h=n[_+1],u=f,v=h),g){case Bt.M:u=n[_++],v=n[_++],f=u,h=v;break;case Bt.L:if(r){if(wr(f,h,n[_],n[_+1],t,i,a))return!0}else s+=Ot(f,h,n[_],n[_+1],i,a)||0;f=n[_++],h=n[_++];break;case Bt.C:if(r){if(Yh(f,h,n[_++],n[_++],n[_++],n[_++],n[_],n[_+1],t,i,a))return!0}else s+=Uh(f,h,n[_++],n[_++],n[_++],n[_++],n[_],n[_+1],i,a)||0;f=n[_++],h=n[_++];break;case Bt.Q:if(r){if($h(f,h,n[_++],n[_++],n[_],n[_+1],t,i,a))return!0}else s+=Zh(f,h,n[_++],n[_++],n[_],n[_+1],i,a)||0;f=n[_++],h=n[_++];break;case Bt.A:var p=n[_++],y=n[_++],m=n[_++],w=n[_++],C=n[_++],T=n[_++];_+=1;var b=!!(1-n[_++]);l=Math.cos(C)*m+p,c=Math.sin(C)*w+y,d?(u=l,v=c):s+=Ot(f,h,l,c,i,a);var M=(i-p)*w/m+p;if(r){if(Wh(p,y,w,C,C+T,b,t,M,a))return!0}else s+=Nh(p,y,w,C,C+T,b,M,a);f=Math.cos(C+T)*m+p,h=Math.sin(C+T)*w+y;break;case Bt.R:u=f=n[_++],v=h=n[_++];var L=n[_++],S=n[_++];if(l=u+L,c=v+S,r){if(wr(u,v,l,v,t,i,a)||wr(l,v,l,c,t,i,a)||wr(l,c,u,c,t,i,a)||wr(u,c,u,v,t,i,a))return!0}else s+=Ot(l,v,l,c,i,a),s+=Ot(u,c,u,v,i,a);break;case Bt.Z:if(r){if(wr(f,h,u,v,t,i,a))return!0}else s+=Ot(f,h,u,v,i,a);f=u,h=v;break}}return!r&&!Gh(h,v)&&(s+=Ot(f,h,u,v,i,a)||0),s!==0}function Vh(e,t,r){return qo(e,0,!1,t,r)}function Qh(e,t,r,i){return qo(e,t,!0,r,i)}var Uo=_t({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},cr),Kh={style:_t({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},li.style)},qi=he.concat(["invisible","culling","z","z2","zlevel","parent"]),Jh=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.update=function(){var r=this;e.prototype.update.call(this);var i=this.style;if(i.decal){var a=this._decalEl=this._decalEl||new t;a.buildPath===t.prototype.buildPath&&(a.buildPath=function(f){r.buildPath(f,r.shape)}),a.silent=!0;var n=a.style;for(var o in i)n[o]!==i[o]&&(n[o]=i[o]);n.fill=i.fill?i.decal:null,n.decal=null,n.shadowColor=null,i.strokeFirst&&(n.stroke=null);for(var s=0;s<qi.length;++s)a[qi[s]]=this[qi[s]];a.__dirty|=ht}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(r){var i=tt(r);this.shape=this.getDefaultShape();var a=this.getDefaultStyle();a&&this.useStyle(a);for(var n=0;n<i.length;n++){var o=i[n],s=r[o];o==="style"?this.style?Y(this.style,s):this.useStyle(s):o==="shape"?Y(this.shape,s):e.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var r=this.style.fill;if(r!=="none"){if(se(r)){var i=Ne(r,0);return i>.5?ca:i>.2?hh:da}else if(r)return da}return ca},t.prototype.getInsideTextStroke=function(r){var i=this.style.fill;if(se(i)){var a=this.__zr,n=!!(a&&a.isDarkMode()),o=Ne(r,0)<la;if(n===o)return i}},t.prototype.buildPath=function(r,i,a){},t.prototype.pathUpdated=function(){this.__dirty&=~Mr},t.prototype.getUpdatedPathProxy=function(r){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,r),this.path},t.prototype.createPathProxy=function(){this.path=new pr(!1)},t.prototype.hasStroke=function(){var r=this.style,i=r.stroke;return!(i==null||i==="none"||!(r.lineWidth>0))},t.prototype.hasFill=function(){var r=this.style,i=r.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var r=this._rect,i=this.style,a=!r;if(a){var n=!1;this.path||(n=!0,this.createPathProxy());var o=this.path;(n||this.__dirty&Mr)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),r=o.getBoundingRect()}if(this._rect=r,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=r.clone());if(this.__dirty||a){s.copy(r);var f=i.strokeNoScale?this.getLineScale():1,h=i.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;h=Math.max(h,u==null?4:u)}f>1e-10&&(s.width+=h/f,s.height+=h/f,s.x-=h/f/2,s.y-=h/f/2)}return s}return r},t.prototype.contain=function(r,i){var a=this.transformCoordToLocal(r,i),n=this.getBoundingRect(),o=this.style;if(r=a[0],i=a[1],n.contain(r,i)){var s=this.path;if(this.hasStroke()){var f=o.lineWidth,h=o.strokeNoScale?this.getLineScale():1;if(h>1e-10&&(this.hasFill()||(f=Math.max(f,this.strokeContainThreshold)),Qh(s,f/h,r,i)))return!0}if(this.hasFill())return Vh(s,r,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Mr,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(r){return this.animate("shape",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():r==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(r,i){r==="shape"?this.setShape(i):e.prototype.attrKV.call(this,r,i)},t.prototype.setShape=function(r,i){var a=this.shape;return a||(a=this.shape={}),typeof r=="string"?a[r]=i:Y(a,r),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Mr)},t.prototype.createStyle=function(r){return hi(Uo,r)},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var i=this._normalState;r.shape&&!i.shape&&(i.shape=Y({},this.shape))},t.prototype._applyStateObj=function(r,i,a,n,o,s){e.prototype._applyStateObj.call(this,r,i,a,n,o,s);var f=!(i&&n),h;if(i&&i.shape?o?n?h=i.shape:(h=Y({},a.shape),Y(h,i.shape)):(h=Y({},n?this.shape:a.shape),Y(h,i.shape)):f&&(h=a.shape),h)if(o){this.shape=Y({},this.shape);for(var u={},v=tt(h),l=0;l<v.length;l++){var c=v[l];typeof h[c]=="object"?this.shape[c]=h[c]:u[c]=h[c]}this._transitionState(r,{shape:u},s)}else this.shape=h,this.dirtyShape()},t.prototype._mergeStates=function(r){for(var i=e.prototype._mergeStates.call(this,r),a,n=0;n<r.length;n++){var o=r[n];o.shape&&(a=a||{},this._mergeStyle(a,o.shape))}return a&&(i.shape=a),i},t.prototype.getAnimationStyleProps=function(){return Kh},t.prototype.isZeroArea=function(){return!1},t.extend=function(r){var i=function(n){k(o,n);function o(s){var f=n.call(this,s)||this;return r.init&&r.init.call(f,s),f}return o.prototype.getDefaultStyle=function(){return zr(r.style)},o.prototype.getDefaultShape=function(){return zr(r.shape)},o}(t);for(var a in r)typeof r[a]=="function"&&(i.prototype[a]=r[a]);return i},t.initDefaultProps=function(){var r=t.prototype;r.type="path",r.strokeContainThreshold=5,r.segmentIgnoreThreshold=0,r.subPixelOptimize=!1,r.autoBatch=!1,r.__dirty=ht|Kr|Mr}(),t}(pe);const q=Jh;var jh=_t({strokeFirst:!0,font:dr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Uo),Zo=function(e){k(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var r=this.style,i=r.stroke;return i!=null&&i!=="none"&&r.lineWidth>0},t.prototype.hasFill=function(){var r=this.style,i=r.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(r){return hi(jh,r)},t.prototype.setBoundingRect=function(r){this._rect=r},t.prototype.getBoundingRect=function(){var r=this.style;if(!this._rect){var i=r.text;i!=null?i+="":i="";var a=lh(i,r.font,r.textAlign,r.textBaseline);if(a.x+=r.x||0,a.y+=r.y||0,this.hasStroke()){var n=r.lineWidth;a.x-=n/2,a.y-=n/2,a.width+=n,a.height+=n}this._rect=a}return this._rect},t.initDefaultProps=function(){var r=t.prototype;r.dirtyRectTolerance=10}(),t}(pe);Zo.prototype.type="tspan";const Qe=Zo;var tu=_t({x:0,y:0},cr),ru={style:_t({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},li.style)};function eu(e){return!!(e&&typeof e!="string"&&e.width&&e.height)}var No=function(e){k(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.createStyle=function(r){return hi(tu,r)},t.prototype._getSize=function(r){var i=this.style,a=i[r];if(a!=null)return a;var n=eu(i.image)?i.image:this.__image;if(!n)return 0;var o=r==="width"?"height":"width",s=i[o];return s==null?n[r]:n[r]/n[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return ru},t.prototype.getBoundingRect=function(){var r=this.style;return this._rect||(this._rect=new $(r.x||0,r.y||0,this.getWidth(),this.getHeight())),this._rect},t}(pe);No.prototype.type="image";const Ia=No;function iu(e,t){var r=t.x,i=t.y,a=t.width,n=t.height,o=t.r,s,f,h,u;a<0&&(r=r+a,a=-a),n<0&&(i=i+n,n=-n),typeof o=="number"?s=f=h=u=o:o instanceof Array?o.length===1?s=f=h=u=o[0]:o.length===2?(s=h=o[0],f=u=o[1]):o.length===3?(s=o[0],f=u=o[1],h=o[2]):(s=o[0],f=o[1],h=o[2],u=o[3]):s=f=h=u=0;var v;s+f>a&&(v=s+f,s*=a/v,f*=a/v),h+u>a&&(v=h+u,h*=a/v,u*=a/v),f+h>n&&(v=f+h,f*=n/v,h*=n/v),s+u>n&&(v=s+u,s*=n/v,u*=n/v),e.moveTo(r+s,i),e.lineTo(r+a-f,i),f!==0&&e.arc(r+a-f,i+f,f,-Math.PI/2,0),e.lineTo(r+a,i+n-h),h!==0&&e.arc(r+a-h,i+n-h,h,0,Math.PI/2),e.lineTo(r+u,i+n),u!==0&&e.arc(r+u,i+n-u,u,Math.PI/2,Math.PI),e.lineTo(r,i+s),s!==0&&e.arc(r+s,i+s,s,Math.PI,Math.PI*1.5)}var Fr=Math.round;function au(e,t,r){if(!!t){var i=t.x1,a=t.x2,n=t.y1,o=t.y2;e.x1=i,e.x2=a,e.y1=n,e.y2=o;var s=r&&r.lineWidth;return s&&(Fr(i*2)===Fr(a*2)&&(e.x1=e.x2=Er(i,s,!0)),Fr(n*2)===Fr(o*2)&&(e.y1=e.y2=Er(n,s,!0))),e}}function nu(e,t,r){if(!!t){var i=t.x,a=t.y,n=t.width,o=t.height;e.x=i,e.y=a,e.width=n,e.height=o;var s=r&&r.lineWidth;return s&&(e.x=Er(i,s,!0),e.y=Er(a,s,!0),e.width=Math.max(Er(i+n,s,!1)-e.x,n===0?0:1),e.height=Math.max(Er(a+o,s,!1)-e.y,o===0?0:1)),e}}function Er(e,t,r){if(!t)return e;var i=Fr(e*2);return(i+Fr(t))%2===0?i/2:(i+(r?1:-1))/2}var ou=function(){function e(){this.x=0,this.y=0,this.width=0,this.height=0}return e}(),su={},Vo=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new ou},t.prototype.buildPath=function(r,i){var a,n,o,s;if(this.subPixelOptimize){var f=nu(su,i,this.style);a=f.x,n=f.y,o=f.width,s=f.height,f.r=i.r,i=f}else a=i.x,n=i.y,o=i.width,s=i.height;i.r?iu(r,i):r.rect(a,n,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(q);Vo.prototype.type="rect";const Ke=Vo;var yn={fill:"#000"},mn=2,fu={style:_t({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},li.style)},Qo=function(e){k(t,e);function t(r){var i=e.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=yn,i.attr(r),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){e.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var r=0;r<this._children.length;r++){var i=this._children[r];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var r=this.innerTransformable;r?(r.updateTransform(),r.transform&&(this.transform=r.transform)):e.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(r){var i=this.innerTransformable;return i?i.getLocalTransform(r):e.prototype.getLocalTransform.call(this,r)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),e.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,cu(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var i=0;i<this._children.length;i++)this._children[i].__zr=r},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var r=new $(0,0,0,0),i=this._children,a=[],n=null,o=0;o<i.length;o++){var s=i[o],f=s.getBoundingRect(),h=s.getLocalTransform(a);h?(r.copy(f),r.applyTransform(h),n=n||r.clone(),n.union(r)):(n=n||f.clone(),n.union(f))}this._rect=n||r}return this._rect},t.prototype.setDefaultTextStyle=function(r){this._defaultStyle=r||yn},t.prototype.setTextContent=function(r){},t.prototype._mergeStyle=function(r,i){if(!i)return r;var a=i.rich,n=r.rich||a&&{};return Y(r,i),a&&n?(this._mergeRich(n,a),r.rich=n):n&&(r.rich=n),r},t.prototype._mergeRich=function(r,i){for(var a=tt(i),n=0;n<a.length;n++){var o=a[n];r[o]=r[o]||{},Y(r[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return fu},t.prototype._getOrCreateChild=function(r){var i=this._children[this._childCursor];return(!i||!(i instanceof r))&&(i=new r),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var r=this.style,i=r.font||dr,a=r.padding,n=Sn(r),o=Sh(n,r),s=Ui(r),f=!!r.backgroundColor,h=o.outerHeight,u=o.outerWidth,v=o.contentWidth,l=o.lines,c=o.lineHeight,_=this._defaultStyle;this.isTruncated=!!o.isTruncated;var g=r.x||0,d=r.y||0,p=r.align||_.align||"left",y=r.verticalAlign||_.verticalAlign||"top",m=g,w=Lr(d,o.contentHeight,y);if(s||a){var C=jr(g,u,p),T=Lr(d,h,y);s&&this._renderBackground(r,r,C,T,u,h)}w+=c/2,a&&(m=Ln(g,p,a),y==="top"?w+=a[0]:y==="bottom"&&(w-=a[2]));for(var b=0,M=!1,L=Mn("fill"in r?r.fill:(M=!0,_.fill)),S=Cn("stroke"in r?r.stroke:!f&&(!_.autoStroke||M)?(b=mn,_.stroke):null),P=r.textShadowBlur>0,D=r.width!=null&&(r.overflow==="truncate"||r.overflow==="break"||r.overflow==="breakAll"),A=o.calculatedLineHeight,R=0;R<l.length;R++){var E=this._getOrCreateChild(Qe),x=E.createStyle();E.useStyle(x),x.text=l[R],x.x=m,x.y=w,p&&(x.textAlign=p),x.textBaseline="middle",x.opacity=r.opacity,x.strokeFirst=!0,P&&(x.shadowBlur=r.textShadowBlur||0,x.shadowColor=r.textShadowColor||"transparent",x.shadowOffsetX=r.textShadowOffsetX||0,x.shadowOffsetY=r.textShadowOffsetY||0),x.stroke=S,x.fill=L,S&&(x.lineWidth=r.lineWidth||b,x.lineDash=r.lineDash,x.lineDashOffset=r.lineDashOffset||0),x.font=i,Tn(x,r),w+=c,D&&E.setBoundingRect(new $(jr(x.x,v,x.textAlign),Lr(x.y,A,x.textBaseline),v,A))}},t.prototype._updateRichTexts=function(){var r=this.style,i=Sn(r),a=Dh(i,r),n=a.width,o=a.outerWidth,s=a.outerHeight,f=r.padding,h=r.x||0,u=r.y||0,v=this._defaultStyle,l=r.align||v.align,c=r.verticalAlign||v.verticalAlign;this.isTruncated=!!a.isTruncated;var _=jr(h,o,l),g=Lr(u,s,c),d=_,p=g;f&&(d+=f[3],p+=f[0]);var y=d+n;Ui(r)&&this._renderBackground(r,r,_,g,o,s);for(var m=!!r.backgroundColor,w=0;w<a.lines.length;w++){for(var C=a.lines[w],T=C.tokens,b=T.length,M=C.lineHeight,L=C.width,S=0,P=d,D=y,A=b-1,R=void 0;S<b&&(R=T[S],!R.align||R.align==="left");)this._placeToken(R,r,M,p,P,"left",m),L-=R.width,P+=R.width,S++;for(;A>=0&&(R=T[A],R.align==="right");)this._placeToken(R,r,M,p,D,"right",m),L-=R.width,D-=R.width,A--;for(P+=(n-(P-d)-(y-D)-L)/2;S<=A;)R=T[S],this._placeToken(R,r,M,p,P+R.width/2,"center",m),P+=R.width,S++;p+=M}},t.prototype._placeToken=function(r,i,a,n,o,s,f){var h=i.rich[r.styleName]||{};h.text=r.text;var u=r.verticalAlign,v=n+a/2;u==="top"?v=n+r.height/2:u==="bottom"&&(v=n+a-r.height/2);var l=!r.isLineHolder&&Ui(h);l&&this._renderBackground(h,i,s==="right"?o-r.width:s==="center"?o-r.width/2:o,v-r.height/2,r.width,r.height);var c=!!h.backgroundColor,_=r.textPadding;_&&(o=Ln(o,s,_),v-=r.height/2-_[0]-r.innerHeight/2);var g=this._getOrCreateChild(Qe),d=g.createStyle();g.useStyle(d);var p=this._defaultStyle,y=!1,m=0,w=Mn("fill"in h?h.fill:"fill"in i?i.fill:(y=!0,p.fill)),C=Cn("stroke"in h?h.stroke:"stroke"in i?i.stroke:!c&&!f&&(!p.autoStroke||y)?(m=mn,p.stroke):null),T=h.textShadowBlur>0||i.textShadowBlur>0;d.text=r.text,d.x=o,d.y=v,T&&(d.shadowBlur=h.textShadowBlur||i.textShadowBlur||0,d.shadowColor=h.textShadowColor||i.textShadowColor||"transparent",d.shadowOffsetX=h.textShadowOffsetX||i.textShadowOffsetX||0,d.shadowOffsetY=h.textShadowOffsetY||i.textShadowOffsetY||0),d.textAlign=s,d.textBaseline="middle",d.font=r.font||dr,d.opacity=ke(h.opacity,i.opacity,1),Tn(d,h),C&&(d.lineWidth=ke(h.lineWidth,i.lineWidth,m),d.lineDash=ut(h.lineDash,i.lineDash),d.lineDashOffset=i.lineDashOffset||0,d.stroke=C),w&&(d.fill=w);var b=r.contentWidth,M=r.contentHeight;g.setBoundingRect(new $(jr(d.x,b,d.textAlign),Lr(d.y,M,d.textBaseline),b,M))},t.prototype._renderBackground=function(r,i,a,n,o,s){var f=r.backgroundColor,h=r.borderWidth,u=r.borderColor,v=f&&f.image,l=f&&!v,c=r.borderRadius,_=this,g,d;if(l||r.lineHeight||h&&u){g=this._getOrCreateChild(Ke),g.useStyle(g.createStyle()),g.style.fill=null;var p=g.shape;p.x=a,p.y=n,p.width=o,p.height=s,p.r=c,g.dirtyShape()}if(l){var y=g.style;y.fill=f||null,y.fillOpacity=ut(r.fillOpacity,1)}else if(v){d=this._getOrCreateChild(Ia),d.onload=function(){_.dirtyStyle()};var m=d.style;m.image=f.image,m.x=a,m.y=n,m.width=o,m.height=s}if(h&&u){var y=g.style;y.lineWidth=h,y.stroke=u,y.strokeOpacity=ut(r.strokeOpacity,1),y.lineDash=r.borderDash,y.lineDashOffset=r.borderDashOffset||0,g.strokeContainThreshold=0,g.hasFill()&&g.hasStroke()&&(y.strokeFirst=!0,y.lineWidth*=2)}var w=(g||d).style;w.shadowBlur=r.shadowBlur||0,w.shadowColor=r.shadowColor||"transparent",w.shadowOffsetX=r.shadowOffsetX||0,w.shadowOffsetY=r.shadowOffsetY||0,w.opacity=ke(r.opacity,i.opacity,1)},t.makeFont=function(r){var i="";return lu(r)&&(i=[r.fontStyle,r.fontWeight,vu(r.fontSize),r.fontFamily||"sans-serif"].join(" ")),i&&Rr(i)||r.textFont||r.font},t}(pe),hu={left:!0,right:1,center:1},uu={top:1,bottom:1,middle:1},wn=["fontStyle","fontWeight","fontSize","fontFamily"];function vu(e){return typeof e=="string"&&(e.indexOf("px")!==-1||e.indexOf("rem")!==-1||e.indexOf("em")!==-1)?e:isNaN(+e)?La+"px":e+"px"}function Tn(e,t){for(var r=0;r<wn.length;r++){var i=wn[r],a=t[i];a!=null&&(e[i]=a)}}function lu(e){return e.fontSize!=null||e.fontFamily||e.fontWeight}function cu(e){return bn(e),j(e.rich,bn),e}function bn(e){if(e){e.font=Qo.makeFont(e);var t=e.align;t==="middle"&&(t="center"),e.align=t==null||hu[t]?t:"left";var r=e.verticalAlign;r==="center"&&(r="middle"),e.verticalAlign=r==null||uu[r]?r:"top";var i=e.padding;i&&(e.padding=$s(e.padding))}}function Cn(e,t){return e==null||t<=0||e==="transparent"||e==="none"?null:e.image||e.colorStops?"#000":e}function Mn(e){return e==null||e==="none"?null:e.image||e.colorStops?"#000":e}function Ln(e,t,r){return t==="right"?e-r[1]:t==="center"?e+r[3]/2-r[1]/2:e+r[3]}function Sn(e){var t=e.text;return t!=null&&(t+=""),t}function Ui(e){return!!(e.backgroundColor||e.lineHeight||e.borderWidth&&e.borderColor)}const vl=Qo;var Tr=pr.CMD,du=[[],[],[]],Pn=Math.sqrt,pu=Math.atan2;function Ko(e,t){if(!!t){var r=e.data,i=e.len(),a,n,o,s,f,h,u=Tr.M,v=Tr.C,l=Tr.L,c=Tr.R,_=Tr.A,g=Tr.Q;for(o=0,s=0;o<i;){switch(a=r[o++],s=o,n=0,a){case u:n=1;break;case l:n=1;break;case v:n=3;break;case g:n=2;break;case _:var d=t[4],p=t[5],y=Pn(t[0]*t[0]+t[1]*t[1]),m=Pn(t[2]*t[2]+t[3]*t[3]),w=pu(-t[1]/m,t[0]/y);r[o]*=y,r[o++]+=d,r[o]*=m,r[o++]+=p,r[o++]*=y,r[o++]*=m,r[o++]+=w,r[o++]+=w,o+=2,s=o;break;case c:h[0]=r[o++],h[1]=r[o++],ee(h,h,t),r[s++]=h[0],r[s++]=h[1],h[0]+=r[o++],h[1]+=r[o++],ee(h,h,t),r[s++]=h[0],r[s++]=h[1]}for(f=0;f<n;f++){var C=du[f];C[0]=r[o++],C[1]=r[o++],ee(C,C,t),r[s++]=C[0],r[s++]=C[1]}}e.increaseVersion()}}var Zi=Math.sqrt,De=Math.sin,xe=Math.cos,Qr=Math.PI;function Rn(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1])}function ya(e,t){return(e[0]*t[0]+e[1]*t[1])/(Rn(e)*Rn(t))}function Dn(e,t){return(e[0]*t[1]<e[1]*t[0]?-1:1)*Math.acos(ya(e,t))}function xn(e,t,r,i,a,n,o,s,f,h,u){var v=f*(Qr/180),l=xe(v)*(e-r)/2+De(v)*(t-i)/2,c=-1*De(v)*(e-r)/2+xe(v)*(t-i)/2,_=l*l/(o*o)+c*c/(s*s);_>1&&(o*=Zi(_),s*=Zi(_));var g=(a===n?-1:1)*Zi((o*o*(s*s)-o*o*(c*c)-s*s*(l*l))/(o*o*(c*c)+s*s*(l*l)))||0,d=g*o*c/s,p=g*-s*l/o,y=(e+r)/2+xe(v)*d-De(v)*p,m=(t+i)/2+De(v)*d+xe(v)*p,w=Dn([1,0],[(l-d)/o,(c-p)/s]),C=[(l-d)/o,(c-p)/s],T=[(-1*l-d)/o,(-1*c-p)/s],b=Dn(C,T);if(ya(C,T)<=-1&&(b=Qr),ya(C,T)>=1&&(b=0),b<0){var M=Math.round(b/Qr*1e6)/1e6;b=Qr*2+M%2*Qr}u.addData(h,y,m,o,s,w,b,v,n)}var _u=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,gu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function yu(e){var t=new pr;if(!e)return t;var r=0,i=0,a=r,n=i,o,s=pr.CMD,f=e.match(_u);if(!f)return t;for(var h=0;h<f.length;h++){for(var u=f[h],v=u.charAt(0),l=void 0,c=u.match(gu)||[],_=c.length,g=0;g<_;g++)c[g]=parseFloat(c[g]);for(var d=0;d<_;){var p=void 0,y=void 0,m=void 0,w=void 0,C=void 0,T=void 0,b=void 0,M=r,L=i,S=void 0,P=void 0;switch(v){case"l":r+=c[d++],i+=c[d++],l=s.L,t.addData(l,r,i);break;case"L":r=c[d++],i=c[d++],l=s.L,t.addData(l,r,i);break;case"m":r+=c[d++],i+=c[d++],l=s.M,t.addData(l,r,i),a=r,n=i,v="l";break;case"M":r=c[d++],i=c[d++],l=s.M,t.addData(l,r,i),a=r,n=i,v="L";break;case"h":r+=c[d++],l=s.L,t.addData(l,r,i);break;case"H":r=c[d++],l=s.L,t.addData(l,r,i);break;case"v":i+=c[d++],l=s.L,t.addData(l,r,i);break;case"V":i=c[d++],l=s.L,t.addData(l,r,i);break;case"C":l=s.C,t.addData(l,c[d++],c[d++],c[d++],c[d++],c[d++],c[d++]),r=c[d-2],i=c[d-1];break;case"c":l=s.C,t.addData(l,c[d++]+r,c[d++]+i,c[d++]+r,c[d++]+i,c[d++]+r,c[d++]+i),r+=c[d-2],i+=c[d-1];break;case"S":p=r,y=i,S=t.len(),P=t.data,o===s.C&&(p+=r-P[S-4],y+=i-P[S-3]),l=s.C,M=c[d++],L=c[d++],r=c[d++],i=c[d++],t.addData(l,p,y,M,L,r,i);break;case"s":p=r,y=i,S=t.len(),P=t.data,o===s.C&&(p+=r-P[S-4],y+=i-P[S-3]),l=s.C,M=r+c[d++],L=i+c[d++],r+=c[d++],i+=c[d++],t.addData(l,p,y,M,L,r,i);break;case"Q":M=c[d++],L=c[d++],r=c[d++],i=c[d++],l=s.Q,t.addData(l,M,L,r,i);break;case"q":M=c[d++]+r,L=c[d++]+i,r+=c[d++],i+=c[d++],l=s.Q,t.addData(l,M,L,r,i);break;case"T":p=r,y=i,S=t.len(),P=t.data,o===s.Q&&(p+=r-P[S-4],y+=i-P[S-3]),r=c[d++],i=c[d++],l=s.Q,t.addData(l,p,y,r,i);break;case"t":p=r,y=i,S=t.len(),P=t.data,o===s.Q&&(p+=r-P[S-4],y+=i-P[S-3]),r+=c[d++],i+=c[d++],l=s.Q,t.addData(l,p,y,r,i);break;case"A":m=c[d++],w=c[d++],C=c[d++],T=c[d++],b=c[d++],M=r,L=i,r=c[d++],i=c[d++],l=s.A,xn(M,L,r,i,T,b,m,w,C,l,t);break;case"a":m=c[d++],w=c[d++],C=c[d++],T=c[d++],b=c[d++],M=r,L=i,r+=c[d++],i+=c[d++],l=s.A,xn(M,L,r,i,T,b,m,w,C,l,t);break}}(v==="z"||v==="Z")&&(l=s.Z,t.addData(l),r=a,i=n),o=l}return t.toStatic(),t}var Jo=function(e){k(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.applyTransform=function(r){},t}(q);function jo(e){return e.setData!=null}function ts(e,t){var r=yu(e),i=Y({},t);return i.buildPath=function(a){if(jo(a)){a.setData(r.data);var n=a.getContext();n&&a.rebuildPath(n,1)}else{var n=a;r.rebuildPath(n,1)}},i.applyTransform=function(a){Ko(r,a),this.dirtyShape()},i}function mu(e,t){return new Jo(ts(e,t))}function ll(e,t){var r=ts(e,t),i=function(a){k(n,a);function n(o){var s=a.call(this,o)||this;return s.applyTransform=r.applyTransform,s.buildPath=r.buildPath,s}return n}(Jo);return i}function cl(e,t){for(var r=[],i=e.length,a=0;a<i;a++){var n=e[a];r.push(n.getUpdatedPathProxy(!0))}var o=new q(t);return o.createPathProxy(),o.buildPath=function(s){if(jo(s)){s.appendPath(r);var f=s.getContext();f&&s.rebuildPath(f,1)}},o}function rs(e,t){t=t||{};var r=new q;return e.shape&&r.setShape(e.shape),r.setStyle(e.style),t.bakeTransform?Ko(r.path,e.getComputedTransform()):t.toLocal?r.setLocalTransform(e.getComputedTransform()):r.copyTransform(e),r.buildPath=e.buildPath,r.applyTransform=r.applyTransform,r.z=e.z,r.z2=e.z2,r.zlevel=e.zlevel,r}var wu=function(){function e(){this.cx=0,this.cy=0,this.r=0}return e}(),es=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new wu},t.prototype.buildPath=function(r,i){r.moveTo(i.cx+i.r,i.cy),r.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(q);es.prototype.type="circle";const Tu=es;var bu=function(){function e(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return e}(),is=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new bu},t.prototype.buildPath=function(r,i){var a=.5522848,n=i.cx,o=i.cy,s=i.rx,f=i.ry,h=s*a,u=f*a;r.moveTo(n-s,o),r.bezierCurveTo(n-s,o-u,n-h,o-f,n,o-f),r.bezierCurveTo(n+h,o-f,n+s,o-u,n+s,o),r.bezierCurveTo(n+s,o+u,n+h,o+f,n,o+f),r.bezierCurveTo(n-h,o+f,n-s,o+u,n-s,o),r.closePath()},t}(q);is.prototype.type="ellipse";const Cu=is;var as=Math.PI,Ni=as*2,or=Math.sin,br=Math.cos,Mu=Math.acos,V=Math.atan2,An=Math.abs,ae=Math.sqrt,te=Math.max,St=Math.min,gt=1e-4;function Lu(e,t,r,i,a,n,o,s){var f=r-e,h=i-t,u=o-a,v=s-n,l=v*f-u*h;if(!(l*l<gt))return l=(u*(t-n)-v*(e-a))/l,[e+l*f,t+l*h]}function Ae(e,t,r,i,a,n,o){var s=e-r,f=t-i,h=(o?n:-n)/ae(s*s+f*f),u=h*f,v=-h*s,l=e+u,c=t+v,_=r+u,g=i+v,d=(l+_)/2,p=(c+g)/2,y=_-l,m=g-c,w=y*y+m*m,C=a-n,T=l*g-_*c,b=(m<0?-1:1)*ae(te(0,C*C*w-T*T)),M=(T*m-y*b)/w,L=(-T*y-m*b)/w,S=(T*m+y*b)/w,P=(-T*y+m*b)/w,D=M-d,A=L-p,R=S-d,E=P-p;return D*D+A*A>R*R+E*E&&(M=S,L=P),{cx:M,cy:L,x0:-u,y0:-v,x1:M*(a/C-1),y1:L*(a/C-1)}}function Su(e){var t;if(oe(e)){var r=e.length;if(!r)return e;r===1?t=[e[0],e[0],0,0]:r===2?t=[e[0],e[0],e[1],e[1]]:r===3?t=e.concat(e[2]):t=e}else t=[e,e,e,e];return t}function Pu(e,t){var r,i=te(t.r,0),a=te(t.r0||0,0),n=i>0,o=a>0;if(!(!n&&!o)){if(n||(i=a,a=0),a>i){var s=i;i=a,a=s}var f=t.startAngle,h=t.endAngle;if(!(isNaN(f)||isNaN(h))){var u=t.cx,v=t.cy,l=!!t.clockwise,c=An(h-f),_=c>Ni&&c%Ni;if(_>gt&&(c=_),!(i>gt))e.moveTo(u,v);else if(c>Ni-gt)e.moveTo(u+i*br(f),v+i*or(f)),e.arc(u,v,i,f,h,!l),a>gt&&(e.moveTo(u+a*br(h),v+a*or(h)),e.arc(u,v,a,h,f,l));else{var g=void 0,d=void 0,p=void 0,y=void 0,m=void 0,w=void 0,C=void 0,T=void 0,b=void 0,M=void 0,L=void 0,S=void 0,P=void 0,D=void 0,A=void 0,R=void 0,E=i*br(f),x=i*or(f),H=a*br(h),z=a*or(h),X=c>gt;if(X){var U=t.cornerRadius;U&&(r=Su(U),g=r[0],d=r[1],p=r[2],y=r[3]);var G=An(i-a)/2;if(m=St(G,p),w=St(G,y),C=St(G,g),T=St(G,d),L=b=te(m,w),S=M=te(C,T),(b>gt||M>gt)&&(P=i*br(h),D=i*or(h),A=a*br(f),R=a*or(f),c<as)){var W=Lu(E,x,A,R,P,D,H,z);if(W){var ot=E-W[0],st=x-W[1],Ft=P-W[0],qt=D-W[1],Ut=1/or(Mu((ot*Ft+st*qt)/(ae(ot*ot+st*st)*ae(Ft*Ft+qt*qt)))/2),Zt=ae(W[0]*W[0]+W[1]*W[1]);L=St(b,(i-Zt)/(Ut+1)),S=St(M,(a-Zt)/(Ut-1))}}}if(!X)e.moveTo(u+E,v+x);else if(L>gt){var rt=St(p,L),Z=St(y,L),F=Ae(A,R,E,x,i,rt,l),O=Ae(P,D,H,z,i,Z,l);e.moveTo(u+F.cx+F.x0,v+F.cy+F.y0),L<b&&rt===Z?e.arc(u+F.cx,v+F.cy,L,V(F.y0,F.x0),V(O.y0,O.x0),!l):(rt>0&&e.arc(u+F.cx,v+F.cy,rt,V(F.y0,F.x0),V(F.y1,F.x1),!l),e.arc(u,v,i,V(F.cy+F.y1,F.cx+F.x1),V(O.cy+O.y1,O.cx+O.x1),!l),Z>0&&e.arc(u+O.cx,v+O.cy,Z,V(O.y1,O.x1),V(O.y0,O.x0),!l))}else e.moveTo(u+E,v+x),e.arc(u,v,i,f,h,!l);if(!(a>gt)||!X)e.lineTo(u+H,v+z);else if(S>gt){var rt=St(g,S),Z=St(d,S),F=Ae(H,z,P,D,a,-Z,l),O=Ae(E,x,A,R,a,-rt,l);e.lineTo(u+F.cx+F.x0,v+F.cy+F.y0),S<M&&rt===Z?e.arc(u+F.cx,v+F.cy,S,V(F.y0,F.x0),V(O.y0,O.x0),!l):(Z>0&&e.arc(u+F.cx,v+F.cy,Z,V(F.y0,F.x0),V(F.y1,F.x1),!l),e.arc(u,v,a,V(F.cy+F.y1,F.cx+F.x1),V(O.cy+O.y1,O.cx+O.x1),l),rt>0&&e.arc(u+O.cx,v+O.cy,rt,V(O.y1,O.x1),V(O.y0,O.x0),!l))}else e.lineTo(u+H,v+z),e.arc(u,v,a,h,f,l)}e.closePath()}}}var Ru=function(){function e(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return e}(),ns=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Ru},t.prototype.buildPath=function(r,i){Pu(r,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(q);ns.prototype.type="sector";const Fn=ns;var Du=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return e}(),os=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Du},t.prototype.buildPath=function(r,i){var a=i.cx,n=i.cy,o=Math.PI*2;r.moveTo(a+i.r,n),r.arc(a,n,i.r,0,o,!1),r.moveTo(a+i.r0,n),r.arc(a,n,i.r0,0,o,!0)},t}(q);os.prototype.type="ring";const dl=os;function xu(e,t,r,i){var a=[],n=[],o=[],s=[],f,h,u,v;if(i){u=[1/0,1/0],v=[-1/0,-1/0];for(var l=0,c=e.length;l<c;l++)Dr(u,u,e[l]),xr(v,v,e[l]);Dr(u,u,i[0]),xr(v,v,i[1])}for(var l=0,c=e.length;l<c;l++){var _=e[l];if(r)f=e[l?l-1:c-1],h=e[(l+1)%c];else if(l===0||l===c-1){a.push(Us(e[l]));continue}else f=e[l-1],h=e[l+1];Zs(n,h,f),di(n,n,t);var g=ji(_,f),d=ji(_,h),p=g+d;p!==0&&(g/=p,d/=p),di(o,n,-g),di(s,n,d);var y=ka([],_,o),m=ka([],_,s);i&&(xr(y,y,u),Dr(y,y,v),xr(m,m,u),Dr(m,m,v)),a.push(y),a.push(m)}return r&&a.push(a.shift()),a}function ss(e,t,r){var i=t.smooth,a=t.points;if(a&&a.length>=2){if(i){var n=xu(a,i,r,t.smoothConstraint);e.moveTo(a[0][0],a[0][1]);for(var o=a.length,s=0;s<(r?o:o-1);s++){var f=n[s*2],h=n[s*2+1],u=a[(s+1)%o];e.bezierCurveTo(f[0],f[1],h[0],h[1],u[0],u[1])}}else{e.moveTo(a[0][0],a[0][1]);for(var s=1,v=a.length;s<v;s++)e.lineTo(a[s][0],a[s][1])}r&&e.closePath()}}var Au=function(){function e(){this.points=null,this.smooth=0,this.smoothConstraint=null}return e}(),fs=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Au},t.prototype.buildPath=function(r,i){ss(r,i,!0)},t}(q);fs.prototype.type="polygon";const hs=fs;var Fu=function(){function e(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return e}(),us=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Fu},t.prototype.buildPath=function(r,i){ss(r,i,!1)},t}(q);us.prototype.type="polyline";const Eu=us;var Iu={},Ou=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return e}(),vs=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Ou},t.prototype.buildPath=function(r,i){var a,n,o,s;if(this.subPixelOptimize){var f=au(Iu,i,this.style);a=f.x1,n=f.y1,o=f.x2,s=f.y2}else a=i.x1,n=i.y1,o=i.x2,s=i.y2;var h=i.percent;h!==0&&(r.moveTo(a,n),h<1&&(o=a*(1-h)+o*h,s=n*(1-h)+s*h),r.lineTo(o,s))},t.prototype.pointAt=function(r){var i=this.shape;return[i.x1*(1-r)+i.x2*r,i.y1*(1-r)+i.y2*r]},t}(q);vs.prototype.type="line";const Hu=vs;var et=[],Bu=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return e}();function En(e,t,r){var i=e.cpx2,a=e.cpy2;return i!=null||a!=null?[(r?Va:N)(e.x1,e.cpx1,e.cpx2,e.x2,t),(r?Va:N)(e.y1,e.cpy1,e.cpy2,e.y2,t)]:[(r?Qa:Q)(e.x1,e.cpx1,e.x2,t),(r?Qa:Q)(e.y1,e.cpy1,e.y2,t)]}var ls=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Bu},t.prototype.buildPath=function(r,i){var a=i.x1,n=i.y1,o=i.x2,s=i.y2,f=i.cpx1,h=i.cpy1,u=i.cpx2,v=i.cpy2,l=i.percent;l!==0&&(r.moveTo(a,n),u==null||v==null?(l<1&&(Ze(a,f,o,l,et),f=et[1],o=et[2],Ze(n,h,s,l,et),h=et[1],s=et[2]),r.quadraticCurveTo(f,h,o,s)):(l<1&&(Gt(a,f,u,o,l,et),f=et[1],u=et[2],o=et[3],Gt(n,h,v,s,l,et),h=et[1],v=et[2],s=et[3]),r.bezierCurveTo(f,h,u,v,o,s)))},t.prototype.pointAt=function(r){return En(this.shape,r,!1)},t.prototype.tangentAt=function(r){var i=En(this.shape,r,!0);return Qs(i,i)},t}(q);ls.prototype.type="bezier-curve";const pl=ls;var ku=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return e}(),cs=function(e){k(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new ku},t.prototype.buildPath=function(r,i){var a=i.cx,n=i.cy,o=Math.max(i.r,0),s=i.startAngle,f=i.endAngle,h=i.clockwise,u=Math.cos(s),v=Math.sin(s);r.moveTo(u*o+a,v*o+n),r.arc(a,n,o,s,f,!h)},t}(q);cs.prototype.type="arc";const _l=cs;var zu=function(e){k(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.type="compound",r}return t.prototype._updatePathDirty=function(){for(var r=this.shape.paths,i=this.shapeChanged(),a=0;a<r.length;a++)i=i||r[a].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var r=this.shape.paths||[],i=this.getGlobalScale(),a=0;a<r.length;a++)r[a].path||r[a].createPathProxy(),r[a].path.setScale(i[0],i[1],r[a].segmentIgnoreThreshold)},t.prototype.buildPath=function(r,i){for(var a=i.paths||[],n=0;n<a.length;n++)a[n].buildPath(r,a[n].shape,!0)},t.prototype.afterBrush=function(){for(var r=this.shape.paths||[],i=0;i<r.length;i++)r[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),q.prototype.getBoundingRect.call(this)},t}(q);const gl=zu;var Yu=function(){function e(t){this.colorStops=t||[]}return e.prototype.addColorStop=function(t,r){this.colorStops.push({offset:t,color:r})},e}();const ds=Yu;var $u=function(e){k(t,e);function t(r,i,a,n,o,s){var f=e.call(this,o)||this;return f.x=r==null?0:r,f.y=i==null?0:i,f.x2=a==null?1:a,f.y2=n==null?0:n,f.type="linear",f.global=s||!1,f}return t}(ds);const Wu=$u;var Xu=function(e){k(t,e);function t(r,i,a,n,o){var s=e.call(this,n)||this;return s.x=r==null?.5:r,s.y=i==null?.5:i,s.r=a==null?.5:a,s.type="radial",s.global=o||!1,s}return t}(ds);const Gu=Xu;var sr=[0,0],fr=[0,0],Fe=new I,Ee=new I,qu=function(){function e(t,r){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new I;for(var i=0;i<2;i++)this._axes[i]=new I;t&&this.fromBoundingRect(t,r)}return e.prototype.fromBoundingRect=function(t,r){var i=this._corners,a=this._axes,n=t.x,o=t.y,s=n+t.width,f=o+t.height;if(i[0].set(n,o),i[1].set(s,o),i[2].set(s,f),i[3].set(n,f),r)for(var h=0;h<4;h++)i[h].transform(r);I.sub(a[0],i[1],i[0]),I.sub(a[1],i[3],i[0]),a[0].normalize(),a[1].normalize();for(var h=0;h<2;h++)this._origin[h]=a[h].dot(i[0])},e.prototype.intersect=function(t,r){var i=!0,a=!r;return Fe.set(1/0,1/0),Ee.set(0,0),!this._intersectCheckOneSide(this,t,Fe,Ee,a,1)&&(i=!1,a)||!this._intersectCheckOneSide(t,this,Fe,Ee,a,-1)&&(i=!1,a)||a||I.copy(r,i?Fe:Ee),i},e.prototype._intersectCheckOneSide=function(t,r,i,a,n,o){for(var s=!0,f=0;f<2;f++){var h=this._axes[f];if(this._getProjMinMaxOnAxis(f,t._corners,sr),this._getProjMinMaxOnAxis(f,r._corners,fr),sr[1]<fr[0]||sr[0]>fr[1]){if(s=!1,n)return s;var u=Math.abs(fr[0]-sr[1]),v=Math.abs(sr[0]-fr[1]);Math.min(u,v)>a.len()&&(u<v?I.scale(a,h,-u*o):I.scale(a,h,v*o))}else if(i){var u=Math.abs(fr[0]-sr[1]),v=Math.abs(sr[0]-fr[1]);Math.min(u,v)<i.len()&&(u<v?I.scale(i,h,u*o):I.scale(i,h,-v*o))}}return s},e.prototype._getProjMinMaxOnAxis=function(t,r,i){for(var a=this._axes[t],n=this._origin,o=r[0].dot(a)+n[t],s=o,f=o,h=1;h<r.length;h++){var u=r[h].dot(a)+n[t];s=Math.min(u,s),f=Math.max(u,f)}i[0]=s,i[1]=f},e}();const yl=qu;var Uu=[],Zu=function(e){k(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.notClear=!0,r.incremental=!0,r._displayables=[],r._temporaryDisplayables=[],r._cursor=0,r}return t.prototype.traverse=function(r,i){r.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(r,i){i?this._temporaryDisplayables.push(r):this._displayables.push(r),this.markRedraw()},t.prototype.addDisplayables=function(r,i){i=i||!1;for(var a=0;a<r.length;a++)this.addDisplayable(r[a],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(r){for(var i=this._cursor;i<this._displayables.length;i++)r&&r(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)r&&r(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var r=this._cursor;r<this._displayables.length;r++){var i=this._displayables[r];i.parent=this,i.update(),i.parent=null}for(var r=0;r<this._temporaryDisplayables.length;r++){var i=this._temporaryDisplayables[r];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var r=new $(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var a=this._displayables[i],n=a.getBoundingRect().clone();a.needLocalTransform()&&n.applyTransform(a.getLocalTransform(Uu)),r.union(n)}this._rect=r}return this._rect},t.prototype.contain=function(r,i){var a=this.transformCoordToLocal(r,i),n=this.getBoundingRect();if(n.contain(a[0],a[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(r,i))return!0}return!1},t}(pe);const ml=Zu;var Nu=Math.round(Math.random()*9),Vu=typeof Object.defineProperty=="function",Qu=function(){function e(){this._id="__ec_inner_"+Nu++}return e.prototype.get=function(t){return this._guard(t)[this._id]},e.prototype.set=function(t,r){var i=this._guard(t);return Vu?Object.defineProperty(i,this._id,{value:r,enumerable:!1,configurable:!0}):i[this._id]=r,this},e.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},e.prototype.has=function(t){return!!this._guard(t)[this._id]},e.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},e}();const wl=Qu;function ur(e){return isFinite(e)}function Ku(e,t,r){var i=t.x==null?0:t.x,a=t.x2==null?1:t.x2,n=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*r.width+r.x,a=a*r.width+r.x,n=n*r.height+r.y,o=o*r.height+r.y),i=ur(i)?i:0,a=ur(a)?a:1,n=ur(n)?n:0,o=ur(o)?o:0;var s=e.createLinearGradient(i,n,a,o);return s}function Ju(e,t,r){var i=r.width,a=r.height,n=Math.min(i,a),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,f=t.r==null?.5:t.r;t.global||(o=o*i+r.x,s=s*a+r.y,f=f*n),o=ur(o)?o:.5,s=ur(s)?s:.5,f=f>=0&&ur(f)?f:.5;var h=e.createRadialGradient(o,s,0,o,s,f);return h}function ma(e,t,r){for(var i=t.type==="radial"?Ju(e,t,r):Ku(e,t,r),a=t.colorStops,n=0;n<a.length;n++)i.addColorStop(a[n].offset,a[n].color);return i}function ju(e,t){if(e===t||!e&&!t)return!1;if(!e||!t||e.length!==t.length)return!0;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!0;return!1}function Ie(e){return parseInt(e,10)}function Oe(e,t,r){var i=["width","height"][t],a=["clientWidth","clientHeight"][t],n=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(r[i]!=null&&r[i]!=="auto")return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(e);return(e[a]||Ie(s[i])||Ie(e.style[i]))-(Ie(s[n])||0)-(Ie(s[o])||0)|0}function tv(e,t){return!e||e==="solid"||!(t>0)?null:e==="dashed"?[4*t,2*t]:e==="dotted"?[t]:Be(e)?[e]:oe(e)?e:null}function ps(e){var t=e.style,r=t.lineDash&&t.lineWidth>0&&tv(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(r){var a=t.strokeNoScale&&e.getLineScale?e.getLineScale():1;a&&a!==1&&(r=Tt(r,function(n){return n/a}),i/=a)}return[r,i]}var rv=new pr(!0);function Je(e){var t=e.stroke;return!(t==null||t==="none"||!(e.lineWidth>0))}function In(e){return typeof e=="string"&&e!=="none"}function je(e){var t=e.fill;return t!=null&&t!=="none"}function On(e,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.fillOpacity*t.opacity,e.fill(),e.globalAlpha=r}else e.fill()}function Hn(e,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.strokeOpacity*t.opacity,e.stroke(),e.globalAlpha=r}else e.stroke()}function wa(e,t,r){var i=Yo(t.image,t.__image,r);if(vi(i)){var a=e.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&a&&a.setTransform){var n=new DOMMatrix;n.translateSelf(t.x||0,t.y||0),n.rotateSelf(0,0,(t.rotation||0)*qs),n.scaleSelf(t.scaleX||1,t.scaleY||1),a.setTransform(n)}return a}}function ev(e,t,r,i){var a,n=Je(r),o=je(r),s=r.strokePercent,f=s<1,h=!t.path;(!t.silent||f)&&h&&t.createPathProxy();var u=t.path||rv,v=t.__dirty;if(!i){var l=r.fill,c=r.stroke,_=o&&!!l.colorStops,g=n&&!!c.colorStops,d=o&&!!l.image,p=n&&!!c.image,y=void 0,m=void 0,w=void 0,C=void 0,T=void 0;(_||g)&&(T=t.getBoundingRect()),_&&(y=v?ma(e,l,T):t.__canvasFillGradient,t.__canvasFillGradient=y),g&&(m=v?ma(e,c,T):t.__canvasStrokeGradient,t.__canvasStrokeGradient=m),d&&(w=v||!t.__canvasFillPattern?wa(e,l,t):t.__canvasFillPattern,t.__canvasFillPattern=w),p&&(C=v||!t.__canvasStrokePattern?wa(e,c,t):t.__canvasStrokePattern,t.__canvasStrokePattern=w),_?e.fillStyle=y:d&&(w?e.fillStyle=w:o=!1),g?e.strokeStyle=m:p&&(C?e.strokeStyle=C:n=!1)}var b=t.getGlobalScale();u.setScale(b[0],b[1],t.segmentIgnoreThreshold);var M,L;e.setLineDash&&r.lineDash&&(a=ps(t),M=a[0],L=a[1]);var S=!0;(h||v&Mr)&&(u.setDPR(e.dpr),f?u.setContext(null):(u.setContext(e),S=!1),u.reset(),t.buildPath(u,t.shape,i),u.toStatic(),t.pathUpdated()),S&&u.rebuildPath(e,f?s:1),M&&(e.setLineDash(M),e.lineDashOffset=L),i||(r.strokeFirst?(n&&Hn(e,r),o&&On(e,r)):(o&&On(e,r),n&&Hn(e,r))),M&&e.setLineDash([])}function iv(e,t,r){var i=t.__image=Yo(r.image,t.__image,t,t.onload);if(!(!i||!vi(i))){var a=r.x||0,n=r.y||0,o=t.getWidth(),s=t.getHeight(),f=i.width/i.height;if(o==null&&s!=null?o=s*f:s==null&&o!=null?s=o/f:o==null&&s==null&&(o=i.width,s=i.height),r.sWidth&&r.sHeight){var h=r.sx||0,u=r.sy||0;e.drawImage(i,h,u,r.sWidth,r.sHeight,a,n,o,s)}else if(r.sx&&r.sy){var h=r.sx,u=r.sy,v=o-h,l=s-u;e.drawImage(i,h,u,v,l,a,n,o,s)}else e.drawImage(i,a,n,o,s)}}function av(e,t,r){var i,a=r.text;if(a!=null&&(a+=""),a){e.font=r.font||dr,e.textAlign=r.textAlign,e.textBaseline=r.textBaseline;var n=void 0,o=void 0;e.setLineDash&&r.lineDash&&(i=ps(t),n=i[0],o=i[1]),n&&(e.setLineDash(n),e.lineDashOffset=o),r.strokeFirst?(Je(r)&&e.strokeText(a,r.x,r.y),je(r)&&e.fillText(a,r.x,r.y)):(je(r)&&e.fillText(a,r.x,r.y),Je(r)&&e.strokeText(a,r.x,r.y)),n&&e.setLineDash([])}}var Bn=["shadowBlur","shadowOffsetX","shadowOffsetY"],kn=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function _s(e,t,r,i,a){var n=!1;if(!i&&(r=r||{},t===r))return!1;if(i||t.opacity!==r.opacity){nt(e,a),n=!0;var o=Math.max(Math.min(t.opacity,1),0);e.globalAlpha=isNaN(o)?cr.opacity:o}(i||t.blend!==r.blend)&&(n||(nt(e,a),n=!0),e.globalCompositeOperation=t.blend||cr.blend);for(var s=0;s<Bn.length;s++){var f=Bn[s];(i||t[f]!==r[f])&&(n||(nt(e,a),n=!0),e[f]=e.dpr*(t[f]||0))}return(i||t.shadowColor!==r.shadowColor)&&(n||(nt(e,a),n=!0),e.shadowColor=t.shadowColor||cr.shadowColor),n}function zn(e,t,r,i,a){var n=ve(t,a.inHover),o=i?null:r&&ve(r,a.inHover)||{};if(n===o)return!1;var s=_s(e,n,o,i,a);if((i||n.fill!==o.fill)&&(s||(nt(e,a),s=!0),In(n.fill)&&(e.fillStyle=n.fill)),(i||n.stroke!==o.stroke)&&(s||(nt(e,a),s=!0),In(n.stroke)&&(e.strokeStyle=n.stroke)),(i||n.opacity!==o.opacity)&&(s||(nt(e,a),s=!0),e.globalAlpha=n.opacity==null?1:n.opacity),t.hasStroke()){var f=n.lineWidth,h=f/(n.strokeNoScale&&t.getLineScale?t.getLineScale():1);e.lineWidth!==h&&(s||(nt(e,a),s=!0),e.lineWidth=h)}for(var u=0;u<kn.length;u++){var v=kn[u],l=v[0];(i||n[l]!==o[l])&&(s||(nt(e,a),s=!0),e[l]=n[l]||v[1])}return s}function nv(e,t,r,i,a){return _s(e,ve(t,a.inHover),r&&ve(r,a.inHover),i,a)}function gs(e,t){var r=t.transform,i=e.dpr||1;r?e.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):e.setTransform(i,0,0,i,0,0)}function ov(e,t,r){for(var i=!1,a=0;a<e.length;a++){var n=e[a];i=i||n.isZeroArea(),gs(t,n),t.beginPath(),n.buildPath(t,n.shape),t.clip()}r.allClipped=i}function sv(e,t){return e&&t?e[0]!==t[0]||e[1]!==t[1]||e[2]!==t[2]||e[3]!==t[3]||e[4]!==t[4]||e[5]!==t[5]:!(!e&&!t)}var Yn=1,$n=2,Wn=3,Xn=4;function fv(e){var t=je(e),r=Je(e);return!(e.lineDash||!(+t^+r)||t&&typeof e.fill!="string"||r&&typeof e.stroke!="string"||e.strokePercent<1||e.strokeOpacity<1||e.fillOpacity<1)}function nt(e,t){t.batchFill&&e.fill(),t.batchStroke&&e.stroke(),t.batchFill="",t.batchStroke=""}function ve(e,t){return t&&e.__hoverStyle||e.style}function hv(e,t){vr(e,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function vr(e,t,r,i){var a=t.transform;if(!t.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1)){t.__dirty&=~ht,t.__isRendered=!1;return}var n=t.__clipPaths,o=r.prevElClipPaths,s=!1,f=!1;if((!o||ju(n,o))&&(o&&o.length&&(nt(e,r),e.restore(),f=s=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),n&&n.length&&(nt(e,r),e.save(),ov(n,e,r),s=!0),r.prevElClipPaths=n),r.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var h=r.prevEl;h||(f=s=!0);var u=t instanceof q&&t.autoBatch&&fv(t.style);s||sv(a,h.transform)?(nt(e,r),gs(e,t)):u||nt(e,r);var v=ve(t,r.inHover);t instanceof q?(r.lastDrawType!==Yn&&(f=!0,r.lastDrawType=Yn),zn(e,t,h,f,r),(!u||!r.batchFill&&!r.batchStroke)&&e.beginPath(),ev(e,t,v,u),u&&(r.batchFill=v.fill||"",r.batchStroke=v.stroke||"")):t instanceof Qe?(r.lastDrawType!==Wn&&(f=!0,r.lastDrawType=Wn),zn(e,t,h,f,r),av(e,t,v)):t instanceof Ia?(r.lastDrawType!==$n&&(f=!0,r.lastDrawType=$n),nv(e,t,h,f,r),iv(e,t,v)):t.getTemporalDisplayables&&(r.lastDrawType!==Xn&&(f=!0,r.lastDrawType=Xn),uv(e,t,r)),u&&i&&nt(e,r),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),r.prevEl=t,t.__dirty=0,t.__isRendered=!0}function uv(e,t,r){var i=t.getDisplayables(),a=t.getTemporalDisplayables();e.save();var n={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var f=i[o];f.beforeBrush&&f.beforeBrush(),f.innerBeforeBrush(),vr(e,f,n,o===s-1),f.innerAfterBrush(),f.afterBrush&&f.afterBrush(),n.prevEl=f}for(var h=0,u=a.length;h<u;h++){var f=a[h];f.beforeBrush&&f.beforeBrush(),f.innerBeforeBrush(),vr(e,f,n,h===u-1),f.innerAfterBrush(),f.afterBrush&&f.afterBrush(),n.prevEl=f}t.clearTemporalDisplayables(),t.notClear=!0,e.restore()}var vv=1e-8;function Gn(e,t){return Math.abs(e-t)<vv}function Tl(e,t,r){var i=0,a=e[0];if(!a)return!1;for(var n=1;n<e.length;n++){var o=e[n];i+=Ot(a[0],a[1],o[0],o[1],t,r),a=o}var s=e[0];return(!Gn(a[0],s[0])||!Gn(a[1],s[1]))&&(i+=Ot(a[0],a[1],s[0],s[1],t,r)),i!==0}function lv(e){if(se(e)){var t=new DOMParser;e=t.parseFromString(e,"text/xml")}var r=e;for(r.nodeType===9&&(r=r.firstChild);r.nodeName.toLowerCase()!=="svg"||r.nodeType!==1;)r=r.nextSibling;return r}var Vi,ti={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},qn=tt(ti),ri={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Un=tt(ri),cv=function(){function e(){this._defs={},this._root=null}return e.prototype.parse=function(t,r){r=r||{};var i=lv(t);this._defsUsePending=[];var a=new Sr;this._root=a;var n=[],o=i.getAttribute("viewBox")||"",s=parseFloat(i.getAttribute("width")||r.width),f=parseFloat(i.getAttribute("height")||r.height);isNaN(s)&&(s=null),isNaN(f)&&(f=null),ft(i,a,null,!0,!1);for(var h=i.firstChild;h;)this._parseNode(h,a,n,null,!1,!1),h=h.nextSibling;_v(this._defs,this._defsUsePending),this._defsUsePending=[];var u,v;if(o){var l=ci(o);l.length>=4&&(u={x:parseFloat(l[0]||0),y:parseFloat(l[1]||0),width:parseFloat(l[2]),height:parseFloat(l[3])})}if(u&&s!=null&&f!=null&&(v=Tv(u,{x:0,y:0,width:s,height:f}),!r.ignoreViewBox)){var c=a;a=new Sr,a.add(c),c.scaleX=c.scaleY=v.scale,c.x=v.x,c.y=v.y}return!r.ignoreRootClip&&s!=null&&f!=null&&a.setClipPath(new Ke({shape:{x:0,y:0,width:s,height:f}})),{root:a,width:s,height:f,viewBoxRect:u,viewBoxTransform:v,named:n}},e.prototype._parseNode=function(t,r,i,a,n,o){var s=t.nodeName.toLowerCase(),f,h=a;if(s==="defs"&&(n=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")f=r;else{if(!n){var u=Vi[s];if(u&&qe(Vi,s)){f=u.call(this,t,r);var v=t.getAttribute("name");if(v){var l={name:v,namedFrom:null,svgNodeTagLower:s,el:f};i.push(l),s==="g"&&(h=l)}else a&&i.push({name:a.name,namedFrom:a,svgNodeTagLower:s,el:f});r.add(f)}}var c=Zn[s];if(c&&qe(Zn,s)){var _=c.call(this,t),g=t.getAttribute("id");g&&(this._defs[g]=_)}}if(f&&f.isGroup)for(var d=t.firstChild;d;)d.nodeType===1?this._parseNode(d,f,i,h,n,o):d.nodeType===3&&o&&this._parseText(d,f),d=d.nextSibling},e.prototype._parseText=function(t,r){var i=new Qe({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});lt(r,i),ft(t,i,this._defsUsePending,!1,!1),dv(i,r);var a=i.style,n=a.fontSize;n&&n<9&&(a.fontSize=9,i.scaleX*=n/9,i.scaleY*=n/9);var o=(a.fontSize||a.fontFamily)&&[a.fontStyle,a.fontWeight,(a.fontSize||12)+"px",a.fontFamily||"sans-serif"].join(" ");a.font=o;var s=i.getBoundingRect();return this._textX+=s.width,r.add(i),i},e.internalField=function(){Vi={g:function(t,r){var i=new Sr;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i},rect:function(t,r){var i=new Ke;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),i.silent=!0,i},circle:function(t,r){var i=new Tu;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),i.silent=!0,i},line:function(t,r){var i=new Hu;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),i.silent=!0,i},ellipse:function(t,r){var i=new Cu;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),i.silent=!0,i},polygon:function(t,r){var i=t.getAttribute("points"),a;i&&(a=Qn(i));var n=new hs({shape:{points:a||[]},silent:!0});return lt(r,n),ft(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,r){var i=t.getAttribute("points"),a;i&&(a=Qn(i));var n=new Eu({shape:{points:a||[]},silent:!0});return lt(r,n),ft(t,n,this._defsUsePending,!1,!1),n},image:function(t,r){var i=new Ia;return lt(r,i),ft(t,i,this._defsUsePending,!1,!1),i.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),i.silent=!0,i},text:function(t,r){var i=t.getAttribute("x")||"0",a=t.getAttribute("y")||"0",n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(i)+parseFloat(n),this._textY=parseFloat(a)+parseFloat(o);var s=new Sr;return lt(r,s),ft(t,s,this._defsUsePending,!1,!0),s},tspan:function(t,r){var i=t.getAttribute("x"),a=t.getAttribute("y");i!=null&&(this._textX=parseFloat(i)),a!=null&&(this._textY=parseFloat(a));var n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",s=new Sr;return lt(r,s),ft(t,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),s},path:function(t,r){var i=t.getAttribute("d")||"",a=mu(i);return lt(r,a),ft(t,a,this._defsUsePending,!1,!1),a.silent=!0,a}}}(),e}(),Zn={lineargradient:function(e){var t=parseInt(e.getAttribute("x1")||"0",10),r=parseInt(e.getAttribute("y1")||"0",10),i=parseInt(e.getAttribute("x2")||"10",10),a=parseInt(e.getAttribute("y2")||"0",10),n=new Wu(t,r,i,a);return Nn(e,n),Vn(e,n),n},radialgradient:function(e){var t=parseInt(e.getAttribute("cx")||"0",10),r=parseInt(e.getAttribute("cy")||"0",10),i=parseInt(e.getAttribute("r")||"0",10),a=new Gu(t,r,i);return Nn(e,a),Vn(e,a),a}};function Nn(e,t){var r=e.getAttribute("gradientUnits");r==="userSpaceOnUse"&&(t.global=!0)}function Vn(e,t){for(var r=e.firstChild;r;){if(r.nodeType===1&&r.nodeName.toLocaleLowerCase()==="stop"){var i=r.getAttribute("offset"),a=void 0;i&&i.indexOf("%")>0?a=parseInt(i,10)/100:i?a=parseFloat(i):a=0;var n={};ys(r,n,n);var o=n.stopColor||r.getAttribute("stop-color")||"#000000";t.colorStops.push({offset:a,color:o})}r=r.nextSibling}}function lt(e,t){e&&e.__inheritedStyle&&(t.__inheritedStyle||(t.__inheritedStyle={}),_t(t.__inheritedStyle,e.__inheritedStyle))}function Qn(e){for(var t=ci(e),r=[],i=0;i<t.length;i+=2){var a=parseFloat(t[i]),n=parseFloat(t[i+1]);r.push([a,n])}return r}function ft(e,t,r,i,a){var n=t,o=n.__inheritedStyle=n.__inheritedStyle||{},s={};e.nodeType===1&&(mv(e,t),ys(e,o,s),i||wv(e,o,s)),n.style=n.style||{},o.fill!=null&&(n.style.fill=Kn(n,"fill",o.fill,r)),o.stroke!=null&&(n.style.stroke=Kn(n,"stroke",o.stroke,r)),j(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(f){o[f]!=null&&(n.style[f]=parseFloat(o[f]))}),j(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(f){o[f]!=null&&(n.style[f]=o[f])}),a&&(n.__selfStyle=s),o.lineDash&&(n.style.lineDash=Tt(ci(o.lineDash),function(f){return parseFloat(f)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(n.invisible=!0),o.display==="none"&&(n.ignore=!0)}function dv(e,t){var r=t.__selfStyle;if(r){var i=r.textBaseline,a=i;!i||i==="auto"||i==="baseline"?a="alphabetic":i==="before-edge"||i==="text-before-edge"?a="top":i==="after-edge"||i==="text-after-edge"?a="bottom":(i==="central"||i==="mathematical")&&(a="middle"),e.style.textBaseline=a}var n=t.__inheritedStyle;if(n){var o=n.textAlign,s=o;o&&(o==="middle"&&(s="center"),e.style.textAlign=s)}}var pv=/^url\(\s*#(.*?)\)/;function Kn(e,t,r,i){var a=r&&r.match(pv);if(a){var n=Rr(a[1]);i.push([e,t,n]);return}return r==="none"&&(r=null),r}function _v(e,t){for(var r=0;r<t.length;r++){var i=t[r];i[0].style[i[1]]=e[i[2]]}}var gv=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function ci(e){return e.match(gv)||[]}var yv=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Qi=Math.PI/180;function mv(e,t){var r=e.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],a=null;r.replace(yv,function(v,l,c){return i.push(l,c),""});for(var n=i.length-1;n>0;n-=2){var o=i[n],s=i[n-1],f=ci(o);switch(a=a||Br(),s){case"translate":ia(a,a,[parseFloat(f[0]),parseFloat(f[1]||"0")]);break;case"scale":yo(a,a,[parseFloat(f[0]),parseFloat(f[1]||f[0])]);break;case"rotate":go(a,a,-parseFloat(f[0])*Qi,[parseFloat(f[1]||"0"),parseFloat(f[2]||"0")]);break;case"skewX":var h=Math.tan(parseFloat(f[0])*Qi);ie(a,[1,0,h,1,0,0],a);break;case"skewY":var u=Math.tan(parseFloat(f[0])*Qi);ie(a,[1,u,0,1,0,0],a);break;case"matrix":a[0]=parseFloat(f[0]),a[1]=parseFloat(f[1]),a[2]=parseFloat(f[2]),a[3]=parseFloat(f[3]),a[4]=parseFloat(f[4]),a[5]=parseFloat(f[5]);break}}t.setLocalTransform(a)}}var Jn=/([^\s:;]+)\s*:\s*([^:;]+)/g;function ys(e,t,r){var i=e.getAttribute("style");if(!!i){Jn.lastIndex=0;for(var a;(a=Jn.exec(i))!=null;){var n=a[1],o=qe(ti,n)?ti[n]:null;o&&(t[o]=a[2]);var s=qe(ri,n)?ri[n]:null;s&&(r[s]=a[2])}}}function wv(e,t,r){for(var i=0;i<qn.length;i++){var a=qn[i],n=e.getAttribute(a);n!=null&&(t[ti[a]]=n)}for(var i=0;i<Un.length;i++){var a=Un[i],n=e.getAttribute(a);n!=null&&(r[ri[a]]=n)}}function Tv(e,t){var r=t.width/e.width,i=t.height/e.height,a=Math.min(r,i);return{scale:a,x:-(e.x+e.width/2)*a+(t.x+t.width/2),y:-(e.y+e.height/2)*a+(t.y+t.height/2)}}function bl(e,t){var r=new cv;return r.parse(e,t)}function jn(e,t,r){var i=le.createCanvas(),a=t.getWidth(),n=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=a+"px",o.height=n+"px",i.setAttribute("data-zr-dom-id",e)),i.width=a*r,i.height=n*r,i}var bv=function(e){k(t,e);function t(r,i,a){var n=e.call(this)||this;n.motionBlur=!1,n.lastFrameAlpha=.7,n.dpr=1,n.virtual=!1,n.config={},n.incremental=!1,n.zlevel=0,n.maxRepaintRectCount=5,n.__dirty=!0,n.__firstTimePaint=!0,n.__used=!1,n.__drawIndex=0,n.__startIndex=0,n.__endIndex=0,n.__prevStartIndex=null,n.__prevEndIndex=null;var o;a=a||Ve,typeof r=="string"?o=jn(r,i,a):Yt(r)&&(o=r,r=o.id),n.id=r,n.dom=o;var s=o.style;return s&&(po(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),n.painter=i,n.dpr=a,n}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var r=this.dpr;this.domBack=jn("back-"+this.id,this.painter,r),this.ctxBack=this.domBack.getContext("2d"),r!==1&&this.ctxBack.scale(r,r)},t.prototype.createRepaintRects=function(r,i,a,n){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,f=!1,h=new $(0,0,0,0);function u(y){if(!(!y.isFinite()||y.isZero()))if(o.length===0){var m=new $(0,0,0,0);m.copy(y),o.push(m)}else{for(var w=!1,C=1/0,T=0,b=0;b<o.length;++b){var M=o[b];if(M.intersect(y)){var L=new $(0,0,0,0);L.copy(M),L.union(y),o[b]=L,w=!0;break}else if(f){h.copy(y),h.union(M);var S=y.width*y.height,P=M.width*M.height,D=h.width*h.height,A=D-S-P;A<C&&(C=A,T=b)}}if(f&&(o[T].union(y),w=!0),!w){var m=new $(0,0,0,0);m.copy(y),o.push(m)}f||(f=o.length>=s)}}for(var v=this.__startIndex;v<this.__endIndex;++v){var l=r[v];if(l){var c=l.shouldBePainted(a,n,!0,!0),_=l.__isRendered&&(l.__dirty&ht||!c)?l.getPrevPaintRect():null;_&&u(_);var g=c&&(l.__dirty&ht||!l.__isRendered)?l.getPaintRect():null;g&&u(g)}}for(var v=this.__prevStartIndex;v<this.__prevEndIndex;++v){var l=i[v],c=l&&l.shouldBePainted(a,n,!0,!0);if(l&&(!c||!l.__zr)&&l.__isRendered){var _=l.getPrevPaintRect();_&&u(_)}}var d;do{d=!1;for(var v=0;v<o.length;){if(o[v].isZero()){o.splice(v,1);continue}for(var p=v+1;p<o.length;)o[v].intersect(o[p])?(d=!0,o[v].union(o[p]),o.splice(p,1)):p++;v++}}while(d);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(r,i){var a=this.dpr,n=this.dom,o=n.style,s=this.domBack;o&&(o.width=r+"px",o.height=i+"px"),n.width=r*a,n.height=i*a,s&&(s.width=r*a,s.height=i*a,a!==1&&this.ctxBack.scale(a,a))},t.prototype.clear=function(r,i,a){var n=this.dom,o=this.ctx,s=n.width,f=n.height;i=i||this.clearColor;var h=this.motionBlur&&!r,u=this.lastFrameAlpha,v=this.dpr,l=this;h&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,s/v,f/v));var c=this.domBack;function _(g,d,p,y){if(o.clearRect(g,d,p,y),i&&i!=="transparent"){var m=void 0;if(fi(i)){var w=i.global||i.__width===p&&i.__height===y;m=w&&i.__canvasGradient||ma(o,i,{x:0,y:0,width:p,height:y}),i.__canvasGradient=m,i.__width=p,i.__height=y}else zs(i)&&(i.scaleX=i.scaleX||v,i.scaleY=i.scaleY||v,m=wa(o,i,{dirty:function(){l.setUnpainted(),l.painter.refresh()}}));o.save(),o.fillStyle=m||i,o.fillRect(g,d,p,y),o.restore()}h&&(o.save(),o.globalAlpha=u,o.drawImage(c,g,d,p,y),o.restore())}!a||h?_(0,0,s,f):a.length&&j(a,function(g){_(g.x*v,g.y*v,g.width*v,g.height*v)})},t}($r);const Ki=bv;var to=1e5,hr=314159,He=.01,Cv=.001;function Mv(e){return e?e.__builtin__?!0:!(typeof e.resize!="function"||typeof e.refresh!="function"):!1}function Lv(e,t){var r=document.createElement("div");return r.style.cssText=["position:relative","width:"+e+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",r}var Sv=function(){function e(t,r,i,a){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=Y({},i||{}),this.dpr=i.devicePixelRatio||Ve,this._singleCanvas=n,this.root=t;var o=t.style;o&&(po(t),t.innerHTML=""),this.storage=r;var s=this._zlevelList;this._prevDisplayList=[];var f=this._layers;if(n){var u=t,v=u.width,l=u.height;i.width!=null&&(v=i.width),i.height!=null&&(l=i.height),this.dpr=i.devicePixelRatio||1,u.width=v*this.dpr,u.height=l*this.dpr,this._width=v,this._height=l;var c=new Ki(u,this,this.dpr);c.__builtin__=!0,c.initContext(),f[hr]=c,c.zlevel=hr,s.push(hr),this._domRoot=t}else{this._width=Oe(t,0,i),this._height=Oe(t,1,i);var h=this._domRoot=Lv(this._width,this._height);t.appendChild(h)}}return e.prototype.getType=function(){return"canvas"},e.prototype.isSingleCanvas=function(){return this._singleCanvas},e.prototype.getViewportRoot=function(){return this._domRoot},e.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},e.prototype.refresh=function(t){var r=this.storage.getDisplayList(!0),i=this._prevDisplayList,a=this._zlevelList;this._redrawId=Math.random(),this._paintList(r,i,t,this._redrawId);for(var n=0;n<a.length;n++){var o=a[n],s=this._layers[o];if(!s.__builtin__&&s.refresh){var f=n===0?this._backgroundColor:null;s.refresh(f)}}return this._opts.useDirtyRect&&(this._prevDisplayList=r.slice()),this},e.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},e.prototype._paintHoverList=function(t){var r=t.length,i=this._hoverlayer;if(i&&i.clear(),!!r){for(var a={inHover:!0,viewWidth:this._width,viewHeight:this._height},n,o=0;o<r;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(to)),n||(n=i.ctx,n.save()),vr(n,s,a,o===r-1))}n&&n.restore()}},e.prototype.getHoverLayer=function(){return this.getLayer(to)},e.prototype.paintOne=function(t,r){hv(t,r)},e.prototype._paintList=function(t,r,i,a){if(this._redrawId===a){i=i||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,r,i),o=n.finished,s=n.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(h){h.afterBrush&&h.afterBrush()});else{var f=this;aa(function(){f._paintList(t,r,i,a)})}}},e.prototype._compositeManually=function(){var t=this.getLayer(hr).ctx,r=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,r,i),this.eachBuiltinLayer(function(a){a.virtual&&t.drawImage(a.dom,0,0,r,i)})},e.prototype._doPaintList=function(t,r,i){for(var a=this,n=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var f=this._zlevelList[s],h=this._layers[f];h.__builtin__&&h!==this._hoverlayer&&(h.__dirty||i)&&n.push(h)}for(var u=!0,v=!1,l=function(g){var d=n[g],p=d.ctx,y=o&&d.createRepaintRects(t,r,c._width,c._height),m=i?d.__startIndex:d.__drawIndex,w=!i&&d.incremental&&Date.now,C=w&&Date.now(),T=d.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(d.__startIndex===d.__endIndex)d.clear(!1,T,y);else if(m===d.__startIndex){var b=t[m];(!b.incremental||!b.notClear||i)&&d.clear(!1,T,y)}m===-1&&(console.error("For some unknown reason. drawIndex is -1"),m=d.__startIndex);var M,L=function(A){var R={inHover:!1,allClipped:!1,prevEl:null,viewWidth:a._width,viewHeight:a._height};for(M=m;M<d.__endIndex;M++){var E=t[M];if(E.__inHover&&(v=!0),a._doPaintEl(E,d,o,A,R,M===d.__endIndex-1),w){var x=Date.now()-C;if(x>15)break}}R.prevElClipPaths&&p.restore()};if(y)if(y.length===0)M=d.__endIndex;else for(var S=c.dpr,P=0;P<y.length;++P){var D=y[P];p.save(),p.beginPath(),p.rect(D.x*S,D.y*S,D.width*S,D.height*S),p.clip(),L(D),p.restore()}else p.save(),L(),p.restore();d.__drawIndex=M,d.__drawIndex<d.__endIndex&&(u=!1)},c=this,_=0;_<n.length;_++)l(_);return K.wxa&&j(this._layers,function(g){g&&g.ctx&&g.ctx.draw&&g.ctx.draw()}),{finished:u,needsRefreshHover:v}},e.prototype._doPaintEl=function(t,r,i,a,n,o){var s=r.ctx;if(i){var f=t.getPaintRect();(!a||f&&f.intersect(a))&&(vr(s,t,n,o),t.setPrevPaintRect(f))}else vr(s,t,n,o)},e.prototype.getLayer=function(t,r){this._singleCanvas&&!this._needsManuallyCompositing&&(t=hr);var i=this._layers[t];return i||(i=new Ki("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?Pr(i,this._layerConfig[t],!0):this._layerConfig[t-He]&&Pr(i,this._layerConfig[t-He],!0),r&&(i.virtual=r),this.insertLayer(t,i),i.initContext()),i},e.prototype.insertLayer=function(t,r){var i=this._layers,a=this._zlevelList,n=a.length,o=this._domRoot,s=null,f=-1;if(!i[t]&&!!Mv(r)){if(n>0&&t>a[0]){for(f=0;f<n-1&&!(a[f]<t&&a[f+1]>t);f++);s=i[a[f]]}if(a.splice(f+1,0,t),i[t]=r,!r.virtual)if(s){var h=s.dom;h.nextSibling?o.insertBefore(r.dom,h.nextSibling):o.appendChild(r.dom)}else o.firstChild?o.insertBefore(r.dom,o.firstChild):o.appendChild(r.dom);r.painter||(r.painter=this)}},e.prototype.eachLayer=function(t,r){for(var i=this._zlevelList,a=0;a<i.length;a++){var n=i[a];t.call(r,this._layers[n],n)}},e.prototype.eachBuiltinLayer=function(t,r){for(var i=this._zlevelList,a=0;a<i.length;a++){var n=i[a],o=this._layers[n];o.__builtin__&&t.call(r,o,n)}},e.prototype.eachOtherLayer=function(t,r){for(var i=this._zlevelList,a=0;a<i.length;a++){var n=i[a],o=this._layers[n];o.__builtin__||t.call(r,o,n)}},e.prototype.getLayers=function(){return this._layers},e.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(v,l){v.__dirty=v.__used=!1});function r(v){n&&(n.__endIndex!==v&&(n.__dirty=!0),n.__endIndex=v)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var a=t[i];if(a.zlevel!==t[i-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}}var n=null,o=0,s,f;for(f=0;f<t.length;f++){var a=t[f],h=a.zlevel,u=void 0;s!==h&&(s=h,o=0),a.incremental?(u=this.getLayer(h+Cv,this._needsManuallyCompositing),u.incremental=!0,o=1):u=this.getLayer(h+(o>0?He:0),this._needsManuallyCompositing),u.__builtin__||Ra("ZLevel "+h+" has been used by unkown layer "+u.id),u!==n&&(u.__used=!0,u.__startIndex!==f&&(u.__dirty=!0),u.__startIndex=f,u.incremental?u.__drawIndex=-1:u.__drawIndex=f,r(f),n=u),a.__dirty&ht&&!a.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=f))}r(f),this.eachBuiltinLayer(function(v,l){!v.__used&&v.getElementCount()>0&&(v.__dirty=!0,v.__startIndex=v.__endIndex=v.__drawIndex=0),v.__dirty&&v.__drawIndex<0&&(v.__drawIndex=v.__startIndex)})},e.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},e.prototype._clearLayer=function(t){t.clear()},e.prototype.setBackgroundColor=function(t){this._backgroundColor=t,j(this._layers,function(r){r.setUnpainted()})},e.prototype.configLayer=function(t,r){if(r){var i=this._layerConfig;i[t]?Pr(i[t],r,!0):i[t]=r;for(var a=0;a<this._zlevelList.length;a++){var n=this._zlevelList[a];if(n===t||n===t+He){var o=this._layers[n];Pr(o,i[t],!0)}}}},e.prototype.delLayer=function(t){var r=this._layers,i=this._zlevelList,a=r[t];!a||(a.dom.parentNode.removeChild(a.dom),delete r[t],i.splice(Rt(i,t),1))},e.prototype.resize=function(t,r){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var a=this._opts,n=this.root;if(t!=null&&(a.width=t),r!=null&&(a.height=r),t=Oe(n,0,a),r=Oe(n,1,a),i.style.display="",this._width!==t||r!==this._height){i.style.width=t+"px",i.style.height=r+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,r);this.refresh(!0)}this._width=t,this._height=r}else{if(t==null||r==null)return;this._width=t,this._height=r,this.getLayer(hr).resize(t,r)}return this},e.prototype.clearLayer=function(t){var r=this._layers[t];r&&r.clear()},e.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},e.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[hr].dom;var r=new Ki("image",this,t.pixelRatio||this.dpr);r.initContext(),r.clear(!1,t.backgroundColor||this._backgroundColor);var i=r.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var a=r.dom.width,n=r.dom.height;this.eachLayer(function(v){v.__builtin__?i.drawImage(v.dom,0,0,a,n):v.renderToCanvas&&(i.save(),v.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),f=0,h=s.length;f<h;f++){var u=s[f];vr(i,u,o,f===h-1)}return r.dom},e.prototype.getWidth=function(){return this._width},e.prototype.getHeight=function(){return this._height},e}();const Cl=Sv;var Pt=pr.CMD;function Ir(e,t){return Math.abs(e-t)<1e-5}function Ta(e){var t=e.data,r=e.len(),i=[],a,n=0,o=0,s=0,f=0;function h(A,R){a&&a.length>2&&i.push(a),a=[A,R]}function u(A,R,E,x){Ir(A,E)&&Ir(R,x)||a.push(A,R,E,x,E,x)}function v(A,R,E,x,H,z){var X=Math.abs(R-A),U=Math.tan(X/4)*4/3,G=R<A?-1:1,W=Math.cos(A),ot=Math.sin(A),st=Math.cos(R),Ft=Math.sin(R),qt=W*H+E,Ut=ot*z+x,Zt=st*H+E,rt=Ft*z+x,Z=H*U*G,F=z*U*G;a.push(qt-Z*ot,Ut+F*W,Zt+Z*Ft,rt-F*st,Zt,rt)}for(var l,c,_,g,d=0;d<r;){var p=t[d++],y=d===1;switch(y&&(n=t[d],o=t[d+1],s=n,f=o,(p===Pt.L||p===Pt.C||p===Pt.Q)&&(a=[s,f])),p){case Pt.M:n=s=t[d++],o=f=t[d++],h(s,f);break;case Pt.L:l=t[d++],c=t[d++],u(n,o,l,c),n=l,o=c;break;case Pt.C:a.push(t[d++],t[d++],t[d++],t[d++],n=t[d++],o=t[d++]);break;case Pt.Q:l=t[d++],c=t[d++],_=t[d++],g=t[d++],a.push(n+2/3*(l-n),o+2/3*(c-o),_+2/3*(l-_),g+2/3*(c-g),_,g),n=_,o=g;break;case Pt.A:var m=t[d++],w=t[d++],C=t[d++],T=t[d++],b=t[d++],M=t[d++]+b;d+=1;var L=!t[d++];l=Math.cos(b)*C+m,c=Math.sin(b)*T+w,y?(s=l,f=c,h(s,f)):u(n,o,l,c),n=Math.cos(M)*C+m,o=Math.sin(M)*T+w;for(var S=(L?-1:1)*Math.PI/2,P=b;L?P>M:P<M;P+=S){var D=L?Math.max(P+S,M):Math.min(P+S,M);v(P,D,m,w,C,T)}break;case Pt.R:s=n=t[d++],f=o=t[d++],l=s+t[d++],c=f+t[d++],h(l,f),u(l,f,l,c),u(l,c,s,c),u(s,c,s,f),u(s,f,l,f);break;case Pt.Z:a&&u(n,o,s,f),n=s,o=f;break}}return a&&a.length>2&&i.push(a),i}function ba(e,t,r,i,a,n,o,s,f,h){if(Ir(e,r)&&Ir(t,i)&&Ir(a,o)&&Ir(n,s)){f.push(o,s);return}var u=2/h,v=u*u,l=o-e,c=s-t,_=Math.sqrt(l*l+c*c);l/=_,c/=_;var g=r-e,d=i-t,p=a-o,y=n-s,m=g*g+d*d,w=p*p+y*y;if(m<v&&w<v){f.push(o,s);return}var C=l*g+c*d,T=-l*p-c*y,b=m-C*C,M=w-T*T;if(b<v&&C>=0&&M<v&&T>=0){f.push(o,s);return}var L=[],S=[];Gt(e,r,a,o,.5,L),Gt(t,i,n,s,.5,S),ba(L[0],S[0],L[1],S[1],L[2],S[2],L[3],S[3],f,h),ba(L[4],S[4],L[5],S[5],L[6],S[6],L[7],S[7],f,h)}function Pv(e,t){var r=Ta(e),i=[];t=t||1;for(var a=0;a<r.length;a++){var n=r[a],o=[],s=n[0],f=n[1];o.push(s,f);for(var h=2;h<n.length;){var u=n[h++],v=n[h++],l=n[h++],c=n[h++],_=n[h++],g=n[h++];ba(s,f,u,v,l,c,_,g,o,t),s=_,f=g}i.push(o)}return i}function ms(e,t,r){var i=e[t],a=e[1-t],n=Math.abs(i/a),o=Math.ceil(Math.sqrt(n*r)),s=Math.floor(r/o);s===0&&(s=1,o=r);for(var f=[],h=0;h<o;h++)f.push(s);var u=o*s,v=r-u;if(v>0)for(var h=0;h<v;h++)f[h%o]+=1;return f}function ro(e,t,r){for(var i=e.r0,a=e.r,n=e.startAngle,o=e.endAngle,s=Math.abs(o-n),f=s*a,h=a-i,u=f>Math.abs(h),v=ms([f,h],u?0:1,t),l=(u?s:h)/v.length,c=0;c<v.length;c++)for(var _=(u?h:s)/v[c],g=0;g<v[c];g++){var d={};u?(d.startAngle=n+l*c,d.endAngle=n+l*(c+1),d.r0=i+_*g,d.r=i+_*(g+1)):(d.startAngle=n+_*g,d.endAngle=n+_*(g+1),d.r0=i+l*c,d.r=i+l*(c+1)),d.clockwise=e.clockwise,d.cx=e.cx,d.cy=e.cy,r.push(d)}}function Rv(e,t,r){for(var i=e.width,a=e.height,n=i>a,o=ms([i,a],n?0:1,t),s=n?"width":"height",f=n?"height":"width",h=n?"x":"y",u=n?"y":"x",v=e[s]/o.length,l=0;l<o.length;l++)for(var c=e[f]/o[l],_=0;_<o[l];_++){var g={};g[h]=l*v,g[u]=_*c,g[s]=v,g[f]=c,g.x+=e.x,g.y+=e.y,r.push(g)}}function eo(e,t,r,i){return e*i-r*t}function Dv(e,t,r,i,a,n,o,s){var f=r-e,h=i-t,u=o-a,v=s-n,l=eo(u,v,f,h);if(Math.abs(l)<1e-6)return null;var c=e-a,_=t-n,g=eo(c,_,u,v)/l;return g<0||g>1?null:new I(g*f+e,g*h+t)}function xv(e,t,r){var i=new I;I.sub(i,r,t),i.normalize();var a=new I;I.sub(a,e,t);var n=a.dot(i);return n}function Cr(e,t){var r=e[e.length-1];r&&r[0]===t[0]&&r[1]===t[1]||e.push(t)}function Av(e,t,r){for(var i=e.length,a=[],n=0;n<i;n++){var o=e[n],s=e[(n+1)%i],f=Dv(o[0],o[1],s[0],s[1],t.x,t.y,r.x,r.y);f&&a.push({projPt:xv(f,t,r),pt:f,idx:n})}if(a.length<2)return[{points:e},{points:e}];a.sort(function(d,p){return d.projPt-p.projPt});var h=a[0],u=a[a.length-1];if(u.idx<h.idx){var v=h;h=u,u=v}for(var l=[h.pt.x,h.pt.y],c=[u.pt.x,u.pt.y],_=[l],g=[c],n=h.idx+1;n<=u.idx;n++)Cr(_,e[n].slice());Cr(_,c),Cr(_,l);for(var n=u.idx+1;n<=h.idx+i;n++)Cr(g,e[n%i].slice());return Cr(g,l),Cr(g,c),[{points:_},{points:g}]}function io(e){var t=e.points,r=[],i=[];Go(t,r,i);var a=new $(r[0],r[1],i[0]-r[0],i[1]-r[1]),n=a.width,o=a.height,s=a.x,f=a.y,h=new I,u=new I;return n>o?(h.x=u.x=s+n/2,h.y=f,u.y=f+o):(h.y=u.y=f+o/2,h.x=s,u.x=s+n),Av(t,h,u)}function ei(e,t,r,i){if(r===1)i.push(t);else{var a=Math.floor(r/2),n=e(t);ei(e,n[0],a,i),ei(e,n[1],r-a,i)}return i}function Fv(e,t){for(var r=[],i=0;i<t;i++)r.push(rs(e));return r}function Ev(e,t){t.setStyle(e.style),t.z=e.z,t.z2=e.z2,t.zlevel=e.zlevel}function Iv(e){for(var t=[],r=0;r<e.length;)t.push([e[r++],e[r++]]);return t}function Ov(e,t){var r=[],i=e.shape,a;switch(e.type){case"rect":Rv(i,t,r),a=Ke;break;case"sector":ro(i,t,r),a=Fn;break;case"circle":ro({r0:0,r:i.r,startAngle:0,endAngle:Math.PI*2,cx:i.cx,cy:i.cy},t,r),a=Fn;break;default:var n=e.getComputedTransform(),o=n?Math.sqrt(Math.max(n[0]*n[0]+n[1]*n[1],n[2]*n[2]+n[3]*n[3])):1,s=Tt(Pv(e.getUpdatedPathProxy(),o),function(p){return Iv(p)}),f=s.length;if(f===0)ei(io,{points:s[0]},t,r);else if(f===t)for(var h=0;h<f;h++)r.push({points:s[h]});else{var u=0,v=Tt(s,function(p){var y=[],m=[];Go(p,y,m);var w=(m[1]-y[1])*(m[0]-y[0]);return u+=w,{poly:p,area:w}});v.sort(function(p,y){return y.area-p.area});for(var l=t,h=0;h<f;h++){var c=v[h];if(l<=0)break;var _=h===f-1?l:Math.ceil(c.area/u*t);_<0||(ei(io,{points:c.poly},_,r),l-=_)}}a=hs;break}if(!a)return Fv(e,t);for(var g=[],h=0;h<r.length;h++){var d=new a;d.setShape(r[h]),Ev(e,d),g.push(d)}return g}function Hv(e,t){var r=e.length,i=t.length;if(r===i)return[e,t];for(var a=[],n=[],o=r<i?e:t,s=Math.min(r,i),f=Math.abs(i-r)/6,h=(s-2)/6,u=Math.ceil(f/h)+1,v=[o[0],o[1]],l=f,c=2;c<s;){var _=o[c-2],g=o[c-1],d=o[c++],p=o[c++],y=o[c++],m=o[c++],w=o[c++],C=o[c++];if(l<=0){v.push(d,p,y,m,w,C);continue}for(var T=Math.min(l,u-1)+1,b=1;b<=T;b++){var M=b/T;Gt(_,d,y,w,M,a),Gt(g,p,m,C,M,n),_=a[3],g=n[3],v.push(a[1],n[1],a[2],n[2],_,g),d=a[5],p=n[5],y=a[6],m=n[6]}l-=T-1}return o===e?[v,t]:[e,v]}function ao(e,t){for(var r=e.length,i=e[r-2],a=e[r-1],n=[],o=0;o<t.length;)n[o++]=i,n[o++]=a;return n}function Bv(e,t){for(var r,i,a,n=[],o=[],s=0;s<Math.max(e.length,t.length);s++){var f=e[s],h=t[s],u=void 0,v=void 0;f?h?(r=Hv(f,h),u=r[0],v=r[1],i=u,a=v):(v=ao(a||f,f),u=f):(u=ao(i||h,h),v=h),n.push(u),o.push(v)}return[n,o]}function no(e){for(var t=0,r=0,i=0,a=e.length,n=0,o=a-2;n<a;o=n,n+=2){var s=e[o],f=e[o+1],h=e[n],u=e[n+1],v=s*u-h*f;t+=v,r+=(s+h)*v,i+=(f+u)*v}return t===0?[e[0]||0,e[1]||0]:[r/t/3,i/t/3,t]}function kv(e,t,r,i){for(var a=(e.length-2)/6,n=1/0,o=0,s=e.length,f=s-2,h=0;h<a;h++){for(var u=h*6,v=0,l=0;l<s;l+=2){var c=l===0?u:(u+l-2)%f+2,_=e[c]-r[0],g=e[c+1]-r[1],d=t[l]-i[0],p=t[l+1]-i[1],y=d-_,m=p-g;v+=y*y+m*m}v<n&&(n=v,o=h)}return o}function zv(e){for(var t=[],r=e.length,i=0;i<r;i+=2)t[i]=e[r-i-2],t[i+1]=e[r-i-1];return t}function Yv(e,t,r,i){for(var a=[],n,o=0;o<e.length;o++){var s=e[o],f=t[o],h=no(s),u=no(f);n==null&&(n=h[2]<0!=u[2]<0);var v=[],l=[],c=0,_=1/0,g=[],d=s.length;n&&(s=zv(s));for(var p=kv(s,f,h,u)*6,y=d-2,m=0;m<y;m+=2){var w=(p+m)%y+2;v[m+2]=s[w]-h[0],v[m+3]=s[w+1]-h[1]}if(v[0]=s[p]-h[0],v[1]=s[p+1]-h[1],r>0)for(var C=i/r,T=-i/2;T<=i/2;T+=C){for(var b=Math.sin(T),M=Math.cos(T),L=0,m=0;m<s.length;m+=2){var S=v[m],P=v[m+1],D=f[m]-u[0],A=f[m+1]-u[1],R=D*M-A*b,E=D*b+A*M;g[m]=R,g[m+1]=E;var x=R-S,H=E-P;L+=x*x+H*H}if(L<_){_=L,c=T;for(var z=0;z<g.length;z++)l[z]=g[z]}}else for(var X=0;X<d;X+=2)l[X]=f[X]-u[0],l[X+1]=f[X+1]-u[1];a.push({from:v,to:l,fromCp:h,toCp:u,rotation:-c})}return a}function Ca(e){return e.__isCombineMorphing}var ws="__mOriginal_";function ii(e,t,r){var i=ws+t,a=e[i]||e[t];e[i]||(e[i]=e[t]);var n=r.replace,o=r.after,s=r.before;e[t]=function(){var f=arguments,h;return s&&s.apply(this,f),n?h=n.apply(this,f):h=a.apply(this,f),o&&o.apply(this,f),h}}function ne(e,t){var r=ws+t;e[r]&&(e[t]=e[r],e[r]=null)}function oo(e,t){for(var r=0;r<e.length;r++)for(var i=e[r],a=0;a<i.length;){var n=i[a],o=i[a+1];i[a++]=t[0]*n+t[2]*o+t[4],i[a++]=t[1]*n+t[3]*o+t[5]}}function Ts(e,t){var r=e.getUpdatedPathProxy(),i=t.getUpdatedPathProxy(),a=Bv(Ta(r),Ta(i)),n=a[0],o=a[1],s=e.getComputedTransform(),f=t.getComputedTransform();function h(){this.transform=null}s&&oo(n,s),f&&oo(o,f),ii(t,"updateTransform",{replace:h}),t.transform=null;var u=Yv(n,o,10,Math.PI),v=[];ii(t,"buildPath",{replace:function(l){for(var c=t.__morphT,_=1-c,g=[],d=0;d<u.length;d++){var p=u[d],y=p.from,m=p.to,w=p.rotation*c,C=p.fromCp,T=p.toCp,b=Math.sin(w),M=Math.cos(w);js(g,C,T,c);for(var L=0;L<y.length;L+=2){var S=y[L],P=y[L+1],D=m[L],A=m[L+1],R=S*_+D*c,E=P*_+A*c;v[L]=R*M-E*b+g[0],v[L+1]=R*b+E*M+g[1]}var x=v[0],H=v[1];l.moveTo(x,H);for(var L=2;L<y.length;){var D=v[L++],A=v[L++],z=v[L++],X=v[L++],U=v[L++],G=v[L++];x===D&&H===A&&z===U&&X===G?l.lineTo(U,G):l.bezierCurveTo(D,A,z,X,U,G),x=U,H=G}}}})}function bs(e,t,r){if(!e||!t)return t;var i=r.done,a=r.during;Ts(e,t),t.__morphT=0;function n(){ne(t,"buildPath"),ne(t,"updateTransform"),t.__morphT=-1,t.createPathProxy(),t.dirtyShape()}return t.animateTo({__morphT:1},_t({during:function(o){t.dirtyShape(),a&&a(o)},done:function(){n(),i&&i()}},r)),t}function $v(e,t,r,i,a,n){var o=16;e=a===r?0:Math.round(32767*(e-r)/(a-r)),t=n===i?0:Math.round(32767*(t-i)/(n-i));for(var s=0,f,h=(1<<o)/2;h>0;h/=2){var u=0,v=0;(e&h)>0&&(u=1),(t&h)>0&&(v=1),s+=h*h*(3*u^v),v===0&&(u===1&&(e=h-1-e,t=h-1-t),f=e,e=t,t=f)}return s}function ai(e){var t=1/0,r=1/0,i=-1/0,a=-1/0,n=Tt(e,function(s){var f=s.getBoundingRect(),h=s.getComputedTransform(),u=f.x+f.width/2+(h?h[4]:0),v=f.y+f.height/2+(h?h[5]:0);return t=Math.min(u,t),r=Math.min(v,r),i=Math.max(u,i),a=Math.max(v,a),[u,v]}),o=Tt(n,function(s,f){return{cp:s,z:$v(s[0],s[1],t,r,i,a),path:e[f]}});return o.sort(function(s,f){return s.z-f.z}).map(function(s){return s.path})}function Cs(e){return Ov(e.path,e.count)}function Ma(){return{fromIndividuals:[],toIndividuals:[],count:0}}function Ml(e,t,r){var i=[];function a(C){for(var T=0;T<C.length;T++){var b=C[T];Ca(b)?a(b.childrenRef()):b instanceof q&&i.push(b)}}a(e);var n=i.length;if(!n)return Ma();var o=r.dividePath||Cs,s=o({path:t,count:n});if(s.length!==n)return console.error("Invalid morphing: unmatched splitted path"),Ma();i=ai(i),s=ai(s);for(var f=r.done,h=r.during,u=r.individualDelay,v=new Aa,l=0;l<n;l++){var c=i[l],_=s[l];_.parent=t,_.copyTransform(v),u||Ts(c,_)}t.__isCombineMorphing=!0,t.childrenRef=function(){return s};function g(C){for(var T=0;T<s.length;T++)s[T].addSelfToZr(C)}ii(t,"addSelfToZr",{after:function(C){g(C)}}),ii(t,"removeSelfFromZr",{after:function(C){for(var T=0;T<s.length;T++)s[T].removeSelfFromZr(C)}});function d(){t.__isCombineMorphing=!1,t.__morphT=-1,t.childrenRef=null,ne(t,"addSelfToZr"),ne(t,"removeSelfFromZr")}var p=s.length;if(u)for(var y=p,m=function(){y--,y===0&&(d(),f&&f())},l=0;l<p;l++){var w=u?_t({delay:(r.delay||0)+u(l,p,i[l],s[l]),done:m},r):r;bs(i[l],s[l],w)}else t.__morphT=0,t.animateTo({__morphT:1},_t({during:function(C){for(var T=0;T<p;T++){var b=s[T];b.__morphT=t.__morphT,b.dirtyShape()}h&&h(C)},done:function(){d();for(var C=0;C<e.length;C++)ne(e[C],"updateTransform");f&&f()}},r));return t.__zr&&g(t.__zr),{fromIndividuals:i,toIndividuals:s,count:p}}function Ll(e,t,r){var i=t.length,a=[],n=r.dividePath||Cs;function o(c){for(var _=0;_<c.length;_++){var g=c[_];Ca(g)?o(g.childrenRef()):g instanceof q&&a.push(g)}}if(Ca(e)){o(e.childrenRef());var s=a.length;if(s<i)for(var f=0,h=s;h<i;h++)a.push(rs(a[f++%s]));a.length=i}else{a=n({path:e,count:i});for(var u=e.getComputedTransform(),h=0;h<a.length;h++)a[h].setLocalTransform(u);if(a.length!==i)return console.error("Invalid morphing: unmatched splitted path"),Ma()}a=ai(a),t=ai(t);for(var v=r.individualDelay,h=0;h<i;h++){var l=v?_t({delay:(r.delay||0)+v(h,i,a[h],t[h])},r):r;bs(a[h],t[h],l)}return{fromIndividuals:a,toIndividuals:t,count:t.length}}export{Rr as $,mf as A,ee as B,_t as C,qe as D,ll as E,Tu as F,Cu as G,hs as H,Eu as I,Ke as J,pl as K,Hu as L,_l as M,Sr as N,vl as O,q as P,ml as Q,dl as R,Fn as S,Aa as T,gl as U,Wu as V,Gu as W,$ as X,yl as Y,Ia as Z,I as _,Yt as a,rl as a$,Pr as a0,zr as a1,uo as a2,$s as a3,el as a4,Zv as a5,ks as a6,Ha as a7,Uv as a8,Kv as a9,nl as aA,zh as aB,ke as aC,Vv as aD,go as aE,qv as aF,il as aG,df as aH,lv as aI,bl as aJ,Tv as aK,pe as aL,yf as aM,Br as aN,tl as aO,Xv as aP,al as aQ,de as aR,ol as aS,sl as aT,xt as aU,ia as aV,he as aW,Ye as aX,Ji as aY,lf as aZ,yt as a_,jv as aa,Or as ab,ch as ac,wl as ad,ui as ae,le as af,hv as ag,$r as ah,hl as ai,Af as aj,vo as ak,ue as al,Ys as am,ul as an,Tl as ao,Dr as ap,xr as aq,lh as ar,Ks as as,js as at,Bf as au,If as av,pr as aw,Re as ax,Po as ay,N as az,Qv as b,Cl as b0,Ca as b1,bs as b2,Ml as b3,Ll as b4,rs as b5,Jv as c,Be as d,j as e,Nv as f,oe as g,Rt as h,se as i,K as j,Gv as k,Y as l,Tt as m,si as n,At as o,tt as p,fl as q,oi as r,ut as s,mu as t,cl as u,au as v,nu as w,Er as x,gf as y,ie as z};
