function ot(n){return n instanceof Map?n.clear=n.delete=n.set=function(){throw new Error("map is read-only")}:n instanceof Set&&(n.add=n.clear=n.delete=function(){throw new Error("set is read-only")}),Object.freeze(n),Object.getOwnPropertyNames(n).forEach(e=>{const t=n[e],r=typeof t;(r==="object"||r==="function")&&!Object.isFrozen(t)&&ot(t)}),n}class En{constructor(e){e.data===void 0&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function ct(n){return n.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function de(n,...e){const t=Object.create(null);for(const r in n)t[r]=n[r];return e.forEach(function(r){for(const d in r)t[d]=r[d]}),t}const Ot="</span>",hn=n=>!!n.scope,At=(n,{prefix:e})=>{if(n.startsWith("language:"))return n.replace("language:","language-");if(n.includes(".")){const t=n.split(".");return[`${e}${t.shift()}`,...t.map((r,d)=>`${r}${"_".repeat(d+1)}`)].join(" ")}return`${e}${n}`};class Rt{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=ct(e)}openNode(e){if(!hn(e))return;const t=At(e.scope,{prefix:this.classPrefix});this.span(t)}closeNode(e){!hn(e)||(this.buffer+=Ot)}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}const Nn=(n={})=>{const e={children:[]};return Object.assign(e,n),e};class pn{constructor(){this.rootNode=Nn(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){const t=Nn({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return typeof t=="string"?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(r=>this._walk(e,r)),e.closeNode(t)),e}static _collapse(e){typeof e!="string"&&(!e.children||(e.children.every(t=>typeof t=="string")?e.children=[e.children.join("")]:e.children.forEach(t=>{pn._collapse(t)})))}}class Mt extends pn{constructor(e){super(),this.options=e}addText(e){e!==""&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,t){const r=e.root;t&&(r.scope=`language:${t}`),this.add(r)}toHTML(){return new Rt(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function ye(n){return n?typeof n=="string"?n:n.source:null}function lt(n){return ge("(?=",n,")")}function kt(n){return ge("(?:",n,")*")}function It(n){return ge("(?:",n,")?")}function ge(...n){return n.map(t=>ye(t)).join("")}function xt(n){const e=n[n.length-1];return typeof e=="object"&&e.constructor===Object?(n.splice(n.length-1,1),e):{}}function mn(...n){return"("+(xt(n).capture?"":"?:")+n.map(r=>ye(r)).join("|")+")"}function dt(n){return new RegExp(n.toString()+"|").exec("").length-1}function Ct(n,e){const t=n&&n.exec(e);return t&&t.index===0}const Lt=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function _n(n,{joinWith:e}){let t=0;return n.map(r=>{t+=1;const d=t;let l=ye(r),a="";for(;l.length>0;){const s=Lt.exec(l);if(!s){a+=l;break}a+=l.substring(0,s.index),l=l.substring(s.index+s[0].length),s[0][0]==="\\"&&s[1]?a+="\\"+String(Number(s[1])+d):(a+=s[0],s[0]==="("&&t++)}return a}).map(r=>`(${r})`).join(e)}const Dt=/\b\B/,ut="[a-zA-Z]\\w*",fn="[a-zA-Z_]\\w*",gt="\\b\\d+(\\.\\d+)?",bt="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",pt="\\b(0b[01]+)",Bt="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",Ut=(n={})=>{const e=/^#![ ]*\//;return n.binary&&(n.begin=ge(e,/.*\b/,n.binary,/\b.*/)),de({scope:"meta",begin:e,end:/$/,relevance:0,"on:begin":(t,r)=>{t.index!==0&&r.ignoreMatch()}},n)},ve={begin:"\\\\[\\s\\S]",relevance:0},Pt={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[ve]},Ft={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[ve]},zt={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Re=function(n,e,t={}){const r=de({scope:"comment",begin:n,end:e,contains:[]},t);r.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const d=mn("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return r.contains.push({begin:ge(/[ ]+/,"(",d,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),r},$t=Re("//","$"),Kt=Re("/\\*","\\*/"),Gt=Re("#","$"),qt={scope:"number",begin:gt,relevance:0},Ht={scope:"number",begin:bt,relevance:0},Wt={scope:"number",begin:pt,relevance:0},Yt={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[ve,{begin:/\[/,end:/\]/,relevance:0,contains:[ve]}]},Zt={scope:"title",begin:ut,relevance:0},Xt={scope:"title",begin:fn,relevance:0},Vt={begin:"\\.\\s*"+fn,relevance:0},Qt=function(n){return Object.assign(n,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})};var Oe=Object.freeze({__proto__:null,APOS_STRING_MODE:Pt,BACKSLASH_ESCAPE:ve,BINARY_NUMBER_MODE:Wt,BINARY_NUMBER_RE:pt,COMMENT:Re,C_BLOCK_COMMENT_MODE:Kt,C_LINE_COMMENT_MODE:$t,C_NUMBER_MODE:Ht,C_NUMBER_RE:bt,END_SAME_AS_BEGIN:Qt,HASH_COMMENT_MODE:Gt,IDENT_RE:ut,MATCH_NOTHING_RE:Dt,METHOD_GUARD:Vt,NUMBER_MODE:qt,NUMBER_RE:gt,PHRASAL_WORDS_MODE:zt,QUOTE_STRING_MODE:Ft,REGEXP_MODE:Yt,RE_STARTERS_RE:Bt,SHEBANG:Ut,TITLE_MODE:Zt,UNDERSCORE_IDENT_RE:fn,UNDERSCORE_TITLE_MODE:Xt});function Jt(n,e){n.input[n.index-1]==="."&&e.ignoreMatch()}function jt(n,e){n.className!==void 0&&(n.scope=n.className,delete n.className)}function ei(n,e){!e||!n.beginKeywords||(n.begin="\\b("+n.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",n.__beforeBegin=Jt,n.keywords=n.keywords||n.beginKeywords,delete n.beginKeywords,n.relevance===void 0&&(n.relevance=0))}function ni(n,e){!Array.isArray(n.illegal)||(n.illegal=mn(...n.illegal))}function ti(n,e){if(!!n.match){if(n.begin||n.end)throw new Error("begin & end are not supported with match");n.begin=n.match,delete n.match}}function ii(n,e){n.relevance===void 0&&(n.relevance=1)}const ai=(n,e)=>{if(!n.beforeMatch)return;if(n.starts)throw new Error("beforeMatch cannot be used with starts");const t=Object.assign({},n);Object.keys(n).forEach(r=>{delete n[r]}),n.keywords=t.keywords,n.begin=ge(t.beforeMatch,lt(t.begin)),n.starts={relevance:0,contains:[Object.assign(t,{endsParent:!0})]},n.relevance=0,delete t.beforeMatch},ri=["of","and","for","in","not","or","if","then","parent","list","value"],si="keyword";function mt(n,e,t=si){const r=Object.create(null);return typeof n=="string"?d(t,n.split(" ")):Array.isArray(n)?d(t,n):Object.keys(n).forEach(function(l){Object.assign(r,mt(n[l],e,l))}),r;function d(l,a){e&&(a=a.map(s=>s.toLowerCase())),a.forEach(function(s){const c=s.split("|");r[c[0]]=[l,oi(c[0],c[1])]})}}function oi(n,e){return e?Number(e):ci(n)?0:1}function ci(n){return ri.includes(n.toLowerCase())}const yn={},ue=n=>{console.error(n)},vn=(n,...e)=>{console.log(`WARN: ${n}`,...e)},me=(n,e)=>{yn[`${n}/${e}`]||(console.log(`Deprecated as of ${n}. ${e}`),yn[`${n}/${e}`]=!0)},Ae=new Error;function _t(n,e,{key:t}){let r=0;const d=n[t],l={},a={};for(let s=1;s<=e.length;s++)a[s+r]=d[s],l[s+r]=!0,r+=dt(e[s-1]);n[t]=a,n[t]._emit=l,n[t]._multi=!0}function li(n){if(!!Array.isArray(n.begin)){if(n.skip||n.excludeBegin||n.returnBegin)throw ue("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),Ae;if(typeof n.beginScope!="object"||n.beginScope===null)throw ue("beginScope must be object"),Ae;_t(n,n.begin,{key:"beginScope"}),n.begin=_n(n.begin,{joinWith:""})}}function di(n){if(!!Array.isArray(n.end)){if(n.skip||n.excludeEnd||n.returnEnd)throw ue("skip, excludeEnd, returnEnd not compatible with endScope: {}"),Ae;if(typeof n.endScope!="object"||n.endScope===null)throw ue("endScope must be object"),Ae;_t(n,n.end,{key:"endScope"}),n.end=_n(n.end,{joinWith:""})}}function ui(n){n.scope&&typeof n.scope=="object"&&n.scope!==null&&(n.beginScope=n.scope,delete n.scope)}function gi(n){ui(n),typeof n.beginScope=="string"&&(n.beginScope={_wrap:n.beginScope}),typeof n.endScope=="string"&&(n.endScope={_wrap:n.endScope}),li(n),di(n)}function bi(n){function e(a,s){return new RegExp(ye(a),"m"+(n.case_insensitive?"i":"")+(n.unicodeRegex?"u":"")+(s?"g":""))}class t{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(s,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,s]),this.matchAt+=dt(s)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const s=this.regexes.map(c=>c[1]);this.matcherRe=e(_n(s,{joinWith:"|"}),!0),this.lastIndex=0}exec(s){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(s);if(!c)return null;const o=c.findIndex((g,b)=>b>0&&g!==void 0),i=this.matchIndexes[o];return c.splice(0,o),Object.assign(c,i)}}class r{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(s){if(this.multiRegexes[s])return this.multiRegexes[s];const c=new t;return this.rules.slice(s).forEach(([o,i])=>c.addRule(o,i)),c.compile(),this.multiRegexes[s]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(s,c){this.rules.push([s,c]),c.type==="begin"&&this.count++}exec(s){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let o=c.exec(s);if(this.resumingScanAtSamePosition()&&!(o&&o.index===this.lastIndex)){const i=this.getMatcher(0);i.lastIndex=this.lastIndex+1,o=i.exec(s)}return o&&(this.regexIndex+=o.position+1,this.regexIndex===this.count&&this.considerAll()),o}}function d(a){const s=new r;return a.contains.forEach(c=>s.addRule(c.begin,{rule:c,type:"begin"})),a.terminatorEnd&&s.addRule(a.terminatorEnd,{type:"end"}),a.illegal&&s.addRule(a.illegal,{type:"illegal"}),s}function l(a,s){const c=a;if(a.isCompiled)return c;[jt,ti,gi,ai].forEach(i=>i(a,s)),n.compilerExtensions.forEach(i=>i(a,s)),a.__beforeBegin=null,[ei,ni,ii].forEach(i=>i(a,s)),a.isCompiled=!0;let o=null;return typeof a.keywords=="object"&&a.keywords.$pattern&&(a.keywords=Object.assign({},a.keywords),o=a.keywords.$pattern,delete a.keywords.$pattern),o=o||/\w+/,a.keywords&&(a.keywords=mt(a.keywords,n.case_insensitive)),c.keywordPatternRe=e(o,!0),s&&(a.begin||(a.begin=/\B|\b/),c.beginRe=e(c.begin),!a.end&&!a.endsWithParent&&(a.end=/\B|\b/),a.end&&(c.endRe=e(c.end)),c.terminatorEnd=ye(c.end)||"",a.endsWithParent&&s.terminatorEnd&&(c.terminatorEnd+=(a.end?"|":"")+s.terminatorEnd)),a.illegal&&(c.illegalRe=e(a.illegal)),a.contains||(a.contains=[]),a.contains=[].concat(...a.contains.map(function(i){return pi(i==="self"?a:i)})),a.contains.forEach(function(i){l(i,c)}),a.starts&&l(a.starts,s),c.matcher=d(c),c}if(n.compilerExtensions||(n.compilerExtensions=[]),n.contains&&n.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return n.classNameAliases=de(n.classNameAliases||{}),l(n)}function ft(n){return n?n.endsWithParent||ft(n.starts):!1}function pi(n){return n.variants&&!n.cachedVariants&&(n.cachedVariants=n.variants.map(function(e){return de(n,{variants:null},e)})),n.cachedVariants?n.cachedVariants:ft(n)?de(n,{starts:n.starts?de(n.starts):null}):Object.isFrozen(n)?de(n):n}var mi="11.11.1";class _i extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}const ke=ct,Tn=de,Sn=Symbol("nomatch"),fi=7,Et=function(n){const e=Object.create(null),t=Object.create(null),r=[];let d=!0;const l="Could not find the language '{}', did you forget to load/include a language module?",a={disableAutodetect:!0,name:"Plain text",contains:[]};let s={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:Mt};function c(u){return s.noHighlightRe.test(u)}function o(u){let f=u.className+" ";f+=u.parentNode?u.parentNode.className:"";const w=s.languageDetectRe.exec(f);if(w){const L=S(w[1]);return L||(vn(l.replace("{}",w[1])),vn("Falling back to no-highlight mode for this block.",u)),L?w[1]:"no-highlight"}return f.split(/\s+/).find(L=>c(L)||S(L))}function i(u,f,w){let L="",z="";typeof f=="object"?(L=u,w=f.ignoreIllegals,z=f.language):(me("10.7.0","highlight(lang, code, ...args) has been deprecated."),me("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),z=u,L=f),w===void 0&&(w=!0);const Z={code:L,language:z};Q("before:highlight",Z);const j=Z.result?Z.result:g(Z.language,Z.code,w);return j.code=Z.code,Q("after:highlight",j),j}function g(u,f,w,L){const z=Object.create(null);function Z(p,y){return p.keywords[y]}function j(){if(!O.keywords){X.addText(B);return}let p=0;O.keywordPatternRe.lastIndex=0;let y=O.keywordPatternRe.exec(B),A="";for(;y;){A+=B.substring(p,y.index);const D=ae.case_insensitive?y[0].toLowerCase():y[0],W=Z(O,D);if(W){const[re,Se]=W;if(X.addText(A),A="",z[D]=(z[D]||0)+1,z[D]<=fi&&(pe+=Se),re.startsWith("_"))A+=y[0];else{const Me=ae.classNameAliases[re]||re;H(y[0],Me)}}else A+=y[0];p=O.keywordPatternRe.lastIndex,y=O.keywordPatternRe.exec(B)}A+=B.substring(p),X.addText(A)}function se(){if(B==="")return;let p=null;if(typeof O.subLanguage=="string"){if(!e[O.subLanguage]){X.addText(B);return}p=g(O.subLanguage,B,!0,Te[O.subLanguage]),Te[O.subLanguage]=p._top}else p=m(B,O.subLanguage.length?O.subLanguage:null);O.relevance>0&&(pe+=p.relevance),X.__addSublanguage(p._emitter,p.language)}function G(){O.subLanguage!=null?se():j(),B=""}function H(p,y){p!==""&&(X.startScope(y),X.addText(p),X.endScope())}function oe(p,y){let A=1;const D=y.length-1;for(;A<=D;){if(!p._emit[A]){A++;continue}const W=ae.classNameAliases[p[A]]||p[A],re=y[A];W?H(re,W):(B=re,j(),B=""),A++}}function q(p,y){return p.scope&&typeof p.scope=="string"&&X.openNode(ae.classNameAliases[p.scope]||p.scope),p.beginScope&&(p.beginScope._wrap?(H(B,ae.classNameAliases[p.beginScope._wrap]||p.beginScope._wrap),B=""):p.beginScope._multi&&(oe(p.beginScope,y),B="")),O=Object.create(p,{parent:{value:O}}),O}function $(p,y,A){let D=Ct(p.endRe,A);if(D){if(p["on:end"]){const W=new En(p);p["on:end"](y,W),W.isMatchIgnored&&(D=!1)}if(D){for(;p.endsParent&&p.parent;)p=p.parent;return p}}if(p.endsWithParent)return $(p.parent,y,A)}function J(p){return O.matcher.regexIndex===0?(B+=p[0],1):(le=!0,0)}function ee(p){const y=p[0],A=p.rule,D=new En(A),W=[A.__beforeBegin,A["on:begin"]];for(const re of W)if(!!re&&(re(p,D),D.isMatchIgnored))return J(y);return A.skip?B+=y:(A.excludeBegin&&(B+=y),G(),!A.returnBegin&&!A.excludeBegin&&(B=y)),q(A,p),A.returnBegin?0:y.length}function ne(p){const y=p[0],A=f.substring(p.index),D=$(O,p,A);if(!D)return Sn;const W=O;O.endScope&&O.endScope._wrap?(G(),H(y,O.endScope._wrap)):O.endScope&&O.endScope._multi?(G(),oe(O.endScope,p)):W.skip?B+=y:(W.returnEnd||W.excludeEnd||(B+=y),G(),W.excludeEnd&&(B=y));do O.scope&&X.closeNode(),!O.skip&&!O.subLanguage&&(pe+=O.relevance),O=O.parent;while(O!==D.parent);return D.starts&&q(D.starts,p),W.returnEnd?0:y.length}function ie(){const p=[];for(let y=O;y!==ae;y=y.parent)y.scope&&p.unshift(y.scope);p.forEach(y=>X.openNode(y))}let te={};function fe(p,y){const A=y&&y[0];if(B+=p,A==null)return G(),0;if(te.type==="begin"&&y.type==="end"&&te.index===y.index&&A===""){if(B+=f.slice(y.index,y.index+1),!d){const D=new Error(`0 width match regex (${u})`);throw D.languageName=u,D.badRule=te.rule,D}return 1}if(te=y,y.type==="begin")return ee(y);if(y.type==="illegal"&&!w){const D=new Error('Illegal lexeme "'+A+'" for mode "'+(O.scope||"<unnamed>")+'"');throw D.mode=O,D}else if(y.type==="end"){const D=ne(y);if(D!==Sn)return D}if(y.type==="illegal"&&A==="")return B+=`
`,1;if(he>1e5&&he>y.index*3)throw new Error("potential infinite loop, way more iterations than matches");return B+=A,A.length}const ae=S(u);if(!ae)throw ue(l.replace("{}",u)),new Error('Unknown language: "'+u+'"');const Ee=bi(ae);let be="",O=L||Ee;const Te={},X=new s.__emitter(s);ie();let B="",pe=0,ce=0,he=0,le=!1;try{if(ae.__emitTokens)ae.__emitTokens(f,X);else{for(O.matcher.considerAll();;){he++,le?le=!1:O.matcher.considerAll(),O.matcher.lastIndex=ce;const p=O.matcher.exec(f);if(!p)break;const y=f.substring(ce,p.index),A=fe(y,p);ce=p.index+A}fe(f.substring(ce))}return X.finalize(),be=X.toHTML(),{language:u,value:be,relevance:pe,illegal:!1,_emitter:X,_top:O}}catch(p){if(p.message&&p.message.includes("Illegal"))return{language:u,value:ke(f),illegal:!0,relevance:0,_illegalBy:{message:p.message,index:ce,context:f.slice(ce-100,ce+100),mode:p.mode,resultSoFar:be},_emitter:X};if(d)return{language:u,value:ke(f),illegal:!1,relevance:0,errorRaised:p,_emitter:X,_top:O};throw p}}function b(u){const f={value:ke(u),illegal:!1,relevance:0,_top:a,_emitter:new s.__emitter(s)};return f._emitter.addText(u),f}function m(u,f){f=f||s.languages||Object.keys(e);const w=b(u),L=f.filter(S).filter(U).map(G=>g(G,u,!1));L.unshift(w);const z=L.sort((G,H)=>{if(G.relevance!==H.relevance)return H.relevance-G.relevance;if(G.language&&H.language){if(S(G.language).supersetOf===H.language)return 1;if(S(H.language).supersetOf===G.language)return-1}return 0}),[Z,j]=z,se=Z;return se.secondBest=j,se}function _(u,f,w){const L=f&&t[f]||w;u.classList.add("hljs"),u.classList.add(`language-${L}`)}function h(u){let f=null;const w=o(u);if(c(w))return;if(Q("before:highlightElement",{el:u,language:w}),u.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",u);return}if(u.children.length>0&&(s.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(u)),s.throwUnescapedHTML))throw new _i("One of your code blocks includes unescaped HTML.",u.innerHTML);f=u;const L=f.textContent,z=w?i(L,{language:w,ignoreIllegals:!0}):m(L);u.innerHTML=z.value,u.dataset.highlighted="yes",_(u,w,z.language),u.result={language:z.language,re:z.relevance,relevance:z.relevance},z.secondBest&&(u.secondBest={language:z.secondBest.language,relevance:z.secondBest.relevance}),Q("after:highlightElement",{el:u,result:z,text:L})}function E(u){s=Tn(s,u)}const N=()=>{C(),me("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function v(){C(),me("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let T=!1;function C(){function u(){C()}if(document.readyState==="loading"){T||window.addEventListener("DOMContentLoaded",u,!1),T=!0;return}document.querySelectorAll(s.cssSelector).forEach(h)}function M(u,f){let w=null;try{w=f(n)}catch(L){if(ue("Language definition for '{}' could not be registered.".replace("{}",u)),d)ue(L);else throw L;w=a}w.name||(w.name=u),e[u]=w,w.rawDefinition=f.bind(null,n),w.aliases&&I(w.aliases,{languageName:u})}function k(u){delete e[u];for(const f of Object.keys(t))t[f]===u&&delete t[f]}function x(){return Object.keys(e)}function S(u){return u=(u||"").toLowerCase(),e[u]||e[t[u]]}function I(u,{languageName:f}){typeof u=="string"&&(u=[u]),u.forEach(w=>{t[w.toLowerCase()]=f})}function U(u){const f=S(u);return f&&!f.disableAutodetect}function F(u){u["before:highlightBlock"]&&!u["before:highlightElement"]&&(u["before:highlightElement"]=f=>{u["before:highlightBlock"](Object.assign({block:f.el},f))}),u["after:highlightBlock"]&&!u["after:highlightElement"]&&(u["after:highlightElement"]=f=>{u["after:highlightBlock"](Object.assign({block:f.el},f))})}function V(u){F(u),r.push(u)}function Y(u){const f=r.indexOf(u);f!==-1&&r.splice(f,1)}function Q(u,f){const w=u;r.forEach(function(L){L[w]&&L[w](f)})}function K(u){return me("10.7.0","highlightBlock will be removed entirely in v12.0"),me("10.7.0","Please use highlightElement now."),h(u)}Object.assign(n,{highlight:i,highlightAuto:m,highlightAll:C,highlightElement:h,highlightBlock:K,configure:E,initHighlighting:N,initHighlightingOnLoad:v,registerLanguage:M,unregisterLanguage:k,listLanguages:x,getLanguage:S,registerAliases:I,autoDetection:U,inherit:Tn,addPlugin:V,removePlugin:Y}),n.debugMode=function(){d=!1},n.safeMode=function(){d=!0},n.versionString=mi,n.regex={concat:ge,lookahead:lt,either:mn,optional:It,anyNumberOfTimes:kt};for(const u in Oe)typeof Oe[u]=="object"&&ot(Oe[u]);return Object.assign(n,Oe),n},_e=Et({});_e.newInstance=()=>Et({});var Ei=_e;_e.HighlightJS=_e;_e.default=_e;var Ie,wn;function hi(){if(wn)return Ie;wn=1;function n(e){const t=e.regex,r=t.concat(/[\p{L}_]/u,t.optional(/[\p{L}0-9_.-]*:/u),/[\p{L}0-9_.-]*/u),d=/[\p{L}0-9._:-]+/u,l={className:"symbol",begin:/&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/},a={begin:/\s/,contains:[{className:"keyword",begin:/#?[a-z_][a-z1-9_-]+/,illegal:/\n/}]},s=e.inherit(a,{begin:/\(/,end:/\)/}),c=e.inherit(e.APOS_STRING_MODE,{className:"string"}),o=e.inherit(e.QUOTE_STRING_MODE,{className:"string"}),i={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:d,relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[l]},{begin:/'/,end:/'/,contains:[l]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,unicodeRegex:!0,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,relevance:10,contains:[a,o,c,s,{begin:/\[/,end:/\]/,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,contains:[a,s,o,c]}]}]},e.COMMENT(/<!--/,/-->/,{relevance:10}),{begin:/<!\[CDATA\[/,end:/\]\]>/,relevance:10},l,{className:"meta",end:/\?>/,variants:[{begin:/<\?xml/,relevance:10,contains:[o]},{begin:/<\?[a-z][a-z0-9]+/}]},{className:"tag",begin:/<style(?=\s|>)/,end:/>/,keywords:{name:"style"},contains:[i],starts:{end:/<\/style>/,returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:/<script(?=\s|>)/,end:/>/,keywords:{name:"script"},contains:[i],starts:{end:/<\/script>/,returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:/<>|<\/>/},{className:"tag",begin:t.concat(/</,t.lookahead(t.concat(r,t.either(/\/>/,/>/,/\s/)))),end:/\/?>/,contains:[{className:"name",begin:r,relevance:0,starts:i}]},{className:"tag",begin:t.concat(/<\//,t.lookahead(t.concat(r,/>/))),contains:[{className:"name",begin:r,relevance:0},{begin:/>/,relevance:0,endsParent:!0}]}]}}return Ie=n,Ie}var xe,On;function Ni(){if(On)return xe;On=1;function n(e){const t=e.regex,r={},d={begin:/\$\{/,end:/\}/,contains:["self",{begin:/:-/,contains:[r]}]};Object.assign(r,{className:"variable",variants:[{begin:t.concat(/\$[\w\d#@][\w\d_]*/,"(?![\\w\\d])(?![$])")},d]});const l={className:"subst",begin:/\$\(/,end:/\)/,contains:[e.BACKSLASH_ESCAPE]},a=e.inherit(e.COMMENT(),{match:[/(^|\s)/,/#.*$/],scope:{2:"comment"}}),s={begin:/<<-?\s*(?=\w+)/,starts:{contains:[e.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,className:"string"})]}},c={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,r,l]};l.contains.push(c);const o={match:/\\"/},i={className:"string",begin:/'/,end:/'/},g={match:/\\'/},b={begin:/\$?\(\(/,end:/\)\)/,contains:[{begin:/\d+#[0-9a-f]+/,className:"number"},e.NUMBER_MODE,r]},m=["fish","bash","zsh","sh","csh","ksh","tcsh","dash","scsh"],_=e.SHEBANG({binary:`(${m.join("|")})`,relevance:10}),h={className:"function",begin:/\w[\w\d_]*\s*\(\s*\)\s*\{/,returnBegin:!0,contains:[e.inherit(e.TITLE_MODE,{begin:/\w[\w\d_]*/})],relevance:0},E=["if","then","else","elif","fi","time","for","while","until","in","do","done","case","esac","coproc","function","select"],N=["true","false"],v={match:/(\/[a-z._-]+)+/},T=["break","cd","continue","eval","exec","exit","export","getopts","hash","pwd","readonly","return","shift","test","times","trap","umask","unset"],C=["alias","bind","builtin","caller","command","declare","echo","enable","help","let","local","logout","mapfile","printf","read","readarray","source","sudo","type","typeset","ulimit","unalias"],M=["autoload","bg","bindkey","bye","cap","chdir","clone","comparguments","compcall","compctl","compdescribe","compfiles","compgroups","compquote","comptags","comptry","compvalues","dirs","disable","disown","echotc","echoti","emulate","fc","fg","float","functions","getcap","getln","history","integer","jobs","kill","limit","log","noglob","popd","print","pushd","pushln","rehash","sched","setcap","setopt","stat","suspend","ttyctl","unfunction","unhash","unlimit","unsetopt","vared","wait","whence","where","which","zcompile","zformat","zftp","zle","zmodload","zparseopts","zprof","zpty","zregexparse","zsocket","zstyle","ztcp"],k=["chcon","chgrp","chown","chmod","cp","dd","df","dir","dircolors","ln","ls","mkdir","mkfifo","mknod","mktemp","mv","realpath","rm","rmdir","shred","sync","touch","truncate","vdir","b2sum","base32","base64","cat","cksum","comm","csplit","cut","expand","fmt","fold","head","join","md5sum","nl","numfmt","od","paste","ptx","pr","sha1sum","sha224sum","sha256sum","sha384sum","sha512sum","shuf","sort","split","sum","tac","tail","tr","tsort","unexpand","uniq","wc","arch","basename","chroot","date","dirname","du","echo","env","expr","factor","groups","hostid","id","link","logname","nice","nohup","nproc","pathchk","pinky","printenv","printf","pwd","readlink","runcon","seq","sleep","stat","stdbuf","stty","tee","test","timeout","tty","uname","unlink","uptime","users","who","whoami","yes"];return{name:"Bash",aliases:["sh","zsh"],keywords:{$pattern:/\b[a-z][a-z0-9._-]+\b/,keyword:E,literal:N,built_in:[...T,...C,"set","shopt",...M,...k]},contains:[_,e.SHEBANG(),h,b,a,s,v,c,o,i,g,r]}}return xe=n,xe}var Ce,An;function yi(){if(An)return Ce;An=1;function n(e){const t=e.regex,r=e.COMMENT("//","$",{contains:[{begin:/\\\n/}]}),d="decltype\\(auto\\)",l="[a-zA-Z_]\\w*::",a="<[^<>]+>",s="("+d+"|"+t.optional(l)+"[a-zA-Z_]\\w*"+t.optional(a)+")",c={className:"type",variants:[{begin:"\\b[a-z\\d_]*_t\\b"},{match:/\batomic_[a-z]{3,6}\b/}]},o="\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)",i={className:"string",variants:[{begin:'(u8?|U|L)?"',end:'"',illegal:"\\n",contains:[e.BACKSLASH_ESCAPE]},{begin:"(u8?|U|L)?'("+o+"|.)",end:"'",illegal:"."},e.END_SAME_AS_BEGIN({begin:/(?:u8?|U|L)?R"([^()\\ ]{0,16})\(/,end:/\)([^()\\ ]{0,16})"/})]},g={className:"number",variants:[{match:/\b(0b[01']+)/},{match:/(-?)\b([\d']+(\.[\d']*)?|\.[\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)/},{match:/(-?)\b(0[xX][a-fA-F0-9]+(?:'[a-fA-F0-9]+)*(?:\.[a-fA-F0-9]*(?:'[a-fA-F0-9]*)*)?(?:[pP][-+]?[0-9]+)?(l|L)?(u|U)?)/},{match:/(-?)\b\d+(?:'\d+)*(?:\.\d*(?:'\d*)*)?(?:[eE][-+]?\d+)?/}],relevance:0},b={className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{keyword:"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef elifdef elifndef include"},contains:[{begin:/\\\n/,relevance:0},e.inherit(i,{className:"string"}),{className:"string",begin:/<.*?>/},r,e.C_BLOCK_COMMENT_MODE]},m={className:"title",begin:t.optional(l)+e.IDENT_RE,relevance:0},_=t.optional(l)+e.IDENT_RE+"\\s*\\(",N={keyword:["asm","auto","break","case","continue","default","do","else","enum","extern","for","fortran","goto","if","inline","register","restrict","return","sizeof","typeof","typeof_unqual","struct","switch","typedef","union","volatile","while","_Alignas","_Alignof","_Atomic","_Generic","_Noreturn","_Static_assert","_Thread_local","alignas","alignof","noreturn","static_assert","thread_local","_Pragma"],type:["float","double","signed","unsigned","int","short","long","char","void","_Bool","_BitInt","_Complex","_Imaginary","_Decimal32","_Decimal64","_Decimal96","_Decimal128","_Decimal64x","_Decimal128x","_Float16","_Float32","_Float64","_Float128","_Float32x","_Float64x","_Float128x","const","static","constexpr","complex","bool","imaginary"],literal:"true false NULL",built_in:"std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan vfprintf vprintf vsprintf endl initializer_list unique_ptr"},v=[b,c,r,e.C_BLOCK_COMMENT_MODE,g,i],T={variants:[{begin:/=/,end:/;/},{begin:/\(/,end:/\)/},{beginKeywords:"new throw return else",end:/;/}],keywords:N,contains:v.concat([{begin:/\(/,end:/\)/,keywords:N,contains:v.concat(["self"]),relevance:0}]),relevance:0},C={begin:"("+s+"[\\*&\\s]+)+"+_,returnBegin:!0,end:/[{;=]/,excludeEnd:!0,keywords:N,illegal:/[^\w\s\*&:<>.]/,contains:[{begin:d,keywords:N,relevance:0},{begin:_,returnBegin:!0,contains:[e.inherit(m,{className:"title.function"})],relevance:0},{relevance:0,match:/,/},{className:"params",begin:/\(/,end:/\)/,keywords:N,relevance:0,contains:[r,e.C_BLOCK_COMMENT_MODE,i,g,c,{begin:/\(/,end:/\)/,keywords:N,relevance:0,contains:["self",r,e.C_BLOCK_COMMENT_MODE,i,g,c]}]},c,r,e.C_BLOCK_COMMENT_MODE,b]};return{name:"C",aliases:["h"],keywords:N,disableAutodetect:!0,illegal:"</",contains:[].concat(T,C,v,[b,{begin:e.IDENT_RE+"::",keywords:N},{className:"class",beginKeywords:"enum class struct union",end:/[{;:<>=]/,contains:[{beginKeywords:"final class struct"},e.TITLE_MODE]}]),exports:{preprocessor:b,strings:i,keywords:N}}}return Ce=n,Ce}var Le,Rn;function vi(){if(Rn)return Le;Rn=1;function n(e){const t=e.regex,r=e.COMMENT("//","$",{contains:[{begin:/\\\n/}]}),d="decltype\\(auto\\)",l="[a-zA-Z_]\\w*::",a="<[^<>]+>",s="(?!struct)("+d+"|"+t.optional(l)+"[a-zA-Z_]\\w*"+t.optional(a)+")",c={className:"type",begin:"\\b[a-z\\d_]*_t\\b"},o="\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)",i={className:"string",variants:[{begin:'(u8?|U|L)?"',end:'"',illegal:"\\n",contains:[e.BACKSLASH_ESCAPE]},{begin:"(u8?|U|L)?'("+o+"|.)",end:"'",illegal:"."},e.END_SAME_AS_BEGIN({begin:/(?:u8?|U|L)?R"([^()\\ ]{0,16})\(/,end:/\)([^()\\ ]{0,16})"/})]},g={className:"number",variants:[{begin:"[+-]?(?:(?:[0-9](?:'?[0-9])*\\.(?:[0-9](?:'?[0-9])*)?|\\.[0-9](?:'?[0-9])*)(?:[Ee][+-]?[0-9](?:'?[0-9])*)?|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*|0[Xx](?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?|\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)[Pp][+-]?[0-9](?:'?[0-9])*)(?:[Ff](?:16|32|64|128)?|(BF|bf)16|[Ll]|)"},{begin:"[+-]?\\b(?:0[Bb][01](?:'?[01])*|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*|0(?:'?[0-7])*|[1-9](?:'?[0-9])*)(?:[Uu](?:LL?|ll?)|[Uu][Zz]?|(?:LL?|ll?)[Uu]?|[Zz][Uu]|)"}],relevance:0},b={className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{keyword:"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include"},contains:[{begin:/\\\n/,relevance:0},e.inherit(i,{className:"string"}),{className:"string",begin:/<.*?>/},r,e.C_BLOCK_COMMENT_MODE]},m={className:"title",begin:t.optional(l)+e.IDENT_RE,relevance:0},_=t.optional(l)+e.IDENT_RE+"\\s*\\(",h=["alignas","alignof","and","and_eq","asm","atomic_cancel","atomic_commit","atomic_noexcept","auto","bitand","bitor","break","case","catch","class","co_await","co_return","co_yield","compl","concept","const_cast|10","consteval","constexpr","constinit","continue","decltype","default","delete","do","dynamic_cast|10","else","enum","explicit","export","extern","false","final","for","friend","goto","if","import","inline","module","mutable","namespace","new","noexcept","not","not_eq","nullptr","operator","or","or_eq","override","private","protected","public","reflexpr","register","reinterpret_cast|10","requires","return","sizeof","static_assert","static_cast|10","struct","switch","synchronized","template","this","thread_local","throw","transaction_safe","transaction_safe_dynamic","true","try","typedef","typeid","typename","union","using","virtual","volatile","while","xor","xor_eq"],E=["bool","char","char16_t","char32_t","char8_t","double","float","int","long","short","void","wchar_t","unsigned","signed","const","static"],N=["any","auto_ptr","barrier","binary_semaphore","bitset","complex","condition_variable","condition_variable_any","counting_semaphore","deque","false_type","flat_map","flat_set","future","imaginary","initializer_list","istringstream","jthread","latch","lock_guard","multimap","multiset","mutex","optional","ostringstream","packaged_task","pair","promise","priority_queue","queue","recursive_mutex","recursive_timed_mutex","scoped_lock","set","shared_future","shared_lock","shared_mutex","shared_timed_mutex","shared_ptr","stack","string_view","stringstream","timed_mutex","thread","true_type","tuple","unique_lock","unique_ptr","unordered_map","unordered_multimap","unordered_multiset","unordered_set","variant","vector","weak_ptr","wstring","wstring_view"],v=["abort","abs","acos","apply","as_const","asin","atan","atan2","calloc","ceil","cerr","cin","clog","cos","cosh","cout","declval","endl","exchange","exit","exp","fabs","floor","fmod","forward","fprintf","fputs","free","frexp","fscanf","future","invoke","isalnum","isalpha","iscntrl","isdigit","isgraph","islower","isprint","ispunct","isspace","isupper","isxdigit","labs","launder","ldexp","log","log10","make_pair","make_shared","make_shared_for_overwrite","make_tuple","make_unique","malloc","memchr","memcmp","memcpy","memset","modf","move","pow","printf","putchar","puts","realloc","scanf","sin","sinh","snprintf","sprintf","sqrt","sscanf","std","stderr","stdin","stdout","strcat","strchr","strcmp","strcpy","strcspn","strlen","strncat","strncmp","strncpy","strpbrk","strrchr","strspn","strstr","swap","tan","tanh","terminate","to_underlying","tolower","toupper","vfprintf","visit","vprintf","vsprintf"],M={type:E,keyword:h,literal:["NULL","false","nullopt","nullptr","true"],built_in:["_Pragma"],_type_hints:N},k={className:"function.dispatch",relevance:0,keywords:{_hint:v},begin:t.concat(/\b/,/(?!decltype)/,/(?!if)/,/(?!for)/,/(?!switch)/,/(?!while)/,e.IDENT_RE,t.lookahead(/(<[^<>]+>|)\s*\(/))},x=[k,b,c,r,e.C_BLOCK_COMMENT_MODE,g,i],S={variants:[{begin:/=/,end:/;/},{begin:/\(/,end:/\)/},{beginKeywords:"new throw return else",end:/;/}],keywords:M,contains:x.concat([{begin:/\(/,end:/\)/,keywords:M,contains:x.concat(["self"]),relevance:0}]),relevance:0},I={className:"function",begin:"("+s+"[\\*&\\s]+)+"+_,returnBegin:!0,end:/[{;=]/,excludeEnd:!0,keywords:M,illegal:/[^\w\s\*&:<>.]/,contains:[{begin:d,keywords:M,relevance:0},{begin:_,returnBegin:!0,contains:[m],relevance:0},{begin:/::/,relevance:0},{begin:/:/,endsWithParent:!0,contains:[i,g]},{relevance:0,match:/,/},{className:"params",begin:/\(/,end:/\)/,keywords:M,relevance:0,contains:[r,e.C_BLOCK_COMMENT_MODE,i,g,c,{begin:/\(/,end:/\)/,keywords:M,relevance:0,contains:["self",r,e.C_BLOCK_COMMENT_MODE,i,g,c]}]},c,r,e.C_BLOCK_COMMENT_MODE,b]};return{name:"C++",aliases:["cc","c++","h++","hpp","hh","hxx","cxx"],keywords:M,illegal:"</",classNameAliases:{"function.dispatch":"built_in"},contains:[].concat(S,I,k,x,[b,{begin:"\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\s*<(?!<)",end:">",keywords:M,contains:["self",c]},{begin:e.IDENT_RE+"::",keywords:M},{match:[/\b(?:enum(?:\s+(?:class|struct))?|class|struct|union)/,/\s+/,/\w+/],className:{1:"keyword",3:"title.class"}}])}}return Le=n,Le}var De,Mn;function Ti(){if(Mn)return De;Mn=1;function n(e){const t=["bool","byte","char","decimal","delegate","double","dynamic","enum","float","int","long","nint","nuint","object","sbyte","short","string","ulong","uint","ushort"],r=["public","private","protected","static","internal","protected","abstract","async","extern","override","unsafe","virtual","new","sealed","partial"],d=["default","false","null","true"],l=["abstract","as","base","break","case","catch","class","const","continue","do","else","event","explicit","extern","finally","fixed","for","foreach","goto","if","implicit","in","interface","internal","is","lock","namespace","new","operator","out","override","params","private","protected","public","readonly","record","ref","return","scoped","sealed","sizeof","stackalloc","static","struct","switch","this","throw","try","typeof","unchecked","unsafe","using","virtual","void","volatile","while"],a=["add","alias","and","ascending","args","async","await","by","descending","dynamic","equals","file","from","get","global","group","init","into","join","let","nameof","not","notnull","on","or","orderby","partial","record","remove","required","scoped","select","set","unmanaged","value|0","var","when","where","with","yield"],s={keyword:l.concat(a),built_in:t,literal:d},c=e.inherit(e.TITLE_MODE,{begin:"[a-zA-Z](\\.?\\w)*"}),o={className:"number",variants:[{begin:"\\b(0b[01']+)"},{begin:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{begin:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],relevance:0},i={className:"string",begin:/"""("*)(?!")(.|\n)*?"""\1/,relevance:1},g={className:"string",begin:'@"',end:'"',contains:[{begin:'""'}]},b=e.inherit(g,{illegal:/\n/}),m={className:"subst",begin:/\{/,end:/\}/,keywords:s},_=e.inherit(m,{illegal:/\n/}),h={className:"string",begin:/\$"/,end:'"',illegal:/\n/,contains:[{begin:/\{\{/},{begin:/\}\}/},e.BACKSLASH_ESCAPE,_]},E={className:"string",begin:/\$@"/,end:'"',contains:[{begin:/\{\{/},{begin:/\}\}/},{begin:'""'},m]},N=e.inherit(E,{illegal:/\n/,contains:[{begin:/\{\{/},{begin:/\}\}/},{begin:'""'},_]});m.contains=[E,h,g,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,o,e.C_BLOCK_COMMENT_MODE],_.contains=[N,h,b,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,o,e.inherit(e.C_BLOCK_COMMENT_MODE,{illegal:/\n/})];const v={variants:[i,E,h,g,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},T={begin:"<",end:">",contains:[{beginKeywords:"in out"},c]},C=e.IDENT_RE+"(<"+e.IDENT_RE+"(\\s*,\\s*"+e.IDENT_RE+")*>)?(\\[\\])?",M={begin:"@"+e.IDENT_RE,relevance:0};return{name:"C#",aliases:["cs","c#"],keywords:s,illegal:/::/,contains:[e.COMMENT("///","$",{returnBegin:!0,contains:[{className:"doctag",variants:[{begin:"///",relevance:0},{begin:"<!--|-->"},{begin:"</?",end:">"}]}]}),e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,{className:"meta",begin:"#",end:"$",keywords:{keyword:"if else elif endif define undef warning error line region endregion pragma checksum"}},v,o,{beginKeywords:"class interface",relevance:0,end:/[{;=]/,illegal:/[^\s:,]/,contains:[{beginKeywords:"where class"},c,T,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{beginKeywords:"namespace",relevance:0,end:/[{;=]/,illegal:/[^\s:]/,contains:[c,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{beginKeywords:"record",relevance:0,end:/[{;=]/,illegal:/[^\s:]/,contains:[c,T,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{className:"meta",begin:"^\\s*\\[(?=[\\w])",excludeBegin:!0,end:"\\]",excludeEnd:!0,contains:[{className:"string",begin:/"/,end:/"/}]},{beginKeywords:"new return throw await else",relevance:0},{className:"function",begin:"("+C+"\\s+)+"+e.IDENT_RE+"\\s*(<[^=]+>\\s*)?\\(",returnBegin:!0,end:/\s*[{;=]/,excludeEnd:!0,keywords:s,contains:[{beginKeywords:r.join(" "),relevance:0},{begin:e.IDENT_RE+"\\s*(<[^=]+>\\s*)?\\(",returnBegin:!0,contains:[e.TITLE_MODE,T],relevance:0},{match:/\(\)/},{className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:s,relevance:0,contains:[v,o,e.C_BLOCK_COMMENT_MODE]},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},M]}}return De=n,De}var Be,kn;function Si(){if(kn)return Be;kn=1;const n=o=>({IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:o.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[o.APOS_STRING_MODE,o.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:o.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}}),e=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video"],t=["defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],r=[...e,...t],d=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),l=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),a=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),s=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse();function c(o){const i=o.regex,g=n(o),b={begin:/-(webkit|moz|ms|o)-(?=[a-z])/},m="and or not only",_=/@-?\w[\w]*(-\w+)*/,h="[a-zA-Z-][a-zA-Z0-9_-]*",E=[o.APOS_STRING_MODE,o.QUOTE_STRING_MODE];return{name:"CSS",case_insensitive:!0,illegal:/[=|'\$]/,keywords:{keyframePosition:"from to"},classNameAliases:{keyframePosition:"selector-tag"},contains:[g.BLOCK_COMMENT,b,g.CSS_NUMBER_MODE,{className:"selector-id",begin:/#[A-Za-z0-9_-]+/,relevance:0},{className:"selector-class",begin:"\\."+h,relevance:0},g.ATTRIBUTE_SELECTOR_MODE,{className:"selector-pseudo",variants:[{begin:":("+l.join("|")+")"},{begin:":(:)?("+a.join("|")+")"}]},g.CSS_VARIABLE,{className:"attribute",begin:"\\b("+s.join("|")+")\\b"},{begin:/:/,end:/[;}{]/,contains:[g.BLOCK_COMMENT,g.HEXCOLOR,g.IMPORTANT,g.CSS_NUMBER_MODE,...E,{begin:/(url|data-uri)\(/,end:/\)/,relevance:0,keywords:{built_in:"url data-uri"},contains:[...E,{className:"string",begin:/[^)]/,endsWithParent:!0,excludeEnd:!0}]},g.FUNCTION_DISPATCH]},{begin:i.lookahead(/@/),end:"[{;]",relevance:0,illegal:/:/,contains:[{className:"keyword",begin:_},{begin:/\s/,endsWithParent:!0,excludeEnd:!0,relevance:0,keywords:{$pattern:/[a-z-]+/,keyword:m,attribute:d.join(" ")},contains:[{begin:/[a-z-]+(?=:)/,className:"attribute"},...E,g.CSS_NUMBER_MODE]}]},{className:"selector-tag",begin:"\\b("+r.join("|")+")\\b"}]}}return Be=c,Be}var Ue,In;function wi(){if(In)return Ue;In=1;function n(e){const t=e.regex,r={begin:/<\/?[A-Za-z_]/,end:">",subLanguage:"xml",relevance:0},d={begin:"^[-\\*]{3,}",end:"$"},l={className:"code",variants:[{begin:"(`{3,})[^`](.|\\n)*?\\1`*[ ]*"},{begin:"(~{3,})[^~](.|\\n)*?\\1~*[ ]*"},{begin:"```",end:"```+[ ]*$"},{begin:"~~~",end:"~~~+[ ]*$"},{begin:"`.+?`"},{begin:"(?=^( {4}|\\t))",contains:[{begin:"^( {4}|\\t)",end:"(\\n)$"}],relevance:0}]},a={className:"bullet",begin:"^[ 	]*([*+-]|(\\d+\\.))(?=\\s+)",end:"\\s+",excludeEnd:!0},s={begin:/^\[[^\n]+\]:/,returnBegin:!0,contains:[{className:"symbol",begin:/\[/,end:/\]/,excludeBegin:!0,excludeEnd:!0},{className:"link",begin:/:\s*/,end:/$/,excludeBegin:!0}]},c=/[A-Za-z][A-Za-z0-9+.-]*/,o={variants:[{begin:/\[.+?\]\[.*?\]/,relevance:0},{begin:/\[.+?\]\(((data|javascript|mailto):|(?:http|ftp)s?:\/\/).*?\)/,relevance:2},{begin:t.concat(/\[.+?\]\(/,c,/:\/\/.*?\)/),relevance:2},{begin:/\[.+?\]\([./?&#].*?\)/,relevance:1},{begin:/\[.*?\]\(.*?\)/,relevance:0}],returnBegin:!0,contains:[{match:/\[(?=\])/},{className:"string",relevance:0,begin:"\\[",end:"\\]",excludeBegin:!0,returnEnd:!0},{className:"link",relevance:0,begin:"\\]\\(",end:"\\)",excludeBegin:!0,excludeEnd:!0},{className:"symbol",relevance:0,begin:"\\]\\[",end:"\\]",excludeBegin:!0,excludeEnd:!0}]},i={className:"strong",contains:[],variants:[{begin:/_{2}(?!\s)/,end:/_{2}/},{begin:/\*{2}(?!\s)/,end:/\*{2}/}]},g={className:"emphasis",contains:[],variants:[{begin:/\*(?![*\s])/,end:/\*/},{begin:/_(?![_\s])/,end:/_/,relevance:0}]},b=e.inherit(i,{contains:[]}),m=e.inherit(g,{contains:[]});i.contains.push(m),g.contains.push(b);let _=[r,o];return[i,g,b,m].forEach(v=>{v.contains=v.contains.concat(_)}),_=_.concat(i,g),{name:"Markdown",aliases:["md","mkdown","mkd"],contains:[{className:"section",variants:[{begin:"^#{1,6}",end:"$",contains:_},{begin:"(?=^.+?\\n[=-]{2,}$)",contains:[{begin:"^[=-]*$"},{begin:"^",end:"\\n",contains:_}]}]},r,a,i,g,{className:"quote",begin:"^>\\s+",contains:_,end:"$"},l,d,o,s,{scope:"literal",match:/&([a-zA-Z0-9]+|#[0-9]{1,7}|#[Xx][0-9a-fA-F]{1,6});/}]}}return Ue=n,Ue}var Pe,xn;function Oi(){if(xn)return Pe;xn=1;function n(e){const t=e.regex;return{name:"Diff",aliases:["patch"],contains:[{className:"meta",relevance:10,match:t.either(/^@@ +-\d+,\d+ +\+\d+,\d+ +@@/,/^\*\*\* +\d+,\d+ +\*\*\*\*$/,/^--- +\d+,\d+ +----$/)},{className:"comment",variants:[{begin:t.either(/Index: /,/^index/,/={3,}/,/^-{3}/,/^\*{3} /,/^\+{3}/,/^diff --git/),end:/$/},{match:/^\*{15}$/}]},{className:"addition",begin:/^\+/,end:/$/},{className:"deletion",begin:/^-/,end:/$/},{className:"addition",begin:/^!/,end:/$/}]}}return Pe=n,Pe}var Fe,Cn;function Ai(){if(Cn)return Fe;Cn=1;function n(e){const t=e.regex,r="([a-zA-Z_]\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\*\\*|[-/+%^&*~`|]|\\[\\]=?)",d=t.either(/\b([A-Z]+[a-z0-9]+)+/,/\b([A-Z]+[a-z0-9]+)+[A-Z]+/),l=t.concat(d,/(::\w+)*/),s={"variable.constant":["__FILE__","__LINE__","__ENCODING__"],"variable.language":["self","super"],keyword:["alias","and","begin","BEGIN","break","case","class","defined","do","else","elsif","end","END","ensure","for","if","in","module","next","not","or","redo","require","rescue","retry","return","then","undef","unless","until","when","while","yield",...["include","extend","prepend","public","private","protected","raise","throw"]],built_in:["proc","lambda","attr_accessor","attr_reader","attr_writer","define_method","private_constant","module_function"],literal:["true","false","nil"]},c={className:"doctag",begin:"@[A-Za-z]+"},o={begin:"#<",end:">"},i=[e.COMMENT("#","$",{contains:[c]}),e.COMMENT("^=begin","^=end",{contains:[c],relevance:10}),e.COMMENT("^__END__",e.MATCH_NOTHING_RE)],g={className:"subst",begin:/#\{/,end:/\}/,keywords:s},b={className:"string",contains:[e.BACKSLASH_ESCAPE,g],variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/`/,end:/`/},{begin:/%[qQwWx]?\(/,end:/\)/},{begin:/%[qQwWx]?\[/,end:/\]/},{begin:/%[qQwWx]?\{/,end:/\}/},{begin:/%[qQwWx]?</,end:/>/},{begin:/%[qQwWx]?\//,end:/\//},{begin:/%[qQwWx]?%/,end:/%/},{begin:/%[qQwWx]?-/,end:/-/},{begin:/%[qQwWx]?\|/,end:/\|/},{begin:/\B\?(\\\d{1,3})/},{begin:/\B\?(\\x[A-Fa-f0-9]{1,2})/},{begin:/\B\?(\\u\{?[A-Fa-f0-9]{1,6}\}?)/},{begin:/\B\?(\\M-\\C-|\\M-\\c|\\c\\M-|\\M-|\\C-\\M-)[\x20-\x7e]/},{begin:/\B\?\\(c|C-)[\x20-\x7e]/},{begin:/\B\?\\?\S/},{begin:t.concat(/<<[-~]?'?/,t.lookahead(/(\w+)(?=\W)[^\n]*\n(?:[^\n]*\n)*?\s*\1\b/)),contains:[e.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,contains:[e.BACKSLASH_ESCAPE,g]})]}]},m="[1-9](_?[0-9])*|0",_="[0-9](_?[0-9])*",h={className:"number",relevance:0,variants:[{begin:`\\b(${m})(\\.(${_}))?([eE][+-]?(${_})|r)?i?\\b`},{begin:"\\b0[dD][0-9](_?[0-9])*r?i?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*r?i?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*r?i?\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\b"},{begin:"\\b0(_?[0-7])+r?i?\\b"}]},E={variants:[{match:/\(\)/},{className:"params",begin:/\(/,end:/(?=\))/,excludeBegin:!0,endsParent:!0,keywords:s}]},x=[b,{variants:[{match:[/class\s+/,l,/\s+<\s+/,l]},{match:[/\b(class|module)\s+/,l]}],scope:{2:"title.class",4:"title.class.inherited"},keywords:s},{match:[/(include|extend)\s+/,l],scope:{2:"title.class"},keywords:s},{relevance:0,match:[l,/\.new[. (]/],scope:{1:"title.class"}},{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},{relevance:0,match:d,scope:"title.class"},{match:[/def/,/\s+/,r],scope:{1:"keyword",3:"title.function"},contains:[E]},{begin:e.IDENT_RE+"::"},{className:"symbol",begin:e.UNDERSCORE_IDENT_RE+"(!|\\?)?:",relevance:0},{className:"symbol",begin:":(?!\\s)",contains:[b,{begin:r}],relevance:0},h,{className:"variable",begin:"(\\$\\W)|((\\$|@@?)(\\w+))(?=[^@$?])(?![A-Za-z])(?![@$?'])"},{className:"params",begin:/\|(?!=)/,end:/\|/,excludeBegin:!0,excludeEnd:!0,relevance:0,keywords:s},{begin:"("+e.RE_STARTERS_RE+"|unless)\\s*",keywords:"unless",contains:[{className:"regexp",contains:[e.BACKSLASH_ESCAPE,g],illegal:/\n/,variants:[{begin:"/",end:"/[a-z]*"},{begin:/%r\{/,end:/\}[a-z]*/},{begin:"%r\\(",end:"\\)[a-z]*"},{begin:"%r!",end:"![a-z]*"},{begin:"%r\\[",end:"\\][a-z]*"}]}].concat(o,i),relevance:0}].concat(o,i);g.contains=x,E.contains=x;const S="[>?]>",I="[\\w#]+\\(\\w+\\):\\d+:\\d+[>*]",U="(\\w+-)?\\d+\\.\\d+\\.\\d+(p\\d+)?[^\\d][^>]+>",F=[{begin:/^\s*=>/,starts:{end:"$",contains:x}},{className:"meta.prompt",begin:"^("+S+"|"+I+"|"+U+")(?=[ ])",starts:{end:"$",keywords:s,contains:x}}];return i.unshift(o),{name:"Ruby",aliases:["rb","gemspec","podspec","thor","irb"],keywords:s,illegal:/\/\*/,contains:[e.SHEBANG({binary:"ruby"})].concat(F).concat(i).concat(x)}}return Fe=n,Fe}var ze,Ln;function Ri(){if(Ln)return ze;Ln=1;function n(e){const a={keyword:["break","case","chan","const","continue","default","defer","else","fallthrough","for","func","go","goto","if","import","interface","map","package","range","return","select","struct","switch","type","var"],type:["bool","byte","complex64","complex128","error","float32","float64","int8","int16","int32","int64","string","uint8","uint16","uint32","uint64","int","uint","uintptr","rune"],literal:["true","false","iota","nil"],built_in:["append","cap","close","complex","copy","imag","len","make","new","panic","print","println","real","recover","delete"]};return{name:"Go",aliases:["golang"],keywords:a,illegal:"</",contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,{className:"string",variants:[e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,{begin:"`",end:"`"}]},{className:"number",variants:[{match:/-?\b0[xX]\.[a-fA-F0-9](_?[a-fA-F0-9])*[pP][+-]?\d(_?\d)*i?/,relevance:0},{match:/-?\b0[xX](_?[a-fA-F0-9])+((\.([a-fA-F0-9](_?[a-fA-F0-9])*)?)?[pP][+-]?\d(_?\d)*)?i?/,relevance:0},{match:/-?\b0[oO](_?[0-7])*i?/,relevance:0},{match:/-?\.\d(_?\d)*([eE][+-]?\d(_?\d)*)?i?/,relevance:0},{match:/-?\b\d(_?\d)*(\.(\d(_?\d)*)?)?([eE][+-]?\d(_?\d)*)?i?/,relevance:0}]},{begin:/:=/},{className:"function",beginKeywords:"func",end:"\\s*(\\{|$)",excludeEnd:!0,contains:[e.TITLE_MODE,{className:"params",begin:/\(/,end:/\)/,endsParent:!0,keywords:a,illegal:/["']/}]}]}}return ze=n,ze}var $e,Dn;function Mi(){if(Dn)return $e;Dn=1;function n(e){const t=e.regex,r=/[_A-Za-z][_0-9A-Za-z]*/;return{name:"GraphQL",aliases:["gql"],case_insensitive:!0,disableAutodetect:!1,keywords:{keyword:["query","mutation","subscription","type","input","schema","directive","interface","union","scalar","fragment","enum","on"],literal:["true","false","null"]},contains:[e.HASH_COMMENT_MODE,e.QUOTE_STRING_MODE,e.NUMBER_MODE,{scope:"punctuation",match:/[.]{3}/,relevance:0},{scope:"punctuation",begin:/[\!\(\)\:\=\[\]\{\|\}]{1}/,relevance:0},{scope:"variable",begin:/\$/,end:/\W/,excludeEnd:!0,relevance:0},{scope:"meta",match:/@\w+/,excludeEnd:!0},{scope:"symbol",begin:t.concat(r,t.lookahead(/\s*:/)),relevance:0}],illegal:[/[;<']/,/BEGIN/]}}return $e=n,$e}var Ke,Bn;function ki(){if(Bn)return Ke;Bn=1;function n(e){const t=e.regex,r={className:"number",relevance:0,variants:[{begin:/([+-]+)?[\d]+_[\d_]+/},{begin:e.NUMBER_RE}]},d=e.COMMENT();d.variants=[{begin:/;/,end:/$/},{begin:/#/,end:/$/}];const l={className:"variable",variants:[{begin:/\$[\w\d"][\w\d_]*/},{begin:/\$\{(.*?)\}/}]},a={className:"literal",begin:/\bon|off|true|false|yes|no\b/},s={className:"string",contains:[e.BACKSLASH_ESCAPE],variants:[{begin:"'''",end:"'''",relevance:10},{begin:'"""',end:'"""',relevance:10},{begin:'"',end:'"'},{begin:"'",end:"'"}]},c={begin:/\[/,end:/\]/,contains:[d,a,l,s,r,"self"],relevance:0},o=/[A-Za-z0-9_-]+/,i=/"(\\"|[^"])*"/,g=/'[^']*'/,b=t.either(o,i,g),m=t.concat(b,"(\\s*\\.\\s*",b,")*",t.lookahead(/\s*=\s*[^#\s]/));return{name:"TOML, also INI",aliases:["toml"],case_insensitive:!0,illegal:/\S/,contains:[d,{className:"section",begin:/\[+/,end:/\]+/},{begin:m,className:"attr",starts:{end:/$/,contains:[d,c,a,l,s,r]}}]}}return Ke=n,Ke}var Ge,Un;function Ii(){if(Un)return Ge;Un=1;var n="[0-9](_*[0-9])*",e=`\\.(${n})`,t="[0-9a-fA-F](_*[0-9a-fA-F])*",r={className:"number",variants:[{begin:`(\\b(${n})((${e})|\\.)?|(${e}))[eE][+-]?(${n})[fFdD]?\\b`},{begin:`\\b(${n})((${e})[fFdD]?\\b|\\.([fFdD]\\b)?)`},{begin:`(${e})[fFdD]?\\b`},{begin:`\\b(${n})[fFdD]\\b`},{begin:`\\b0[xX]((${t})\\.?|(${t})?\\.(${t}))[pP][+-]?(${n})[fFdD]?\\b`},{begin:"\\b(0|[1-9](_*[0-9])*)[lL]?\\b"},{begin:`\\b0[xX](${t})[lL]?\\b`},{begin:"\\b0(_*[0-7])*[lL]?\\b"},{begin:"\\b0[bB][01](_*[01])*[lL]?\\b"}],relevance:0};function d(a,s,c){return c===-1?"":a.replace(s,o=>d(a,s,c-1))}function l(a){const s=a.regex,c="[\xC0-\u02B8a-zA-Z_$][\xC0-\u02B8a-zA-Z_$0-9]*",o=c+d("(?:<"+c+"~~~(?:\\s*,\\s*"+c+"~~~)*>)?",/~~~/g,2),_={keyword:["synchronized","abstract","private","var","static","if","const ","for","while","strictfp","finally","protected","import","native","final","void","enum","else","break","transient","catch","instanceof","volatile","case","assert","package","default","public","try","switch","continue","throws","protected","public","private","module","requires","exports","do","sealed","yield","permits","goto","when"],literal:["false","true","null"],type:["char","boolean","long","float","int","byte","short","double"],built_in:["super","this"]},h={className:"meta",begin:"@"+c,contains:[{begin:/\(/,end:/\)/,contains:["self"]}]},E={className:"params",begin:/\(/,end:/\)/,keywords:_,relevance:0,contains:[a.C_BLOCK_COMMENT_MODE],endsParent:!0};return{name:"Java",aliases:["jsp"],keywords:_,illegal:/<\/|#/,contains:[a.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{begin:/\w+@/,relevance:0},{className:"doctag",begin:"@[A-Za-z]+"}]}),{begin:/import java\.[a-z]+\./,keywords:"import",relevance:2},a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,{begin:/"""/,end:/"""/,className:"string",contains:[a.BACKSLASH_ESCAPE]},a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,{match:[/\b(?:class|interface|enum|extends|implements|new)/,/\s+/,c],className:{1:"keyword",3:"title.class"}},{match:/non-sealed/,scope:"keyword"},{begin:[s.concat(/(?!else)/,c),/\s+/,c,/\s+/,/=(?!=)/],className:{1:"type",3:"variable",5:"operator"}},{begin:[/record/,/\s+/,c],className:{1:"keyword",3:"title.class"},contains:[E,a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},{beginKeywords:"new throw return else",relevance:0},{begin:["(?:"+o+"\\s+)",a.UNDERSCORE_IDENT_RE,/\s*(?=\()/],className:{2:"title.function"},keywords:_,contains:[{className:"params",begin:/\(/,end:/\)/,keywords:_,relevance:0,contains:[h,a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,r,a.C_BLOCK_COMMENT_MODE]},a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},r,h]}}return Ge=l,Ge}var qe,Pn;function xi(){if(Pn)return qe;Pn=1;const n="[A-Za-z$_][0-9A-Za-z$_]*",e=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],t=["true","false","null","undefined","NaN","Infinity"],r=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],d=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],l=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],a=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],s=[].concat(l,r,d);function c(o){const i=o.regex,g=(q,{after:$})=>{const J="</"+q[0].slice(1);return q.input.indexOf(J,$)!==-1},b=n,m={begin:"<>",end:"</>"},_=/<[A-Za-z0-9\\._:-]+\s*\/>/,h={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(q,$)=>{const J=q[0].length+q.index,ee=q.input[J];if(ee==="<"||ee===","){$.ignoreMatch();return}ee===">"&&(g(q,{after:J})||$.ignoreMatch());let ne;const ie=q.input.substring(J);if(ne=ie.match(/^\s*=/)){$.ignoreMatch();return}if((ne=ie.match(/^\s+extends\s+/))&&ne.index===0){$.ignoreMatch();return}}},E={$pattern:n,keyword:e,literal:t,built_in:s,"variable.language":a},N="[0-9](_?[0-9])*",v=`\\.(${N})`,T="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",C={className:"number",variants:[{begin:`(\\b(${T})((${v})|\\.)?|(${v}))[eE][+-]?(${N})\\b`},{begin:`\\b(${T})\\b((${v})\\b|\\.)?|(${v})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},M={className:"subst",begin:"\\$\\{",end:"\\}",keywords:E,contains:[]},k={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[o.BACKSLASH_ESCAPE,M],subLanguage:"xml"}},x={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[o.BACKSLASH_ESCAPE,M],subLanguage:"css"}},S={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[o.BACKSLASH_ESCAPE,M],subLanguage:"graphql"}},I={className:"string",begin:"`",end:"`",contains:[o.BACKSLASH_ESCAPE,M]},F={className:"comment",variants:[o.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:b+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),o.C_BLOCK_COMMENT_MODE,o.C_LINE_COMMENT_MODE]},V=[o.APOS_STRING_MODE,o.QUOTE_STRING_MODE,k,x,S,I,{match:/\$\d+/},C];M.contains=V.concat({begin:/\{/,end:/\}/,keywords:E,contains:["self"].concat(V)});const Y=[].concat(F,M.contains),Q=Y.concat([{begin:/(\s*)\(/,end:/\)/,keywords:E,contains:["self"].concat(Y)}]),K={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:E,contains:Q},u={variants:[{match:[/class/,/\s+/,b,/\s+/,/extends/,/\s+/,i.concat(b,"(",i.concat(/\./,b),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,b],scope:{1:"keyword",3:"title.class"}}]},f={relevance:0,match:i.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...r,...d]}},w={label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},L={variants:[{match:[/function/,/\s+/,b,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[K],illegal:/%/},z={relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"};function Z(q){return i.concat("(?!",q.join("|"),")")}const j={match:i.concat(/\b/,Z([...l,"super","import"].map(q=>`${q}\\s*\\(`)),b,i.lookahead(/\s*\(/)),className:"title.function",relevance:0},se={begin:i.concat(/\./,i.lookahead(i.concat(b,/(?![0-9A-Za-z$_(])/))),end:b,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},G={match:[/get|set/,/\s+/,b,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},K]},H="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+o.UNDERSCORE_IDENT_RE+")\\s*=>",oe={match:[/const|var|let/,/\s+/,b,/\s*/,/=\s*/,/(async\s*)?/,i.lookahead(H)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[K]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:E,exports:{PARAMS_CONTAINS:Q,CLASS_REFERENCE:f},illegal:/#(?![$_A-z])/,contains:[o.SHEBANG({label:"shebang",binary:"node",relevance:5}),w,o.APOS_STRING_MODE,o.QUOTE_STRING_MODE,k,x,S,I,F,{match:/\$\d+/},C,f,{scope:"attr",match:b+i.lookahead(":"),relevance:0},oe,{begin:"("+o.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[F,o.REGEXP_MODE,{className:"function",begin:H,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:o.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:E,contains:Q}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:m.begin,end:m.end},{match:_},{begin:h.begin,"on:begin":h.isTrulyOpeningTag,end:h.end}],subLanguage:"xml",contains:[{begin:h.begin,end:h.end,skip:!0,contains:["self"]}]}]},L,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+o.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[K,o.inherit(o.TITLE_MODE,{begin:b,className:"title.function"})]},{match:/\.\.\./,relevance:0},se,{match:"\\$"+b,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[K]},j,z,u,G,{match:/\$[(.]/}]}}return qe=c,qe}var He,Fn;function Ci(){if(Fn)return He;Fn=1;function n(e){const t={className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},r={match:/[{}[\],:]/,className:"punctuation",relevance:0},d=["true","false","null"],l={scope:"literal",beginKeywords:d.join(" ")};return{name:"JSON",aliases:["jsonc"],keywords:{literal:d},contains:[t,r,e.QUOTE_STRING_MODE,l,e.C_NUMBER_MODE,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}return He=n,He}var We,zn;function Li(){if(zn)return We;zn=1;var n="[0-9](_*[0-9])*",e=`\\.(${n})`,t="[0-9a-fA-F](_*[0-9a-fA-F])*",r={className:"number",variants:[{begin:`(\\b(${n})((${e})|\\.)?|(${e}))[eE][+-]?(${n})[fFdD]?\\b`},{begin:`\\b(${n})((${e})[fFdD]?\\b|\\.([fFdD]\\b)?)`},{begin:`(${e})[fFdD]?\\b`},{begin:`\\b(${n})[fFdD]\\b`},{begin:`\\b0[xX]((${t})\\.?|(${t})?\\.(${t}))[pP][+-]?(${n})[fFdD]?\\b`},{begin:"\\b(0|[1-9](_*[0-9])*)[lL]?\\b"},{begin:`\\b0[xX](${t})[lL]?\\b`},{begin:"\\b0(_*[0-7])*[lL]?\\b"},{begin:"\\b0[bB][01](_*[01])*[lL]?\\b"}],relevance:0};function d(l){const a={keyword:"abstract as val var vararg get set class object open private protected public noinline crossinline dynamic final enum if else do while for when throw try catch finally import package is in fun override companion reified inline lateinit init interface annotation data sealed internal infix operator out by constructor super tailrec where const inner suspend typealias external expect actual",built_in:"Byte Short Char Int Long Boolean Float Double Void Unit Nothing",literal:"true false null"},s={className:"keyword",begin:/\b(break|continue|return|this)\b/,starts:{contains:[{className:"symbol",begin:/@\w+/}]}},c={className:"symbol",begin:l.UNDERSCORE_IDENT_RE+"@"},o={className:"subst",begin:/\$\{/,end:/\}/,contains:[l.C_NUMBER_MODE]},i={className:"variable",begin:"\\$"+l.UNDERSCORE_IDENT_RE},g={className:"string",variants:[{begin:'"""',end:'"""(?=[^"])',contains:[i,o]},{begin:"'",end:"'",illegal:/\n/,contains:[l.BACKSLASH_ESCAPE]},{begin:'"',end:'"',illegal:/\n/,contains:[l.BACKSLASH_ESCAPE,i,o]}]};o.contains.push(g);const b={className:"meta",begin:"@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\s*:(?:\\s*"+l.UNDERSCORE_IDENT_RE+")?"},m={className:"meta",begin:"@"+l.UNDERSCORE_IDENT_RE,contains:[{begin:/\(/,end:/\)/,contains:[l.inherit(g,{className:"string"}),"self"]}]},_=r,h=l.COMMENT("/\\*","\\*/",{contains:[l.C_BLOCK_COMMENT_MODE]}),E={variants:[{className:"type",begin:l.UNDERSCORE_IDENT_RE},{begin:/\(/,end:/\)/,contains:[]}]},N=E;return N.variants[1].contains=[E],E.variants[1].contains=[N],{name:"Kotlin",aliases:["kt","kts"],keywords:a,contains:[l.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"}]}),l.C_LINE_COMMENT_MODE,h,s,c,b,m,{className:"function",beginKeywords:"fun",end:"[(]|$",returnBegin:!0,excludeEnd:!0,keywords:a,relevance:5,contains:[{begin:l.UNDERSCORE_IDENT_RE+"\\s*\\(",returnBegin:!0,relevance:0,contains:[l.UNDERSCORE_TITLE_MODE]},{className:"type",begin:/</,end:/>/,keywords:"reified",relevance:0},{className:"params",begin:/\(/,end:/\)/,endsParent:!0,keywords:a,relevance:0,contains:[{begin:/:/,end:/[=,\/]/,endsWithParent:!0,contains:[E,l.C_LINE_COMMENT_MODE,h],relevance:0},l.C_LINE_COMMENT_MODE,h,b,m,g,l.C_NUMBER_MODE]},h]},{begin:[/class|interface|trait/,/\s+/,l.UNDERSCORE_IDENT_RE],beginScope:{3:"title.class"},keywords:"class interface trait",end:/[:\{(]|$/,excludeEnd:!0,illegal:"extends implements",contains:[{beginKeywords:"public protected internal private constructor"},l.UNDERSCORE_TITLE_MODE,{className:"type",begin:/</,end:/>/,excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:/[,:]\s*/,end:/[<\(,){\s]|$/,excludeBegin:!0,returnEnd:!0},b,m]},g,{className:"meta",begin:"^#!/usr/bin/env",end:"$",illegal:`
`},_]}}return We=d,We}var Ye,$n;function Di(){if($n)return Ye;$n=1;const n=i=>({IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:i.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[i.APOS_STRING_MODE,i.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:i.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}}),e=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video"],t=["defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],r=[...e,...t],d=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),l=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),a=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),s=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse(),c=l.concat(a).sort().reverse();function o(i){const g=n(i),b=c,m="and or not only",_="[\\w-]+",h="("+_+"|@\\{"+_+"\\})",E=[],N=[],v=function(Y){return{className:"string",begin:"~?"+Y+".*?"+Y}},T=function(Y,Q,K){return{className:Y,begin:Q,relevance:K}},C={$pattern:/[a-z-]+/,keyword:m,attribute:d.join(" ")},M={begin:"\\(",end:"\\)",contains:N,keywords:C,relevance:0};N.push(i.C_LINE_COMMENT_MODE,i.C_BLOCK_COMMENT_MODE,v("'"),v('"'),g.CSS_NUMBER_MODE,{begin:"(url|data-uri)\\(",starts:{className:"string",end:"[\\)\\n]",excludeEnd:!0}},g.HEXCOLOR,M,T("variable","@@?"+_,10),T("variable","@\\{"+_+"\\}"),T("built_in","~?`[^`]*?`"),{className:"attribute",begin:_+"\\s*:",end:":",returnBegin:!0,excludeEnd:!0},g.IMPORTANT,{beginKeywords:"and not"},g.FUNCTION_DISPATCH);const k=N.concat({begin:/\{/,end:/\}/,contains:E}),x={beginKeywords:"when",endsWithParent:!0,contains:[{beginKeywords:"and not"}].concat(N)},S={begin:h+"\\s*:",returnBegin:!0,end:/[;}]/,relevance:0,contains:[{begin:/-(webkit|moz|ms|o)-/},g.CSS_VARIABLE,{className:"attribute",begin:"\\b("+s.join("|")+")\\b",end:/(?=:)/,starts:{endsWithParent:!0,illegal:"[<=$]",relevance:0,contains:N}}]},I={className:"keyword",begin:"@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\b",starts:{end:"[;{}]",keywords:C,returnEnd:!0,contains:N,relevance:0}},U={className:"variable",variants:[{begin:"@"+_+"\\s*:",relevance:15},{begin:"@"+_}],starts:{end:"[;}]",returnEnd:!0,contains:k}},F={variants:[{begin:"[\\.#:&\\[>]",end:"[;{}]"},{begin:h,end:/\{/}],returnBegin:!0,returnEnd:!0,illegal:`[<='$"]`,relevance:0,contains:[i.C_LINE_COMMENT_MODE,i.C_BLOCK_COMMENT_MODE,x,T("keyword","all\\b"),T("variable","@\\{"+_+"\\}"),{begin:"\\b("+r.join("|")+")\\b",className:"selector-tag"},g.CSS_NUMBER_MODE,T("selector-tag",h,0),T("selector-id","#"+h),T("selector-class","\\."+h,0),T("selector-tag","&",0),g.ATTRIBUTE_SELECTOR_MODE,{className:"selector-pseudo",begin:":("+l.join("|")+")"},{className:"selector-pseudo",begin:":(:)?("+a.join("|")+")"},{begin:/\(/,end:/\)/,relevance:0,contains:k},{begin:"!important"},g.FUNCTION_DISPATCH]},V={begin:_+`:(:)?(${b.join("|")})`,returnBegin:!0,contains:[F]};return E.push(i.C_LINE_COMMENT_MODE,i.C_BLOCK_COMMENT_MODE,I,U,V,S,F,x,g.FUNCTION_DISPATCH),{name:"Less",case_insensitive:!0,illegal:`[=>'/<($"]`,contains:E}}return Ye=o,Ye}var Ze,Kn;function Bi(){if(Kn)return Ze;Kn=1;function n(e){const t="\\[=*\\[",r="\\]=*\\]",d={begin:t,end:r,contains:["self"]},l=[e.COMMENT("--(?!"+t+")","$"),e.COMMENT("--"+t,r,{contains:[d],relevance:10})];return{name:"Lua",aliases:["pluto"],keywords:{$pattern:e.UNDERSCORE_IDENT_RE,literal:"true false nil",keyword:"and break do else elseif end for goto if in local not or repeat return then until while",built_in:"_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len __gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring module next pairs pcall print rawequal rawget rawset require select setfenv setmetatable tonumber tostring type unpack xpcall arg self coroutine resume yield status wrap create running debug getupvalue debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv io lines write close flush open output type read stderr stdin input stdout popen tmpfile math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower table setn insert getn foreachi maxn foreach concat sort remove"},contains:l.concat([{className:"function",beginKeywords:"function",end:"\\)",contains:[e.inherit(e.TITLE_MODE,{begin:"([_a-zA-Z]\\w*\\.)*([_a-zA-Z]\\w*:)?[_a-zA-Z]\\w*"}),{className:"params",begin:"\\(",endsWithParent:!0,contains:l}].concat(l)},e.C_NUMBER_MODE,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,{className:"string",begin:t,end:r,contains:[d],relevance:5}])}}return Ze=n,Ze}var Xe,Gn;function Ui(){if(Gn)return Xe;Gn=1;function n(e){const t={className:"variable",variants:[{begin:"\\$\\("+e.UNDERSCORE_IDENT_RE+"\\)",contains:[e.BACKSLASH_ESCAPE]},{begin:/\$[@%<?\^\+\*]/}]},r={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,t]},d={className:"variable",begin:/\$\([\w-]+\s/,end:/\)/,keywords:{built_in:"subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value"},contains:[t,r]},l={begin:"^"+e.UNDERSCORE_IDENT_RE+"\\s*(?=[:+?]?=)"},a={className:"meta",begin:/^\.PHONY:/,end:/$/,keywords:{$pattern:/[\.\w]+/,keyword:".PHONY"}},s={className:"section",begin:/^[^\s]+:/,end:/$/,contains:[t]};return{name:"Makefile",aliases:["mk","mak","make"],keywords:{$pattern:/[\w-]+/,keyword:"define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath"},contains:[e.HASH_COMMENT_MODE,t,r,d,l,a,s]}}return Xe=n,Xe}var Ve,qn;function Pi(){if(qn)return Ve;qn=1;function n(e){const t=e.regex,r=["abs","accept","alarm","and","atan2","bind","binmode","bless","break","caller","chdir","chmod","chomp","chop","chown","chr","chroot","class","close","closedir","connect","continue","cos","crypt","dbmclose","dbmopen","defined","delete","die","do","dump","each","else","elsif","endgrent","endhostent","endnetent","endprotoent","endpwent","endservent","eof","eval","exec","exists","exit","exp","fcntl","field","fileno","flock","for","foreach","fork","format","formline","getc","getgrent","getgrgid","getgrnam","gethostbyaddr","gethostbyname","gethostent","getlogin","getnetbyaddr","getnetbyname","getnetent","getpeername","getpgrp","getpriority","getprotobyname","getprotobynumber","getprotoent","getpwent","getpwnam","getpwuid","getservbyname","getservbyport","getservent","getsockname","getsockopt","given","glob","gmtime","goto","grep","gt","hex","if","index","int","ioctl","join","keys","kill","last","lc","lcfirst","length","link","listen","local","localtime","log","lstat","lt","ma","map","method","mkdir","msgctl","msgget","msgrcv","msgsnd","my","ne","next","no","not","oct","open","opendir","or","ord","our","pack","package","pipe","pop","pos","print","printf","prototype","push","q|0","qq","quotemeta","qw","qx","rand","read","readdir","readline","readlink","readpipe","recv","redo","ref","rename","require","reset","return","reverse","rewinddir","rindex","rmdir","say","scalar","seek","seekdir","select","semctl","semget","semop","send","setgrent","sethostent","setnetent","setpgrp","setpriority","setprotoent","setpwent","setservent","setsockopt","shift","shmctl","shmget","shmread","shmwrite","shutdown","sin","sleep","socket","socketpair","sort","splice","split","sprintf","sqrt","srand","stat","state","study","sub","substr","symlink","syscall","sysopen","sysread","sysseek","system","syswrite","tell","telldir","tie","tied","time","times","tr","truncate","uc","ucfirst","umask","undef","unless","unlink","unpack","unshift","untie","until","use","utime","values","vec","wait","waitpid","wantarray","warn","when","while","write","x|0","xor","y|0"],d=/[dualxmsipngr]{0,12}/,l={$pattern:/[\w.]+/,keyword:r.join(" ")},a={className:"subst",begin:"[$@]\\{",end:"\\}",keywords:l},s={begin:/->\{/,end:/\}/},c={scope:"attr",match:/\s+:\s*\w+(\s*\(.*?\))?/},o={scope:"variable",variants:[{begin:/\$\d/},{begin:t.concat(/[$%@](?!")(\^\w\b|#\w+(::\w+)*|\{\w+\}|\w+(::\w*)*)/,"(?![A-Za-z])(?![@$%])")},{begin:/[$%@](?!")[^\s\w{=]|\$=/,relevance:0}],contains:[c]},i={className:"number",variants:[{match:/0?\.[0-9][0-9_]+\b/},{match:/\bv?(0|[1-9][0-9_]*(\.[0-9_]+)?|[1-9][0-9_]*)\b/},{match:/\b0[0-7][0-7_]*\b/},{match:/\b0x[0-9a-fA-F][0-9a-fA-F_]*\b/},{match:/\b0b[0-1][0-1_]*\b/}],relevance:0},g=[e.BACKSLASH_ESCAPE,a,o],b=[/!/,/\//,/\|/,/\?/,/'/,/"/,/#/],m=(E,N,v="\\1")=>{const T=v==="\\1"?v:t.concat(v,N);return t.concat(t.concat("(?:",E,")"),N,/(?:\\.|[^\\\/])*?/,T,/(?:\\.|[^\\\/])*?/,v,d)},_=(E,N,v)=>t.concat(t.concat("(?:",E,")"),N,/(?:\\.|[^\\\/])*?/,v,d),h=[o,e.HASH_COMMENT_MODE,e.COMMENT(/^=\w/,/=cut/,{endsWithParent:!0}),s,{className:"string",contains:g,variants:[{begin:"q[qwxr]?\\s*\\(",end:"\\)",relevance:5},{begin:"q[qwxr]?\\s*\\[",end:"\\]",relevance:5},{begin:"q[qwxr]?\\s*\\{",end:"\\}",relevance:5},{begin:"q[qwxr]?\\s*\\|",end:"\\|",relevance:5},{begin:"q[qwxr]?\\s*<",end:">",relevance:5},{begin:"qw\\s+q",end:"q",relevance:5},{begin:"'",end:"'",contains:[e.BACKSLASH_ESCAPE]},{begin:'"',end:'"'},{begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE]},{begin:/\{\w+\}/,relevance:0},{begin:"-?\\w+\\s*=>",relevance:0}]},i,{begin:"(\\/\\/|"+e.RE_STARTERS_RE+"|\\b(split|return|print|reverse|grep)\\b)\\s*",keywords:"split return print reverse grep",relevance:0,contains:[e.HASH_COMMENT_MODE,{className:"regexp",variants:[{begin:m("s|tr|y",t.either(...b,{capture:!0}))},{begin:m("s|tr|y","\\(","\\)")},{begin:m("s|tr|y","\\[","\\]")},{begin:m("s|tr|y","\\{","\\}")}],relevance:2},{className:"regexp",variants:[{begin:/(m|qr)\/\//,relevance:0},{begin:_("(?:m|qr)?",/\//,/\//)},{begin:_("m|qr",t.either(...b,{capture:!0}),/\1/)},{begin:_("m|qr",/\(/,/\)/)},{begin:_("m|qr",/\[/,/\]/)},{begin:_("m|qr",/\{/,/\}/)}]}]},{className:"function",beginKeywords:"sub method",end:"(\\s*\\(.*?\\))?[;{]",excludeEnd:!0,relevance:5,contains:[e.TITLE_MODE,c]},{className:"class",beginKeywords:"class",end:"[;{]",excludeEnd:!0,relevance:5,contains:[e.TITLE_MODE,c,i]},{begin:"-\\w\\b",relevance:0},{begin:"^__DATA__$",end:"^__END__$",subLanguage:"mojolicious",contains:[{begin:"^@@.*",end:"$",className:"comment"}]}];return a.contains=h,s.contains=h,{name:"Perl",aliases:["pl","pm"],keywords:l,contains:h}}return Ve=n,Ve}var Qe,Hn;function Fi(){if(Hn)return Qe;Hn=1;function n(e){const t={className:"built_in",begin:"\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\w+"},r=/[a-zA-Z@][a-zA-Z0-9_]*/,c={"variable.language":["this","super"],$pattern:r,keyword:["while","export","sizeof","typedef","const","struct","for","union","volatile","static","mutable","if","do","return","goto","enum","else","break","extern","asm","case","default","register","explicit","typename","switch","continue","inline","readonly","assign","readwrite","self","@synchronized","id","typeof","nonatomic","IBOutlet","IBAction","strong","weak","copy","in","out","inout","bycopy","byref","oneway","__strong","__weak","__block","__autoreleasing","@private","@protected","@public","@try","@property","@end","@throw","@catch","@finally","@autoreleasepool","@synthesize","@dynamic","@selector","@optional","@required","@encode","@package","@import","@defs","@compatibility_alias","__bridge","__bridge_transfer","__bridge_retained","__bridge_retain","__covariant","__contravariant","__kindof","_Nonnull","_Nullable","_Null_unspecified","__FUNCTION__","__PRETTY_FUNCTION__","__attribute__","getter","setter","retain","unsafe_unretained","nonnull","nullable","null_unspecified","null_resettable","class","instancetype","NS_DESIGNATED_INITIALIZER","NS_UNAVAILABLE","NS_REQUIRES_SUPER","NS_RETURNS_INNER_POINTER","NS_INLINE","NS_AVAILABLE","NS_DEPRECATED","NS_ENUM","NS_OPTIONS","NS_SWIFT_UNAVAILABLE","NS_ASSUME_NONNULL_BEGIN","NS_ASSUME_NONNULL_END","NS_REFINED_FOR_SWIFT","NS_SWIFT_NAME","NS_SWIFT_NOTHROW","NS_DURING","NS_HANDLER","NS_ENDHANDLER","NS_VALUERETURN","NS_VOIDRETURN"],literal:["false","true","FALSE","TRUE","nil","YES","NO","NULL"],built_in:["dispatch_once_t","dispatch_queue_t","dispatch_sync","dispatch_async","dispatch_once"],type:["int","float","char","unsigned","signed","short","long","double","wchar_t","unichar","void","bool","BOOL","id|0","_Bool"]},o={$pattern:r,keyword:["@interface","@class","@protocol","@implementation"]};return{name:"Objective-C",aliases:["mm","objc","obj-c","obj-c++","objective-c++"],keywords:c,illegal:"</",contains:[t,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,e.C_NUMBER_MODE,e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,{className:"string",variants:[{begin:'@"',end:'"',illegal:"\\n",contains:[e.BACKSLASH_ESCAPE]}]},{className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{keyword:"if else elif endif define undef warning error line pragma ifdef ifndef include"},contains:[{begin:/\\\n/,relevance:0},e.inherit(e.QUOTE_STRING_MODE,{className:"string"}),{className:"string",begin:/<.*?>/,end:/$/,illegal:"\\n"},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{className:"class",begin:"("+o.keyword.join("|")+")\\b",end:/(\{|$)/,excludeEnd:!0,keywords:o,contains:[e.UNDERSCORE_TITLE_MODE]},{begin:"\\."+e.UNDERSCORE_IDENT_RE,relevance:0}]}}return Qe=n,Qe}var Je,Wn;function zi(){if(Wn)return Je;Wn=1;function n(e){const t=e.regex,r=/(?![A-Za-z0-9])(?![$])/,d=t.concat(/[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/,r),l=t.concat(/(\\?[A-Z][a-z0-9_\x7f-\xff]+|\\?[A-Z]+(?=[A-Z][a-z0-9_\x7f-\xff])){1,}/,r),a=t.concat(/[A-Z]+/,r),s={scope:"variable",match:"\\$+"+d},c={scope:"meta",variants:[{begin:/<\?php/,relevance:10},{begin:/<\?=/},{begin:/<\?/,relevance:.1},{begin:/\?>/}]},o={scope:"subst",variants:[{begin:/\$\w+/},{begin:/\{\$/,end:/\}/}]},i=e.inherit(e.APOS_STRING_MODE,{illegal:null}),g=e.inherit(e.QUOTE_STRING_MODE,{illegal:null,contains:e.QUOTE_STRING_MODE.contains.concat(o)}),b={begin:/<<<[ \t]*(?:(\w+)|"(\w+)")\n/,end:/[ \t]*(\w+)\b/,contains:e.QUOTE_STRING_MODE.contains.concat(o),"on:begin":(K,u)=>{u.data._beginMatch=K[1]||K[2]},"on:end":(K,u)=>{u.data._beginMatch!==K[1]&&u.ignoreMatch()}},m=e.END_SAME_AS_BEGIN({begin:/<<<[ \t]*'(\w+)'\n/,end:/[ \t]*(\w+)\b/}),_=`[ 	
]`,h={scope:"string",variants:[g,i,b,m]},E={scope:"number",variants:[{begin:"\\b0[bB][01]+(?:_[01]+)*\\b"},{begin:"\\b0[oO][0-7]+(?:_[0-7]+)*\\b"},{begin:"\\b0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*\\b"},{begin:"(?:\\b\\d+(?:_\\d+)*(\\.(?:\\d+(?:_\\d+)*))?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?"}],relevance:0},N=["false","null","true"],v=["__CLASS__","__DIR__","__FILE__","__FUNCTION__","__COMPILER_HALT_OFFSET__","__LINE__","__METHOD__","__NAMESPACE__","__TRAIT__","die","echo","exit","include","include_once","print","require","require_once","array","abstract","and","as","binary","bool","boolean","break","callable","case","catch","class","clone","const","continue","declare","default","do","double","else","elseif","empty","enddeclare","endfor","endforeach","endif","endswitch","endwhile","enum","eval","extends","final","finally","float","for","foreach","from","global","goto","if","implements","instanceof","insteadof","int","integer","interface","isset","iterable","list","match|0","mixed","new","never","object","or","private","protected","public","readonly","real","return","string","switch","throw","trait","try","unset","use","var","void","while","xor","yield"],T=["Error|0","AppendIterator","ArgumentCountError","ArithmeticError","ArrayIterator","ArrayObject","AssertionError","BadFunctionCallException","BadMethodCallException","CachingIterator","CallbackFilterIterator","CompileError","Countable","DirectoryIterator","DivisionByZeroError","DomainException","EmptyIterator","ErrorException","Exception","FilesystemIterator","FilterIterator","GlobIterator","InfiniteIterator","InvalidArgumentException","IteratorIterator","LengthException","LimitIterator","LogicException","MultipleIterator","NoRewindIterator","OutOfBoundsException","OutOfRangeException","OuterIterator","OverflowException","ParentIterator","ParseError","RangeException","RecursiveArrayIterator","RecursiveCachingIterator","RecursiveCallbackFilterIterator","RecursiveDirectoryIterator","RecursiveFilterIterator","RecursiveIterator","RecursiveIteratorIterator","RecursiveRegexIterator","RecursiveTreeIterator","RegexIterator","RuntimeException","SeekableIterator","SplDoublyLinkedList","SplFileInfo","SplFileObject","SplFixedArray","SplHeap","SplMaxHeap","SplMinHeap","SplObjectStorage","SplObserver","SplPriorityQueue","SplQueue","SplStack","SplSubject","SplTempFileObject","TypeError","UnderflowException","UnexpectedValueException","UnhandledMatchError","ArrayAccess","BackedEnum","Closure","Fiber","Generator","Iterator","IteratorAggregate","Serializable","Stringable","Throwable","Traversable","UnitEnum","WeakReference","WeakMap","Directory","__PHP_Incomplete_Class","parent","php_user_filter","self","static","stdClass"],M={keyword:v,literal:(K=>{const u=[];return K.forEach(f=>{u.push(f),f.toLowerCase()===f?u.push(f.toUpperCase()):u.push(f.toLowerCase())}),u})(N),built_in:T},k=K=>K.map(u=>u.replace(/\|\d+$/,"")),x={variants:[{match:[/new/,t.concat(_,"+"),t.concat("(?!",k(T).join("\\b|"),"\\b)"),l],scope:{1:"keyword",4:"title.class"}}]},S=t.concat(d,"\\b(?!\\()"),I={variants:[{match:[t.concat(/::/,t.lookahead(/(?!class\b)/)),S],scope:{2:"variable.constant"}},{match:[/::/,/class/],scope:{2:"variable.language"}},{match:[l,t.concat(/::/,t.lookahead(/(?!class\b)/)),S],scope:{1:"title.class",3:"variable.constant"}},{match:[l,t.concat("::",t.lookahead(/(?!class\b)/))],scope:{1:"title.class"}},{match:[l,/::/,/class/],scope:{1:"title.class",3:"variable.language"}}]},U={scope:"attr",match:t.concat(d,t.lookahead(":"),t.lookahead(/(?!::)/))},F={relevance:0,begin:/\(/,end:/\)/,keywords:M,contains:[U,s,I,e.C_BLOCK_COMMENT_MODE,h,E,x]},V={relevance:0,match:[/\b/,t.concat("(?!fn\\b|function\\b|",k(v).join("\\b|"),"|",k(T).join("\\b|"),"\\b)"),d,t.concat(_,"*"),t.lookahead(/(?=\()/)],scope:{3:"title.function.invoke"},contains:[F]};F.contains.push(V);const Y=[U,I,e.C_BLOCK_COMMENT_MODE,h,E,x],Q={begin:t.concat(/#\[\s*\\?/,t.either(l,a)),beginScope:"meta",end:/]/,endScope:"meta",keywords:{literal:N,keyword:["new","array"]},contains:[{begin:/\[/,end:/]/,keywords:{literal:N,keyword:["new","array"]},contains:["self",...Y]},...Y,{scope:"meta",variants:[{match:l},{match:a}]}]};return{case_insensitive:!1,keywords:M,contains:[Q,e.HASH_COMMENT_MODE,e.COMMENT("//","$"),e.COMMENT("/\\*","\\*/",{contains:[{scope:"doctag",match:"@[A-Za-z]+"}]}),{match:/__halt_compiler\(\);/,keywords:"__halt_compiler",starts:{scope:"comment",end:e.MATCH_NOTHING_RE,contains:[{match:/\?>/,scope:"meta",endsParent:!0}]}},c,{scope:"variable.language",match:/\$this\b/},s,V,I,{match:[/const/,/\s/,d],scope:{1:"keyword",3:"variable.constant"}},x,{scope:"function",relevance:0,beginKeywords:"fn function",end:/[;{]/,excludeEnd:!0,illegal:"[$%\\[]",contains:[{beginKeywords:"use"},e.UNDERSCORE_TITLE_MODE,{begin:"=>",endsParent:!0},{scope:"params",begin:"\\(",end:"\\)",excludeBegin:!0,excludeEnd:!0,keywords:M,contains:["self",Q,s,I,e.C_BLOCK_COMMENT_MODE,h,E]}]},{scope:"class",variants:[{beginKeywords:"enum",illegal:/[($"]/},{beginKeywords:"class interface trait",illegal:/[:($"]/}],relevance:0,end:/\{/,excludeEnd:!0,contains:[{beginKeywords:"extends implements"},e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"namespace",relevance:0,end:";",illegal:/[.']/,contains:[e.inherit(e.UNDERSCORE_TITLE_MODE,{scope:"title.class"})]},{beginKeywords:"use",relevance:0,end:";",contains:[{match:/\b(as|const|function)\b/,scope:"keyword"},e.UNDERSCORE_TITLE_MODE]},h,E]}}return Je=n,Je}var je,Yn;function $i(){if(Yn)return je;Yn=1;function n(e){return{name:"PHP template",subLanguage:"xml",contains:[{begin:/<\?(php|=)?/,end:/\?>/,subLanguage:"php",contains:[{begin:"/\\*",end:"\\*/",skip:!0},{begin:'b"',end:'"',skip:!0},{begin:"b'",end:"'",skip:!0},e.inherit(e.APOS_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0}),e.inherit(e.QUOTE_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0})]}]}}return je=n,je}var en,Zn;function Ki(){if(Zn)return en;Zn=1;function n(e){return{name:"Plain text",aliases:["text","txt"],disableAutodetect:!0}}return en=n,en}var nn,Xn;function Gi(){if(Xn)return nn;Xn=1;function n(e){const t=e.regex,r=/[\p{XID_Start}_]\p{XID_Continue}*/u,d=["and","as","assert","async","await","break","case","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","in","is","lambda","match","nonlocal|10","not","or","pass","raise","return","try","while","with","yield"],c={$pattern:/[A-Za-z]\w+|__\w+__/,keyword:d,built_in:["__import__","abs","all","any","ascii","bin","bool","breakpoint","bytearray","bytes","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","exec","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip"],literal:["__debug__","Ellipsis","False","None","NotImplemented","True"],type:["Any","Callable","Coroutine","Dict","List","Literal","Generic","Optional","Sequence","Set","Tuple","Type","Union"]},o={className:"meta",begin:/^(>>>|\.\.\.) /},i={className:"subst",begin:/\{/,end:/\}/,keywords:c,illegal:/#/},g={begin:/\{\{/,relevance:0},b={className:"string",contains:[e.BACKSLASH_ESCAPE],variants:[{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,o],relevance:10},{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,o],relevance:10},{begin:/([fF][rR]|[rR][fF]|[fF])'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,o,g,i]},{begin:/([fF][rR]|[rR][fF]|[fF])"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,o,g,i]},{begin:/([uU]|[rR])'/,end:/'/,relevance:10},{begin:/([uU]|[rR])"/,end:/"/,relevance:10},{begin:/([bB]|[bB][rR]|[rR][bB])'/,end:/'/},{begin:/([bB]|[bB][rR]|[rR][bB])"/,end:/"/},{begin:/([fF][rR]|[rR][fF]|[fF])'/,end:/'/,contains:[e.BACKSLASH_ESCAPE,g,i]},{begin:/([fF][rR]|[rR][fF]|[fF])"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,g,i]},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},m="[0-9](_?[0-9])*",_=`(\\b(${m}))?\\.(${m})|\\b(${m})\\.`,h=`\\b|${d.join("|")}`,E={className:"number",relevance:0,variants:[{begin:`(\\b(${m})|(${_}))[eE][+-]?(${m})[jJ]?(?=${h})`},{begin:`(${_})[jJ]?`},{begin:`\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${h})`},{begin:`\\b0[bB](_?[01])+[lL]?(?=${h})`},{begin:`\\b0[oO](_?[0-7])+[lL]?(?=${h})`},{begin:`\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${h})`},{begin:`\\b(${m})[jJ](?=${h})`}]},N={className:"comment",begin:t.lookahead(/# type:/),end:/$/,keywords:c,contains:[{begin:/# type:/},{begin:/#/,end:/\b\B/,endsWithParent:!0}]},v={className:"params",variants:[{className:"",begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:c,contains:["self",o,E,b,e.HASH_COMMENT_MODE]}]};return i.contains=[b,E,o],{name:"Python",aliases:["py","gyp","ipython"],unicodeRegex:!0,keywords:c,illegal:/(<\/|\?)|=>/,contains:[o,E,{scope:"variable.language",match:/\bself\b/},{beginKeywords:"if",relevance:0},{match:/\bor\b/,scope:"keyword"},b,N,e.HASH_COMMENT_MODE,{match:[/\bdef/,/\s+/,r],scope:{1:"keyword",3:"title.function"},contains:[v]},{variants:[{match:[/\bclass/,/\s+/,r,/\s*/,/\(\s*/,r,/\s*\)/]},{match:[/\bclass/,/\s+/,r]}],scope:{1:"keyword",3:"title.class",6:"title.class.inherited"}},{className:"meta",begin:/^[\t ]*@/,end:/(?=#)|$/,contains:[E,v,b]}]}}return nn=n,nn}var tn,Vn;function qi(){if(Vn)return tn;Vn=1;function n(e){return{aliases:["pycon"],contains:[{className:"meta.prompt",starts:{end:/ |$/,starts:{end:"$",subLanguage:"python"}},variants:[{begin:/^>>>(?=[ ]|$)/},{begin:/^\.\.\.(?=[ ]|$)/}]}]}}return tn=n,tn}var an,Qn;function Hi(){if(Qn)return an;Qn=1;function n(e){const t=e.regex,r=/(?:(?:[a-zA-Z]|\.[._a-zA-Z])[._a-zA-Z0-9]*)|\.(?!\d)/,d=t.either(/0[xX][0-9a-fA-F]+\.[0-9a-fA-F]*[pP][+-]?\d+i?/,/0[xX][0-9a-fA-F]+(?:[pP][+-]?\d+)?[Li]?/,/(?:\d+(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+)?[Li]?/),l=/[=!<>:]=|\|\||&&|:::?|<-|<<-|->>|->|\|>|[-+*\/?!$&|:<=>@^~]|\*\*/,a=t.either(/[()]/,/[{}]/,/\[\[/,/[[\]]/,/\\/,/,/);return{name:"R",keywords:{$pattern:r,keyword:"function if in break next repeat else for while",literal:"NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 NA_character_|10 NA_complex_|10",built_in:"LETTERS letters month.abb month.name pi T F abs acos acosh all any anyNA Arg as.call as.character as.complex as.double as.environment as.integer as.logical as.null.default as.numeric as.raw asin asinh atan atanh attr attributes baseenv browser c call ceiling class Conj cos cosh cospi cummax cummin cumprod cumsum digamma dim dimnames emptyenv exp expression floor forceAndCall gamma gc.time globalenv Im interactive invisible is.array is.atomic is.call is.character is.complex is.double is.environment is.expression is.finite is.function is.infinite is.integer is.language is.list is.logical is.matrix is.na is.name is.nan is.null is.numeric is.object is.pairlist is.raw is.recursive is.single is.symbol lazyLoadDBfetch length lgamma list log max min missing Mod names nargs nzchar oldClass on.exit pos.to.env proc.time prod quote range Re rep retracemem return round seq_along seq_len seq.int sign signif sin sinh sinpi sqrt standardGeneric substitute sum switch tan tanh tanpi tracemem trigamma trunc unclass untracemem UseMethod xtfrm"},contains:[e.COMMENT(/#'/,/$/,{contains:[{scope:"doctag",match:/@examples/,starts:{end:t.lookahead(t.either(/\n^#'\s*(?=@[a-zA-Z]+)/,/\n^(?!#')/)),endsParent:!0}},{scope:"doctag",begin:"@param",end:/$/,contains:[{scope:"variable",variants:[{match:r},{match:/`(?:\\.|[^`\\])+`/}],endsParent:!0}]},{scope:"doctag",match:/@[a-zA-Z]+/},{scope:"keyword",match:/\\[a-zA-Z]+/}]}),e.HASH_COMMENT_MODE,{scope:"string",contains:[e.BACKSLASH_ESCAPE],variants:[e.END_SAME_AS_BEGIN({begin:/[rR]"(-*)\(/,end:/\)(-*)"/}),e.END_SAME_AS_BEGIN({begin:/[rR]"(-*)\{/,end:/\}(-*)"/}),e.END_SAME_AS_BEGIN({begin:/[rR]"(-*)\[/,end:/\](-*)"/}),e.END_SAME_AS_BEGIN({begin:/[rR]'(-*)\(/,end:/\)(-*)'/}),e.END_SAME_AS_BEGIN({begin:/[rR]'(-*)\{/,end:/\}(-*)'/}),e.END_SAME_AS_BEGIN({begin:/[rR]'(-*)\[/,end:/\](-*)'/}),{begin:'"',end:'"',relevance:0},{begin:"'",end:"'",relevance:0}]},{relevance:0,variants:[{scope:{1:"operator",2:"number"},match:[l,d]},{scope:{1:"operator",2:"number"},match:[/%[^%]*%/,d]},{scope:{1:"punctuation",2:"number"},match:[a,d]},{scope:{2:"number"},match:[/[^a-zA-Z0-9._]|^/,d]}]},{scope:{3:"operator"},match:[r,/\s+/,/<-/,/\s+/]},{scope:"operator",relevance:0,variants:[{match:l},{match:/%[^%]*%/}]},{scope:"punctuation",relevance:0,match:a},{begin:"`",end:"`",contains:[{begin:/\\./}]}]}}return an=n,an}var rn,Jn;function Wi(){if(Jn)return rn;Jn=1;function n(e){const t=e.regex,r=/(r#)?/,d=t.concat(r,e.UNDERSCORE_IDENT_RE),l=t.concat(r,e.IDENT_RE),a={className:"title.function.invoke",relevance:0,begin:t.concat(/\b/,/(?!let|for|while|if|else|match\b)/,l,t.lookahead(/\s*\(/))},s="([ui](8|16|32|64|128|size)|f(32|64))?",c=["abstract","as","async","await","become","box","break","const","continue","crate","do","dyn","else","enum","extern","false","final","fn","for","if","impl","in","let","loop","macro","match","mod","move","mut","override","priv","pub","ref","return","self","Self","static","struct","super","trait","true","try","type","typeof","union","unsafe","unsized","use","virtual","where","while","yield"],o=["true","false","Some","None","Ok","Err"],i=["drop ","Copy","Send","Sized","Sync","Drop","Fn","FnMut","FnOnce","ToOwned","Clone","Debug","PartialEq","PartialOrd","Eq","Ord","AsRef","AsMut","Into","From","Default","Iterator","Extend","IntoIterator","DoubleEndedIterator","ExactSizeIterator","SliceConcatExt","ToString","assert!","assert_eq!","bitflags!","bytes!","cfg!","col!","concat!","concat_idents!","debug_assert!","debug_assert_eq!","env!","eprintln!","panic!","file!","format!","format_args!","include_bytes!","include_str!","line!","local_data_key!","module_path!","option_env!","print!","println!","select!","stringify!","try!","unimplemented!","unreachable!","vec!","write!","writeln!","macro_rules!","assert_ne!","debug_assert_ne!"],g=["i8","i16","i32","i64","i128","isize","u8","u16","u32","u64","u128","usize","f32","f64","str","char","bool","Box","Option","Result","String","Vec"];return{name:"Rust",aliases:["rs"],keywords:{$pattern:e.IDENT_RE+"!?",type:g,keyword:c,literal:o,built_in:i},illegal:"</",contains:[e.C_LINE_COMMENT_MODE,e.COMMENT("/\\*","\\*/",{contains:["self"]}),e.inherit(e.QUOTE_STRING_MODE,{begin:/b?"/,illegal:null}),{className:"symbol",begin:/'[a-zA-Z_][a-zA-Z0-9_]*(?!')/},{scope:"string",variants:[{begin:/b?r(#*)"(.|\n)*?"\1(?!#)/},{begin:/b?'/,end:/'/,contains:[{scope:"char.escape",match:/\\('|\w|x\w{2}|u\w{4}|U\w{8})/}]}]},{className:"number",variants:[{begin:"\\b0b([01_]+)"+s},{begin:"\\b0o([0-7_]+)"+s},{begin:"\\b0x([A-Fa-f0-9_]+)"+s},{begin:"\\b(\\d[\\d_]*(\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)"+s}],relevance:0},{begin:[/fn/,/\s+/,d],className:{1:"keyword",3:"title.function"}},{className:"meta",begin:"#!?\\[",end:"\\]",contains:[{className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE]}]},{begin:[/let/,/\s+/,/(?:mut\s+)?/,d],className:{1:"keyword",3:"keyword",4:"variable"}},{begin:[/for/,/\s+/,d,/\s+/,/in/],className:{1:"keyword",3:"variable",5:"keyword"}},{begin:[/type/,/\s+/,d],className:{1:"keyword",3:"title.class"}},{begin:[/(?:trait|enum|struct|union|impl|for)/,/\s+/,d],className:{1:"keyword",3:"title.class"}},{begin:e.IDENT_RE+"::",keywords:{keyword:"Self",built_in:i,type:g}},{className:"punctuation",begin:"->"},a]}}return rn=n,rn}var sn,jn;function Yi(){if(jn)return sn;jn=1;const n=o=>({IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:o.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[o.APOS_STRING_MODE,o.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:o.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}}),e=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video"],t=["defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],r=[...e,...t],d=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),l=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),a=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),s=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse();function c(o){const i=n(o),g=a,b=l,m="@[a-z-]+",_="and or not only",E={className:"variable",begin:"(\\$"+"[a-zA-Z-][a-zA-Z0-9_-]*"+")\\b",relevance:0};return{name:"SCSS",case_insensitive:!0,illegal:"[=/|']",contains:[o.C_LINE_COMMENT_MODE,o.C_BLOCK_COMMENT_MODE,i.CSS_NUMBER_MODE,{className:"selector-id",begin:"#[A-Za-z0-9_-]+",relevance:0},{className:"selector-class",begin:"\\.[A-Za-z0-9_-]+",relevance:0},i.ATTRIBUTE_SELECTOR_MODE,{className:"selector-tag",begin:"\\b("+r.join("|")+")\\b",relevance:0},{className:"selector-pseudo",begin:":("+b.join("|")+")"},{className:"selector-pseudo",begin:":(:)?("+g.join("|")+")"},E,{begin:/\(/,end:/\)/,contains:[i.CSS_NUMBER_MODE]},i.CSS_VARIABLE,{className:"attribute",begin:"\\b("+s.join("|")+")\\b"},{begin:"\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\b"},{begin:/:/,end:/[;}{]/,relevance:0,contains:[i.BLOCK_COMMENT,E,i.HEXCOLOR,i.CSS_NUMBER_MODE,o.QUOTE_STRING_MODE,o.APOS_STRING_MODE,i.IMPORTANT,i.FUNCTION_DISPATCH]},{begin:"@(page|font-face)",keywords:{$pattern:m,keyword:"@page @font-face"}},{begin:"@",end:"[{;]",returnBegin:!0,keywords:{$pattern:/[a-z-]+/,keyword:_,attribute:d.join(" ")},contains:[{begin:m,className:"keyword"},{begin:/[a-z-]+(?=:)/,className:"attribute"},E,o.QUOTE_STRING_MODE,o.APOS_STRING_MODE,i.HEXCOLOR,i.CSS_NUMBER_MODE]},i.FUNCTION_DISPATCH]}}return sn=c,sn}var on,et;function Zi(){if(et)return on;et=1;function n(e){return{name:"Shell Session",aliases:["console","shellsession"],contains:[{className:"meta.prompt",begin:/^\s{0,3}[/~\w\d[\]()@-]*[>%$#][ ]?/,starts:{end:/[^\\](?=\s*$)/,subLanguage:"bash"}}]}}return on=n,on}var cn,nt;function Xi(){if(nt)return cn;nt=1;function n(e){const t=e.regex,r=e.COMMENT("--","$"),d={scope:"string",variants:[{begin:/'/,end:/'/,contains:[{match:/''/}]}]},l={begin:/"/,end:/"/,contains:[{match:/""/}]},a=["true","false","unknown"],s=["double precision","large object","with timezone","without timezone"],c=["bigint","binary","blob","boolean","char","character","clob","date","dec","decfloat","decimal","float","int","integer","interval","nchar","nclob","national","numeric","real","row","smallint","time","timestamp","varchar","varying","varbinary"],o=["add","asc","collation","desc","final","first","last","view"],i=["abs","acos","all","allocate","alter","and","any","are","array","array_agg","array_max_cardinality","as","asensitive","asin","asymmetric","at","atan","atomic","authorization","avg","begin","begin_frame","begin_partition","between","bigint","binary","blob","boolean","both","by","call","called","cardinality","cascaded","case","cast","ceil","ceiling","char","char_length","character","character_length","check","classifier","clob","close","coalesce","collate","collect","column","commit","condition","connect","constraint","contains","convert","copy","corr","corresponding","cos","cosh","count","covar_pop","covar_samp","create","cross","cube","cume_dist","current","current_catalog","current_date","current_default_transform_group","current_path","current_role","current_row","current_schema","current_time","current_timestamp","current_path","current_role","current_transform_group_for_type","current_user","cursor","cycle","date","day","deallocate","dec","decimal","decfloat","declare","default","define","delete","dense_rank","deref","describe","deterministic","disconnect","distinct","double","drop","dynamic","each","element","else","empty","end","end_frame","end_partition","end-exec","equals","escape","every","except","exec","execute","exists","exp","external","extract","false","fetch","filter","first_value","float","floor","for","foreign","frame_row","free","from","full","function","fusion","get","global","grant","group","grouping","groups","having","hold","hour","identity","in","indicator","initial","inner","inout","insensitive","insert","int","integer","intersect","intersection","interval","into","is","join","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","language","large","last_value","lateral","lead","leading","left","like","like_regex","listagg","ln","local","localtime","localtimestamp","log","log10","lower","match","match_number","match_recognize","matches","max","member","merge","method","min","minute","mod","modifies","module","month","multiset","national","natural","nchar","nclob","new","no","none","normalize","not","nth_value","ntile","null","nullif","numeric","octet_length","occurrences_regex","of","offset","old","omit","on","one","only","open","or","order","out","outer","over","overlaps","overlay","parameter","partition","pattern","per","percent","percent_rank","percentile_cont","percentile_disc","period","portion","position","position_regex","power","precedes","precision","prepare","primary","procedure","ptf","range","rank","reads","real","recursive","ref","references","referencing","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","release","result","return","returns","revoke","right","rollback","rollup","row","row_number","rows","running","savepoint","scope","scroll","search","second","seek","select","sensitive","session_user","set","show","similar","sin","sinh","skip","smallint","some","specific","specifictype","sql","sqlexception","sqlstate","sqlwarning","sqrt","start","static","stddev_pop","stddev_samp","submultiset","subset","substring","substring_regex","succeeds","sum","symmetric","system","system_time","system_user","table","tablesample","tan","tanh","then","time","timestamp","timezone_hour","timezone_minute","to","trailing","translate","translate_regex","translation","treat","trigger","trim","trim_array","true","truncate","uescape","union","unique","unknown","unnest","update","upper","user","using","value","values","value_of","var_pop","var_samp","varbinary","varchar","varying","versioning","when","whenever","where","width_bucket","window","with","within","without","year"],g=["abs","acos","array_agg","asin","atan","avg","cast","ceil","ceiling","coalesce","corr","cos","cosh","count","covar_pop","covar_samp","cume_dist","dense_rank","deref","element","exp","extract","first_value","floor","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","last_value","lead","listagg","ln","log","log10","lower","max","min","mod","nth_value","ntile","nullif","percent_rank","percentile_cont","percentile_disc","position","position_regex","power","rank","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","row_number","sin","sinh","sqrt","stddev_pop","stddev_samp","substring","substring_regex","sum","tan","tanh","translate","translate_regex","treat","trim","trim_array","unnest","upper","value_of","var_pop","var_samp","width_bucket"],b=["current_catalog","current_date","current_default_transform_group","current_path","current_role","current_schema","current_transform_group_for_type","current_user","session_user","system_time","system_user","current_time","localtime","current_timestamp","localtimestamp"],m=["create table","insert into","primary key","foreign key","not null","alter table","add constraint","grouping sets","on overflow","character set","respect nulls","ignore nulls","nulls first","nulls last","depth first","breadth first"],_=g,h=[...i,...o].filter(k=>!g.includes(k)),E={scope:"variable",match:/@[a-z0-9][a-z0-9_]*/},N={scope:"operator",match:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,relevance:0},v={match:t.concat(/\b/,t.either(..._),/\s*\(/),relevance:0,keywords:{built_in:_}};function T(k){return t.concat(/\b/,t.either(...k.map(x=>x.replace(/\s+/,"\\s+"))),/\b/)}const C={scope:"keyword",match:T(m),relevance:0};function M(k,{exceptions:x,when:S}={}){const I=S;return x=x||[],k.map(U=>U.match(/\|\d+$/)||x.includes(U)?U:I(U)?`${U}|0`:U)}return{name:"SQL",case_insensitive:!0,illegal:/[{}]|<\//,keywords:{$pattern:/\b[\w\.]+/,keyword:M(h,{when:k=>k.length<3}),literal:a,type:c,built_in:b},contains:[{scope:"type",match:T(s)},C,v,E,d,l,e.C_NUMBER_MODE,e.C_BLOCK_COMMENT_MODE,r,N]}}return cn=n,cn}var ln,tt;function Vi(){if(tt)return ln;tt=1;function n(S){return S?typeof S=="string"?S:S.source:null}function e(S){return t("(?=",S,")")}function t(...S){return S.map(U=>n(U)).join("")}function r(S){const I=S[S.length-1];return typeof I=="object"&&I.constructor===Object?(S.splice(S.length-1,1),I):{}}function d(...S){return"("+(r(S).capture?"":"?:")+S.map(F=>n(F)).join("|")+")"}const l=S=>t(/\b/,S,/\w$/.test(S)?/\b/:/\B/),a=["Protocol","Type"].map(l),s=["init","self"].map(l),c=["Any","Self"],o=["actor","any","associatedtype","async","await",/as\?/,/as!/,"as","borrowing","break","case","catch","class","consume","consuming","continue","convenience","copy","default","defer","deinit","didSet","distributed","do","dynamic","each","else","enum","extension","fallthrough",/fileprivate\(set\)/,"fileprivate","final","for","func","get","guard","if","import","indirect","infix",/init\?/,/init!/,"inout",/internal\(set\)/,"internal","in","is","isolated","nonisolated","lazy","let","macro","mutating","nonmutating",/open\(set\)/,"open","operator","optional","override","package","postfix","precedencegroup","prefix",/private\(set\)/,"private","protocol",/public\(set\)/,"public","repeat","required","rethrows","return","set","some","static","struct","subscript","super","switch","throws","throw",/try\?/,/try!/,"try","typealias",/unowned\(safe\)/,/unowned\(unsafe\)/,"unowned","var","weak","where","while","willSet"],i=["false","nil","true"],g=["assignment","associativity","higherThan","left","lowerThan","none","right"],b=["#colorLiteral","#column","#dsohandle","#else","#elseif","#endif","#error","#file","#fileID","#fileLiteral","#filePath","#function","#if","#imageLiteral","#keyPath","#line","#selector","#sourceLocation","#warning"],m=["abs","all","any","assert","assertionFailure","debugPrint","dump","fatalError","getVaList","isKnownUniquelyReferenced","max","min","numericCast","pointwiseMax","pointwiseMin","precondition","preconditionFailure","print","readLine","repeatElement","sequence","stride","swap","swift_unboxFromSwiftValueWithType","transcode","type","unsafeBitCast","unsafeDowncast","withExtendedLifetime","withUnsafeMutablePointer","withUnsafePointer","withVaList","withoutActuallyEscaping","zip"],_=d(/[/=\-+!*%<>&|^~?]/,/[\u00A1-\u00A7]/,/[\u00A9\u00AB]/,/[\u00AC\u00AE]/,/[\u00B0\u00B1]/,/[\u00B6\u00BB\u00BF\u00D7\u00F7]/,/[\u2016-\u2017]/,/[\u2020-\u2027]/,/[\u2030-\u203E]/,/[\u2041-\u2053]/,/[\u2055-\u205E]/,/[\u2190-\u23FF]/,/[\u2500-\u2775]/,/[\u2794-\u2BFF]/,/[\u2E00-\u2E7F]/,/[\u3001-\u3003]/,/[\u3008-\u3020]/,/[\u3030]/),h=d(_,/[\u0300-\u036F]/,/[\u1DC0-\u1DFF]/,/[\u20D0-\u20FF]/,/[\uFE00-\uFE0F]/,/[\uFE20-\uFE2F]/),E=t(_,h,"*"),N=d(/[a-zA-Z_]/,/[\u00A8\u00AA\u00AD\u00AF\u00B2-\u00B5\u00B7-\u00BA]/,/[\u00BC-\u00BE\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF]/,/[\u0100-\u02FF\u0370-\u167F\u1681-\u180D\u180F-\u1DBF]/,/[\u1E00-\u1FFF]/,/[\u200B-\u200D\u202A-\u202E\u203F-\u2040\u2054\u2060-\u206F]/,/[\u2070-\u20CF\u2100-\u218F\u2460-\u24FF\u2776-\u2793]/,/[\u2C00-\u2DFF\u2E80-\u2FFF]/,/[\u3004-\u3007\u3021-\u302F\u3031-\u303F\u3040-\uD7FF]/,/[\uF900-\uFD3D\uFD40-\uFDCF\uFDF0-\uFE1F\uFE30-\uFE44]/,/[\uFE47-\uFEFE\uFF00-\uFFFD]/),v=d(N,/\d/,/[\u0300-\u036F\u1DC0-\u1DFF\u20D0-\u20FF\uFE20-\uFE2F]/),T=t(N,v,"*"),C=t(/[A-Z]/,v,"*"),M=["attached","autoclosure",t(/convention\(/,d("swift","block","c"),/\)/),"discardableResult","dynamicCallable","dynamicMemberLookup","escaping","freestanding","frozen","GKInspectable","IBAction","IBDesignable","IBInspectable","IBOutlet","IBSegueAction","inlinable","main","nonobjc","NSApplicationMain","NSCopying","NSManaged",t(/objc\(/,T,/\)/),"objc","objcMembers","propertyWrapper","requires_stored_property_inits","resultBuilder","Sendable","testable","UIApplicationMain","unchecked","unknown","usableFromInline","warn_unqualified_access"],k=["iOS","iOSApplicationExtension","macOS","macOSApplicationExtension","macCatalyst","macCatalystApplicationExtension","watchOS","watchOSApplicationExtension","tvOS","tvOSApplicationExtension","swift"];function x(S){const I={match:/\s+/,relevance:0},U=S.COMMENT("/\\*","\\*/",{contains:["self"]}),F=[S.C_LINE_COMMENT_MODE,U],V={match:[/\./,d(...a,...s)],className:{2:"keyword"}},Y={match:t(/\./,d(...o)),relevance:0},Q=o.filter(P=>typeof P=="string").concat(["_|0"]),K=o.filter(P=>typeof P!="string").concat(c).map(l),u={variants:[{className:"keyword",match:d(...K,...s)}]},f={$pattern:d(/\b\w+/,/#\w+/),keyword:Q.concat(b),literal:i},w=[V,Y,u],L={match:t(/\./,d(...m)),relevance:0},z={className:"built_in",match:t(/\b/,d(...m),/(?=\()/)},Z=[L,z],j={match:/->/,relevance:0},se={className:"operator",relevance:0,variants:[{match:E},{match:`\\.(\\.|${h})+`}]},G=[j,se],H="([0-9]_*)+",oe="([0-9a-fA-F]_*)+",q={className:"number",relevance:0,variants:[{match:`\\b(${H})(\\.(${H}))?([eE][+-]?(${H}))?\\b`},{match:`\\b0x(${oe})(\\.(${oe}))?([pP][+-]?(${H}))?\\b`},{match:/\b0o([0-7]_*)+\b/},{match:/\b0b([01]_*)+\b/}]},$=(P="")=>({className:"subst",variants:[{match:t(/\\/,P,/[0\\tnr"']/)},{match:t(/\\/,P,/u\{[0-9a-fA-F]{1,8}\}/)}]}),J=(P="")=>({className:"subst",match:t(/\\/,P,/[\t ]*(?:[\r\n]|\r\n)/)}),ee=(P="")=>({className:"subst",label:"interpol",begin:t(/\\/,P,/\(/),end:/\)/}),ne=(P="")=>({begin:t(P,/"""/),end:t(/"""/,P),contains:[$(P),J(P),ee(P)]}),ie=(P="")=>({begin:t(P,/"/),end:t(/"/,P),contains:[$(P),ee(P)]}),te={className:"string",variants:[ne(),ne("#"),ne("##"),ne("###"),ie(),ie("#"),ie("##"),ie("###")]},fe=[S.BACKSLASH_ESCAPE,{begin:/\[/,end:/\]/,relevance:0,contains:[S.BACKSLASH_ESCAPE]}],ae={begin:/\/[^\s](?=[^/\n]*\/)/,end:/\//,contains:fe},Ee=P=>{const we=t(P,/\//),Ne=t(/\//,P);return{begin:we,end:Ne,contains:[...fe,{scope:"comment",begin:`#(?!.*${Ne})`,end:/$/}]}},be={scope:"regexp",variants:[Ee("###"),Ee("##"),Ee("#"),ae]},O={match:t(/`/,T,/`/)},Te={className:"variable",match:/\$\d+/},X={className:"variable",match:`\\$${v}+`},B=[O,Te,X],pe={match:/(@|#(un)?)available/,scope:"keyword",starts:{contains:[{begin:/\(/,end:/\)/,keywords:k,contains:[...G,q,te]}]}},ce={scope:"keyword",match:t(/@/,d(...M),e(d(/\(/,/\s+/)))},he={scope:"meta",match:t(/@/,T)},le=[pe,ce,he],p={match:e(/\b[A-Z]/),relevance:0,contains:[{className:"type",match:t(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/,v,"+")},{className:"type",match:C,relevance:0},{match:/[?!]+/,relevance:0},{match:/\.\.\./,relevance:0},{match:t(/\s+&\s+/,e(C)),relevance:0}]},y={begin:/</,end:/>/,keywords:f,contains:[...F,...w,...le,j,p]};p.contains.push(y);const A={match:t(T,/\s*:/),keywords:"_|0",relevance:0},D={begin:/\(/,end:/\)/,relevance:0,keywords:f,contains:["self",A,...F,be,...w,...Z,...G,q,te,...B,...le,p]},W={begin:/</,end:/>/,keywords:"repeat each",contains:[...F,p]},re={begin:d(e(t(T,/\s*:/)),e(t(T,/\s+/,T,/\s*:/))),end:/:/,relevance:0,contains:[{className:"keyword",match:/\b_\b/},{className:"params",match:T}]},Se={begin:/\(/,end:/\)/,keywords:f,contains:[re,...F,...w,...G,q,te,...le,p,D],endsParent:!0,illegal:/["']/},Me={match:[/(func|macro)/,/\s+/,d(O.match,T,E)],className:{1:"keyword",3:"title.function"},contains:[W,Se,I],illegal:[/\[/,/%/]},ht={match:[/\b(?:subscript|init[?!]?)/,/\s*(?=[<(])/],className:{1:"keyword"},contains:[W,Se,I],illegal:/\[|%/},Nt={match:[/operator/,/\s+/,E],className:{1:"keyword",3:"title"}},yt={begin:[/precedencegroup/,/\s+/,C],className:{1:"keyword",3:"title"},contains:[p],keywords:[...g,...i],end:/}/},vt={match:[/class\b/,/\s+/,/func\b/,/\s+/,/\b[A-Za-z_][A-Za-z0-9_]*\b/],scope:{1:"keyword",3:"keyword",5:"title.function"}},Tt={match:[/class\b/,/\s+/,/var\b/],scope:{1:"keyword",3:"keyword"}},St={begin:[/(struct|protocol|class|extension|enum|actor)/,/\s+/,T,/\s*/],beginScope:{1:"keyword",3:"title.class"},keywords:f,contains:[W,...w,{begin:/:/,end:/\{/,keywords:f,contains:[{scope:"title.class.inherited",match:C},...w],relevance:0}]};for(const P of te.variants){const we=P.contains.find(wt=>wt.label==="interpol");we.keywords=f;const Ne=[...w,...Z,...G,q,te,...B];we.contains=[...Ne,{begin:/\(/,end:/\)/,contains:["self",...Ne]}]}return{name:"Swift",keywords:f,contains:[...F,Me,ht,vt,Tt,St,Nt,yt,{beginKeywords:"import",end:/$/,contains:[...F],relevance:0},be,...w,...Z,...G,q,te,...B,...le,p,D]}}return ln=x,ln}var dn,it;function Qi(){if(it)return dn;it=1;function n(e){const t="true false yes no null",r="[\\w#;/?:@&=+$,.~*'()[\\]]+",d={className:"attr",variants:[{begin:/[\w*@][\w*@ :()\./-]*:(?=[ \t]|$)/},{begin:/"[\w*@][\w*@ :()\./-]*":(?=[ \t]|$)/},{begin:/'[\w*@][\w*@ :()\./-]*':(?=[ \t]|$)/}]},l={className:"template-variable",variants:[{begin:/\{\{/,end:/\}\}/},{begin:/%\{/,end:/\}/}]},a={className:"string",relevance:0,begin:/'/,end:/'/,contains:[{match:/''/,scope:"char.escape",relevance:0}]},s={className:"string",relevance:0,variants:[{begin:/"/,end:/"/},{begin:/\S+/}],contains:[e.BACKSLASH_ESCAPE,l]},c=e.inherit(s,{variants:[{begin:/'/,end:/'/,contains:[{begin:/''/,relevance:0}]},{begin:/"/,end:/"/},{begin:/[^\s,{}[\]]+/}]}),o="[0-9]{4}(-[0-9][0-9]){0,2}",i="([Tt \\t][0-9][0-9]?(:[0-9][0-9]){2})?",g="(\\.[0-9]*)?",b="([ \\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?",m={className:"number",begin:"\\b"+o+i+g+b+"\\b"},_={end:",",endsWithParent:!0,excludeEnd:!0,keywords:t,relevance:0},h={begin:/\{/,end:/\}/,contains:[_],illegal:"\\n",relevance:0},E={begin:"\\[",end:"\\]",contains:[_],illegal:"\\n",relevance:0},N=[d,{className:"meta",begin:"^---\\s*$",relevance:10},{className:"string",begin:"[\\|>]([1-9]?[+-])?[ ]*\\n( +)[^ ][^\\n]*\\n(\\2[^\\n]+\\n?)*"},{begin:"<%[%=-]?",end:"[%-]?%>",subLanguage:"ruby",excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:"!\\w+!"+r},{className:"type",begin:"!<"+r+">"},{className:"type",begin:"!"+r},{className:"type",begin:"!!"+r},{className:"meta",begin:"&"+e.UNDERSCORE_IDENT_RE+"$"},{className:"meta",begin:"\\*"+e.UNDERSCORE_IDENT_RE+"$"},{className:"bullet",begin:"-(?=[ ]|$)",relevance:0},e.HASH_COMMENT_MODE,{beginKeywords:t,keywords:{literal:t}},m,{className:"number",begin:e.C_NUMBER_RE+"\\b",relevance:0},h,E,a,s],v=[...N];return v.pop(),v.push(c),_.contains=v,{name:"YAML",case_insensitive:!0,aliases:["yml"],contains:N}}return dn=n,dn}var un,at;function Ji(){if(at)return un;at=1;const n="[A-Za-z$_][0-9A-Za-z$_]*",e=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],t=["true","false","null","undefined","NaN","Infinity"],r=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],d=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],l=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],a=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],s=[].concat(l,r,d);function c(i){const g=i.regex,b=($,{after:J})=>{const ee="</"+$[0].slice(1);return $.input.indexOf(ee,J)!==-1},m=n,_={begin:"<>",end:"</>"},h=/<[A-Za-z0-9\\._:-]+\s*\/>/,E={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:($,J)=>{const ee=$[0].length+$.index,ne=$.input[ee];if(ne==="<"||ne===","){J.ignoreMatch();return}ne===">"&&(b($,{after:ee})||J.ignoreMatch());let ie;const te=$.input.substring(ee);if(ie=te.match(/^\s*=/)){J.ignoreMatch();return}if((ie=te.match(/^\s+extends\s+/))&&ie.index===0){J.ignoreMatch();return}}},N={$pattern:n,keyword:e,literal:t,built_in:s,"variable.language":a},v="[0-9](_?[0-9])*",T=`\\.(${v})`,C="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",M={className:"number",variants:[{begin:`(\\b(${C})((${T})|\\.)?|(${T}))[eE][+-]?(${v})\\b`},{begin:`\\b(${C})\\b((${T})\\b|\\.)?|(${T})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},k={className:"subst",begin:"\\$\\{",end:"\\}",keywords:N,contains:[]},x={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,k],subLanguage:"xml"}},S={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,k],subLanguage:"css"}},I={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,k],subLanguage:"graphql"}},U={className:"string",begin:"`",end:"`",contains:[i.BACKSLASH_ESCAPE,k]},V={className:"comment",variants:[i.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:m+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),i.C_BLOCK_COMMENT_MODE,i.C_LINE_COMMENT_MODE]},Y=[i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,x,S,I,U,{match:/\$\d+/},M];k.contains=Y.concat({begin:/\{/,end:/\}/,keywords:N,contains:["self"].concat(Y)});const Q=[].concat(V,k.contains),K=Q.concat([{begin:/(\s*)\(/,end:/\)/,keywords:N,contains:["self"].concat(Q)}]),u={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:N,contains:K},f={variants:[{match:[/class/,/\s+/,m,/\s+/,/extends/,/\s+/,g.concat(m,"(",g.concat(/\./,m),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,m],scope:{1:"keyword",3:"title.class"}}]},w={relevance:0,match:g.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...r,...d]}},L={label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},z={variants:[{match:[/function/,/\s+/,m,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[u],illegal:/%/},Z={relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"};function j($){return g.concat("(?!",$.join("|"),")")}const se={match:g.concat(/\b/,j([...l,"super","import"].map($=>`${$}\\s*\\(`)),m,g.lookahead(/\s*\(/)),className:"title.function",relevance:0},G={begin:g.concat(/\./,g.lookahead(g.concat(m,/(?![0-9A-Za-z$_(])/))),end:m,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},H={match:[/get|set/,/\s+/,m,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},u]},oe="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+i.UNDERSCORE_IDENT_RE+")\\s*=>",q={match:[/const|var|let/,/\s+/,m,/\s*/,/=\s*/,/(async\s*)?/,g.lookahead(oe)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[u]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:N,exports:{PARAMS_CONTAINS:K,CLASS_REFERENCE:w},illegal:/#(?![$_A-z])/,contains:[i.SHEBANG({label:"shebang",binary:"node",relevance:5}),L,i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,x,S,I,U,V,{match:/\$\d+/},M,w,{scope:"attr",match:m+g.lookahead(":"),relevance:0},q,{begin:"("+i.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[V,i.REGEXP_MODE,{className:"function",begin:oe,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:i.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:N,contains:K}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:_.begin,end:_.end},{match:h},{begin:E.begin,"on:begin":E.isTrulyOpeningTag,end:E.end}],subLanguage:"xml",contains:[{begin:E.begin,end:E.end,skip:!0,contains:["self"]}]}]},z,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+i.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[u,i.inherit(i.TITLE_MODE,{begin:m,className:"title.function"})]},{match:/\.\.\./,relevance:0},G,{match:"\\$"+m,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[u]},se,Z,f,H,{match:/\$[(.]/}]}}function o(i){const g=i.regex,b=c(i),m=n,_=["any","void","number","boolean","string","object","never","symbol","bigint","unknown"],h={begin:[/namespace/,/\s+/,i.IDENT_RE],beginScope:{1:"keyword",3:"title.class"}},E={beginKeywords:"interface",end:/\{/,excludeEnd:!0,keywords:{keyword:"interface extends",built_in:_},contains:[b.exports.CLASS_REFERENCE]},N={className:"meta",relevance:10,begin:/^\s*['"]use strict['"]/},v=["type","interface","public","private","protected","implements","declare","abstract","readonly","enum","override","satisfies"],T={$pattern:n,keyword:e.concat(v),literal:t,built_in:s.concat(_),"variable.language":a},C={className:"meta",begin:"@"+m},M=(I,U,F)=>{const V=I.contains.findIndex(Y=>Y.label===U);if(V===-1)throw new Error("can not find mode to replace");I.contains.splice(V,1,F)};Object.assign(b.keywords,T),b.exports.PARAMS_CONTAINS.push(C);const k=b.contains.find(I=>I.scope==="attr"),x=Object.assign({},k,{match:g.concat(m,g.lookahead(/\s*\?:/))});b.exports.PARAMS_CONTAINS.push([b.exports.CLASS_REFERENCE,k,x]),b.contains=b.contains.concat([C,h,E,x]),M(b,"shebang",i.SHEBANG()),M(b,"use_strict",N);const S=b.contains.find(I=>I.label==="func.def");return S.relevance=0,Object.assign(b,{name:"TypeScript",aliases:["ts","tsx","mts","cts"]}),b}return un=o,un}var gn,rt;function ji(){if(rt)return gn;rt=1;function n(e){const t=e.regex,r={className:"string",begin:/"(""|[^/n])"C\b/},d={className:"string",begin:/"/,end:/"/,illegal:/\n/,contains:[{begin:/""/}]},l=/\d{1,2}\/\d{1,2}\/\d{4}/,a=/\d{4}-\d{1,2}-\d{1,2}/,s=/(\d|1[012])(:\d+){0,2} *(AM|PM)/,c=/\d{1,2}(:\d{1,2}){1,2}/,o={className:"literal",variants:[{begin:t.concat(/# */,t.either(a,l),/ *#/)},{begin:t.concat(/# */,c,/ *#/)},{begin:t.concat(/# */,s,/ *#/)},{begin:t.concat(/# */,t.either(a,l),/ +/,t.either(s,c),/ *#/)}]},i={className:"number",relevance:0,variants:[{begin:/\b\d[\d_]*((\.[\d_]+(E[+-]?[\d_]+)?)|(E[+-]?[\d_]+))[RFD@!#]?/},{begin:/\b\d[\d_]*((U?[SIL])|[%&])?/},{begin:/&H[\dA-F_]+((U?[SIL])|[%&])?/},{begin:/&O[0-7_]+((U?[SIL])|[%&])?/},{begin:/&B[01_]+((U?[SIL])|[%&])?/}]},g={className:"label",begin:/^\w+:/},b=e.COMMENT(/'''/,/$/,{contains:[{className:"doctag",begin:/<\/?/,end:/>/}]}),m=e.COMMENT(null,/$/,{variants:[{begin:/'/},{begin:/([\t ]|^)REM(?=\s)/}]});return{name:"Visual Basic .NET",aliases:["vb"],case_insensitive:!0,classNameAliases:{label:"symbol"},keywords:{keyword:"addhandler alias aggregate ansi as async assembly auto binary by byref byval call case catch class compare const continue custom declare default delegate dim distinct do each equals else elseif end enum erase error event exit explicit finally for friend from function get global goto group handles if implements imports in inherits interface into iterator join key let lib loop me mid module mustinherit mustoverride mybase myclass namespace narrowing new next notinheritable notoverridable of off on operator option optional order overloads overridable overrides paramarray partial preserve private property protected public raiseevent readonly redim removehandler resume return select set shadows shared skip static step stop structure strict sub synclock take text then throw to try unicode until using when where while widening with withevents writeonly yield",built_in:"addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort",type:"boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort",literal:"true false nothing"},illegal:"//|\\{|\\}|endif|gosub|variant|wend|^\\$ ",contains:[r,d,o,i,g,b,m,{className:"meta",begin:/[\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\b/,end:/$/,keywords:{keyword:"const disable else elseif enable end externalsource if region then"},contains:[m]}]}}return gn=n,gn}var bn,st;function ea(){if(st)return bn;st=1;function n(e){e.regex;const t=e.COMMENT(/\(;/,/;\)/);t.contains.push("self");const r=e.COMMENT(/;;/,/$/),d=["anyfunc","block","br","br_if","br_table","call","call_indirect","data","drop","elem","else","end","export","func","global.get","global.set","local.get","local.set","local.tee","get_global","get_local","global","if","import","local","loop","memory","memory.grow","memory.size","module","mut","nop","offset","param","result","return","select","set_global","set_local","start","table","tee_local","then","type","unreachable"],l={begin:[/(?:func|call|call_indirect)/,/\s+/,/\$[^\s)]+/],className:{1:"keyword",3:"title.function"}},a={className:"variable",begin:/\$[\w_]+/},s={match:/(\((?!;)|\))+/,className:"punctuation",relevance:0},c={className:"number",relevance:0,match:/[+-]?\b(?:\d(?:_?\d)*(?:\.\d(?:_?\d)*)?(?:[eE][+-]?\d(?:_?\d)*)?|0x[\da-fA-F](?:_?[\da-fA-F])*(?:\.[\da-fA-F](?:_?[\da-fA-D])*)?(?:[pP][+-]?\d(?:_?\d)*)?)\b|\binf\b|\bnan(?::0x[\da-fA-F](?:_?[\da-fA-D])*)?\b/},o={match:/(i32|i64|f32|f64)(?!\.)/,className:"type"},i={className:"keyword",match:/\b(f32|f64|i32|i64)(?:\.(?:abs|add|and|ceil|clz|const|convert_[su]\/i(?:32|64)|copysign|ctz|demote\/f64|div(?:_[su])?|eqz?|extend_[su]\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|nearest|neg?|or|popcnt|promote\/f32|reinterpret\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|store(?:8|16|32)?|sqrt|sub|trunc(?:_[su]\/f(?:32|64))?|wrap\/i64|xor))\b/};return{name:"WebAssembly",keywords:{$pattern:/[\w.]+/,keyword:d},contains:[r,t,{match:[/(?:offset|align)/,/\s*/,/=/],className:{1:"keyword",3:"operator"}},a,s,l,e.QUOTE_STRING_MODE,o,i,c]}}return bn=n,bn}var R=Ei;R.registerLanguage("xml",hi());R.registerLanguage("bash",Ni());R.registerLanguage("c",yi());R.registerLanguage("cpp",vi());R.registerLanguage("csharp",Ti());R.registerLanguage("css",Si());R.registerLanguage("markdown",wi());R.registerLanguage("diff",Oi());R.registerLanguage("ruby",Ai());R.registerLanguage("go",Ri());R.registerLanguage("graphql",Mi());R.registerLanguage("ini",ki());R.registerLanguage("java",Ii());R.registerLanguage("javascript",xi());R.registerLanguage("json",Ci());R.registerLanguage("kotlin",Li());R.registerLanguage("less",Di());R.registerLanguage("lua",Bi());R.registerLanguage("makefile",Ui());R.registerLanguage("perl",Pi());R.registerLanguage("objectivec",Fi());R.registerLanguage("php",zi());R.registerLanguage("php-template",$i());R.registerLanguage("plaintext",Ki());R.registerLanguage("python",Gi());R.registerLanguage("python-repl",qi());R.registerLanguage("r",Hi());R.registerLanguage("rust",Wi());R.registerLanguage("scss",Yi());R.registerLanguage("shell",Zi());R.registerLanguage("sql",Xi());R.registerLanguage("swift",Vi());R.registerLanguage("yaml",Qi());R.registerLanguage("typescript",Ji());R.registerLanguage("vbnet",ji());R.registerLanguage("wasm",ea());R.HighlightJS=R;R.default=R;var na=R;export{na as a,Ei as c};
