<?php

namespace app\common\command;

use app\common\model\file_task\ExportTask;
use app\common\model\promotion\Activity;
use app\common\model\file\File;
use app\common\enum\FileEnum;
use app\common\service\ConfigService;
use app\common\service\FileService;
use app\common\service\storage\Driver as StorageDriver;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\facade\Db;

class HandleExportTask extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('file_task')
            ->addOption('type', 't', Option::VALUE_OPTIONAL, '业务类型')
            ->setDescription('导出任务处理');
    }

    protected function execute(Input $input, Output $output)
    {
        $type = $input->getOption('type');
        if (!in_array($type, [11, 12])) $type = 11;

        $task = ExportTask::where('status', 0)->where("business_type", $type)->order("id", "asc")->find();
        if (!$task) return;

        try {
            // 开始处理
            $task->status = 1;
            $task->save();

            $fileUrl = '';
            switch ($task->business_type) {
                case 11:
                    $fileUrl = $this->getUserOrderStats($task->param);
                    break;
                case 12:
                    $fileUrl = $this->getAgentOrderStats($task->param);
                    break;
            }

            $task->status = 2;
            $task->file_url = $fileUrl;
            $task->save();
        } catch (\Exception $e) {
            $task->status = 3;
            $task->error = $e->getMessage();
            $task->save();
        }
    }

    private function getUserOrderStats($param){
        $actIds = $param["act_ids"];
        if(empty($actIds)){
            $query = Activity::where("scene", $param["scene"]);
            if($param["status"]){
                $query->where("status", $param["status"]);
            }
            $actIds = $query->column("id");
        }
        if (empty($actIds)) throw new Exception("没有符合条件的活动");

        $activityIds = [];
        foreach ($actIds as $id){
            $value = intval($id);
            if ($value <= 0) continue;
            if(!in_array($value, $activityIds)) $activityIds[] = $value;
        }
        if (empty($activityIds)) throw new Exception("没有符合条件的活动");

        $subSql = " and reward_time is not null";
        if($param["settle_type"] > 0){
            $subSql .= " and settle_time is null and unreward_time is null and reward_time <= '".date("Y-m-d", time())." 23:59:59'";
        }

        $groupField = "";
        if ($param["scene"] == 5) $groupField = " pid,";

        $data = Db::connect("adb")->query("select left(receive_time, 10) day, activity_id,$groupField user_id, order_type, count(1) num, sum(reward_amount) amount 
from promotion_orders 
where user_id > 0 and activity_id in (".implode(",", $activityIds).") and order_status = 1 and receive_time between ? and ? ".$subSql." 
group by left(receive_time, 10), activity_id,$groupField user_id, order_type", [$param["start"]." 00:00:00", $param["end"]." 23:59:59"]);

        if (empty($data)) throw new Exception("没有符合条件的数据");

        // TODO: 这里可以添加用户订单统计的 Excel 生成和上传逻辑
        // 目前返回空字符串保持现有逻辑不变
        return '';
    }

    private function getAgentOrderStats($param){
        $actIds = $param["act_ids"];
        if(empty($actIds)){
            $query = Activity::where("scene", $param["scene"]);
            if($param["status"]){
                $query->where("status", $param["status"]);
            }
            $actIds = $query->column("id");
        }
        if (empty($actIds)) throw new Exception("没有符合条件的活动");

        $activityIds = [];
        foreach ($actIds as $id){
            $value = intval($id);
            if ($value <= 0) continue;
            if(!in_array($value, $activityIds)) $activityIds[] = $value;
        }
        if (empty($activityIds)) throw new Exception("没有符合条件的活动");

        $subSql = " and reward_time is not null";
        if($param["settle_type"] > 0){
            $subSql .= " and agent_settle_time is null and unreward_time is null and reward_time <= '".date("Y-m-d", time())." 23:59:59'";
        }

        $groupField = "";
        if ($param["scene"] == 5) $groupField = " pid,";

        $data = Db::connect("adb")->query("select left(receive_time, 10) day,activity_id,$groupField agent_id, user_id, order_type, count(1) num, sum(agent_reward_amount) amount 
from promotion_orders 
where agent_id > 0 and activity_id in (".implode(",", $activityIds).") and order_status = 1 and receive_time between ? and ? ".$subSql." 
group by left(receive_time, 10), activity_id,$groupField agent_id, user_id, order_type", [$param["start"]." 00:00:00", $param["end"]." 23:59:59"]);

        if (empty($data)) throw new Exception("没有符合条件的数据");

        // 直接在这里生成 Excel 并上传到 OSS
        // 创建 Spreadsheet 对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置表头
        $headers = ['日期', '活动ID', '代理商ID', '用户ID', '订单类型', '订单数量', '奖励金额'];
        if ($param["scene"] == 5) {
            $headers = ['日期', '活动ID', 'PID', '代理商ID', '用户ID', '订单类型', '订单数量', '奖励金额'];
        }

        // 写入表头
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $col++;
        }

        // 写入数据
        $row = 2;
        foreach ($data as $item) {
            $col = 'A';
            $sheet->setCellValue($col++ . $row, $item['day']);
            $sheet->setCellValue($col++ . $row, $item['activity_id']);

            if ($param["scene"] == 5) {
                $sheet->setCellValue($col++ . $row, $item['pid'] ?? '');
            }

            $sheet->setCellValue($col++ . $row, $item['agent_id']);
            $sheet->setCellValue($col++ . $row, $item['user_id']);
            $sheet->setCellValue($col++ . $row, $item['order_type']);
            $sheet->setCellValue($col++ . $row, $item['num']);
            $sheet->setCellValue($col++ . $row, $item['amount']);

            $row++;
        }

        // 设置边框
        $lastCol = chr(ord('A') + count($headers) - 1);
        $range = 'A1:' . $lastCol . ($row - 1);
        $sheet->getStyle($range)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);

        // 自动调整列宽
        foreach (range('A', $lastCol) as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // 生成文件名
        $fileName = '代理商订单统计_' . date('YmdHis') . '.xlsx';

        // 创建临时文件
        $tempDir = app()->getRuntimePath() . 'temp/';
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0775, true);
        }
        $tempFilePath = $tempDir . $fileName;

        // 保存 Excel 文件
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($tempFilePath);

        // 上传到 OSS
        try {
            // 获取存储配置
            $config = [
                'default' => \app\common\service\ConfigService::get('storage', 'default', 'local'),
                'engine' => \app\common\service\ConfigService::get('storage') ?? ['local' => []],
            ];

            // 创建存储驱动
            $storageDriver = new \app\common\service\storage\Driver($config);

            // 设置上传文件
            $storageDriver->setUploadFileByReal($tempFilePath);

            // 上传文件
            $saveDir = 'uploads/export/' . date('Ymd');
            if (!$storageDriver->upload($saveDir)) {
                throw new Exception($storageDriver->getError());
            }

            // 获取文件名
            $uploadedFileName = $storageDriver->getFileName();
            $fileUri = $saveDir . '/' . $uploadedFileName;

            // 保存文件记录到数据库
            $file = \app\common\model\file\File::create([
                'cid' => 0,
                'type' => \app\common\enum\FileEnum::FILE_TYPE,
                'name' => $fileName,
                'uri' => $fileUri,
                'source' => \app\common\enum\FileEnum::SOURCE_ADMIN,
                'source_id' => 0,
                'create_time' => time(),
            ]);

            // 获取完整的文件 URL
            $fileUrl = \app\common\service\FileService::getFileUrl($fileUri);

            // 删除临时文件
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }

            return $fileUrl;

        } catch (Exception $e) {
            // 删除临时文件
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }
            throw new Exception('文件上传失败: ' . $e->getMessage());
        }
    }
}