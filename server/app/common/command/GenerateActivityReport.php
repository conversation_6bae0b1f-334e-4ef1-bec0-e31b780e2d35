<?php

namespace app\common\command;

use app\common\enum\PromotionEnum;
use app\common\model\promotion\Activity;
use app\common\model\promotion\ActivityItem;
use app\common\model\promotion\ActivityLog;
use app\common\model\promotion\ActivityReport;
use app\common\model\promotion\Order;
use app\crmapi\logic\ActivityLogic;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class GenerateActivityReport extends Command
{
    protected function configure()
    {
        $this->setName('gen_activity_report')
            ->addOption('activity', 'a', Option::VALUE_OPTIONAL, '活动ID')
            ->addOption('activity_create_time', 't', Option::VALUE_OPTIONAL, '活动创建时间')
            ->addOption('user', 'u', Option::VALUE_OPTIONAL, '用户ID')
            ->addOption('day', 'd', Option::VALUE_OPTIONAL, '日期')
            ->addOption('event', 'e', Option::VALUE_OPTIONAL, '事件')
            ->setDescription("生成活动报表");
    }

    protected function execute(Input $input, Output $output)
    {
        $activityId = intval($input->getOption('activity'));
        if ($activityId <= 0) return false;

        $activityCreateTime = intval($input->getOption('activity_create_time'));
        if ($activityCreateTime <= 0) return false;
        $activityCreateTimeStr = date("Y-m-d H:i:s", $activityCreateTime);

        $userId = intval($input->getOption('user'));
        if ($userId < 0) return false;

        $day = $input->getOption('day');
        if ($day == "") return false;
        $dayRange = [$day . " 00:00:00", $day . " 23:59:59"];

        $event = intval($input->getOption('event'));
        if ($event <= 0) return false;

        $query = ActivityItem::where("activity_id", $activityId)->where("user_id", $userId);
        switch ($event) {
            case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH:
            case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH:
            case PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ:
            case PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ:
                $reportData = ["total_num" => 0, "out_num" => 0, "new_num" => 0, "new_out_num" => 0, "active_num" => 0, "reward_num" => 0, "reward_amount" => 0];
                $items = $query->whereBetween("third_in_time", $dayRange)->select();
                break;
            case PromotionEnum::ACTIVITY_CONDITION_REGISTER:
                $reportData = ["register_num" => 0];
                $items = $query->whereBetween("register_time", $dayRange)->select();
                break;
            case PromotionEnum::ACTIVITY_CONDITION_FIRST_ORDER:
            case PromotionEnum::ACTIVITY_CONDITION_ORDER:
                if($activityCreateTime == PromotionEnum::ACTIVITY_SCENE_PWD) {
                    $sumStatis = Order::where("activity_id", $activityId)->where("user_id", $userId)->whereBetween("receive_time", $dayRange)
                        ->whereIn("order_new", [PromotionEnum::ORDER_TAG_PWD, PromotionEnum::ORDER_TAG_PWD_DEDUCT])->where("order_status", PromotionEnum::ORDER_STATUS_VALID)
                        ->field("count(1) order_total_num, count(if(order_new = 2, 1, null)) order_valid_num, count(if(reward_time is not null and unreward_time is null, 1, null)) reward_num, sum(reward_amount) reward_amount")->select();
                    $reportData = [
                        "order_total_num" => $sumStatis[0]->order_total_num ?? 0,
                        'order_valid_num' => $sumStatis[0]->order_valid_num ?? 0,
                        'order_new_num' => 0,
                        "reward_num" => $sumStatis[0]->reward_num ?? 0,
                        "reward_amount" => $sumStatis[0]->reward_amount ?? 0,
                    ];
                    $act = Activity::where("id", $activityId)->find();
                    if (array_intersect($act->order_type, [PromotionEnum::ACTIVITY_ORDER_TYPE_BWC, PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN, PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU])) {  // 闪购
                        $statis = Db::query("select order_type type, count(1) num, sum(reward_amount) amount from promotion_orders where activity_id = ? and user_id = ? and receive_time between ? and ? and order_new = 2 and order_status = 1 and reward_time is not null GROUP BY order_type", [$activityId, $userId, $dayRange[0], $dayRange[1]]);
                        $reportData["statis"] = $statis ?: [];
                    }
                }elseif($activityCreateTime == PromotionEnum::ACTIVITY_SCENE_LINK){
                    $act = Activity::where("id", $activityId)->find();
                    $timeField = "create_time";
                    if (array_intersect($act->order_type, [PromotionEnum::ACTIVITY_ORDER_TYPE_BWC, PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN, PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU]))  {  // 闪购
                        $timeField = "receive_time";
                    }
                    $sumStatis = Order::where("activity_id", $activityId)->where("user_id", $userId)->whereBetween($timeField, $dayRange)
                        ->field("count(1) order_total_num, count(if(order_status = 1, 1, null)) order_valid_num, count(if(reward_time is not null and unreward_time is null, 1, null)) reward_num, sum(reward_amount) reward_amount")->select();
                    $reportData = [
                        "order_total_num" => $sumStatis[0]->order_total_num ?? 0,
                        'order_valid_num' => $sumStatis[0]->order_valid_num ?? 0,
                        'order_new_num' => 0,
                        "reward_num" => $sumStatis[0]->reward_num ?? 0,
                        "reward_amount" => $sumStatis[0]->reward_amount ?? 0,
                    ];
                    if(array_intersect($act->order_type, [PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU, PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU_KA])) { // 闲鱼
                        $statis = Db::query("select order_type type, count(1) num, sum(reward_amount) amount, count(if(receive_time is not null, 1, null)) receive, sum(if(receive_time is not null, reward_amount, 0)) commission from promotion_orders where activity_id = ? and user_id = ? and create_time between ? and ? and order_type in (2600,2601,2602,2603,2604,2605) and order_status = 1 and reward_time is not null GROUP BY order_type", [$activityId, $userId, $dayRange[0], $dayRange[1]]);
                        //[{"type":2600,"num":1,"amount":"3.00","receive":0}]
                        if ($statis && $day >= "2025-06-11") {
                            $statis2 = Db::query("select order_type type, count(1) num, sum(reward_amount) amount, count(if(receive_time is not null, 1, null)) receive from promotion_orders where activity_id = ? and user_id = ? and create_time between ? and ? and order_type in (2600,2601,2602,2603,2604,2605) and order_new = 1 and order_status = 1 and reward_time is not null GROUP BY order_type", [$activityId, $userId, $dayRange[0], $dayRange[1]]);
                            if ($statis2){
                                foreach ($statis2 as $ss) {
                                    $index = -1;
                                    foreach ($statis as $k => $s) {
                                        if ($s["type"] == $ss["type"]) {
                                            $index = $k;
                                            break;
                                        }
                                    }
                                    if ($index >= 0) {
                                        $statis[$index]["high_num"] = $ss["num"];
                                        $statis[$index]["high_amount"] = $ss["amount"];
                                        $statis[$index]["high_receive"] = $ss["receive"];
                                    }
                                }
                            }
                        }
                        $reportData["statis"] = $statis ?: [];
                    }elseif (array_intersect($act->order_type, [PromotionEnum::ACTIVITY_ORDER_TYPE_BWC, PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN, PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU])) {  // 闪购
                        $statis = Db::query("select order_type type, count(1) num, sum(reward_amount) amount from promotion_orders where activity_id = ? and user_id = ? and receive_time between ? and ? and order_status = 1 and reward_time is not null GROUP BY order_type", [$activityId, $userId, $dayRange[0], $dayRange[1]]);
                        $reportData["statis"] = $statis ?: [];
                    }
                }else{
                    $sumStatis = Order::where("activity_id", $activityId)->where("user_id", $userId)->whereBetween("create_time", $dayRange)
                        ->field("count(1) order_total_num, count(if(order_status = 1, 1, null)) order_valid_num, count(if(order_status = 1 and order_new = 1, 1, null)) order_new_num")->select();
                    $reportData = [
                        "order_total_num" => $sumStatis[0]->order_total_num ?? 0,
                        'order_valid_num' => $sumStatis[0]->order_valid_num ?? 0,
                        'order_new_num' => $sumStatis[0]->order_new_num ?? 0,
                    ];
                }
                break;
            case PromotionEnum::ACTIVITY_CONDITION_CARD:
                $reportData = ["card_num" => 0];
                $items = $query->whereBetween("card_time", $dayRange)->select();
                break;
            default:
                return false;
        }

        if(($items ?? [])){
            foreach ($items as $item) {
                switch ($event) {
                    case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH:
                    case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH:
                    case PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ:
                    case PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ:
                        if ($item->third_in_time > $activityCreateTimeStr) {
                            if ($item->third_out_time) {
                                if ($item->third_in_time < $item->third_out_time) {
                                    $reportData["total_num"] += 1;
                                    $reportData["out_num"] += 1;
                                    $reportData["new_num"] += ($item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_NEW ? 1 : 0);
                                    $reportData["new_out_num"] += ($item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_NEW ? 1 : 0);
                                    $reportData["active_num"] += ($item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_ACTIVE ? 1 : 0);
                                }
                            } else {
                                $reportData["total_num"] += 1;
                                $reportData["out_num"] += 0;
                                $reportData["new_num"] += ($item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_NEW ? 1 : 0);
                                $reportData["active_num"] += ($item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_ACTIVE ? 1 : 0);
                            }
                            if($item->reward_time && empty($item->unreward_time)) {
                                $reportData["reward_num"] += 1;
                                $reportData["reward_amount"] += $item->reward_amount;
                            }
                        }
                        break;
                    case PromotionEnum::ACTIVITY_CONDITION_REGISTER:
                        if ($item->register_time > $activityCreateTimeStr) {
                            $reportData["register_num"] += 1;
                        }
                        break;
                    case PromotionEnum::ACTIVITY_CONDITION_CARD:
                        if ($item->card_time > $activityCreateTimeStr) {
                            $reportData["card_num"] += 1;
                        }
                        break;
                }
            }
        }

        // 报表
        $report = ActivityReport::where("activity_id", $activityId)->where("user_id", $userId)->where("day", $day)->find();
        if ($report) {
            ActivityReport::where("id", $report->id)->update($reportData);
        } else {
            ActivityReport::create(array_merge($reportData, ["activity_id" => $activityId, "user_id" => $userId, "day" => $day]));
        }
        return true;
    }

    public function activityLog()
    {
        ActivityLog::where("id", 708)->chunk(200, function($logs){
            foreach($logs as $log){
                $params = $log->extra["req"];
                foreach ($log->extra as $k => $v){
                    if($k != "req"){
                        $params["extra"][$k] = $v;
                    }
                }
                $result = (new ActivityLogic())->log($params, $log->id);
                if ($result === false) {
                    dd(ActivityLogic::getError());
                }
            }
        });
        return true;
    }
}