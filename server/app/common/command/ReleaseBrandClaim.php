<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\command;

use app\common\model\brand\ClaimRec;
use app\common\service\ConfigService;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Log;


class ReleaseBrandClaim extends Command
{
    protected function configure()
    {
        $this->setName('release_brand_claim')
            ->setDescription('释放品牌认领');
    }


    protected function execute(Input $input, Output $output)
    {
        try {
            $days = intval(ConfigService::get('brand', 'claim_exp_day', 7));
            ClaimRec::where("release_time", 0)
                ->where("succ_time", 0)
                ->where("claim_time", "<", time() - $days * 86400)
                ->update(["release_time" => time()]);
            return true;
        } catch (\Exception $e) {
            Log::error('释放品牌认领失败，原因:' . $e->getMessage());
            return false;
        }
    }
}