<?php

namespace app\common\service\storage\engine;


/**
 * 本地文件驱动
 * Class Local
 * @package app\common\library\storage\drivers
 */
class Local extends Server
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 上传
     * @param $save_dir (保存路径)
     * @return bool
     */
    public function upload($save_dir)
    {
        // 如果是内部上传（通过 setUploadFileByReal 设置的文件）
        if ($this->isInternal) {
            // 确保目标目录存在
            $targetDir = public_path() . $save_dir;
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0775, true);
            }

            // 复制文件到目标位置
            $targetPath = $targetDir . '/' . $this->fileName;
            if (!copy($this->getRealPath(), $targetPath)) {
                $this->error = '文件复制失败';
                return false;
            }
            return true;
        }

        // 验证文件并上传
        $info = $this->file->move($save_dir, $this->fileName);
        if (empty($info)) {
            $this->error = $this->file->getError();
            return false;
        }
        return true;
    }

    public function fetch($url, $key=null) {
        $videoContent = file_get_contents($url);

        if (!$videoContent) {
            return false;  // 下载失败
        }
        $result = file_put_contents($key, $videoContent);

        if (!$result) {
            return false;  // 保存到本地失败
        }

        return true;  // 下载并保存成功
    }

    /**
     * 删除文件
     * @param $fileName
     * @return bool|mixed
     */
    public function delete($fileName)
    {
        $check = strpos($fileName, '/');
        if ($check !== false && $check == 0) {
            // 文件所在目录
            $fileName = substr_replace($fileName,"",0,1);
        }
        $filePath = public_path() . "{$fileName}";
        return !file_exists($filePath) ?: unlink($filePath);
    }

    /**
     * 返回文件路径
     * @return mixed
     */
    public function getFileName()
    {
        return $this->fileName;
    }
}
