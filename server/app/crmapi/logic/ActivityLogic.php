<?php

namespace app\crmapi\logic;

use app\common\enum\AccountLogEnum;
use app\common\enum\PromotionEnum;
use app\common\logic\BaseLogic;
use app\common\model\byn\WxQrcode;
use app\common\model\byn_data\PromotionPage;
use app\common\model\promotion\Activity;
use app\common\model\promotion\ActivityItem;
use app\common\model\promotion\ActivityLeak;
use app\common\model\promotion\ActivityLog;
use app\common\model\promotion\ActivityReport;
use app\common\model\promotion\AgentLog;
use app\common\model\promotion\AgentRebate;
use app\common\model\promotion\ElmPwd;
use app\common\model\promotion\Order;
use app\common\model\promotion\OrderType;
use app\common\model\promotion\Plan;
use app\common\model\promotion\PwdAct;
use app\common\model\promotion\QrcodeUserLog;
use app\common\model\promotion\QwGroup;
use app\common\model\promotion\QwGroupCode;
use app\common\model\promotion\QwQrcode;
use app\common\model\promotion\User;
use app\common\model\promotion\UserAccountLog;
use app\common\model\promotion\UserPwdAct;
use app\common\model\promotion\UserPwdActRec;
use app\common\service\ConfigService;
use app\common\service\LeakService;
use app\common\service\YcService;
use think\facade\Cache;
use think\facade\Console;
use think\facade\Db;
use think\facade\Log;
use zjkal\ChinaHoliday;
use function Qcloud\Cos\startWith;

class ActivityLogic extends BaseLogic
{

    public function log(array $params, $logId = 0)
    {
        try {
            $redis = Cache::store('redis')->handler();
        } catch (\Exception $e) {
            self::$error = "LOG_REDIS初始化失败：" . $e->getMessage();
            return false;
        }

        $logKey = sprintf("act_log_%d_%d_%d_%d_%d_%s", intval($params["activity_id"]), intval($params["qrcode_id"]), intval($params["event_id"]), intval($params["action"]), intval($params["event_time"]), $params["event_value"]);
        if (!$redis->setnx($logKey, 1)) {
            self::$error = "重复请求";
            return false;
        }
        $redis->expire($logKey, 60);

        $activity = Activity::find($params["activity_id"]);
        if (!$activity) {
            self::$error = "未找到该活动";
            $redis->del($logKey);
            return false;
        }

        if ($activity->scene == PromotionEnum::ACTIVITY_SCENE_PWD) { // 口令订单处理
            $res = $this->pwdOrder($activity, $params, $logId, $redis);
            $redis->del($logKey);
            if ($res !== true) {
                self::$error = $res;
                return false;
            }
            return true;
        }

        Db::startTrans();
        try {
            if (in_array($params["event_id"], [PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH, PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH, PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ, PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ])) {
                if (!in_array($params["event_id"], $activity->condition)) throw new \Exception("该活动不支持的事件");
            }

            if ($activity->scene == PromotionEnum::ACTIVITY_SCENE_DTQ) {
                $qrcode = QwGroupCode::where("id", $params["qrcode_id"])->where("act_id", $activity->id)->find();
            } elseif ($activity->scene == PromotionEnum::ACTIVITY_SCENE_SJQ) {
                $qrcode = QwGroup::where("id", $params["qrcode_id"])->where("act_id", $activity->id)->find();
            } elseif ($activity->scene == PromotionEnum::ACTIVITY_SCENE_GH) {
                $qrcode = QwQrcode::where("id", $params["qrcode_id"])->where("act_id", $activity->id)->find();
            } else {
                $qrcode = WxQrcode::where("id", $params["qrcode_id"])->where("act_id", $activity->id)->find();
            }
            if (!$qrcode) throw new \Exception("未找到该活动物料");

            $eventTime = date("Y-m-d H:i:s", $params["event_time"]);
            if ($params["event_id"] == PromotionEnum::ACTIVITY_CONDITION_ORDER) { // 订单找第一次关注进来的推广人
                $unionId = $params["extra"]["unionid"];
                $firstInItem = ActivityItem::where("unionid", $unionId)->where("qrcode_id", $qrcode->id)->where("third_new", PromotionEnum::ACTIVITY_ITEM_THIRD_NEW)->order("id", "asc")->find();
                $userId = $firstInItem->user_id ?? 0;
            } else {
                //找码与用户的关联
                $assign = QrcodeUserLog::where("qrcode_id", $qrcode->id)->where("type", $activity->scene)
                    ->where("created_at", "<", $eventTime)
                    ->order("created_at", "desc")->order("id", "desc")->find();
                $userId = $assign->user_id ?? 0;
            }

            // 插入回传日志
            if ($logId > 0) {
                ActivityLog::where("id", $logId)->update(["user_id" => $userId]);
            } else {
                $extra = $params["extra"] ?? [];
                $extra["req"] = $params;
                unset($extra["req"]["extra"]);
                $log = ActivityLog::create([
                    "activity_id" => $activity->id,
                    "user_id" => $userId,
                    "event" => $params["event_id"],
                    'event_type' => $params["action"],
                    "event_value" => $params["event_value"],
                    "extra" => $extra,
                ]);
            }

            // 处理活动用户任务数据
            $itemData = $this->getItemData($params, $activity->id, $userId, $activity->created_at);
            $item = null;
            if ($itemData["out_user_id"]) { // 有外部用户ID
                $item = ActivityItem::where("activity_id", $activity->id)->where("user_id", $userId)->where("out_user_id", $itemData["out_user_id"])->find();
            }
            if (!$item && $itemData["unionid"]) { // 找不到则用unionid找
                $item = ActivityItem::where("activity_id", $activity->id)->where("user_id", $userId)->where("unionid", $itemData["unionid"])->find();
            }
            if (!$item) {
                if (in_array($params["event_id"], [PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH, PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH, PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ, PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ])) {
                    $item = ActivityItem::where("activity_id", $activity->id)->where("user_id", $userId)->where("third_id", $itemData["third_id"])->find();
                }
            }

            $orderId = $itemData["order_id"] ?? 0;
            if (isset($itemData["order_id"])) unset($itemData["order_id"]);

            // 保存活动用户任务数据
            if ($item) {
                foreach ($item->extra as $k => $v) {
                    if (isset($itemData["extra"][$k])) {
                        if (empty($itemData["extra"][$k])) $itemData["extra"][$k] = $v;
                    } else {
                        $itemData["extra"][$k] = $v;
                    }
                }
                if ($item->qrcode_id <= 0) $item->qrcode_id = $qrcode->id;
                $item->extra = $itemData["extra"];
                foreach ($itemData as $k => $v) {
                    if (in_array($k, ["third_id", "third_in_time", "third_out_time", "register_time", "card_time", "unionid", "out_user_id"])) {
                        if (empty($item->$k) && $v) $item->$k = $v;
                    } elseif (in_array($k, ["order_num", "order_new_num"])) {
                        if ($v != $item->$k) $item->$k = $v;
                    } elseif ($k == "third_new") {
                        if ($v > 0) $item->$k = $v;
                    }
                }
                $item->save();
            } else {
                $item = ActivityItem::create(array_merge($itemData, ["activity_id" => $activity->id, "user_id" => $userId, 'qrcode_id' => $qrcode->id]));
            }

            // 判断完成情况
            $finishRes = $this->validateFinish($activity->condition, $activity->created_at, $item);

            // 奖励
            if ($userId > 0) {
                if ($finishRes["cpa"] || $finishRes["cps"]) {
                    $userRole = User::where("id", $userId)->value("role_id");
                    $plans = Plan::where("activity_id", $activity->id)->where("role_id", $userRole)->select();
                }
                $this->cpaReward($finishRes["cpa"], $userId, $item, $plans, $eventTime, $redis);
                if ($orderId) {
                    $orderInfo = Order::find($orderId);
                    $isMatched = $orderInfo->activity_id == $item->activity_id && $orderInfo->user_id == $item->user_id && $orderInfo->out_user_id == $item->out_user_id && $orderInfo->create_time > $activity->created_at && $orderInfo->order_status == PromotionEnum::ORDER_STATUS_VALID;
                    $this->cpsReward($isMatched, $userId, $orderInfo, $item, $plans);
                }
            }
            Db::commit();
        } catch (\Exception  $e) {
            Db::rollback();
            self::$error = $e->getMessage();

            $redis->del($logKey);
            return false;
        }
        $redis->sAdd("gen_activity_report_queue", json_encode(['a' => $activity->id, 't' => strtotime($activity->created_at), 'u' => $userId, 'd' => ($finishRes["report"][$params["event_id"]] ?? substr($eventTime, 0, 10)), 'e' => $params["event_id"]]));
//        Console::call('gen_activity_report', ['-a' . $activity->id, '-t' . strtotime($activity->created_at), '-u' . $userId, '-d' . ($finishRes["report"][$params["event_id"]] ?? substr($eventTime, 0, 10)), '-e' . $params["event_id"]]);
        return true;
    }

    private function getItemData($params, $activityId, $userId, $activityCreateTime)
    {
        $itemData = [
            "out_user_id" => $params["out_user_id"] && $params["out_user_id"] != "0" ? $params["out_user_id"] : "",
            "extra" => $params["extra"] ?? [],
            "unionid" => $params["extra"]["unionid"] ?? "",
        ];
        $eventTime = date("Y-m-d H:i:s", $params["event_time"]);
        switch ($params["event_id"]) {
            case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH:
            case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH:
            case PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ:
            case PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ:
                $itemData["third_id"] = $params["event_value"];
                if ($params["action"] == 1) {
                    $itemData["third_in_time"] = $eventTime;
                } elseif ($params["action"] == 2) {
                    $itemData["third_out_time"] = $eventTime;
                }
                if ($params["new"] == PromotionEnum::ACTIVITY_ITEM_THIRD_ACTIVE) {
                    $has = ActivityItem::where("third_id", $itemData["third_id"])->find();
                    if ($has) $params["new"] = PromotionEnum::ACTIVITY_ITEM_THIRD_OLD;
                }
                $itemData["third_new"] = $params["new"];
                break;
            case PromotionEnum::ACTIVITY_CONDITION_REGISTER:
                $itemData["register_time"] = $eventTime;
                break;
            case PromotionEnum::ACTIVITY_CONDITION_FIRST_ORDER:
                // 保存订单数据
                $order = Order::where("order_id", $params["event_value"])->find();
                if (!$order) {
                    $order = new Order();
                    $order->order_new = $params["new"];
                }
                $collectOrder = Db::connect("byn")->query("select * from collect_order where id=:id limit 1", ['id' => $params["event_value"]]);
                $order->activity_id = $activityId;
                $order->user_id = $userId;
                $order->out_user_id = $itemData["out_user_id"];
                $order->order_id = $params["event_value"];
                if ($params["new"] > PromotionEnum::ORDER_TAG_NONE) $order->order_new = $params["new"];
                $order->parent_order_id = $collectOrder[0]["order_no"] ?? "";
                $order->sub_order_id = $collectOrder[0]["sub_order_no"] ?? "";
                $order->order_type = $collectOrder[0]["type"] ?? 0;
                $order->order_status = $params["action"];
                $order->item_title = $collectOrder[0]["goods_name"] ?? "";
                $order->item_cover = $collectOrder[0]["goods_pic"] ?? "";
                $order->order_price = $collectOrder[0]["order_price"] ?? 0;
                $order->pay_price = $collectOrder[0]["pay_amount"] ?? 0;
                $order->ratio = ($collectOrder[0]["out_commission_rate"] ?? 0) - ($collectOrder[0]["out_platform_commission_rate"] ?? 0);
                $order->ratio = max($order->ratio, 0);
                $order->pre_commission = ($collectOrder[0]["pre_commission"] ?? 0) - ($collectOrder[0]["out_platform_commission_fee"] ?? 0);
                $order->pre_commission = max($order->pre_commission, 0);
                $order->commission = ($collectOrder[0]["out_commission_fee"] ?? 0) - ($collectOrder[0]["out_platform_commission_fee"] ?? 0);
                $order->commission = max($order->commission, 0);
                $order->subsidy = ($collectOrder[0]["out_activity_fee"] ?? 0) - ($collectOrder[0]["out_activity_service_fee"] ?? 0);
                $order->subsidy = max($order->subsidy, 0);
                $order->pid = $collectOrder[0]["pid"] ?? "";
                $order->sid = $collectOrder[0]["sid"] ?? "";
                $order->city = $collectOrder[0]["city"] ?? "";
                $order->create_time = $collectOrder[0]["create_time"] ?? $eventTime;
                if (($collectOrder[0]["receive_time"] ?? "")) $order->receive_time = $collectOrder[0]["receive_time"];
                $order->save();

                $count = Order::where("activity_id", $activityId)->where("user_id", $userId)->where("out_user_id", $itemData["out_user_id"])
                    ->where("create_time", ">", $activityCreateTime)->where("order_status", PromotionEnum::ORDER_STATUS_VALID)->where("order_new", PromotionEnum::ORDER_TAG_NEW)->count();
                $itemData["order_new_num"] = $count;
                break;
            case PromotionEnum::ACTIVITY_CONDITION_CARD:
                $itemData["card_time"] = $eventTime;
                break;
            case PromotionEnum::ACTIVITY_CONDITION_ORDER:
                // 保存订单数据
                $order = Order::where("order_id", $params["event_value"])->find();
                if (!$order) {
                    $order = new Order();
                    $order->order_new = $params["new"];
                }
                $collectOrder = Db::connect("byn")->query("select * from collect_order where id=:id limit 1", ['id' => $params["event_value"]]);
                $order->activity_id = $activityId;
                $order->user_id = $userId;
                $order->out_user_id = $itemData["out_user_id"];
                $order->order_id = $params["event_value"];
                if ($params["new"] > PromotionEnum::ORDER_TAG_NONE) $order->order_new = $params["new"];
                $order->parent_order_id = $collectOrder[0]["order_no"] ?? "";
                $order->sub_order_id = $collectOrder[0]["sub_order_no"] ?? "";
                $order->order_type = $collectOrder[0]["type"] ?? 0;
                $order->order_status = $params["action"];
                $order->item_title = $collectOrder[0]["goods_name"] ?? "";
                $order->item_cover = $collectOrder[0]["goods_pic"] ?? "";
                $order->order_price = $collectOrder[0]["order_price"] ?? 0;
                $order->pay_price = $collectOrder[0]["pay_amount"] ?? 0;
                $order->ratio = ($collectOrder[0]["out_commission_rate"] ?? 0) - ($collectOrder[0]["out_platform_commission_rate"] ?? 0);
                $order->ratio = max($order->ratio, 0);
                $order->pre_commission = ($collectOrder[0]["pre_commission"] ?? 0) - ($collectOrder[0]["out_subsidy_fee"] ?? 0) - ($collectOrder[0]["out_platform_commission_fee"] ?? 0);
                $order->pre_commission = max($order->pre_commission, 0);
                $order->commission = ($collectOrder[0]["out_commission_fee"] ?? 0) - ($collectOrder[0]["out_platform_commission_fee"] ?? 0);
                $order->commission = max($order->commission, 0);
                $order->subsidy = ($collectOrder[0]["out_activity_fee"] ?? 0) - ($collectOrder[0]["out_activity_service_fee"] ?? 0);
                $order->subsidy = max($order->subsidy, 0);
                $order->pid = $collectOrder[0]["pid"] ?? "";
                $order->sid = $collectOrder[0]["sid"] ?? "";
                $order->city = $collectOrder[0]["city"] ?? "";
                $order->create_time = $collectOrder[0]["create_time"] ?? $eventTime;
                if (($collectOrder[0]["receive_time"] ?? "")) $order->receive_time = $collectOrder[0]["receive_time"];
                $order->save();
                $itemData["order_id"] = $order->id;
                break;
        }
        return $itemData;
    }

    private function validateFinish(array $activityCondition, string $activityCreateTime, ActivityItem $item)
    {
        $finishMap = [
            PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH => true, PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH => true, PromotionEnum::ACTIVITY_CONDITION_REGISTER => true, PromotionEnum::ACTIVITY_CONDITION_FIRST_ORDER => true,
            PromotionEnum::ACTIVITY_CONDITION_CARD => true, PromotionEnum::ACTIVITY_CONDITION_ORDER => true, PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ => true, PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ => true];
        $reportDayMap = [PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH => "", PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH => "", PromotionEnum::ACTIVITY_CONDITION_REGISTER => "", PromotionEnum::ACTIVITY_CONDITION_CARD => "", PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ => "", PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ => ""];
        foreach ($activityCondition as $event) {
            $finishMap[$event] = false;
            switch ($event) {
                case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GZH:
                case PromotionEnum::ACTIVITY_CONDITION_FOLLOW_GH:
                case PromotionEnum::ACTIVITY_CONDITION_JOIN_SJQ:
                case PromotionEnum::ACTIVITY_CONDITION_JOIN_DTQ:
                    if ($item->third_in_time && $item->third_in_time > $activityCreateTime) {
                        if ($item->third_out_time) {
                            if ($item->third_in_time < $item->third_out_time) {
                                $reportDayMap[$event] = substr($item->third_in_time, 0, 10);
                                $finishMap[$event] = $item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_NEW;
                            }
                        } else {
                            $reportDayMap[$event] = substr($item->third_in_time, 0, 10);
                            $finishMap[$event] = $item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_NEW;
                        }
                        if ($reportDayMap[$event]) {
                            if ($item->third_new == PromotionEnum::ACTIVITY_ITEM_THIRD_ACTIVE) {
                                $switch = intval(ConfigService::get("crm", "active_user_reward_switch", 0));
                                if ($switch > 0) {
                                    $finishMap[$event] = $item->third_new > PromotionEnum::ACTIVITY_ITEM_THIRD_OLD;
                                }
                            }
                            if ($finishMap[$event]) {
                                $uniqueSwitch = intval(ConfigService::get("crm", "unionid_user_switch", 0));
                                if ($uniqueSwitch > 0 && $item->unionid) {
                                    $has = ActivityItem::where("id", "<>", $item->id)->where("unionid", $item->unionid)->whereNotNull("reward_time")->find();
                                    if ($has) $finishMap[$event] = false;
                                }
                            }
                        }
                    }
                    break;
                case PromotionEnum::ACTIVITY_CONDITION_REGISTER:
                    if ($item->register_time && $item->register_time > $activityCreateTime) {
                        $finishMap[$event] = true;
                        $reportDayMap[$event] = substr($item->register_time, 0, 10);
                    }
                    break;
                case PromotionEnum::ACTIVITY_CONDITION_FIRST_ORDER:
                    $finishMap[$event] = $item->order_new_num > 0;
                    break;
                case PromotionEnum::ACTIVITY_CONDITION_CARD:
                    if ($item->card_time && $item->card_time > $activityCreateTime) {
                        $finishMap[$event] = true;
                        $reportDayMap[$event] = substr($item->card_time, 0, 10);
                    }
                    break;
                case PromotionEnum::ACTIVITY_CONDITION_ORDER:
                    $finishMap[$event] = true;
                    break;

            }
        }
        $cpa = true;
        $cps = true;
        foreach ($finishMap as $k => $v) {
            if (!$v) {
                if ($k == PromotionEnum::ACTIVITY_CONDITION_ORDER) {
                    $cps = false;
                } else {
                    $cpa = false;
                }
                break;
            }
        }
        return ["cpa" => $cpa, "cps" => $cps, 'report' => $reportDayMap];
    }

    private function cpaReward(bool $finish, $userId, ActivityItem $item, $plans, $eventTime, $redis)
    {
        if ($finish) { // 暂时没有考虑首单情况
            if (empty($item->reward_time)) { // 已经奖励则无需再奖励
                $rewardAmount = 0;
                $matchedPlanCount = 0;
                foreach ($plans as $plan) {
                    if ($plan->reward_type != PromotionEnum::PLAN_REWARD_TYPE_CPA) continue;
                    $settleTime = Plan::settleTime(strtotime($eventTime), $plan->settle_type, $plan->settle_day);
                    switch ($plan->reward_mode) {
                        case PromotionEnum::PLAN_REWARD_MODE_STEP: // 阶梯
                            $eventDay = substr($eventTime, 0, 10);
                            $num = ActivityItem::where("activity_id", $item->activity_id)->where("user_id", $item->user_id)->whereNotNull("reward_time")
                                ->whereBetween("third_in_time", [$eventDay . " 00:00:00", $eventDay . " 23:59:59"])->count();
                            $num += 1;
                            $amount = 0;
                            foreach ($plan->rewards as $rk => $rv) {
                                if ($num > $rv["num"]) $amount = $rv["unit"];
                                if ($num >= $rv["num"] && $rv["amount"] > 0) {
                                    $cacheKey = sprintf("extra_reward_%s_%s_%s", $item->user_id, $item->activity_id, $eventDay);
                                    $rewardedStep = $redis->hGetAll($cacheKey) ?: [];
                                    if (isset($rewardedStep["s_$rk"])) continue; // 有奖励过的阶梯 不奖励
                                    if (count($rewardedStep) > 0) { // 有比当前粉丝数大的奖励，不奖励
                                        $iser = true;
                                        foreach ($rewardedStep as $rsv) {
                                            if ($rsv >= $rv["num"]) {
                                                $iser = false;
                                                break;
                                            }
                                        }
                                        if (!$iser) continue;
                                    }

                                    if ($redis->hSetNx($cacheKey, "s_$rk", intval($rv["num"]))) {
                                        $redis->expireAt($cacheKey, strtotime($eventDay . " 23:59:59") + 3600);

                                        $cacheKey2 = sprintf("extra_reward_%s_%s", $item->user_id, $eventDay);
                                        $reminds = Cache::get($cacheKey2, []);
                                        $reminds[] = [intval($item->activity_id), intval($rv["num"]), $rv["amount"]];
                                        Cache::set($cacheKey2, $reminds, new \DateTime($eventDay . " 23:59:59"));

                                        UserAccountLog::add($userId, AccountLogEnum::BALANCE_INVITE_EXTRA, AccountLogEnum::INC, $rv["amount"], $item->activity_id, sprintf("加粉数达%s", format_amount($rv["num"])), ["plan_id" => $plan->id], 0, $settleTime, strtotime($eventTime));
                                    }
                                }
                            }
                            break;
                        default:
                            $amount = $plan->reward_amount;
                    }
                    $amount += $plan->subsidy_amount;
                    if ($amount > 0) {
                        UserAccountLog::add($userId, AccountLogEnum::BALANCE_INVITE, AccountLogEnum::INC, $amount, $item->id, "", ["plan_id" => $plan->id], 0, $settleTime);
                    }
                    $rewardAmount += $amount;
                    $matchedPlanCount += 1;
                }
                if ($matchedPlanCount > 0) {
                    $item->reward_amount = $rewardAmount;
                    $item->reward_time = date("Y-m-d H:i:s");
                    $item->save();
                }
            }
        } else {
            // 找到未结算的收益
            $accountLogs = UserAccountLog::where("user_id", $userId)->where("change_type", AccountLogEnum::BALANCE_INVITE)->where("action", AccountLogEnum::INC)->where("relate_id", $item->id)->where("status", 0)->select();
            if ($accountLogs->count() > 0) {
                $unRewardAmount = 0;
                $cancelCount = 0;
                foreach ($accountLogs as $al) {
                    $alres = UserAccountLog::where("id", $al->id)->where("status", 0)->update(["status" => 2]);
                    if ($alres > 0) {
                        $unRewardAmount += $al->change_amount;
                        $cancelCount += 1;
                        UserAccountLog::add($userId, $al->change_type, AccountLogEnum::DEC, $al->change_amount, $item->id, "取消关注", $al->extra);
                    }
                }
                if ($cancelCount > 0) {
                    ActivityItem::where("id", $item->id)->update([
                        "reward_amount" => Db::raw("reward_amount-$unRewardAmount"),
                        "unreward_time" => date("Y-m-d H:i:s"),
                    ]);
                }
            }
        }
    }

    private function cpsReward(bool $finish, $userId, $orderInfo, ActivityItem $item, $plans)
    {
        if ($finish) {
            if (empty($orderInfo->reward_time)) {
                $rewardAmount = 0;
                $matchedPlanCount = 0;
                foreach ($plans as $plan) {
                    if ($plan->reward_type != PromotionEnum::PLAN_REWARD_TYPE_CPS) continue;
                    if ($plan->order_type > 0) {
                        $group = OrderType::find($plan->order_type);
                        if (!in_array($orderInfo->order_type, ($group->items ?? []))) continue;
                    }
                    if ($orderInfo->receive_time) {
                        $settleTime = Plan::settleTime(strtotime($orderInfo->receive_time), $plan->settle_type, $plan->settle_day);
                    }
                    $amount = round(($orderInfo->receive_time ? $orderInfo->commission : $orderInfo->pre_commission) * $plan->reward_amount * 0.01 + $orderInfo->subsidy * $plan->subsidy_amount * 0.01, 2);
                    UserAccountLog::add($userId, AccountLogEnum::BALANCE_CPS_ORDER, AccountLogEnum::INC, $amount, $orderInfo->id, "", ["plan_id" => $plan->id, "settle_type" => $plan->settle_type, "settle_day" => $plan->settle_day, "reward_amount" => $plan->reward_amount, "subsidy_amount" => $plan->subsidy_amount, "reward_type" => $plan->reward_type, "reward_mode" => $plan->reward_mode], 0, $settleTime ?? "");
                    $rewardAmount += $amount;
                    $matchedPlanCount += 1;
                }
                if ($matchedPlanCount) {
                    $orderInfo->reward_amount = $rewardAmount;
                    $orderInfo->reward_time = date("Y-m-d H:i:s");
                    $orderInfo->save();
                }
            } elseif ($orderInfo->receive_time) {
                $accountLogs = UserAccountLog::where("user_id", $userId)->where("change_type", AccountLogEnum::BALANCE_CPS_ORDER)->where("action", AccountLogEnum::INC)->where("relate_id", $orderInfo->id)->where("status", 0)->select();
                if ($accountLogs->count() > 0) {
                    foreach ($accountLogs as $al) {
                        $settleTime = Plan::settleTime(strtotime($orderInfo->receive_time), $al->extra['settle_type'], $al->extra['settle_day']);
                        $amount = round(($orderInfo->receive_time ? $orderInfo->commission : $orderInfo->pre_commission) * $al->extra['reward_amount'] * 0.01 + $orderInfo->subsidy * $al->extra['subsidy_amount'] * 0.01, 2);
                        $al->change_amount = $amount;
                        $al->settle_time = date("Y-m-d H:i:s", $settleTime);
                        $al->save();
                    }
                    $totalRewardAmount = UserAccountLog::where("user_id", $userId)->where("change_type", AccountLogEnum::BALANCE_CPS_ORDER)->where("relate_id", $orderInfo->id)->field("sum(if(action = 2, -1, 1)*change_amount) sum_amount")->select();
                    $orderInfo->reward_amount = $totalRewardAmount[0]->sum_amount ?? 0;
                    $orderInfo->save();
                }
            }
        } else {
            $accountLogs = UserAccountLog::where("user_id", $userId)->where("change_type", AccountLogEnum::BALANCE_CPS_ORDER)->where("action", AccountLogEnum::INC)->where("relate_id", $orderInfo->id)->where("status", 0)->select();
            if ($accountLogs->count() > 0) {
                $unRewardAmount = 0;
                $cancelCount = 0;
                foreach ($accountLogs as $al) {
                    $alres = UserAccountLog::where("id", $al->id)->where("status", 0)->update(["status" => 2]);
                    if ($alres > 0) {
                        $unRewardAmount += $al->change_amount;
                        $cancelCount += 1;
                        UserAccountLog::add($userId, $al->change_type, AccountLogEnum::DEC, $al->change_amount, $orderInfo->id, "无效订单", $al->extra);
                    }
                }
                if ($cancelCount > 0) {
                    Order::where("id", $orderInfo->id)->update([
                        "reward_amount" => Db::raw("reward_amount-$unRewardAmount"),
                        "unreward_time" => date("Y-m-d H:i:s"),
                    ]);
                }
            }
        }
        $sumData = Order::where("activity_id", $item->activity_id)->where("user_id", $item->user_id)->where("out_user_id", $item->out_user_id)
            ->whereNotNull("reward_time")->whereRaw("unreward_time is null or reward_amount > 0")
            ->field("sum(reward_amount) sum_reward_amount, count(id) count_id")->select();
        $item->order_num = $sumData[0]->count_id ?? 0;
        $item->order_reward_amount = $sumData[0]->sum_reward_amount ?? 0;
        $item->save();
        $collectOrder = Db::connect("byn")->query("select * from collect_order where id=:id limit 1", ['id' => $orderInfo->order_id]);
        if (count($collectOrder) > 0) {
            $distributionCommission = Order::where("id", $orderInfo->id)->value("reward_amount");
            Db::connect("byn")->execute("update collect_order set distribution_commission=:amount where id=:id", ['amount' => $distributionCommission, 'id' => $orderInfo->order_id]);
            Db::connect("byn")->execute("update orders_" . substr($orderInfo->create_time, 0, 4) . " set distribution_commission=:amount where id=:id", ['amount' => $distributionCommission, 'id' => $collectOrder[0]["order_id"]]);
        }
    }

    private function pwdOrder(Activity $activity, array $params, $logId, $redis)
    {
        return true;
        if (!($params["event_id"] == PromotionEnum::ACTIVITY_CONDITION_ORDER && $params["new"] == PromotionEnum::ORDER_TAG_PWD)) return "该活动不支持的事件";

        $pwd = ElmPwd::where("id", $params["qrcode_id"])->where("activity_id", $activity->id)->find();
        if (!$pwd) return "未找到该活动口令";

        if (!in_array($pwd->pid, ["alsc_12555424_7063002_29575016"])) {
            if (!in_array($pwd->state, [3,4])) return "该口令状态无效";
        }

        $collectOrder = Db::connect("byn")->query("select * from collect_order where id=:id limit 1", ['id' => $params["event_value"]]);
        if (count($collectOrder) <= 0) return "未找到外部订单";
        if (!in_array($collectOrder[0]["type"], [1400,1401,1410,1411])) return "非指定订单类型";
        if (empty($collectOrder[0]['receive_time'])) return "订单未确认收获";
        if (!in_array($pwd->pid, ["alsc_12555424_10260002_28443082", "alsc_12555424_7063002_29519357", "alsc_12555424_7063002_29575016", "alsc_12555424_7063002_29527381"])) {
            $sid = strtolower($collectOrder[0]["sid"]);
            if (!startWith($sid, "search_word_")) return "非口令订单";
            if($pwd->pwd_id > 0) {
                if($sid != "search_word_".$pwd->pwd_id) return "非该口令订单";
            }
        }

        $isOrderType = 0;
        foreach ($activity->order_type as $ot) {
            switch ($ot){
                case PromotionEnum::ACTIVITY_ORDER_TYPE_BWC:
                    $isOrderType = $collectOrder[0]["type"] == 1401 ? $ot : 0;
                    break;
                case PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN:
                    $isOrderType = in_array($collectOrder[0]["type"], [1410, 1411]) ? $ot : 0;
                    break;
                case PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU:
                    if($collectOrder[0]["type"] == 1400) {
                        if(isset($params["order_channel"])) {
                            if($params["order_channel"] == "taobao_shangou") $isOrderType = $ot;
                        }else{
                            $orderSuffix = date("Ym", strtotime($collectOrder[0]["create_time"]));
                            $ycOrders = Db::query("select * from warehouse.cps_orders_{$orderSuffix} where order_type = ? and parent_order_id = ?", [$collectOrder[0]["type"], $collectOrder[0]["order_no"]]);
                            if(count($ycOrders) > 0 && $ycOrders[0]["order_channel"] == "taobao_shangou") {
                                $isOrderType = $ot;
                            }
                        }
                    }
                    break;
            }
            if ($isOrderType > 0) break;
        }

        $order = Order::where("order_id", $params["event_value"])->find();
//        if(!$order) {
//            if($activity->status != PromotionEnum::STATUS_UP) {
//                return "活动已下线";
//            }
//        }

        $userId = intval($order->user_id ?? 0);
        if ($userId <= 0) {
            $assign = QrcodeUserLog::whereIn("qrcode_id", $pwd->id)->where("type", $activity->scene)->where("is_default", 1)->find();
            $userId = $assign->user_id ?? 0;
        }

        $eventTime = date("Y-m-d H:i:s", $params["event_time"]);

        $actCommissionKey = "act_order_commission_" . substr($eventTime, 0, 10) . "_a" . $activity->id . "_u" . $userId."_ot".$isOrderType; $actCommissionLeak = false;
        $actOrderCountKey = "act_order_count_" . substr($eventTime, 0, 10) . "_a" . $activity->id . "_u" . $userId."_ot".$isOrderType; $actOrderCountLeak = false;
        $pwdKey = "pwd_order_" . substr($eventTime, 0, 10) . "_p" . $pwd->pid . "_u" . $userId."_ot".$isOrderType; $pwdLeak = false;

        $eventTimeSecond = strtotime($eventTime);

        Db::startTrans();
        try {
            // 插入回传日志
            if ($logId > 0) {
                ActivityLog::where("id", $logId)->update(["user_id" => $userId]);
            } else {
                $extra = $params["extra"] ?? [];
                $extra["req"] = $params;
                unset($extra["req"]["extra"]);
                ActivityLog::create([
                    "activity_id" => $activity->id,
                    "user_id" => $userId,
                    "event" => $params["event_id"],
                    'event_type' => $params["action"],
                    "event_value" => $params["event_value"],
                    "extra" => $extra,
                ]);
            }

            $commission = $collectOrder[0]["out_commission_fee"] - $collectOrder[0]["out_platform_commission_fee"];
            $commission = max($commission, 0);
            if (!$order) {
                $order = new Order();
                $order->activity_id = $activity->id;
                $order->order_id = $params["event_value"];
                if (intval($params["out_user_id"] ?? 0) > 0) {
                    $order->out_user_id = $params["out_user_id"];
                }
                $order->order_new = PromotionEnum::ORDER_TAG_PWD_CPS;
                if ($collectOrder[0]["type"] == 1410) {
                    $order->order_new = PromotionEnum::ORDER_TAG_PWD_CPA;
                }
                if ($isOrderType > 0) {
                    $order->order_new = $params["new"];

                    // 活动佣金扣量
                    $commissionLeak = ActivityLeak::where("activity_id", $activity->id)->where("type", PromotionEnum::ACTIVITY_LEAK_TYPE_COMMISSION)
                        ->where("order_type", $isOrderType)
                        ->whereRaw("(find_in_set(".intval($userId).", user_ids) or user_ids = '')")
                        ->whereRaw("start_time <= ? and (end_time >= ? or end_time = 0)", [$eventTimeSecond, $eventTimeSecond])
                        ->order(["created_at" => 'desc', 'id' => 'desc'])
                        ->find();
                    if (($commissionLeak->is_enable ?? 0) == 1) {
                        foreach ($commissionLeak->steps as $index => $step) {
                            if ($step["min"] <= $commission && $step["max"] >= $commission) {
                                $actCommissionKey .= "_s" . $index;
                                $actCommissionNum = $redis->incr($actCommissionKey);
                                $actCommissionLeak = true;
                                if ($step["rate"] > 0) {
                                    if ((new LeakService(0, number_format($step["rate"] / 100, 4, ".", "")))->processOrder($actCommissionNum)) {
                                        $order->order_new = PromotionEnum::ORDER_TAG_PWD_DEDUCT;
                                    }
                                }
                                break;
                            }
                        }
                    }

                    // 活动订单数口令
                    if ($order->order_new != PromotionEnum::ORDER_TAG_PWD_DEDUCT) {
                        $orderCountLeak = ActivityLeak::where("activity_id", $activity->id)->where("type", PromotionEnum::ACTIVITY_LEAK_TYPE_ORDER_COUNT)
                            ->where("order_type", $isOrderType)
                            ->whereRaw("(find_in_set(".intval($userId).", user_ids) or user_ids = '')")
                            ->whereRaw("start_time <= ? and (end_time >= ? or end_time = 0)", [$eventTimeSecond, $eventTimeSecond])
                            ->order(["created_at" => 'desc', 'id' => 'desc'])
                            ->find();
                        if (($orderCountLeak->is_enable ?? 0) == 1) {
                            $actOrderCountNum = $redis->incr($actOrderCountKey);
                            $actOrderCountLeak = true;
                            foreach ($orderCountLeak->steps as $step) {
                                if ($step["min"] <= $actOrderCountNum && $step["max"] >= $actOrderCountNum) {
                                    if ($step["rate"] > 0) {
                                        if ((new LeakService(($step["min"] > 0 ? $step["min"] - 1 : 0), number_format($step["rate"] / 100, 4, ".", "")))->processOrder($actOrderCountNum)) {
                                            $order->order_new = PromotionEnum::ORDER_TAG_PWD_DEDUCT;
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                    }

                    // 口令扣量
                    if ($order->order_new != PromotionEnum::ORDER_TAG_PWD_DEDUCT) {
                        $withdrawType = User::where("id", $userId)->value("withdraw_type");
                        $baseLeakRate = 0;
                        $leakNum = 0;
                        $leakRate = 0;
                        if ($withdrawType == PromotionEnum::TX_MODE_PERSON) {
                            $baseLeakRate = 6.6;
                            $leakRate = $baseLeakRate;
                        }
                        if ($pwd->deduct_type > 0) {
                            $city = $collectOrder[0]["city"];
                            if ($city && !in_array($city, $pwd->cities)) {
                                $leakNum += $pwd->order_num;
                                $leakRate += $pwd->deduct_rate;
                            }
                        }
                        if ($leakRate > 0) {
                            if ($leakRate > 100) $leakRate = 100;

                            $pwdNum = $redis->incr($pwdKey);
                            $pwdLeak = true;

                            if ($baseLeakRate > 0 && $leakNum > 0 && $pwdNum <= $leakNum) {
                                if ((new LeakService(0, round($baseLeakRate / 100, 2)))->processOrder($pwdNum)) {
                                    $order->order_new = PromotionEnum::ORDER_TAG_PWD_DEDUCT;
                                }
                            } else {
                                if ((new LeakService($leakNum, round($leakRate / 100, 2)))->processOrder($pwdNum)) {
                                    $order->order_new = PromotionEnum::ORDER_TAG_PWD_DEDUCT;
                                }
                            }
                        }
                    }
                }
            }
            // 保存订单数据
            $order->user_id = $userId;
            $order->parent_order_id = $collectOrder[0]["order_no"];
            $order->sub_order_id = $collectOrder[0]["sub_order_no"];
            $order->order_type = $collectOrder[0]["type"];
            $order->order_status = $params["action"];
            $order->item_title = $collectOrder[0]["goods_name"];
            $order->item_cover = $collectOrder[0]["goods_pic"];
            $order->order_price = $collectOrder[0]["order_price"];
            $order->pay_price = $collectOrder[0]["pay_amount"];
            $order->ratio = $collectOrder[0]["out_commission_rate"] - $collectOrder[0]["out_platform_commission_rate"];
            $order->ratio = max($order->ratio, 0);
            $order->pre_commission = $collectOrder[0]["pre_commission"] - $collectOrder[0]["out_subsidy_fee"] - $collectOrder[0]["out_platform_commission_fee"];
            $order->pre_commission = max($order->pre_commission, 0);
            $order->commission = $commission;
            $order->subsidy = $collectOrder[0]["out_activity_fee"] - $collectOrder[0]["out_activity_service_fee"];
            $order->subsidy = max($order->subsidy, 0);
            $order->pid = $collectOrder[0]["pid"];
            $order->sid = $collectOrder[0]["sid"];
            $order->city = $collectOrder[0]["city"];
            $order->create_time = $collectOrder[0]["create_time"];
            $order->receive_time = $collectOrder[0]["receive_time"] ?: null;

            //奖励
            if ($userId > 0) {
                if (empty($order->reward_time)) { // 没有奖励
                    if ($order->order_new == PromotionEnum::ORDER_TAG_PWD && $order->order_status == PromotionEnum::ORDER_STATUS_VALID) { // 是否可奖励
                        $userRole = User::where("id", $userId)->value("role_id");
                        $canSettle = false;
                        if (in_array($userRole, [54, 61])) { // 高校/媒体 每周三结
                            if ($order->receive_time >= "2024-08-26 00:00:00") {
                                $canSettle = true;
                            }
                        } elseif (in_array($userRole, [55, 60])) { // 高校/媒体 次月结
                            if ($order->receive_time >= "2024-09-01 00:00:00") {
                                $canSettle = true;
                            }
                        } else { // 后援会
                            if ($order->receive_time >= "2024-07-25 00:00:00") {
                                $canSettle = true;
                            }
                        }
                        if ($canSettle) {
                            $plans = Plan::where("activity_id", $activity->id)->where("role_id", $userRole)->where("reward_type", PromotionEnum::PLAN_REWARD_TYPE_CPA)->select();
                            foreach ($plans as $plan) {
                                $isMatchedOrderType = true;
                                if ($plan->order_type > 0) {
                                    $group = OrderType::find($plan->order_type);
                                    $isMatchedOrderType = in_array($order->order_type, ($group->items ?? []));
                                }
                                if ($isMatchedOrderType) {
                                    $order->reward_time = date("Y-m-d H:i:s", Plan::settleTime(strtotime($order->receive_time), $plan->settle_type, $plan->settle_day));
                                    $order->reward_amount = $plan->reward_amount + $plan->subsidy_amount;

                                    $userAgent = AgentLog::where("user_id", $userId)->where("change_time", "<=", strtotime($order->create_time))->order("change_time", "desc")->order("id", "desc")->find();
                                    $agentRate = null;
                                    if ($userAgent) {
                                        if ($userAgent->is_rebate > 0 && $userAgent->level == 1 && $userAgent->pid > 0) {
                                            $parentAgent = AgentLog::where("user_id", $userAgent->pid)->where("change_time", "<=", strtotime($order->create_time))->order("change_time", "desc")->order("id", "desc")->find();
                                            if ($parentAgent) {
                                                if ($parentAgent->is_rebate > 0 && $parentAgent->level == 2) {
                                                    $agentRebate = AgentRebate::where("agent_id", $userAgent->id)->whereRaw("activity_id = 0 or activity_id = ?", [$activity->id])->order("id", "desc")->find();
                                                    $agentRate = $agentRebate->rate ?? 100;
                                                    $order->agent_id = $parentAgent->user_id;
                                                    $order->agent_reward_amount = round($order->reward_amount * (100 - $agentRate) / 100, 2);
                                                    $order->reward_amount -= $order->agent_reward_amount;
                                                }
                                            }
                                        }
                                    }
                                    $order->plan = ["plan_id" => $plan->id, "settle_type" => $plan->settle_type, "settle_day" => $plan->settle_day, "reward_amount" => $plan->reward_amount, "subsidy_amount" => $plan->subsidy_amount, "reward_type" => $plan->reward_type, "reward_mode" => $plan->reward_mode, "agent_rate" => $agentRate];
                                    break;
                                }
                            }
                        }
                    }
                } else { // 奖励了
                    if ($order->order_status != PromotionEnum::ORDER_STATUS_VALID && empty($order->unreward_time) && empty($order->settle_time)) { // 是否需要退回
                        $order->unreward_time = date("Y-m-d H:i:s");
                        $order->reward_amount = 0;
                        $order->agent_reward_amount = 0;
                    }
                }
            }
            $order->save();

            if ($userId > 0) {
                if ($order->order_status == PromotionEnum::ORDER_STATUS_VALID) {
                    $acts = UserPwdAct::with(["act"])->where("user_id", $userId)->whereRaw("end_time is null or end_time > ?", [date("Y-m-d H:i:s")])->select();
                    foreach ($acts as $v) {
                        if (in_array($v->act->type, [PromotionEnum::PWD_ACT_TYPE_PF, PromotionEnum::PWD_ACT_TYPE_ORDER]) && $isOrderType <= 0) continue;
                        $has = UserPwdActRec::where("act_id", $v->act_id)->whereIn("relate_type", [PromotionEnum::PWD_ACT_CONDITION_ORDER, PromotionEnum::PWD_ACT_CONDITION_SPECIAL_ORDER])->where("relate_id", $order->id)->find();
                        if ($has) continue;
                        // 皮肤
                        $day = substr($order->receive_time, 0, 10);

                        $relateType = PromotionEnum::PWD_ACT_CONDITION_ORDER;
                        $sv = 0;
                        if ($v->act->type == PromotionEnum::PWD_ACT_TYPE_SV) {
                            foreach ($v->act->conditions as $con) {
                                $conItem = (array)$con;
                                if ($conItem["type"] == PromotionEnum::PWD_ACT_CONDITION_ORDER) {
                                    if ($isOrderType <= 0) { // cps结算佣金是小于0.1元 不给星愿值
                                        if ($order->commission < 0.1) continue;
                                    }
                                    $conSv = $isOrderType > 0 ? $conItem["sv"] : $conItem["cps_sv"];
                                    if ($conSv > $sv) {
                                        $sv = $conSv;
                                        $relateType = PromotionEnum::PWD_ACT_CONDITION_ORDER;
                                    }
                                } elseif ($conItem["type"] == PromotionEnum::PWD_ACT_CONDITION_SPECIAL_ORDER) {
                                    continue;
                                    if (!"特殊活动") continue;
                                    if ($conItem["start"]) {
                                        if ($day < $conItem["start"]) continue;
                                    }
                                    if ($conItem["end"]) {
                                        if ($day > $conItem["end"]) continue;
                                    }
                                    if ($conItem["sv"] > $sv) {
                                        $sv = $conItem["sv"];
                                        $relateType = PromotionEnum::PWD_ACT_CONDITION_SPECIAL_ORDER;
                                    }
                                }
                            }
                        }
                        UserPwdActRec::create([
                            "act_id" => $v->act_id,
                            "user_id" => $v->user_id,
                            "user_act_id" => $v->id,
                            "relate_type" => $relateType,
                            "relate_id" => $order->id,
                            "day" => $day,
                            "sv" => $sv
                        ]);
                        switch ($v->act->type) {
                            case PromotionEnum::PWD_ACT_TYPE_PF:
                                $version = implode("", explode("-", $day));
                                if ($v->version < $version) {
                                    $orderCount = UserPwdActRec::where("user_act_id", $v->id)->where("relate_type", PromotionEnum::PWD_ACT_CONDITION_ORDER)->where("day", $day)->count();
                                    if ($orderCount >= $v->order_num) {
                                        UserPwdAct::where("id", $v->id)->where("version", $v->version)->where("finish_num", $v->finish_num)->update(['version' => $version, 'finish_num' => Db::raw("finish_num+1")]);
                                    }
                                }
                                break;
                            case PromotionEnum::PWD_ACT_TYPE_SV:
                                UserPwdAct::where("id", $v->id)->update(['finish_num' => Db::raw("finish_num+$sv")]);
                                break;
                            case PromotionEnum::PWD_ACT_TYPE_ORDER:
                                UserPwdAct::where("id", $v->id)->update(['finish_num' => Db::raw("finish_num+1")]);
                                break;
                        }
                    }
                } else {
                    $actIds = PwdAct::where("type", PromotionEnum::PWD_ACT_TYPE_ORDER)->column("id"); // 冲单活动ID
                    if ($actIds) {
                        // 找这个订单是否被统计
                        $recs = UserPwdActRec::whereIn("act_id", $actIds)->where("relate_type", PromotionEnum::PWD_ACT_CONDITION_ORDER)->where("relate_id", $order->id)->select();
                        foreach ($recs as $rec) {
                            $affectRows = UserPwdActRec::where("id", $rec->id)->whereNull("deleted_at")->update(["deleted_at" => date("Y-m-d H:i:s")]);
                            if ($affectRows > 0) {
                                // 减去统计
                                UserPwdAct::where("id", $rec->user_act_id)->update(['finish_num' => Db::raw("finish_num-1")]);
                            }
                        }
                    }
                }
            }
            Db::commit();
        } catch (\Exception  $e) {
            Db::rollback();
            if ($actCommissionLeak) $redis->decr($actCommissionKey);
            if ($actOrderCountLeak) $redis->decr($actOrderCountKey);
            if ($pwdLeak) $redis->decr($pwdKey);
            return $e->getMessage();
        }
        if ($isOrderType > 0) {
            $redis->sAdd("gen_activity_report_queue", json_encode(['a' => $activity->id, 't' => $activity->scene, 'u' => $userId, 'd' => substr($eventTime, 0, 10), 'e' => $params["event_id"]]));
//            Console::call('gen_activity_report', ['-a' . $activity->id, '-t' . $activity->scene, '-u' . $userId, '-d' . substr($eventTime, 0, 10), '-e' . $params["event_id"]]);
        }
        return true;
    }

    public static function report($activityId, $activityScene, $userId, $day, $sqcrmDb, $sqDb, $clickTbl, $userRoleType)
    {
        $res = [
            "total_num" => 0, "out_num" => 0, "new_num" => 0, "active_num" => 0, "register_num" => 0,
            "card_num" => 0, "red_num" => 0, "dj_red_num" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_new_num' => 0,
            "statis" => [
                "risk" => [
                    "$activityScene" => ["rate" => 0, "mul" => 0],
                    "101" => ["rate" => 0, "mul" => 0],
                ],
                "risk_mul" => 0,
                "quality" => [
                    "1" => ["rate" => 0, "level" => 0, "mul" => 0],
                    "2" => ["rate" => 0, "level" => 0, "mul" => 0],
                ],
                "amount" => 0,
            ]
        ];
        $report = ActivityReport::where("activity_id", $activityId)->where("user_id", $userId)->where("day", $day)->find();
        if ($report) {
            $dayStart = $day . " 00:00:00";
            $dayEnd = $day . " 23:59:59";
//            $res["total_num"] = $report->new_num;
            $res["total_num"] = $report->reward_num;
            $res["out_num"] = $report->new_out_num;
            $res["new_num"] = $report->new_num;
            $res["active_num"] = $report->active_num;
            if ($report->statis) $res["statis"] = $report->statis;

            $amount = Db::query("select sum(reward_amount) cpa_amount, sum(order_reward_amount) cps_amount from promotion_activity_items where activity_id = ? and user_id = ? and third_in_time between ? and ?", [$activityId, $userId, $dayStart, $dayEnd]);

            $extraAmount = UserAccountLog::where("change_type", AccountLogEnum::BALANCE_INVITE_EXTRA)->where("user_id", $userId)->where("relate_id", $activityId)->whereBetween("created_at", [$dayStart, $dayEnd])->field("sum(if(action = 2, -1, 1)*change_amount) sum_amount")->select();

            // 要开启需要统计
//            $res["register_num"] = 0;
//            $res["red_num"] = 0;
//            $res["dj_red_num"] = 0;
//            $res["order_valid_num"] = 0;
        }
        $res["statis"]["mul"] = round($res["statis"]["risk_mul"] * $res["statis"]["quality"]["1"]["mul"] * $res["statis"]["quality"]["2"]["mul"], 2);
        $res["statis"]["cpa_amount"] = round($amount[0]["cpa_amount"] ?? 0, 2);
        $res["statis"]["cps_amount"] = round($amount[0]["cps_amount"] ?? 0, 2);
        $res["statis"]["extra_amount"] = round(($extraAmount[0]->sum_amount ?? 0) * $res["statis"]["risk_mul"], 2);
        return $res;
    }

    public static function overview($activityId, $userIds, $sqcrmDb, $sqDb, $clickTbl, $userRoleType)
    {
        $today = date("Y-m-d");
        $yesterday = date("Y-m-d", time() - 86400);
        $thisWeek = date("Y-m-d", time() - (date("N") - 1) * 86400);
        $thisMonth = date("Y-m", time()) . "-01";

        $overview = [
            ["day" => $today, "day_desc" => "今日",
                "total_num" => 0, "out_num" => 0, "new_num" => 0, "active_num" => 0,
                "register_num" => 0, "card_num" => 0, "red_num" => 0, "dj_red_num" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_new_num' => 0, "red_level" => 0, "order_level" => 0],
            ["day" => $yesterday, "day_desc" => "昨日",
                "total_num" => 0, "out_num" => 0, "new_num" => 0, "active_num" => 0,
                "register_num" => 0, "card_num" => 0, "red_num" => 0, "dj_red_num" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_new_num' => 0, "red_level" => 0, "order_level" => 0],
            ["day" => $thisWeek, "day_desc" => "本周",
                "total_num" => 0, "out_num" => 0, "new_num" => 0, "active_num" => 0,
                "register_num" => 0, "card_num" => 0, "red_num" => 0, "dj_red_num" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_new_num' => 0],
            ["day" => $thisMonth, "day_desc" => "本月",
                "total_num" => 0, "out_num" => 0, "new_num" => 0, "active_num" => 0,
                "register_num" => 0, "card_num" => 0, "red_num" => 0, "dj_red_num" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_new_num' => 0],
        ];

        $minDay = $yesterday;
        foreach ([$thisWeek, $thisMonth] as $day) {
            if ($day < $minDay) $minDay = $day;
        }
        $reports = ActivityReport::where("activity_id", $activityId)->whereIn("user_id", $userIds)->where("day", ">=", $minDay)->select();
        foreach ($reports as $report) {
            foreach ($overview as $k => $v) {
                if (($k == 1 && $report->day == $v["day"]) || ($k != 1 && $report->day >= $v["day"])) {
//                    $overview[$k]["total_num"] += $report->total_num;
//                    $overview[$k]["out_num"] += $report->out_num;
//                    $overview[$k]["total_num"] += $report->new_num;
                    $overview[$k]["total_num"] += $report->reward_num;
                    $overview[$k]["out_num"] += $report->new_out_num;
                    $overview[$k]["new_num"] += $report->new_num;
                    $overview[$k]["active_num"] += $report->active_num;
                    if ($k == 1) {
                        $overview[$k]["red_level"] = $report->statis["quality"]["1"]["level"] ?? 0;
                        $overview[$k]["order_level"] = $report->statis["quality"]["2"]["level"] ?? 0;
                    }
                }
            }
        }
        $registerData = ActivityLogic::getRegisterNum(1, $sqDb, $sqcrmDb, $activityId, $userIds, $minDay);
        foreach ($registerData as $s) {
            foreach ($overview as $k => $v) {
                if (($k == 1 && $s['day'] == $v["day"]) || ($k != 1 && $s['day'] >= $v["day"])) {
                    if ($userRoleType == 1) {
                        $overview[$k]["register_num"] += $s['day_total'];
                    } else {
                        $overview[$k]["register_num"] += $s['total'];
                    }
                }
            }
        }
        $orderData = ActivityLogic::getOrderNum(1, $sqDb, $sqcrmDb, $activityId, $userIds, $minDay);
        foreach ($orderData as $s) {
            foreach ($overview as $k => $v) {
                if (($k == 1 && $s['day'] == $v["day"]) || ($k != 1 && $s['day'] >= $v["day"])) {
                    if ($userRoleType == 1) {
                        $overview[$k]["order_valid_num"] += $s['day_valid'];
                    } else {
                        $overview[$k]["order_valid_num"] += $s['valid'];
                    }
                }
            }
        }
        $redData = ActivityLogic::getRedNum(1, $clickTbl, $sqcrmDb, $activityId, $userIds, $minDay);
        foreach ($redData as $s) {
            foreach ($overview as $k => $v) {
                if (($k == 1 && $s['day'] == $v["day"]) || ($k != 1 && $s['day'] >= $v["day"])) {
                    if ($userRoleType == 1) {
                        $overview[$k]["red_num"] += $s['day_one'];
                        $overview[$k]["dj_red_num"] += $s['day_two'];
                    } else {
                        $overview[$k]["red_num"] += $s['one'];
                        $overview[$k]["dj_red_num"] += $s['two'];
                    }
                }
            }
        }
        return $overview;
    }

    public static function daily($isMyself, $activityId, $activityScene, $userIds, $sqcrmDb, $sqDb, $clickTbl, $userRoleType)
    {
        $detail = [];
        for ($i = ($isMyself ? 1 : 0); $i < ($isMyself ? 31 : 30); $i++) {
            $detail[] = ["day" => date("Y-m-d", time() - $i * 86400),
                "total_num" => 0, "out_num" => 0, "new_num" => 0, "active_num" => 0,
                "register_num" => 0, "card_num" => 0, "red_num" => 0, "dj_red_num" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_new_num' => 0, "statis" => $isMyself ? [
                    "risk" => [
                        "$activityScene" => ["rate" => 0, "mul" => 0],
                        "101" => ["rate" => 0, "mul" => 0],
                    ],
                    "risk_mul" => 0,
                    "quality" => [
                        "1" => ["rate" => 0, "level" => 0, "mul" => 0],
                        "2" => ["rate" => 0, "level" => 0, "mul" => 0],
                    ],
                    "amount" => 0
                ] : null];
        }
        $minDay = date("Y-m-d", time() - 86400 * 30);
        if ($isMyself) {
            $reports = ActivityReport::where("activity_id", $activityId)->where("user_id", $userIds[0])->where("day", ">=", $minDay)
                ->field("day,total_num sum_total_num,out_num sum_out_num,new_num sum_new_num,new_out_num sum_new_out_num,active_num sum_active_num, reward_num sum_reward_num, statis")
                ->select();
        } else {
            $reports = ActivityReport::where("activity_id", $activityId)->whereIn("user_id", $userIds)->where("day", ">=", date("Y-m-d", time() - 86400 * 29))
                ->field("day,sum(total_num) sum_total_num,sum(out_num) sum_out_num,sum(new_num) sum_new_num,sum(new_out_num) sum_new_out_num,sum(active_num) sum_active_num, sum(reward_num) sum_reward_num")
                ->group("day")->select();
        }
        foreach ($reports as $report) {
            foreach ($detail as $k => $v) {
                if ($report->day == $v["day"]) {
//                    $detail[$k]["total_num"] = $report->sum_total_num;
//                    $detail[$k]["out_num"] = $report->sum_out_num;
//                    $detail[$k]["total_num"] = $report->sum_new_num;
                    $detail[$k]["total_num"] = $report->sum_reward_num;
                    $detail[$k]["out_num"] = $report->sum_new_out_num;
                    $detail[$k]["new_num"] = $report->sum_new_num;
                    $detail[$k]["active_num"] = $report->sum_active_num;
                    $detail[$k]["statis"] = $report->statis ?? $v["statis"];
                    break;
                }
            }
        }
        $registerData = ActivityLogic::getRegisterNum(1, $sqDb, $sqcrmDb, $activityId, $userIds, $minDay);
        foreach ($registerData as $s) {
            foreach ($detail as $k => $v) {
                if ($s['day'] == $v["day"]) {
                    if ($userRoleType == 1) {
                        $detail[$k]["register_num"] = $s['day_total'];
                    } else {
                        $detail[$k]["register_num"] = $s['total'];
                    }
                    break;
                }
            }
        }
        $orderData = ActivityLogic::getOrderNum(1, $sqDb, $sqcrmDb, $activityId, $userIds, $minDay);
        foreach ($orderData as $s) {
            foreach ($detail as $k => $v) {
                if ($s['day'] == $v["day"]) {
                    if ($userRoleType == 1) {
                        $detail[$k]["order_valid_num"] = $s['day_valid'];
                    } else {
                        $detail[$k]["order_valid_num"] = $s['valid'];
                    }
                    break;
                }
            }
        }
        $redData = ActivityLogic::getRedNum(1, $clickTbl, $sqcrmDb, $activityId, $userIds, $minDay);
        foreach ($redData as $s) {
            foreach ($detail as $k => $v) {
                if ($s['day'] == $v["day"]) {
                    if ($userRoleType == 1) {
                        $detail[$k]["red_num"] = $s['day_one'];
                        $detail[$k]["dj_red_num"] = $s['day_two'];
                    } else {
                        $detail[$k]["red_num"] = $s['one'];
                        $detail[$k]["dj_red_num"] = $s['two'];
                    }
                    break;
                }
            }
        }
        return $detail;
    }

    public static function team($userMap, $activityId, $userIds, $sqcrmDb, $sqDb, $clickTbl, $userRoleType, $startDay = "", $endDay = "")
    {
        $team = [];
        foreach ($userMap as $k => $v) {
            $team[] = ["id" => $k, 'name' => $v,
                "total_num" => 0, "out_num" => 0, "new_num" => 0, "active_num" => 0,
                "register_num" => 0, "card_num" => 0, "red_num" => 0, "dj_red_num" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_new_num' => 0];
        }
        $reportQuery = ActivityReport::where("activity_id", $activityId)->whereIn("user_id", $userIds);
        if ($startDay || $endDay) {
            if ($startDay) {
                $reportQuery->where("day", ">=", $startDay);
            }
            if ($endDay) {
                $reportQuery->where("day", "<=", $endDay);
            }
        }
        $reports = $reportQuery->field("user_id,sum(total_num) sum_total_num,sum(out_num) sum_out_num,sum(new_num) sum_new_num,sum(new_out_num) sum_new_out_num,sum(active_num) sum_active_num,sum(reward_num) sum_reward_num")->group("user_id")->select();
        foreach ($reports as $report) {
            foreach ($team as $k => $v) {
                if ($report->user_id == $v["id"]) {
//                    $team[$k]["total_num"] = $report->sum_total_num;
//                    $team[$k]["out_num"] = $report->sum_out_num;
//                    $team[$k]["total_num"] = $report->sum_new_num;
                    $team[$k]["total_num"] = $report->sum_reward_num;
                    $team[$k]["out_num"] = $report->sum_new_out_num;
                    $team[$k]["new_num"] = $report->sum_new_num;
                    $team[$k]["active_num"] = $report->sum_active_num;
                    break;
                }
            }
        }
        $registerData = ActivityLogic::getRegisterNum(2, $sqDb, $sqcrmDb, $activityId, $userIds, $startDay, $endDay);
        foreach ($registerData as $s) {
            foreach ($team as $k => $v) {
                if ($s['user_id'] == $v["id"]) {
                    if ($userRoleType == 1) {
                        $team[$k]["register_num"] = $s['day_total'];
                    } else {
                        $team[$k]["register_num"] = $s['total'];
                    }
                    break;
                }
            }
        }
        $orderData = ActivityLogic::getOrderNum(2, $sqDb, $sqcrmDb, $activityId, $userIds, $startDay, $endDay);
        foreach ($orderData as $s) {
            foreach ($team as $k => $v) {
                if ($s['user_id'] == $v["id"]) {
                    if ($userRoleType == 1) {
                        $team[$k]["order_valid_num"] = $s['day_valid'];
                    } else {
                        $team[$k]["order_valid_num"] = $s['valid'];
                    }
                    break;
                }
            }
        }
        $redData = ActivityLogic::getRedNum(2, $clickTbl, $sqcrmDb, $activityId, $userIds, $startDay, $endDay);
        foreach ($redData as $s) {
            foreach ($team as $k => $v) {
                if ($s['user_id'] == $v["id"]) {
                    if ($userRoleType == 1) {
                        $team[$k]["red_num"] = $s['day_one'];
                        $team[$k]["dj_red_num"] = $s['day_two'];
                    } else {
                        $team[$k]["red_num"] = $s['one'];
                        $team[$k]["dj_red_num"] = $s['two'];
                    }
                    break;
                }
            }
        }
        return $team;
    }


    private static function getRegisterNum($groupType, $sqDb, $sqcrmDb, $activityId, $userIds, $startDay = "", $endDay = "")
    {
        $daySql = "b.third_in_time is not null";
        if ($startDay || $endDay) {
            $daySql = "";
            if ($startDay) {
                $startDay .= " 00:00:00";
                $daySql = "b.third_in_time >= '$startDay'";
            }
            if ($endDay) {
                $endDay .= " 23:59:59";
                if ($daySql) $daySql .= " and ";
                $daySql .= "b.third_in_time <= '$endDay'";
            }
        }
        $daySql .= " and (b.third_out_time is null or (b.third_out_time is not null and b.third_out_time > b.third_in_time)) and b.reward_time is not null and b.unreward_time is null";

        switch ($groupType) {
            case 1: // third_in_time
                $sql = "
select left(b.third_in_time, 10) day, count(a.id) total, count(if(FROM_UNIXTIME(a.register_time,'%Y-%m-%d') = left(b.third_in_time, 10), a.id, null)) day_total
from $sqDb.client_user a 
left join $sqcrmDb.promotion_activity_items b on b.out_user_id = a.open_user_id 
where a.register_time > unix_timestamp(b.third_in_time) and b.activity_id = $activityId and b.user_id in (" . implode(",", $userIds) . ") and $daySql
group by left(b.third_in_time, 10)
";
                break;
            case 2: // user_id
                $sql = "
select b.user_id, count(a.id) total, count(if(FROM_UNIXTIME(a.register_time,'%Y-%m-%d') = left(b.third_in_time, 10), a.id, null)) day_total
from $sqDb.client_user a 
left join $sqcrmDb.promotion_activity_items b on b.out_user_id = a.open_user_id 
where a.register_time > unix_timestamp(b.third_in_time) and b.activity_id = $activityId and b.user_id in (" . implode(",", $userIds) . ") and $daySql
group by b.user_id
";
                break;
            default:
                return [];
        }
        return Db::connect("adb")->query($sql);
    }

    private static function getOrderNum($groupType, $sqDb, $sqcrmDb, $activityId, $userIds, $startDay = "", $endDay = "")
    {
        $daySql = "b.third_in_time is not null";
        if ($startDay || $endDay) {
            $daySql = "";
            if ($startDay) {
                $startDay .= " 00:00:00";
                $daySql = "b.third_in_time >= '$startDay'";
            }
            if ($endDay) {
                $endDay .= " 23:59:59";
                if ($daySql) $daySql .= " and ";
                $daySql .= "b.third_in_time <= '$endDay'";
            }
        }
        $daySql .= " and (b.third_out_time is null or (b.third_out_time is not null and b.third_out_time > b.third_in_time)) and b.reward_time is not null and b.unreward_time is null";

        switch ($groupType) {
            case 1: // third_in_time
                $sql = "
select left(b.third_in_time, 10) day, count(a.id) total, count(distinct a.open_user_id) valid, count(distinct if(left(a.create_time, 10) = left(b.third_in_time, 10), a.open_user_id, null)) day_valid
from $sqDb.collect_order a 
left join $sqcrmDb.promotion_activity_items b on b.out_user_id = a.open_user_id 
where a.order_status >= 1 and a.create_time > b.third_in_time and b.activity_id = $activityId and b.user_id in (" . implode(",", $userIds) . ") and $daySql
group by left(b.third_in_time, 10)
";
                break;
            case 2: // user_id
                $sql = "
select b.user_id, count(a.id) total, count(distinct a.open_user_id) valid, count(distinct if(left(a.create_time, 10) = left(b.third_in_time, 10), a.open_user_id, null)) day_valid
from $sqDb.collect_order a 
left join $sqcrmDb.promotion_activity_items b on b.out_user_id = a.open_user_id 
where a.order_status >= 1 and a.create_time > b.third_in_time and b.activity_id = $activityId and b.user_id in (" . implode(",", $userIds) . ") and $daySql
group by b.user_id
";
                break;
            default:
                return [];
        }
        return Db::connect("adb")->query($sql);
    }

    private static function getRedNum($groupType, $clickTbl, $sqcrmDb, $activityId, $userIds, $startDay = "", $endDay = "")
    {
        $daySql = "a.third_in_time is not null";
        if ($startDay || $endDay) {
            $daySql = "";
            if ($startDay) {
                $startDay .= " 00:00:00";
                $daySql = "a.third_in_time >= '$startDay'";
            }
            if ($endDay) {
                $endDay .= " 23:59:59";
                if ($daySql) $daySql .= " and ";
                $daySql .= "a.third_in_time <= '$endDay'";
            }
        }
        $daySql .= " and (a.third_out_time is null or (a.third_out_time is not null and a.third_out_time > a.third_in_time)) and a.reward_time is not null and a.unreward_time is null";

        switch ($groupType) {
            case 1: // third_in_time
                $sql = "
select left(a.third_in_time, 10) day, 
    count(distinct if(b.id is not null, b.open_user_id, if(c.id is not null, c.open_user_id, null))) one, 
    count(distinct if(b.id is not null and c.id is not null, b.open_user_id, null)) two, 
    count(distinct if(b.id is not null and left(b.created_at, 10) = left(a.third_in_time, 10), b.open_user_id, if(c.id is not null and left(c.created_at, 10) = left(a.third_in_time, 10), c.open_user_id, null))) day_one, 
    count(distinct if(b.id is not null and left(b.created_at, 10) = left(a.third_in_time, 10) and c.id is not null and left(c.created_at, 10) = left(a.third_in_time, 10), b.open_user_id, null)) day_two
from $sqcrmDb.promotion_activity_items a 
left join shenquan.$clickTbl b on b.open_user_id = a.out_user_id and b.created_at > a.third_in_time and b.click_type = 1
left join shenquan.$clickTbl c on c.open_user_id = a.out_user_id and c.created_at > a.third_in_time and c.click_type = 2
where a.activity_id = $activityId and a.user_id in (" . implode(",", $userIds) . ") and $daySql
group by left(a.third_in_time, 10)
";
                break;
            case 2: // user_id
                $sql = "
select a.user_id, 
    count(distinct if(b.id is not null, b.open_user_id, if(c.id is not null, c.open_user_id, null))) one, 
    count(distinct if(b.id is not null and c.id is not null, b.open_user_id, null)) two, 
    count(distinct if(b.id is not null and left(b.created_at, 10) = left(a.third_in_time, 10), b.open_user_id, if(c.id is not null and left(c.created_at, 10) = left(a.third_in_time, 10), c.open_user_id, null))) day_one, 
    count(distinct if(b.id is not null and left(b.created_at, 10) = left(a.third_in_time, 10) and c.id is not null and left(c.created_at, 10) = left(a.third_in_time, 10), b.open_user_id, null)) day_two
from $sqcrmDb.promotion_activity_items a 
left join shenquan.$clickTbl b on b.open_user_id = a.out_user_id and b.created_at > a.third_in_time and b.click_type = 1
left join shenquan.$clickTbl c on c.open_user_id = a.out_user_id and c.created_at > a.third_in_time and c.click_type = 2
where a.activity_id = $activityId and a.user_id in (" . implode(",", $userIds) . ") and $daySql
group by a.user_id
";
                break;
            default:
                return [];
        }
        return Db::connect("adb")->query($sql);
    }

    public static function pwdOrderStatis($activityId, $userId, $tab, $subTab, $tabValue)
    {
        $res = [
            "overview" => [],
            'detail' => [],
        ];
        if (!in_array($tab, [1,2]) || !in_array($subTab, [1, 2])) return $res;
        if ($subTab == 2) {
            $minDay = date("Y-m-d", time() - 86400 * 29);
        } else {
            $today = date("Y-m-d");
            $yesterday = date("Y-m-d", time() - 86400);
            $thisWeek = date("Y-m-d", time() - (date("N") - 1) * 86400);
            $thisMonth = date("Y-m", time()) . "-01";

            $minDay = $yesterday;
            foreach ([$thisWeek, $thisMonth] as $day) {
                if ($day < $minDay) $minDay = $day;
            }
        }

        if($tab == 2){
            $userSql = "";
            if ($tabValue){
                $userSql = " and user_id = $tabValue";
            }
            $reports = Db::query("select left(receive_time, 10) day, count(1) order_total_num, count(if(order_new = 2, 1, null)) order_valid_num, count(if(reward_time is not null and unreward_time is null, 1, null)) reward_num, sum(agent_reward_amount) reward_amount, 
    count(if(order_type = 1400 and order_new = 2 and reward_time is not null, 1, null)) 1400_num, 
    count(if(order_type = 1401 and order_new = 2 and reward_time is not null, 1, null)) 1401_num, 
    count(if(order_type = 1410 and order_new = 2 and reward_time is not null, 1, null)) 1410_num, 
    count(if(order_type = 1411 and order_new = 2 and reward_time is not null, 1, null)) 1411_num 
from promotion_orders 
where activity_id = ? and agent_id = ? and receive_time >= ? and order_new in (2,100) and order_status = 1 $userSql 
group by left(receive_time, 10)", [$activityId, $userId, $minDay." 00:00:00"]);
        }else{
            if($tabValue){
                $reports = Db::query("select left(receive_time, 10) day, count(1) order_total_num, count(if(order_new = 2, 1, null)) order_valid_num, count(if(reward_time is not null and unreward_time is null, 1, null)) reward_num, sum(reward_amount) reward_amount, 
    count(if(order_type = 1400 and order_new = 2 and reward_time is not null, 1, null)) 1400_num, 
    count(if(order_type = 1401 and order_new = 2 and reward_time is not null, 1, null)) 1401_num, 
    count(if(order_type = 1410 and order_new = 2 and reward_time is not null, 1, null)) 1410_num, 
    count(if(order_type = 1411 and order_new = 2 and reward_time is not null, 1, null)) 1411_num 
from promotion_orders 
where activity_id = ? and user_id = ? and receive_time >= ? and order_new in (2,100) and order_status = 1 and pid = ? 
group by left(receive_time, 10)", [$activityId, $userId, $minDay." 00:00:00", $tabValue]);
                $pids = [$tabValue];
            }else{
                $reports = ActivityReport::where("activity_id", $activityId)->where("user_id", $userId)->where("day", ">=", $minDay)->field("day, order_total_num, order_valid_num, reward_num, reward_amount, statis")->select()->toArray();
                $pids = ElmPwd::whereRaw("id in (select qrcode_id from promotion_qrcode_user_logs where type = ? and is_default = 1 and user_id = ?) and activity_id = ?", [PromotionEnum::ACTIVITY_SCENE_PWD, $userId, $activityId])->column("pid");
            }
            if ($pids) {
                $data = Db::query("select report_date day, sum(click_pv) sum_click_pv, sum(click_uv) sum_click_uv, sum(pv) sum_pv, sum(uv) sum_uv, sum(order_num) sum_order_num, sum(income) sum_income, sum(settle) sum_settle from byn_data.elm_ad_zone_reports where pid in ('" . implode("','", $pids) . "') and report_date >= '" . $minDay . "' group by report_date");
            }
        }
        $act = Activity::where("id", $activityId)->find();
        $firstOrderType = $act->order_type[0] ?? 0;

        $uvDeductMap = [
            "2025-08-14" => 0.1,
            "2025-08-15" => 0.15,
            "2025-08-16" => 0.2,
            "2025-08-17" => 0.2,
            "2025-08-18" => 0.25,
            "2025-08-19" => 0.3,
            "2025-08-20" => 0.3,
            "2025-08-21" => 0.35,
            "2025-08-22" => 0.35,
            "2025-08-23" => 0.4,
        ];
        switch ($subTab) {
            case 2:
                $detail = [];
                for ($i = 0; $i < 30; $i++) {
                    $detail[] = ["day" => date("Y-m-d", time() - $i * 86400), "pv" => 0, "uv" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_num' => 0, "reward_amount" => 0, "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]];
                }
                foreach (($reports??[]) as $report) {
                    $reportMap = [];
                    if(isset($report['statis'])){
                        foreach (($report['statis'] ?: []) as $rItem){
                            $reportMap[$rItem['type']] = $rItem;
                        }
                    }elseif(isset($report['1400_num'])){
                        $reportMap["1400"] = ["num" => $report["1400_num"] ?? 0];
                        $reportMap["1401"] = ["num" => $report["1401_num"] ?? 0];
                        $reportMap["1410"] = ["num" => $report["1410_num"] ?? 0];
                        $reportMap["1411"] = ["num" => $report["1411_num"] ?? 0];
                    }

                    foreach ($detail as $k => $v) {
                        if ($report['day'] == $v["day"]) {
                            $detail[$k]["order_total_num"] += $report['order_total_num'];
                            $detail[$k]["order_valid_num"] += $report['order_valid_num'];
                            $detail[$k]["order_num"] += $report['reward_num'];
                            $detail[$k]["reward_amount"] += $report['reward_amount'];

                            switch ($firstOrderType) {
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_BWC:
                                    $detail[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? $report['reward_num'];
                                    $detail[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $detail[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN:
                                    $detail[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $detail[$k]["elm"]["pt_num"] += (isset($reportMap['1410']) || isset($reportMap['1411'])) ? (($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0)) : $report['reward_num'];
                                    $detail[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU:
                                    $detail[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $detail[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $detail[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? $report['reward_num'];
                                    break;
                            }
                        }
                    }
                }
                foreach (($data ?? []) as $d) {
                    foreach ($detail as $k => $v) {
                        if ($d['day'] == $v["day"]) {
                            $rate = $uvDeductMap[$d["day"]] ?? ($d["day"] > "2025-08-23" ? 0.4 : 0);
                            $detail[$k]["pv"] += intval(ceil($d['sum_click_pv']*(1-$rate)));
                            $detail[$k]["uv"] += intval(ceil($d['sum_click_uv']*(1-$rate)));
                        }
                    }
                }
                $res["detail"] = $detail;
                break;
            case 1:
                $overview = [
                    ["day" => $today, "day_desc" => "今日", "pv" => 0, "uv" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_num' => 0, "reward_amount" => 0, "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                    ["day" => $yesterday, "day_desc" => "昨日", "pv" => 0, "uv" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_num' => 0, "reward_amount" => 0, "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                    ["day" => $thisWeek, "day_desc" => "本周", "pv" => 0, "uv" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_num' => 0, "reward_amount" => 0, "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                    ["day" => $thisMonth, "day_desc" => "本月", "pv" => 0, "uv" => 0, "order_total_num" => 0, 'order_valid_num' => 0, 'order_num' => 0, "reward_amount" => 0, "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                ];
                foreach (($reports??[]) as $report) {
                    $reportMap = [];
                    if(isset($report['statis'])){
                        foreach (($report['statis'] ?: []) as $rItem){
                            $reportMap[$rItem['type']] = $rItem;
                        }
                    }elseif(isset($report['1400_num'])){
                        $reportMap["1400"] = ["num" => $report["1400_num"] ?? 0];
                        $reportMap["1401"] = ["num" => $report["1401_num"] ?? 0];
                        $reportMap["1410"] = ["num" => $report["1410_num"] ?? 0];
                        $reportMap["1411"] = ["num" => $report["1411_num"] ?? 0];
                    }

                    foreach ($overview as $k => $v) {
                        if (($k == 1 && $report['day'] == $v["day"]) || ($k != 1 && $report['day'] >= $v["day"])) {
                            $overview[$k]["order_total_num"] += $report['order_total_num'];
                            $overview[$k]["order_valid_num"] += $report['order_valid_num'];
                            $overview[$k]["order_num"] += $report['reward_num'];
                            $overview[$k]["reward_amount"] += $report['reward_amount'];

                            switch ($firstOrderType) {
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_BWC:
                                    $overview[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? $report['reward_num'];
                                    $overview[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $overview[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN:
                                    $overview[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $overview[$k]["elm"]["pt_num"] += (isset($reportMap['1410']) || isset($reportMap['1411'])) ? (($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0)) : $report['reward_num'];
                                    $overview[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU:
                                    $overview[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $overview[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $overview[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? $report['reward_num'];
                                    break;
                            }
                        }
                    }
                }
                foreach (($data ?? []) as $d) {
                    foreach ($overview as $k => $v) {
                        if (($k == 1 && $d['day'] == $v["day"]) || ($k != 1 && $d['day'] >= $v["day"])) {
                            $rate = $uvDeductMap[$d["day"]] ?? ($d["day"] > "2025-08-23" ? 0.4 : 0);
                            $overview[$k]["pv"] += intval(ceil($d['sum_click_pv']*(1-$rate)));
                            $overview[$k]["uv"] += intval(ceil($d['sum_click_uv']*(1-$rate)));
                        }
                    }
                }
                $res["overview"] = $overview;
                break;
        }
        return $res;
    }


    public static function linkOrderStatis($activityId, $userId, $tab, $subTab, $tabValue)
    {
        $res = [
            "overview" => [],
            'detail' => [],
            "info" => [
                "url" => "",
            ],
        ];
        if (!in_array($tab, [1,2]) || !in_array($subTab, [1, 2])) return $res;
        if ($subTab == 2) {
            $minDay = date("Y-m-d", time() - 86400 * 29);
        } else {
            $today = date("Y-m-d");
            $yesterday = date("Y-m-d", time() - 86400);
            $thisWeek = date("Y-m-d", time() - (date("N") - 1) * 86400);
            $thisMonth = date("Y-m", time()) . "-01";

            $minDay = $yesterday;
            foreach ([$thisWeek, $thisMonth] as $day) {
                if ($day < $minDay) $minDay = $day;
            }
        }

        $act = Activity::where("id", $activityId)->find();
        $timeField = "create_time";
        if (array_intersect($act->order_type, [PromotionEnum::ACTIVITY_ORDER_TYPE_BWC, PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN, PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU]))  {  // 闪购
            $timeField = "receive_time";
        }
        if ($tab == 2) {
            $userSql = "";
            if ($tabValue){
                $userSql = " and user_id = $tabValue";
            }
            $reports = Db::query("select left($timeField, 10) day, count(1) reward_num, sum(agent_reward_amount) reward_amount, 
    count(if(order_type = 2600, 1, null)) 2600_num, sum(if(order_type = 2600, agent_reward_amount, 0)) 2600_amount, count(if(order_type = 2600 and receive_time is not null, 1, null)) 2600_receive_num, sum(if(order_type = 2600 and receive_time is not null, agent_reward_amount, 0)) 2600_receive_amount, 
    count(if(order_type = 2602, 1, null)) 2602_num, 
    count(if(order_type = 2603, 1, null)) 2603_num, count(if(order_type = 2603 and receive_time is not null, 1, null)) 2603_receive_num,
    count(if(order_type = 2604, 1, null)) 2604_num, 
    count(if(order_type = 2605, 1, null)) 2605_num,
    count(if(order_type = 1400, 1, null)) 1400_num, 
    count(if(order_type = 1401, 1, null)) 1401_num, 
    count(if(order_type = 1410, 1, null)) 1410_num, 
    count(if(order_type = 1411, 1, null)) 1411_num 
from promotion_orders 
where activity_id = ? and agent_id = ? and $timeField >= ? and order_status = 1 and reward_time is not null $userSql 
group by left($timeField, 10)", [$activityId, $userId, $minDay." 00:00:00"]);
        }else{
            if($act->scene == PromotionEnum::ACTIVITY_SCENE_PWD && $tabValue) {
                // 2600,2601,2602,2603,2604,2605
                $reports = Db::query("select left($timeField, 10) day, count(1) reward_num, sum(reward_amount) reward_amount, 
    count(if(order_type = 2600, 1, null)) 2600_num, sum(if(order_type = 2600, reward_amount, 0)) 2600_amount, count(if(order_type = 2600 and receive_time is not null, 1, null)) 2600_receive_num, sum(if(order_type = 2600 and receive_time is not null, reward_amount, 0)) 2600_receive_amount, 
    count(if(order_type = 2602, 1, null)) 2602_num, 
    count(if(order_type = 2603, 1, null)) 2603_num, count(if(order_type = 2603 and receive_time is not null, 1, null)) 2603_receive_num,
    count(if(order_type = 2604, 1, null)) 2604_num, 
    count(if(order_type = 2605, 1, null)) 2605_num, 
    count(if(order_type = 1400, 1, null)) 1400_num, 
    count(if(order_type = 1401, 1, null)) 1401_num, 
    count(if(order_type = 1410, 1, null)) 1410_num, 
    count(if(order_type = 1411, 1, null)) 1411_num 
from promotion_orders 
where activity_id = ? and user_id = ? and $timeField >= ? and order_status = 1 and sid = ? and reward_time is not null 
group by left($timeField, 10)", [$activityId, $userId, $minDay." 00:00:00", $tabValue]);
            }else{
                $reports = ActivityReport::where("activity_id", $activityId)->where("user_id", $userId)->where("day", ">=", $minDay)
                    ->field("day, order_total_num, order_valid_num, reward_num, reward_amount, statis")->select()->toArray();
            }

            if (startWith($act->out_act_id, PromotionEnum::SPECIAL_ELM_ACT_PREFIX)){
                $type = ltrim($act->out_act_id, PromotionEnum::SPECIAL_ELM_ACT_PREFIX);
                $page = PromotionPage::where("type", $type)->where("user_type", PromotionPage::USER_TYPE_LTB)->where("user_id", $userId)->where("status", PromotionPage::STATUS_SUCCESS)->find();
                if($page){
                    $logInfo = QrcodeUserLog::where("qrcode_id", $activityId)->where("user_id", $userId)->where("type", PromotionEnum::ACTIVITY_SCENE_LINK)->find();
                    if($logInfo) {
                        $res["info"]["url"] = $logInfo->short_url ?: "https://promote.shenquanwaimai.com/act/favorable?log_id=".$logInfo->id;
                    }
                    $data = Db::query("select report_date day, sum(click_pv) pv, sum(click_uv) uv from byn_data.elm_ad_zone_reports where pid = ? and report_date >= ? group by report_date", [$page->pid, $minDay]);
                }
            }else {
                if ($act->scene == PromotionEnum::ACTIVITY_SCENE_LINK) {
                    $logIds = QrcodeUserLog::where("qrcode_id", $activityId)->where("user_id", $userId)->where("type", PromotionEnum::ACTIVITY_SCENE_LINK)->column("id");
                    if ($logIds) {
                        $data = Db::connect("adb")->query("select left(created_at, 10) day, count(distinct open_uuid) uv from byn_track.laitb_log where app_id = 'laituiba' and system = 'laituiba' and log_id in ('" . implode("','", $logIds) . "') and created_at >= '" . $minDay . " 00:00:00" . "' group by left(created_at, 10)");
                    }
                    if (array_intersect($act->order_type, [PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU, PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU_KA])){
                        $resp = (new YcService())->do("/api/activities/promote", ["id" => intval($act->out_act_id), "need_we_app" => true, "need_short_url" => true, "need_deeplink" => true, "need_ali_app" => true, "need_pwd" => true, "sid" => sprintf("a%d_u%d", $activityId, $userId)]);
                        if ($resp && $resp['code'] == 0) {
                            if ($resp["data"]) {
                                $res["info"]["url"] = $resp["data"]["short_url"];
                                if (empty($res["info"]["url"])) $res["info"]["url"] = $resp["data"]["url"];
                            }
                        }
                    }else{
                        if($logIds) {
                            $logInfo = QrcodeUserLog::where("id", $logIds[0])->find();
                            if ($logInfo) {
                                $res["info"]["url"] = $logInfo->short_url ?: "https://promote.shenquanwaimai.com/act/favorable?log_id=".$logInfo->id;
                            }
                        }
                    }
                }
            }
        }

        $firstOrderType = $act->order_type[0] ?? 0;

        switch ($subTab) {
            case 2:
                $detail = [];
                for ($i = 0; $i < 30; $i++) {
                    $detail[] = ["day" => date("Y-m-d", time() - $i * 86400), "order_num" => 0, 'commission' => 0, "pv" => 0, "uv" => 0, "order_receive_num" => 0, "card_new_num" => 0, "card_new_receive_num" => 0, "ka" => ["2600_num" => 0, "2600_amount" => 0, "2600_receive_amount" => 0, "2602_num" => 0, "2603_num" => 0, "2603_receive_num" => 0, "2604_num" => 0, "2605_num" => 0], "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]];
                }
                foreach ($reports as $report) {
                    $reportMap = [];
                    if(isset($report['statis'])){
                        // [{"type":2600,"num":1,"amount":"3.00","receive":0, "commission":"0", "high_num":0,"high_amount":"0","high_receive":0}]
                        foreach (($report['statis'] ?: []) as $rItem){
                            $reportMap[$rItem['type']] = $rItem;
                        }
                    }else{
                        $reportMap["2600"] = ["num" => $report["2600_num"] ?? 0, "amount" => $report["2600_amount"] ?? 0, 'receive' => $report["2600_receive_num"] ?? 0, "commission" => $report["2600_receive_amount"] ?? 0];
                        $reportMap["2602"] = ["num" => $report["2602_num"] ?? 0];
                        $reportMap["2603"] = ["num" => $report["2603_num"] ?? 0, "receive" => $report["2603_receive_num"] ?? 0];
                        $reportMap["2604"] = ["num" => $report["2604_num"] ?? 0];
                        $reportMap["2605"] = ["num" => $report["2605_num"] ?? 0];
                        $reportMap["1400"] = ["num" => $report["1400_num"] ?? 0];
                        $reportMap["1401"] = ["num" => $report["1401_num"] ?? 0];
                        $reportMap["1410"] = ["num" => $report["1410_num"] ?? 0];
                        $reportMap["1411"] = ["num" => $report["1411_num"] ?? 0];
                    }

                    foreach ($detail as $k => $v) {
                        if ($report['day'] == $v["day"]) {
                            $detail[$k]["order_num"] += $report['reward_num'] - ($reportMap['2600']["high_num"] ?? 0);
                            $detail[$k]["commission"] += $report['reward_amount'];
                            $detail[$k]["order_receive_num"] += ($reportMap['2600']["receive"] ?? 0) - ($reportMap['2600']["high_receive"] ?? 0);
                            $detail[$k]["card_new_num"] += $reportMap['2600']["high_num"] ?? 0;
                            $detail[$k]["card_new_receive_num"] += $reportMap['2600']["high_receive"] ?? 0;

                            switch ($firstOrderType) {
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_BWC:
                                    $detail[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? $report['reward_num'];
                                    $detail[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $detail[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN:
                                    $detail[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $detail[$k]["elm"]["pt_num"] += (isset($reportMap['1410']) || isset($reportMap['1411'])) ? (($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0)) : $report['reward_num'];
                                    $detail[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU:
                                    $detail[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $detail[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $detail[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? $report['reward_num'];
                                    break;
                            }

                            $detail[$k]["ka"]["2600_num"] += $reportMap['2600']["num"] ?? 0;
                            $detail[$k]["ka"]["2600_amount"] += $reportMap['2600']["amount"] ?? 0;
                            $detail[$k]["ka"]["2600_receive_amount"] += $reportMap['2600']["commission"] ?? 0;
                            $detail[$k]["ka"]["2602_num"] += $reportMap['2602']["num"] ?? 0;
                            $detail[$k]["ka"]["2603_num"] += $reportMap['2603']["num"] ?? 0;
                            $detail[$k]["ka"]["2603_receive_num"] += $reportMap['2603']["receive"] ?? 0;
                            $detail[$k]["ka"]["2604_num"] += $reportMap['2604']["num"] ?? 0;
                            $detail[$k]["ka"]["2605_num"] += $reportMap['2605']["num"] ?? 0;
                        }
                    }
                }
                foreach (($data ?? []) as $d) {
                    foreach ($detail as $k => $v) {
                        if ($d['day'] == $v["day"]) {
                            $detail[$k]["pv"] += $d['pv'] ?? 0;
                            $detail[$k]["uv"] += $d['uv'];
                        }
                    }
                }
                $res["detail"] = $detail;
                break;
            case 1:
                $overview = [
                    ["day" => $today, "day_desc" => "今日", "order_num" => 0, 'commission' => 0, "pv" => 0, "uv" => 0, "order_receive_num" => 0, "card_new_num" => 0, "card_new_receive_num" => 0, "ka" => ["2600_num" => 0, "2600_amount" => 0, "2600_receive_amount" => 0, "2602_num" => 0, "2603_num" => 0, "2603_receive_num" => 0, "2604_num" => 0, "2605_num" => 0], "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                    ["day" => $yesterday, "day_desc" => "昨日", "order_num" => 0, 'commission' => 0, "pv" => 0, "uv" => 0, "order_receive_num" => 0, "card_new_num" => 0, "card_new_receive_num" => 0, "ka" => ["2600_num" => 0, "2600_amount" => 0, "2600_receive_amount" => 0, "2602_num" => 0, "2603_num" => 0, "2603_receive_num" => 0, "2604_num" => 0, "2605_num" => 0], "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                    ["day" => $thisWeek, "day_desc" => "本周", "order_num" => 0, 'commission' => 0, "pv" => 0, "uv" => 0, "order_receive_num" => 0, "card_new_num" => 0, "card_new_receive_num" => 0, "ka" => ["2600_num" => 0, "2600_amount" => 0, "2600_receive_amount" => 0, "2602_num" => 0, "2603_num" => 0, "2603_receive_num" => 0, "2604_num" => 0, "2605_num" => 0], "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                    ["day" => $thisMonth, "day_desc" => "本月", "order_num" => 0, 'commission' => 0, "pv" => 0, "uv" => 0, "order_receive_num" => 0, "card_new_num" => 0, "card_new_receive_num" => 0, "ka" => ["2600_num" => 0, "2600_amount" => 0, "2600_receive_amount" => 0, "2602_num" => 0, "2603_num" => 0, "2603_receive_num" => 0, "2604_num" => 0, "2605_num" => 0], "elm" => ["bwc_num" => 0, "pt_num" => 0, "sg_num" => 0]],
                ];
                foreach ($reports as $report) {
                    $reportMap = [];
                    if(isset($report['statis'])) {
                        foreach (($report['statis'] ?: []) as $rItem) {
                            $reportMap[$rItem['type']] = $rItem;
                        }
                    }else{
                        $reportMap["2600"] = ["num" => $report["2600_num"] ?? 0, "amount" => $report["2600_amount"] ?? 0, 'receive' => $report["2600_receive_num"] ?? 0, "commission" => $report["2600_receive_amount"] ?? 0];
                        $reportMap["2602"] = ["num" => $report["2602_num"] ?? 0];
                        $reportMap["2603"] = ["num" => $report["2603_num"] ?? 0, "receive" => $report["2603_receive_num"] ?? 0];
                        $reportMap["2604"] = ["num" => $report["2604_num"] ?? 0];
                        $reportMap["2605"] = ["num" => $report["2605_num"] ?? 0];
                        $reportMap["1400"] = ["num" => $report["1400_num"] ?? 0];
                        $reportMap["1401"] = ["num" => $report["1401_num"] ?? 0];
                        $reportMap["1410"] = ["num" => $report["1410_num"] ?? 0];
                        $reportMap["1411"] = ["num" => $report["1411_num"] ?? 0];
                    }
                    foreach ($overview as $k => $v) {
                        if (($k == 1 && $report['day'] == $v["day"]) || ($k != 1 && $report['day'] >= $v["day"])) {
                            $overview[$k]["order_num"] += $report['reward_num'] - ($reportMap['2600']["high_num"] ?? 0);
                            $overview[$k]["commission"] += $report['reward_amount'];
                            $overview[$k]["order_receive_num"] += ($reportMap['2600']["receive"] ?? 0) - ($reportMap['2600']["high_receive"] ?? 0);
                            $overview[$k]["card_new_num"] += $reportMap['2600']["high_num"] ?? 0;
                            $overview[$k]["card_new_receive_num"] += $reportMap['2600']["high_receive"] ?? 0;

                            switch ($firstOrderType) {
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_BWC:
                                    $overview[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? $report['reward_num'];
                                    $overview[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $overview[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN:
                                    $overview[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $overview[$k]["elm"]["pt_num"] += (isset($reportMap['1410']) || isset($reportMap['1411'])) ? (($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0)) : $report['reward_num'];
                                    $overview[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? 0;
                                    break;
                                case PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU:
                                    $overview[$k]["elm"]["bwc_num"] += $reportMap['1401']["num"] ?? 0;
                                    $overview[$k]["elm"]["pt_num"] += ($reportMap['1410']["num"] ?? 0) + ($reportMap['1411']["num"] ?? 0);
                                    $overview[$k]["elm"]["sg_num"] += $reportMap['1400']["num"] ?? $report['reward_num'];
                                    break;
                            }

                            $overview[$k]["ka"]["2600_num"] += $reportMap['2600']["num"] ?? 0;
                            $overview[$k]["ka"]["2600_amount"] += $reportMap['2600']["amount"] ?? 0;
                            $overview[$k]["ka"]["2600_receive_amount"] += $reportMap['2600']["commission"] ?? 0;
                            $overview[$k]["ka"]["2602_num"] += $reportMap['2602']["num"] ?? 0;
                            $overview[$k]["ka"]["2603_num"] += $reportMap['2603']["num"] ?? 0;
                            $overview[$k]["ka"]["2603_receive_num"] += $reportMap['2603']["receive"] ?? 0;
                            $overview[$k]["ka"]["2604_num"] += $reportMap['2604']["num"] ?? 0;
                            $overview[$k]["ka"]["2605_num"] += $reportMap['2605']["num"] ?? 0;
                        }
                    }
                }
                foreach (($data ?? []) as $d) {
                    foreach ($overview as $k => $v) {
                        if (($k == 1 && $d['day'] == $v["day"]) || ($k != 1 && $d['day'] >= $v["day"])) {
                            $overview[$k]["pv"] += $d['pv'] ?? 0;
                            $overview[$k]["uv"] += $d['uv'];
                        }
                    }
                }
                $res["overview"] = $overview;
                break;
        }
        return $res;
    }

    public static function pwdReport($userId, $mode, $dayIndex)
    {
        $data = [];
        if($mode == 2){
            if($dayIndex){
                $end = date("Y-m-d", strtotime($dayIndex) - 86400);
            }else{
                $end = date("Y-m-d");
            }
            $start = date("Y-m-d", strtotime($end) - 86400 * 19);
        }else{
            $end = date("Y-m-d");
            $yesterday = date("Y-m-d", time() - 86400);
            $thisWeek = date("Y-m-d", time() - (date("N") - 1) * 86400);
            $thisMonth = date("Y-m", time()) . "-01";
            $start = $yesterday;
            foreach ([$thisWeek, $thisMonth] as $day) {
                if ($day < $start) $start = $day;
            }
            $data = [
                ["day" => $yesterday, "day_desc" => "昨日", "order_num" => 0, "order_amount" => 0, "uv_sv_amount" => 0, "order_sv_amount" => 0],
                ["day" => $thisWeek, "day_desc" => "本周", "order_num" => 0, "order_amount" => 0, "uv_sv_amount" => 0, "order_sv_amount" => 0],
                ["day" => $thisMonth, "day_desc" => "本月", "order_num" => 0, "order_amount" => 0, "uv_sv_amount" => 0, "order_sv_amount" => 0],
            ];
        }
        $dataMap = [];
        $activityIds = Activity::where("type", PromotionEnum::ACTIVITY_TYPE_B)->where("scene", PromotionEnum::ACTIVITY_SCENE_PWD)->column("id"); // 获取口令活动ID集合
        if($activityIds){
            // 订单
            $orderData = ActivityReport::where("user_id", $userId)->whereIn("activity_id", $activityIds)->whereBetween("day", [$start, $end])
                ->field("day, sum(reward_num) sum_num, sum(reward_amount) sum_amount")->group("day")->select()->toArray();
            if($orderData){
                foreach ($orderData as $vo){
                    $dataMap[$vo["day"]] = ["order_num" => intval($vo["sum_num"]), "order_amount" => round($vo["sum_amount"], 2)];
                }
            }

            // 星愿值
            $svData = UserPwdActRec::where('user_id', $userId)->where("sv", ">", 0)->whereBetween("day", [$start, $end])
                ->field('day, relate_type, sum(sv) sv')->group('day,relate_type')->select()->toArray();
            if($svData) {
                $unitSv = intval(ConfigService::get("crm", "sv_exchange", 200));
                foreach ($svData as $s){
                    switch ($s['relate_type']) {
                        case PromotionEnum::PWD_ACT_CONDITION_UV:
                            $dataMap[$s["day"]]["uv_sv_amount"] = ($dataMap[$s["day"]]["uv_sv_amount"] ?? 0) + round($s['sv'] / $unitSv, 2);
                            break;
                        case PromotionEnum::PWD_ACT_CONDITION_ORDER:
                        case PromotionEnum::PWD_ACT_CONDITION_SPECIAL_ORDER:
                            $dataMap[$s["day"]]["order_sv_amount"] = ($dataMap[$s["day"]]["order_sv_amount"] ?? 0) + round($s['sv'] / $unitSv, 2);
                            break;
                    }
                }
            }
        }
        if(empty($dataMap)) return $data;
        if($mode == 2){
            for ($i = 0; $i < 20; $i++){
                $day = date("Y-m-d", strtotime($end) - 86400 * $i);
                $data[] = [
                    "day" => $day,
                    "order_num" => $dataMap[$day]["order_num"] ?? 0,
                    "order_amount" => $dataMap[$day]["order_amount"] ?? 0,
                    "uv_sv_amount" => $dataMap[$day]["uv_sv_amount"] ?? 0,
                    "order_sv_amount" => $dataMap[$day]["order_sv_amount"] ?? 0
                ];
            }
        }else{
            foreach ($dataMap as $day => $vo){
                foreach ($data as $k => $v) {
                    if (($k == 0 && $day == $v["day"]) || ($k > 0 && $day >= $v["day"])) {
                        $data[$k]["order_num"] += $vo['order_num'] ?? 0;
                        $data[$k]["order_amount"] += $vo['order_amount'] ?? 0;
                        $data[$k]["uv_sv_amount"] += $vo['uv_sv_amount'] ?? 0;
                        $data[$k]["order_sv_amount"] += $vo['order_sv_amount'] ?? 0;
                    }
                }
            }
        }
        return $data;
    }

    public function orderCallback(array $orderData)
    {
        $orderId = date("Ym", strtotime($orderData["create_time"])) . "_" . $orderData["id"];

        try {
            $redis = Cache::store('redis')->handler();
        } catch (\Exception $e) {
            self::$error = "LOG_REDIS初始化失败：" . $e->getMessage();
            return false;
        }

        $logKey = "order_log_" . $orderId;
        if (!$redis->setnx($logKey, 1)) {
            self::$error = "重复请求";
            return false;
        }
        $redis->expire($logKey, 60);

        if ($orderData["order_status"] < 2) {
            self::$error = "订单未支付";
            $redis->del($logKey);
            return false;
        }

        $activityId = 0; $userId = 0; $pid = "";
        if ($orderData["supplier_id"] == 104) { // 饿了么
            if (empty($orderData["receive_time"])) {
                self::$error = "订单未确认收获";
                $redis->del($logKey);
                return false;
            }
            if ($orderData["pid"]) { // 淘宝闪购
                $page = PromotionPage::where("type", ">", 1)->where("user_type", PromotionPage::USER_TYPE_LTB)->where("pid", $orderData["pid"])->where("status", PromotionPage::STATUS_SUCCESS)->find();
                if ($page) {
                    $act = Activity::where("scene", PromotionEnum::ACTIVITY_SCENE_LINK)->where("out_act_id", PromotionEnum::SPECIAL_ELM_ACT_PREFIX . $page->type)->where("type", PromotionEnum::ACTIVITY_TYPE_B)->find();
                    if ($act) {
                        $assign = QrcodeUserLog::where("qrcode_id", $act->id)->where("type", $act->scene)->where("user_id", $page->user_id)->where("is_default", 1)->find();
                        if ($assign) {
                            $activityId = intval($act->id);
                            $userId = intval($page->user_id);
                            $pid = $page->pid;
                        }
                    }
                }
            }
        }

        if ($activityId <= 0) {
            if (empty($orderData["sid"])) {
                self::$error = "缺少sid";
                $redis->del($logKey);
                return false;
            }
            $pid = $orderData["sid"];
            $sidArr = explode("_", $orderData["sid"]);
            if (count($sidArr) == 2 && substr($sidArr[0], 0, 1) == "a" && substr($sidArr[1], 0, 1) == "u") {// a%d_u%d
                $activityId = intval(substr($sidArr[0], 1));
                $userId = intval(substr($sidArr[1], 1));
            } else { // 口令
                if ($orderData["supplier_id"] == 121) {
                    $pwd = ElmPwd::where("pid", $orderData["sid"])->whereIn("order_type", [PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU, PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU_KA])->find();
                    if ($pwd) {
                        $assign = QrcodeUserLog::whereIn("qrcode_id", $pwd->id)->whereIn("type", PromotionEnum::ACTIVITY_SCENE_PWD)->where("is_default", 1)->find();
                        if ($assign) {
                            $activityId = intval($pwd->activity_id);
                            $userId = intval($assign->user_id);
                        }
                    }
                }
            }

            if ($activityId <= 0) {
                self::$error = "sid格式错误";
                $redis->del($logKey);
                return false;
            }
        }

        $order = Order::where("order_id", $orderId)->find();
        if ($order) {
            $activityId = $order->activity_id;
            $userId = $order->user_id;
        }

        $activity = Activity::where("id", $activityId)->find();
        if (!$activity) {
            self::$error = "活动不存在";
            $redis->del($logKey);
            return false;
        }
//        if (!$order) {
//            if ($activity->status != PromotionEnum::STATUS_UP) {
//                self::$error = "活动已下线";
//                $redis->del($logKey);
//                return false;
//            }
//        }
        $isOrderType = 0;
        $eventTime = $orderData["create_time"]; $rewardType = PromotionEnum::PLAN_REWARD_TYPE_CPS;
        foreach ($activity->order_type as $ot) {
            switch ($ot) {
                case PromotionEnum::ACTIVITY_ORDER_TYPE_BWC:
                    $isOrderType = $orderData["order_type"] == 1401 ? $ot : 0;
                    $eventTime = $orderData["receive_time"]; $rewardType = PromotionEnum::PLAN_REWARD_TYPE_CPA;
                    break;
                case PromotionEnum::ACTIVITY_ORDER_TYPE_PINTUAN:
                    $isOrderType = in_array($orderData["order_type"], [1410, 1411]) ? $ot : 0;
                    $eventTime = $orderData["receive_time"]; $rewardType = PromotionEnum::PLAN_REWARD_TYPE_CPA;
                    break;
                case PromotionEnum::ACTIVITY_ORDER_TYPE_TAOBAO_SHANGOU:
                    $isOrderType = ($orderData["order_type"] == 1400 && $orderData["order_channel"] == "taobao_shangou") ? $ot : 0;
                    $eventTime = $orderData["receive_time"]; $rewardType = PromotionEnum::PLAN_REWARD_TYPE_CPA;
                    break;
                case PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU:
                    if ($orderData["create_time"] >= "2025-06-11 00:00:00") {
                        $isOrderType = $orderData["order_type"] == 2600 ? $ot : 0;
                    }else{
                        $isOrderType = ($orderData["order_type"] == 2600 && $orderData["attr_order_desc"] == 1) ? $ot : 0;
                    }
                    $rewardType = PromotionEnum::PLAN_REWARD_TYPE_CPA;
                    break;
                case PromotionEnum::ACTIVITY_ORDER_TYPE_XIANYU_KA:
                    $isOrderType = $ot;
                    if ($orderData["order_type"] != 2600) $rewardType = PromotionEnum::PLAN_REWARD_TYPE_CPA;
                    break;
                case PromotionEnum::ACTIVITY_ORDER_TYPE_ALL:
                    $isOrderType = $ot;
                    break;

            }
            if ($isOrderType > 0) break;
        }

        $actCommissionKey = "act_order_commission_" . substr($eventTime, 0, 10) . "_a" . $activityId . "_u" . $userId."_ot".$isOrderType; $actCommissionLeak = 0;
        $actOrderCountKey = "act_order_count_" . substr($eventTime, 0, 10) . "_a" . $activityId . "_u" . $userId."_ot".$isOrderType; $actOrderCountLeak = 0;
        $pwdKey = "pwd_order_" . substr($eventTime, 0, 10) . "_p" . $pid . "_u" . $userId."_ot".$isOrderType; $pwdLeak = 0;

        $action = in_array($orderData["order_status"], [2, 3, 4]) ? 1 : 2;
        $eventTimeSecond = strtotime($eventTime);

        Db::startTrans();
        try {
            $hasLog = 0;

            $preCommission = $orderData["pre_commission"] - $orderData["third_service_fee"];
            $preCommission = max($preCommission, 0);
            $commission = $orderData["commission"] - $orderData["third_service_fee"];
            $commission = max($commission, 0);
            $subsidy = $orderData["activity_fee"] - $orderData["activity_service_fee"];
            $subsidy = max($subsidy, 0);

            if (!$order) {
                $log = ActivityLog::where("event_value", $orderId)->find();
                if ($log) {
                    $hasLog = 1;
                }
                if ($hasLog == 0) {
                    $order = new Order();
                    $order->activity_id = $activityId;
                    $order->user_id = $userId;
                    $order->order_id = $orderId;

                    if ($isOrderType > 0){
                        // 活动佣金扣量
                        $commissionLeak = ActivityLeak::where("activity_id", $activityId)->where("type", PromotionEnum::ACTIVITY_LEAK_TYPE_COMMISSION)
                            ->where("order_type", $isOrderType)
                            ->whereRaw("(find_in_set(".intval($userId).", user_ids) or user_ids = '')")
                            ->whereRaw("start_time <= ? and (end_time >= ? or end_time = 0)", [$eventTimeSecond, $eventTimeSecond])
                            ->order(["created_at" => 'desc', 'id' => 'desc'])
                            ->find();
                        if (($commissionLeak->is_enable ?? 0) == 1) {
                            $actCommission = $orderData["receive_time"] ? $commission : $preCommission;
                            foreach ($commissionLeak->steps as $index => $step) {
                                if ($step["min"] <= $actCommission && $step["max"] >= $actCommission) {
                                    $actCommissionKey .= "_s" . $index;
                                    $actCommissionNum = $redis->incr($actCommissionKey);
                                    $actCommissionLeak = 1;
                                    if ($step["rate"] > 0) {
                                        if ((new LeakService(0, number_format($step["rate"] / 100, 4, ".", "")))->processOrder($actCommissionNum)) {
                                            $actCommissionLeak = 2;
                                        }
                                    }
                                    break;
                                }
                            }
                        }

                        // 活动订单数口令
                        if ($actCommissionLeak != 2) {
                            $orderCountLeak = ActivityLeak::where("activity_id", $activityId)->where("type", PromotionEnum::ACTIVITY_LEAK_TYPE_ORDER_COUNT)
                                ->where("order_type", $isOrderType)
                                ->whereRaw("(find_in_set(".intval($userId).", user_ids) or user_ids = '')")
                                ->whereRaw("start_time <= ? and (end_time >= ? or end_time = 0)", [$eventTimeSecond, $eventTimeSecond])
                                ->order(["created_at" => 'desc', 'id' => 'desc'])
                                ->find();
                            if (($orderCountLeak->is_enable ?? 0) == 1) {
                                $actOrderCountNum = $redis->incr($actOrderCountKey);
                                $actOrderCountLeak = 1;
                                foreach ($orderCountLeak->steps as $step) {
                                    if ($step["min"] <= $actOrderCountNum && $step["max"] >= $actOrderCountNum) {
                                        if ($step["rate"] > 0) {
                                            if ((new LeakService(($step["min"] > 0 ? $step["min"] - 1 : 0), number_format($step["rate"] / 100, 4, ".", "")))->processOrder($actOrderCountNum)) {
                                                $actOrderCountLeak = 2;
                                            }
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        // 口令扣量
                        if($actCommissionLeak != 2 && $actOrderCountLeak != 2){
                            $withdrawType = User::where("id", $userId)->value("withdraw_type");
                            $baseLeakRate = 0;
                            $leakNum = 0;
                            $leakRate = 0;
                            if ($withdrawType == PromotionEnum::TX_MODE_PERSON) {
                                $baseLeakRate = 6.6;
                                $leakRate = $baseLeakRate;
                            }
                            if (($pwd->deduct_type ?? 0) > 0) {
                                $leakNum += $pwd->order_num;
                                $leakRate += $pwd->deduct_rate;
                            }
                            if ($leakRate > 0) {
                                if ($leakRate > 100) $leakRate = 100;

                                $pwdNum = $redis->incr($pwdKey);
                                $pwdLeak = 1;

                                if ($baseLeakRate > 0 && $leakNum > 0 && $pwdNum <= $leakNum) {
                                    if ((new LeakService(0, round($baseLeakRate / 100, 2)))->processOrder($pwdNum)) {
                                        $pwdLeak = 2;
                                    }
                                } else {
                                    if ((new LeakService($leakNum, round($leakRate / 100, 2)))->processOrder($pwdNum)) {
                                        $pwdLeak = 2;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if ($hasLog == 0 && $actCommissionLeak != 2 && $actOrderCountLeak != 2 && $pwdLeak != 2) {
                // 保存订单数据
                $order->parent_order_id = $orderData["parent_order_id"];
                $order->sub_order_id = $orderData["sub_order_id"];
                $order->order_type = $orderData["order_type"];
                $order->order_status = $action;
                $order->item_id = $orderData["item_id"];
                $order->item_title = $orderData["item_title"];
                $order->item_cover = $orderData["item_cover"];
                $order->order_price = $orderData["order_price"];
                $order->pay_price = $orderData["pay_price"];
                $order->ratio = $orderData["rate"] - $orderData["third_service_ratio"];
                $order->ratio = max($order->ratio, 0);
                $order->pre_commission = $preCommission;
                $order->commission = $commission;
                $order->subsidy = $subsidy;
                $order->pid = $orderData["pid"];
                $order->sid = $orderData["sid"];
                $order->city = $orderData["city"];
                $order->create_time = $orderData["create_time"];
                $order->receive_time = $orderData["receive_time"] ?: null;

                //奖励
                if ($userId > 0) {
                    if ($isOrderType > 0 && $order->order_status == PromotionEnum::ORDER_STATUS_VALID) { // 是否可奖励
                        if (empty($order->reward_time)) { // 没有奖励
                            $userRole = User::where("id", $userId)->value("role_id");
                            $plans = Plan::where("activity_id", $activityId)->where("role_id", $userRole)->where("reward_type", $rewardType)->select();
                            foreach ($plans as $plan) {
                                $isMatchedOrderType = true;
                                if ($plan->order_type > 0) {
                                    $group = OrderType::find($plan->order_type);
                                    $isMatchedOrderType = in_array($order->order_type, ($group->items ?? []));
                                }
                                if ($isMatchedOrderType) {
                                    $receiveTime = $order->receive_time ? strtotime($order->receive_time) : strtotime($order->create_time) + 365 * 86400;
                                    $order->reward_time = date("Y-m-d H:i:s", Plan::settleTime($receiveTime, $plan->settle_type, $plan->settle_day));

                                    if ($plan->reward_type == PromotionEnum::PLAN_REWARD_TYPE_CPA) {
                                        $order->reward_amount = $plan->reward_amount + $plan->subsidy_amount;
                                    } else {
                                        if ($plan->reward_mode == PromotionEnum::PLAN_REWARD_MODE_ACTUAL) {
                                            if ($order->ratio * 100 > $plan->reward_amount) {
                                                $amount = round(($order->receive_time ? $order->commission : $order->pre_commission) * ($plan->reward_amount * 0.01 / $order->ratio) + $order->subsidy * $plan->subsidy_amount * 0.01, 2);
                                            } else {
                                                $amount = round(($order->receive_time ? $order->commission : $order->pre_commission) + $order->subsidy * $plan->subsidy_amount * 0.01, 2);
                                            }
                                        } else {
                                            $amount = round(($order->receive_time ? $order->commission : $order->pre_commission) * $plan->reward_amount * 0.01 + $order->subsidy * $plan->subsidy_amount * 0.01, 2);
                                        }
                                        $order->reward_amount = $amount;
                                    }

                                    $userAgent = AgentLog::where("user_id", $userId)->where("change_time", "<=", strtotime($order->create_time))->order("change_time", "desc")->order("id", "desc")->find();
                                    $agentRate = null;
                                    if ($userAgent) {
                                        if ($userAgent->is_rebate > 0 && $userAgent->level == 1 && $userAgent->pid > 0) {
                                            $parentAgent = AgentLog::where("user_id", $userAgent->pid)->where("change_time", "<=", strtotime($order->create_time))->order("change_time", "desc")->order("id", "desc")->find();
                                            if ($parentAgent) {
                                                if ($parentAgent->is_rebate > 0 && $parentAgent->level == 2) {
                                                    $agentRebate = AgentRebate::where("agent_id", $userAgent->id)->whereRaw("activity_id = 0 or activity_id = ?", [$activityId])->order("id", "desc")->find();
                                                    $agentRate = $agentRebate->rate ?? 100;
                                                    $order->agent_id = $parentAgent->user_id;
                                                    $order->agent_reward_amount = round($order->reward_amount * (100 - $agentRate) / 100, 2);
                                                    $order->reward_amount -= $order->agent_reward_amount;
                                                }
                                            }
                                        }
                                    }
                                    $order->plan = ["plan_id" => $plan->id, "settle_type" => $plan->settle_type, "settle_day" => $plan->settle_day, "reward_amount" => $plan->reward_amount, "subsidy_amount" => $plan->subsidy_amount, "reward_type" => $plan->reward_type, "reward_mode" => $plan->reward_mode, "agent_rate" => $agentRate];
                                    break;
                                }
                            }
                        } else { // 奖励了
                            if ($order->receive_time){
                                if($order->plan['reward_type'] == PromotionEnum::PLAN_REWARD_TYPE_CPS) {
                                    if($order->plan['reward_mode'] == PromotionEnum::PLAN_REWARD_MODE_ACTUAL) {
                                        if($order->ratio*100 > $order->plan['reward_amount']) {
                                            $amount = round($order->commission * ($order->plan['reward_amount'] * 0.01 / $order->ratio) + $order->subsidy * $order->plan['subsidy_amount'] * 0.01, 2);
                                        }else{
                                            $amount = round($order->commission + $order->subsidy * $order->plan['subsidy_amount'] * 0.01, 2);
                                        }
                                    }else{
                                        $amount = round($order->commission * $order->plan['reward_amount'] * 0.01 + $order->subsidy * $order->plan['subsidy_amount'] * 0.01, 2);
                                    }
                                    $order->reward_amount = $amount;
                                    if ($order->agent_id > 0 && !is_null($order->plan['agent_rate'] ?? null)) {
                                        $order->agent_reward_amount = round($order->reward_amount * (100 - $order->plan['agent_rate']) / 100, 2);
                                        $order->reward_amount -= $order->agent_reward_amount;
                                    }
                                }
                                $order->reward_time = date("Y-m-d H:i:s", Plan::settleTime(strtotime($order->receive_time), $order->plan['settle_type'], $order->plan['settle_day']));
                            }
                        }
                    } else {
                        if ($order->reward_time && empty($order->unreward_time) && empty($order->settle_time)) { // 是否需要退回
                            $order->unreward_time = date("Y-m-d H:i:s");
                            $order->reward_amount = 0;
                            $order->agent_reward_amount = 0;
                        }
                    }
                }
                $order->save();
            }

            // 插入回传日志
            ActivityLog::create([
                "activity_id" => $activityId,
                "user_id" => $userId,
                "event" => 6,
                'event_type' => $action,
                "event_value" => $orderId,
                "extra" => $orderData,
            ]);

            Db::commit();
        } catch (\Exception  $e) {
            Db::rollback();
            if ($actCommissionLeak > 0) $redis->decr($actCommissionKey);
            if ($actOrderCountLeak > 0) $redis->decr($actOrderCountKey);
            if ($pwdLeak > 0) $redis->decr($pwdKey);
            self::$error = $e->getMessage();
            $redis->del($logKey);
            return false;
        }
        $redis->del($logKey);
        if ($hasLog == 0 && $actCommissionLeak != 2 && $actOrderCountLeak != 2 && $pwdLeak != 2) {
            $redis->sAdd("gen_activity_report_queue", json_encode(['a' => $activityId, 't' => PromotionEnum::ACTIVITY_SCENE_LINK, 'u' => $userId, 'd' => substr($eventTime, 0, 10), 'e' => PromotionEnum::ACTIVITY_CONDITION_ORDER]));
//            Console::call('gen_activity_report', ['-a' . $activityId, '-t' . PromotionEnum::ACTIVITY_SCENE_LINK, '-u' . $userId, '-d' . substr($eventTime, 0, 10), '-e' . PromotionEnum::ACTIVITY_CONDITION_ORDER]);
        }
        return true;
    }
}