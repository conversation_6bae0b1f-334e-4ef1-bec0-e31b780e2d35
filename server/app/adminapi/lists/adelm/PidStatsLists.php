<?php

namespace app\adminapi\lists\adelm;

use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsExcelInterface;
use app\common\lists\ListsSearchInterface;
use app\common\model\elm_adzone_categories\AdzoneCategoryRel;
use app\common\model\elm_adzone_categories\Category;
use think\facade\Cache;
use think\facade\Db;

class PidStatsLists extends BaseAdminDataLists implements ListsSearchInterface, ListsExcelInterface
{
    public function setSearch(): array
    {
        return [];
    }

    private function getAdzoneSql(){
        $type = $this->request->get("type", 1);
        $start = $this->request->get("ts");
        $end = $this->request->get("te");
        if (empty($start) && empty($end)) {
            $start = date("Y-m-d");
            $end = $start;
        }elseif(empty($start)){
            $start = $end;
        }elseif(empty($end)){
            $end = $start;
        }
        $channelId = $this->request->get('channel_id');
        $categoryId = $this->request->get("category_id");

        if ($type == 2) {
            $adzoneSql = "username = '18618192650'";
        } else {
            $adzoneSql = "(media_id != 1205005)";
        }
        if (intval($categoryId) > 0) {
            $adzones = AdzoneCategoryRel::whereIn("category_id", Category::getAllCategoryById($categoryId))->column("adzone");
            if ($adzones) {
                if ($channelId) {
                    if (in_array($channelId, $adzones)) {
                        $adzoneSql .= " and ad_zone_id = '$channelId'";
                        $pwdAdzones = [$channelId];
                    } else {
                        $adzoneSql .= " and ad_zone_id = '-1'";
                        $pwdAdzones = [];
                    }
                } else {
                    $adzoneSql .= " and ad_zone_id in ('" . implode("','", $adzones) . "')";
                    $pwdAdzones = $adzones;
                }
            } else {
                $adzoneSql .= " and ad_zone_id = '-1'";
                $pwdAdzones = [];
            }
        } elseif ($channelId) {
            $adzoneSql .= " and ad_zone_id = '$channelId'";
            $pwdAdzones = [$channelId];
        }

        if ($type == 2) {
            if (isset($pwdAdzones)) {
                $adzoneList = [];
                if (count($pwdAdzones) > 0) {
                    $adzoneList = Db::query("select adzone_id from warehouse.pid_client_logs where supplier_id = 104 and member_id = '12555424' and adzone_id in ('" . implode("','", $pwdAdzones) . "') and type = 1 and exp_time = 0");
                }
            }else{
                $adzoneList = Db::query("select adzone_id from warehouse.pid_client_logs where supplier_id = 104 and member_id = '12555424' and adzone_id != '' and type = 1 and exp_time = 0");
            }
            if ($adzoneList) {
                $adzones = [];
                foreach ($adzoneList as $item) {
                    if (!in_array($item["adzone_id"], $adzones)) $adzones[] = $item["adzone_id"];
                }
                $adzoneSql = "username = '18618192650' and ad_zone_id in ('" . implode("','", $adzones) . "')";
            }else{
                $adzoneSql = "username = '18618192650' and ad_zone_id = '-1'";
            }
        }

        return [
            "adzone_sql" => $adzoneSql,
            "start" => $start,
            "end" => $end,
            "type" => $type,
        ];
    }


    public function lists(): array
    {
        $req = $this->getAdzoneSql();

        $statis = Db::query("select report_date, pid, ad_zone_id, ad_zone_name, click_pv, click_uv, pv, uv, order_num, income, settle, sg_order_num, sg_fix_price_order_num, sg_old_user_order_num, sg_new_user_order_num 
    from byn_data.elm_ad_zone_reports where report_date BETWEEN ? AND ? and ".$req["adzone_sql"], [$req["start"], $req["end"]]);

        if (empty($statis)) return [];

        $dataMap = []; $pids = [];
        foreach ($statis as $s) {
            if (!in_array($s["pid"], $pids)) $pids[] = $s["pid"];
            $dataMap[$s["report_date"]][$s["pid"]] = array_merge($s, ["cps_order_num" => 0, "cps_income" => 0, "cps_settle" => 0, "cpa_order_num" => 0, "cpa_income" => 0, "cpa_settle" => 0, "bwc_order_num" => 0, "bwc_income" => 0, "bwc_settle" => 0]);
        }

        { // 获取redis存放的最小日期数据
            $t = date("Y-m-d", time() - 86400 * 91);
            for ($i = 0; ; $i++) {
                $rday = date("Y-m-d", strtotime($t) + $i * 86400);
                $expireTime = strtotime(substr($rday, 0, 7)."-01 23:59:59") + 86400 * 91;
                if($expireTime <= time()) continue;
                $mrd = $rday;
                break;
            }
        }

        // 时间段拆分，redis时间段和mysql时间段分开处理
        $redisTime = []; $mysqlTime = [];
        if ($req["start"] >= $mrd) {
            $redisTime = [$req["start"], $req["end"]];
        }else{
            if($req["end"] < $mrd){
                $mysqlTime = [$req["start"], $req["end"]];
            }else{
                $mysqlTime = [$req["start"], date("Y-m-d", strtotime($mrd) - 86400)];
                $redisTime = [$mrd, $req["end"]];
            }
        }

        if ($redisTime){
            $redisData = [];
            try {
                $redis = Cache::store('redis')->handler();
                for ($i = 0; ; $i++) {
                    $rday = date("Y-m-d", strtotime($redisTime[0]) + $i * 86400);
                    if ($rday > $redisTime[1]) break;
                    $redisData[$rday] = $redis->hGetAll("stats_pid_order_".$rday) ?: [];
                }
                foreach ($redisData as $rday => $rd) {
                    foreach ($rd as $pid => $pidData){
                        if (isset($dataMap[$rday][$pid])) {
                            $pidData = json_decode($pidData, true);
                            $dataMap[$rday][$pid]["cps_order_num"] += $pidData[0];
                            $dataMap[$rday][$pid]["cps_income"] += $pidData[1];
                            $dataMap[$rday][$pid]["cps_settle"] += $pidData[2];
                            $dataMap[$rday][$pid]["cpa_order_num"] += $pidData[3];
                            $dataMap[$rday][$pid]["cpa_income"] += $pidData[4];
                            $dataMap[$rday][$pid]["cpa_settle"] += $pidData[5];
                            $dataMap[$rday][$pid]["bwc_order_num"] += $pidData[6] ?? 0;
                            $dataMap[$rday][$pid]["bwc_income"] += $pidData[7] ?? 0;
                            $dataMap[$rday][$pid]["bwc_settle"] += $pidData[8] ?? 0;
                        }
                    }
                }
            } catch (\Exception $e) { // redis连接失败，直接用mysql
                $mysqlTime = [$req["start"], $req["end"]];
            }
        }

        if ($mysqlTime) {
            $unionId = "16547094";
            if ($req["type"] == 2) {
                $unionId = "12555424";
            }
            $types = Db::query("select * from warehouse.order_types where FIND_IN_SET(104, supplier_id)");
            $cpsOrderTypes = ""; $cpaOrderTypes = "";
            foreach ($types as $type) {
                if ($type["type"] == 2) {
                    if ($cpaOrderTypes) {
                        $cpaOrderTypes .= ",";
                    }
                    $cpaOrderTypes .= $type["id"];
                }else{
                    if ($cpsOrderTypes) {
                        $cpsOrderTypes .= ",";
                    }
                    $cpsOrderTypes .= $type["id"];
                }
            }
            $months = getMonths($mysqlTime[0], $mysqlTime[1]);
            $dbName = "warehouse";
            foreach ($months as $month) {
                $tableName = "cps_orders_" . str_replace("-", "", $month);
                if (Db::query("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$dbName}' AND TABLE_NAME = '{$tableName}'")) {
                    $d4 = Db::query("
select
    pid, left(create_time, 10) day,
    count(if(order_type in ({$cpsOrderTypes}), 1, null)) cps_order_num, 
    sum(if(order_type in ({$cpsOrderTypes}), pre_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cps_pre_commission, 
    sum(if(order_type in ({$cpsOrderTypes}) and receive_time is not null, commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cps_commission,
    count(if(order_type in ({$cpaOrderTypes}), 1, null)) cpa_order_num, 
    sum(if(order_type in ({$cpaOrderTypes}), pre_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cpa_pre_commission, 
    sum(if(order_type in ({$cpaOrderTypes}) and receive_time is not null, commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cpa_commission, 
    count(if(order_type = 1401, 1, null)) bwc_order_num, 
    sum(if(order_type = 1401, pre_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_bwc_pre_commission, 
    sum(if(order_type = 1401 and receive_time is not null, commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_bwc_commission 
from {$dbName}.{$tableName} use index (cps_orders_union_index) 
where union_id = ? and pid in ('" . implode("','", $pids) . "') and supplier_id = 104 and create_time between ? and ? and order_status in (2,3,4) 
group by pid, left(create_time, 10)
", [$unionId, $mysqlTime[0]." 00:00:00", $mysqlTime[1]." 23:59:59"]);
                    if ($d4) {
                        foreach ($d4 as $dd){
                            if (isset($dataMap[$dd["day"]][$dd["pid"]])) {
                                $dataMap[$dd["day"]][$dd["pid"]]["cps_order_num"] += $dd["cps_order_num"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cps_income"] += $dd["sum_cps_pre_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cps_settle"] += $dd["sum_cps_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cpa_order_num"] += $dd["cpa_order_num"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cpa_income"] += $dd["sum_cpa_pre_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cpa_settle"] += $dd["sum_cpa_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["bwc_order_num"] += $dd["bwc_order_num"];
                                $dataMap[$dd["day"]][$dd["pid"]]["bwc_income"] += $dd["sum_bwc_pre_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["bwc_settle"] += $dd["sum_bwc_commission"];
                            }
                        }
                    }
                }
            }
            $dbName = "adpro";
            foreach ($months as $month) {
                $tableName = "ad_orders_" . str_replace("-", "", $month);
                if (Db::query("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$dbName}' AND TABLE_NAME = '{$tableName}'")) {
                    $d5 = Db::query("
select
    pid, left(create_time, 10) day,
    count(if(order_type in ({$cpsOrderTypes}), 1, null)) cps_order_num, 
    sum(if(order_type in ({$cpsOrderTypes}), ori_pre_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cps_pre_commission, 
    sum(if(order_type in ({$cpsOrderTypes}) and receive_time is not null, ori_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cps_commission,
    count(if(order_type in ({$cpaOrderTypes}), 1, null)) cpa_order_num, 
    sum(if(order_type in ({$cpaOrderTypes}), ori_pre_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cpa_pre_commission, 
    sum(if(order_type in ({$cpaOrderTypes}) and receive_time is not null, ori_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_cpa_commission, 
    count(if(order_type = 1401, 1, null)) bwc_order_num, 
    sum(if(order_type = 1401, ori_pre_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_bwc_pre_commission, 
    sum(if(order_type = 1401 and receive_time is not null, ori_commission - third_service_fee + activity_fee - activity_service_fee, 0)) sum_bwc_commission 
from {$dbName}.{$tableName} use index (cps_orders_union_index) 
where union_id = ? and pid in ('" . implode("','", $pids) . "') and supplier_id = 104 and create_time between ? and ? and order_status in (2,3,4) 
group by pid, left(create_time, 10)
", [$unionId, $mysqlTime[0]." 00:00:00", $mysqlTime[1]." 23:59:59"]);
                    if ($d5) {
                        foreach ($d5 as $dd){
                            if (isset($dataMap[$dd["day"]][$dd["pid"]])) {
                                $dataMap[$dd["day"]][$dd["pid"]]["cps_order_num"] += $dd["cps_order_num"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cps_income"] += $dd["sum_cps_pre_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cps_settle"] += $dd["sum_cps_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cpa_order_num"] += $dd["cpa_order_num"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cpa_income"] += $dd["sum_cpa_pre_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["cpa_settle"] += $dd["sum_cpa_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["bwc_order_num"] += $dd["bwc_order_num"];
                                $dataMap[$dd["day"]][$dd["pid"]]["bwc_income"] += $dd["sum_bwc_pre_commission"];
                                $dataMap[$dd["day"]][$dd["pid"]]["bwc_settle"] += $dd["sum_bwc_commission"];
                            }
                        }
                    }
                }
            }
        }

        $hasPermission = $this->request->get("has_permission", 0);
        $data = [];
        for ($i = 0; ; $i++){
            $day = date("Y-m-d", strtotime($req["end"]) - $i * 86400);
            if ($day < $req["start"]) break;
            if(isset($dataMap[$day])){
                foreach ($dataMap[$day] as $pid => $s){
                    $data[] = [
                        "adzone_name" => $s["ad_zone_name"],
                        "pid" => $pid,
                        "day" => $day,
                        "click_pv" => intval($s["click_pv"]),
                        "click_uv" => intval($s["click_uv"]),
                        "pv" => intval($s["pv"]),
                        "uv" => intval($s["uv"]),
                        "order_num" => intval($s["order_num"]),
                        "income" => round($s["income"] - ($hasPermission == 0 ? $s["cpa_income"] : 0), 2),
                        "settle" => round($s["settle"] - ($hasPermission == 0 ? $s["cpa_settle"] : 0), 2),
                        "sg_order_num" => intval($s["sg_order_num"]),
                        "sg_fix_price_order_num" => intval($s["sg_fix_price_order_num"]),
                        "sg_old_user_order_num" => intval($s["sg_old_user_order_num"]),
                        "sg_new_user_order_num" => intval($s["sg_new_user_order_num"]),
                        "cps_order_num" => intval($s["cps_order_num"]),
                        "cps_income" => round($s["cps_income"], 2),
                        "cps_settle" => round($s["cps_settle"], 2),
                        "cpa_order_num" => intval($hasPermission == 0 ? 0 : $s["cpa_order_num"]),
                        "cpa_income" => round($hasPermission == 0 ? 0 : $s["cpa_income"], 2),
                        "cpa_settle" => round($hasPermission == 0 ? 0 : $s["cpa_settle"], 2),
                        "bwc_order_num" => intval($s["bwc_order_num"]),
                        "bwc_income" => round($s["bwc_income"], 2),
                        "bwc_settle" => round($s["bwc_settle"], 2),
                    ];
                }
            }
        }
        return $data;
    }

    public function count(): int
    {
        return 20;
    }

    public function setExcelFields(): array
    {
        return [
            "adzone_name" => "推广位名称",
            "pid" => "推广位",
            "day" => "日期",
            "click_pv" => "页面访问次数",
            "click_uv" => "页面访问人数",
            "pv" => "详情页访问次数",
            "uv" => "详情页访问人数",
            "order_num" => "订单数",
            "income" => "预估佣金",
            "settle" => "结算佣金",
            "sg_order_num" => "闪购会场订单数",
            "sg_fix_price_order_num" => "闪购会场一口价订单数",
            "sg_old_user_order_num" => "闪购老客订单数",
            "sg_new_user_order_num" => "闪购新客订单数",
            "cps_order_num" => "CPS订单数",
            "cps_income" => "CPS预估佣金",
            "cps_settle" => "CPS结算佣金",
            "cpa_order_num" => "CPA订单数",
            "cpa_income" => "CPA预估佣金",
            "cpa_settle" => "CPA结算佣金",
            "bwc_order_num" => "叠红包订单数",
            "bwc_income" => "叠红包预估佣金",
            "bwc_settle" => "叠红包结算佣金",
        ];
    }

    public function setFileName(): string
    {
        return "口令统计";
    }

}