<?php

namespace app\adminapi\lists\shop;

use app\adminapi\lists\BaseAdminDataLists;
use app\common\model\byn\ElmActivity;
use app\common\model\byn\ElmShop;
use app\common\model\byn\ElmShopActRel;

class ClueActLists extends BaseAdminDataLists
{
    protected function queryWhere()
    {
        return ElmShopActRel::where('city_name', $this->params['city'])->whereIn('audit_status', [1, 3])->where('act_rec_id', '>', 0);
    }

    public function lists(): array
    {
        $list =  $this->queryWhere()->limit($this->limitOffset, $this->limitLength)->order('id', 'desc')->select();
        $actMap = ElmActivity::column('name', 'activity_id');
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'id' => $item['id'],
                'activity_name' => $actMap[$item['activity_id']] ?? '',
                'activity_id' => $item['activity_id'],
                'audit_status' => $item['audit_status'],
                'bonus_fee' => $item['bonus_fee'],
                'bonus_order_num' => $item['bouns_order_num'],
                'promotion_state' => $item['promotion_state'],
                'services_fee' => $item['services_fee'],
                'start_date' => $item['start_date'],
                'end_date' => $item['end_date'],
                'enroll_date' => $item['enroll_date'],
                'shop_code' => $item['shop_code'],
                'terminate_time' => $item['terminate_time'],
                'city_name' => $item['city_name'],
                'shop_name' => $item['ori_shop_name'],
                'order_amt_limit' => $item['order_amt_limit'],
            ];
        }
        return $data;
    }

    public function count(): int
    {
        return $this->queryWhere()->count();
    }
}