<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\validate\team_member;


use app\common\validate\BaseValidate;


/**
 * team_member验证器
 * Class TeamMemberValidate
 * @package app\adminapi\validate\team_member
 */
class TeamMemberValidate extends BaseValidate
{

     /**
      * 设置校验规则
      * @var string[]
      */
    protected $rule = [
        'id' => 'require',
        "team_id" => "require",
        "employee_id" => "require",
        "type" => "require",
    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',
        "team_id" => "团队",
        "employee_id" => "员工",
        "type" => "类型",
    ];


    /**
     * @notes 添加场景
     * @return TeamMemberValidate
     * <AUTHOR>
     * @date 2024/03/02 15:30
     */
    public function sceneAdd()
    {
        return $this->remove('id', true);
    }


    /**
     * @notes 删除场景
     * @return TeamMemberValidate
     * <AUTHOR>
     * @date 2024/03/02 15:30
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return TeamMemberValidate
     * <AUTHOR>
     * @date 2024/03/02 15:30
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}