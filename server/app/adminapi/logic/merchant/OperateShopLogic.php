<?php

namespace app\adminapi\logic\merchant;

use app\common\logic\BaseLogic;
use app\common\model\byn\ImportTask;
use app\common\model\clue\TrackRecords;
use app\common\model\evaluate_tool\StorePerformance;
use app\common\model\operate\OperateClick;
use app\common\model\operate\OperateIndexRule;
use app\common\model\operate\OperatePromotion;
use app\common\model\operate\OperateRuleTemp;
use app\common\model\operate\OperateShopRuleRels;
use app\common\model\operate\OperateShops;
use app\common\service\ConfigService;
use think\Exception;
use think\facade\Db;

class OperateShopLogic extends BaseLogic
{
    public function add(array $params)
    {
        if (empty($params['platform_info'])) {
            throw new \Exception("平台不存在");
        }
        foreach ($params['platform_info'] as $key => $value) {
            $shop = OperateShops::where('name', $value['name'])->where('platform', $value['platform'])->find();
            if ($shop) {
                throw new \Exception("客户已有代运营合作");
            }
            $userId = $value['user_id'] ?? 0;
            $shop = OperateShops::create($this->toModel($value));
            if (isset($params['temp_id'])) {
                $this->shopTempRel($params['temp_id'], $shop->id);
            }
            $this->bindAndUnbind($value, $shop, $userId);
        }
    }

    public function edit(array $params)
    {
        $shop = OperateShops::where('id', $params['id'])->find();
        if (!$shop) throw new \Exception('店铺不存在');
        OperateShops::where('id', $shop->id)->update($this->toModel($params));
        $shop = OperateShops::where('id', $params['id'])->find();
        $userId = $params['user_id'] ?? 0;
        if (isset($params['temp_id'])) {
            $this->shopTempRel($params['temp_id'], $shop->id);
        }
        $this->bindAndUnbind($params, $shop, $userId);
    }

    public function shopTempRel($tempId, $shopId)
    {
        OperateShopRuleRels::where('shop_id', $shopId)->delete();
        if ($tempId == 0) {
            return;
        }
        OperateShopRuleRels::create([
            'shop_id' => $shopId,
            'temp_id' => $tempId,
        ]);
        $total = OperateShopRuleRels::where('temp_id', $tempId)->count();
        OperateRuleTemp::where('id', $tempId)->update(['shop_count' => $total]);
    }

    public function toModel(array $params): array
    {
        $platform = $params['platform'];
        $fee = $params['fee'] ?? 0;
        if ($params['mode'] == 2) {
            if ($platform == 2) {
                $fee = 15;
            } elseif ($platform == 4) {
                $fee = 25;
            }
        }
        $data = [
            'name' => $params['name'],
            'category_id' => $params['category_id'] ?? 0,
            'tel' => $params['tel'] ?? '',
            'address' => $params['address'] ?? '',
            'is_dine' => $params['is_dine'] ?? 0,
            'is_charge' => $params['is_charge'] ?? 0,
            'store_area' => $params['store_area'] ?? 0,
            'is_new' => $params['is_new'] ?? 0,
            'brand_id' => $params['is_brand'] ?? 0,
            'start_time' => isset($params['start_time']) ? strtotime($params['start_time']) : 0,
            'end_time' => isset($params['end_time']) ? strtotime($params['end_time']) : 0,
            'fee' => $fee,
            'commission_rate' => $params['commission_rate'] ?? 0,
            'remark' => $params['remark'] ?? '',
            'level_tier' => $params['level_tier'] ?? '',
            'is_scaling' => $params['is_scaling'] ?? -1,
            'has_promotion' => $params['has_promotion'] ?? -1,
            'has_bwc' => $params['has_bwc'] ?? -1,
            'main_issues' => isset($params['main_issues']) ? json_encode(array_map('intval', $params['main_issues'])) : '',
            'reason' => $params['reason'] ?? '',
            'mode' => $params['mode'] ?? 0,
            'store_id' => $params['store_id'] ?? 0,
        ];
        if (!empty($params['user_id'])) {
            $data['user_id'] = $params['user_id'];
            $data['user_type'] = $params['user_type'] ?? 1;
        }
        if ($platform > 0) {
            $data['platform'] = $platform;
        }
        return $data;
    }

    public function bind(array $params)
    {
        $shop = OperateShops::where('id', $params['id'])->find();
        if (!$shop) {
            throw new \Exception('店铺不存在');
        }
        $params['platform'] = $shop->platform;
        $params['operate_id'] = $params['platform_info'][0]['user_id'] ?? 0;
        $params['store_id'] = $shop->store_id;
        $this->bindAndUnbind($params, $shop, $shop->user_id);
    }

    public function unbind(array $params)
    {
        $shop = OperateShops::where('id', $params['id'])->find();
        if (!$shop) {
            throw new \Exception('店铺不存在');
        }

        $day = 0;
        $dayAmount = 0;
        $now = time();
        if ($shop->start_time > 0) {
            $day = intval(($now - $shop->start_time) / 86400);
            if ($shop->mode == 2 || $shop->mode == 4) {
                $amount = $shop->platform == 2 ? 15 : 25;
                $dayAmount = $amount * $day;
            }
        }

        TrackRecords::where('clue_type', 2)
            ->where('clue_id', $shop->id)
            ->where('status', 3)
            ->where('cancel_time', 0)
            ->update([
                'cancel_time' => $now,
                'day' => $day,
                'day_amount' => $dayAmount,
            ]);
        OperateShops::where('id', $params['id'])->update([
            'status' => 3,
            "termination_time" => $now,
            'reason' => $params['reason'] ?? '',
        ]);
    }

    public function bindAndUnbind(array $params, OperateShops $shop, int $userId)
    {
        $day = 0;
        $dayAmount = 0;
        if ($shop->start_time > 0 && $shop->end_time > 0) {
            $day = intval(($shop->end_time - $shop->start_time) / 86400);
            if ($shop->mode == 2 || $shop->mode == 4) {
                $amount = $shop->platform == 2 ? 15 : 25;
                $dayAmount = $amount * $day;
            }
        }
        $frontAmount = 0;
        if ($shop->mode == 4 || $shop->mode == 3) {
            $frontAmount = $shop->fee;
        }

        $track = TrackRecords::where('clue_id', $shop->id)->where('clue_type', 2)->where('platform', $params['platform'])->where('cancel_time', 0)->find();
        if ($track) {
            if ($track->user_id == $params['operate_id'] || $track->user_id == 0) {
                TrackRecords::where('id', $track->id)->update([
                    'platform' => $params['platform'],
                    'store_id' => $shop->store_id ?? '',
                    'user_id' => $params['operate_id'],
                    'status' => 3,
                    'belong_id' => $userId,
                    'day' => $day,
                    'day_amount' => $dayAmount,
                    'front_amount' => $frontAmount,
                ]);
            } else {
                TrackRecords::where('id', $track->id)->update([
                    'cancel_time' => time(),
                    'status' => -1,
                ]);
            }
        } else {
            TrackRecords::create([
                'clue_id' => $shop->id,
                'clue_type' => 2,
                'user_id' => $params['operate_id'],
                'status' => 3,
                'platform' => $params['platform'],
                'store_id' => $shop->store_id ?? '',
                'belong_id' => $userId,
                'day' => $day,
                'day_amount' => $dayAmount,
                'front_amount' => $frontAmount,
            ]);
        }

        OperateShops::where('id', $shop->id)->update(['status' => 2]);
    }

    public function claimCounts(array $params)
    {
        return [
            'not_sign_count' => $this->queryWhere($params, 1)->count(),
            'sign_count' => $this->queryWhere($params, 2)->count(),
            'termination_count' => $this->queryWhere($params, 3)->count(),
            'expire_count' => $this->queryWhere($params, 4)->count(),
        ];
    }

    private function queryWhere(array $params, int $status)
    {
        $query = OperateShops::alias("a")
            ->leftJoin(['sq_track_records' => 'b'], "a.id = b.clue_id and b.clue_type = 2 and b.cancel_time = 0");
        if (isset($params['platform_type']) && $params['platform_type']) {
            if ($params['platform_type'] == 1) {
                $query->where("a.platform", '<', 6);
            } elseif ($params['platform_type'] == 2) {
                $query->where("a.platform", 6);
            }
        }
        if (isset($params['platform']) && $params['platform']) {
            if ($params['platform'] == 2) {
                $query->where("a.platform", 'in', [2, 6]);
            } elseif
            ($params['platform'] == 4) {
                $query->where("a.platform", 'in', [4, 6]);
            } else {
                $query->where("a.platform", $params['platform']);
            }
        }
        switch ($status) {
            case 1:
                $query->where("a.status", 1)->where(function ($subQuery) {
                    $subQuery->where('a.end_time', '>', time())->whereOr('a.end_time', 0);
                });
                break;
            case 2:
                $query->where("a.status", 2)->where(function ($subQuery) {
                    $subQuery->where('a.end_time', '>', time())->whereOr('a.end_time', 0);
                });
                break;
            case 3:
                $query->where("a.status", 3);
                break;
            case 4:
                $query->where("a.status", 'in', [1, 2])->where('a.end_time', '<', time())->where('a.end_time', '>', 0)->order(['a.end_time' => 'desc']);
                break;
        }

        if (isset($params["category_id"]) && $params['category_id']) {
            $query->where('a.category_id', $params['category_id']);
        }

        if (isset($params["is_new"]) && $params['is_new'] >= 0) {
            $query->where('a.is_new', $params['is_new']);
        }
        if (isset($params["is_brand"]) && $params['is_brand'] >= 0) {
            if ($params['is_brand'] == 1) {
                $query->where('a.brand_id', '>', 0);
            } else {
                $query->where('a.brand_id', 0);
            }
        }
        if (isset($params["is_dine"]) && $params['is_dine'] >= 0) {
            $query->where('a.is_dine', $params['is_dine']);
        }
        if (isset($params["store_area_start"]) && $params['store_area_start'] >= 0 && isset($params["store_area_end"]) && $params['store_area_end'] >= 0) {
            $query->whereBetween('a.store_area', [$params['store_area_start'], $params['store_area_end']]);
        }
        if (isset($params["user_id"]) && $params['user_id']) {
            $query->where('a.user_id', $params['user_id']);
        }
        if (isset($params["operate_id"]) && $params['operate_id']) {
            $query->where('b.user_id', $params['operate_id']);
        }
        if (isset($params["city"]) && $params['city']) {
            $query->where('a.city', 'like', '%' . $params['city'] . '%');
        }
        if (isset($params["keyword"]) && $params['keyword']) {
            $query->where(function ($query) use ($params) {
                $query->where('a.name', 'like', '%' . $params['keyword'] . '%')->whereOr('a.tel', $params['keyword']);
            });
        }
        if (isset($params["start_time"]) && $params['start_time'] && isset($params["end_time"]) && $params['end_time']) {
            switch ($params["time_type"]) {
                case 2:
                    $query->whereBetween('a.end_time', [strtotime($params["start_time"]), strtotime($params["end_time"])]);
                    break;
                case 3:
                    $query->whereBetween('a.create_time', [strtotime($params["start_time"]), strtotime($params["end_time"])]);
                    break;
                case 4:
                    $query->whereBetween('a.update_time', [strtotime($params["start_time"]), strtotime($params["end_time"])]);
                    break;
                default:
                    $query->whereBetween('a.start_time', [strtotime($params["start_time"]), strtotime($params["end_time"])]);
                    break;
            }
        }
        if (isset($params['level_tier']) && $params['level_tier']) {
            $query->where('a.level_tier', $params['level_tier']);
        }
        if (isset($params['is_scaling']) && $params['is_scaling'] >= 0) {
            $query->where('a.is_scaling', $params['is_scaling']);
        }
        if (isset($params['has_promotion']) && $params['has_promotion'] >= 0) {
            $query->where('a.has_promotion', $params['has_promotion']);
        }
        if (isset($params['has_bwc']) && $params['has_bwc'] >= 0) {
            $query->where('a.has_bwc', $params['has_bwc']);
        }
        if (isset($params['main_issues']) && $params['main_issues']) {
            $issues = explode(',', $params['main_issues']);
            $mainIssues = '';
            foreach ($issues as $issue) {
                $mainIssues .= $issue . '|';
            }
            $mainIssues = rtrim($mainIssues, '|');
            $query->where('a.main_issues', "regexp", $mainIssues);
        }
        if (isset($params['operating_day_star']) && $params['operating_day_star'] && isset($params['operating_day_end']) && $params['operating_day_end']) {
            $query->whereRaw("a.start_time > 0 and FLOOR((unix_timestamp(now())-a.start_time)/86400) between {$params['operating_day_star']} and {$params['operating_day_end']}");
        }
        if (isset($params['mode']) && $params['mode']) {
            $query->where('a.mode', $params['mode']);
        }
        return $query->group('a.id');
    }

    public function getConfig()
    {
        $operateConfig = ConfigService::get('operate');
        $clickRes = OperateClick::where('type', '<', 5)->field('sum(if(type = 1,num, null)) framework_clicks, sum(if(type = 2,num,null)) case_clicks, sum(if(type = 3,num, null)) policy_clicks, sum(if(type = 4,num, null)) merchant_policy_clicks, sum(if(type = 6,num, null)) signing_process_clicks')->select();
        return [
            'ad_img' => $operateConfig['ad_img'] ?? '',
            'framework' => $operateConfig['framework'] ?? '',
            'case' => $operateConfig['case'] ?? [],
            'policy' => $operateConfig['policy'] ?? '',
            'merchant_policy' => $operateConfig['merchant_policy'] ?? '',
            'signing_process' => $operateConfig['signing_process'] ?? '',
            'patrol_shop' => $operateConfig['patrol_shop'] ?? '',
            'framework_clicks' => (int)$clickRes[0]['framework_clicks'] ?? 0,
            'case_clicks' => (int)$clickRes[0]['case_clicks'] ?? 0,
            'policy_clicks' => (int)$clickRes[0]['policy_clicks'] ?? 0,
            'merchant_policy_clicks' => (int)$clickRes[0]['merchant_policy_clicks'] ?? 0,
            'signing_process_clicks' => (int)$clickRes[0]['signing_process_clicks'] ?? 0,
        ];
    }

    public function setConfig($params)
    {
        if (isset($params['ad_img']) && $params['ad_img']) {
            ConfigService::set('operate', 'ad_img', $params['ad_img']);
        }
        if (isset($params['framework']) && $params['framework']) {
            ConfigService::set('operate', 'framework', $params['framework']);
        }
        if (isset($params['case']) && $params['case']) {
            ConfigService::set('operate', 'case', $params['case']);
        }
        if (isset($params['policy']) && $params['policy']) {
            ConfigService::set('operate', 'policy', $params['policy']);
        }
        if (isset($params['merchant_policy']) && $params['merchant_policy']) {
            ConfigService::set('operate', 'merchant_policy', $params['merchant_policy']);
        }
        if (isset($params['signing_process']) && $params['signing_process']) {
            ConfigService::set('operate', 'signing_process', $params['signing_process']);
        }
        if (isset($params['patrol_shop']) && $params['patrol_shop']) {
            ConfigService::set('operate', 'patrol_shop', $params['patrol_shop']);
        }
    }

    public function upload($params)
    {
        ImportTask::create([
            'created_at' => date('Y-m-d H:i:s', time()),
            'updated_at' => date('Y-m-d H:i:s', time()),
            'file_type' => $params['platform'] == 1 ? 7 : 8,
            'file_name' => $params['platform'] == 1 ? '饿了么收益报表' : '美团收益报表',
            'url' => $params['url'],
            'status' => 1,
            'create_time' => date('Y-m-d'),
        ]);
    }

    public function addOperatePromotion($params)
    {
        OperatePromotion::create($params);
    }

    public function editOperatePromotion($params)
    {
        unset($params['create_time'], $params['update_time']);  // 同时删除多个 key

        OperatePromotion::where('id', $params['id'])->update($params);
    }

    public function deleteOperatePromotion($params)
    {
        OperatePromotion::where('id', $params['id'])->delete();
    }


    public function getShopScoreConfig()
    {
        $conf = ConfigService::get('operate', 'shop_score_config');
        return [
            'mt' => intval($conf['mt'] ?? 0),
            'elm' => intval($conf['elm'] ?? 0),
        ];
    }

    public function setShopScoreConfig($params)
    {
        return ConfigService::set('operate', 'shop_score_config', [
            'mt' => $params['mt'] ?? 0,
            'elm' => $params['elm'] ?? 0,
        ]);
    }

    public function shopScore($params)
    {
        $time = time();
        $evaluateToolDb = config("database.connections.evaluate_tool.database");
        $sql = "select count(1) total_count,
       count(if(b.score_anomaly_count = 0, 1, null)) normal_count
      from sq_operate_shops a
               inner join $evaluateToolDb.store_performance b
                          on a.store_id = b.store_id and b.day_dimension = '{$params['date_time']}'
      where a.status = 2 and a.end_time > {$time}";
        if (isset($params['admin_id']) && $params['admin_id']) {
            $sql .= " and a.id in (select clue_id from sq_track_records where clue_type = 2 and user_id = {$params['admin_id']} and cancel_time = 0)";
        }
        if (isset($params['name']) && $params['name']) {
            $sql .= " and a.name like '%{$params['name']}%'";
        }
        if (isset($params['platform']) && $params['platform']) {
            $sql .= " and a.platform = {$params['platform']}";
        }
        $res = Db::connect('mysql')->query($sql);
        $data = [
            'total_count' => 0,
            'normal_count' => 0,
            'normal_rate' => 0,
            'abnormal_count' => 0,
            'abnormal_rate' => 0,
        ];
        if (!isset($res[0])) {
            return $data;
        }

        $data['total_count'] = $res[0]['total_count'] ?? 0;
        $data['normal_count'] = $res[0]['normal_count'] ?? 0;
        $data['abnormal_count'] = $data['total_count'] - $data['normal_count'];
        if ($data['total_count'] > 0) {
            $data['normal_rate'] = round($data['normal_count'] / $data['total_count'] * 100, 2);
            $data['abnormal_rate'] = round($data['abnormal_count'] / $data['total_count'] * 100, 2);
        }
        return $data;
    }

    public function getShopGoodsConfig()
    {
        $conf = ConfigService::get('operate', 'shop_goods_config');
        return [
            'mt' => intval($conf['mt'] ?? 0),
            'elm' => intval($conf['elm'] ?? 0),
        ];
    }

    public function setShopGoodsConfig($params)
    {
        return ConfigService::set('operate', 'shop_goods_config', [
            'mt' => $params['mt'] ?? 0,
            'elm' => $params['elm'] ?? 0,
        ]);
    }

    public function shopGoods($params)
    {
        $time = time();
        $evaluateToolDb = config("database.connections.evaluate_tool.database");
        $sql = "select count(1) total_count,
       count(if(b.goods_anomaly_count = 0, 1, null)) normal_count
      from sq_operate_shops a
               inner join $evaluateToolDb.store_performance b
                          on a.store_id = b.store_id and b.day_dimension = '{$params['date_time']}'
      where a.status = 2 and a.end_time > {$time}";
        if (isset($params['admin_id']) && $params['admin_id']) {
            $sql .= " and a.id in (select clue_id from sq_track_records where clue_type = 2 and user_id = {$params['admin_id']} and cancel_time = 0)";
        }
        if (isset($params['name']) && $params['name']) {
            $sql .= " and a.name like '%{$params['name']}%'";
        }
        if (isset($params['platform']) && $params['platform']) {
            $sql .= " and a.platform = {$params['platform']}";
        }
        if (isset($params['anomaly_type']) && $params['anomaly_type']) {
            $anomalyType = [
                1 => '低质商品优化',
                2 => '进阶商品优化',
                3 => '详细信息补充',
                4 => '商品价格优化',
                5 => '商品异常变动',
                6 => '商品数量过少',
            ];
            $sql .= " and b.goods_anomaly_info regexp '{$anomalyType[$params['anomaly_type']]}'";
        }
        $res = Db::connect('mysql')->query($sql);
        $data = [
            'total_count' => 0,
            'normal_count' => 0,
            'normal_rate' => 0,
            'abnormal_count' => 0,
            'abnormal_rate' => 0,
        ];
        if (!isset($res[0])) {
            return $data;
        }

        $data['total_count'] = $res[0]['total_count'] ?? 0;
        $data['normal_count'] = $res[0]['normal_count'] ?? 0;
        $data['abnormal_count'] = $data['total_count'] - $data['normal_count'];
        if ($data['total_count'] > 0) {
            $data['normal_rate'] = round($data['normal_count'] / $data['total_count'] * 100, 2);
            $data['abnormal_rate'] = round($data['abnormal_count'] / $data['total_count'] * 100, 2);
        }
        return $data;
    }

    public function storeAnomalyIndex($params)
    {
        $time = time();
        $evaluateToolDb = config("database.connections.evaluate_tool.database");
        $sql = "select count(1) total_count,
       count(if(b.anomaly_index_count = 0, 1, null)) normal_count
      from sq_operate_shops a
               inner join $evaluateToolDb.store_performance b
                          on a.store_id = b.store_id and b.day_dimension = '{$params['date_time']}'
      where a.status = 2 and a.end_time > {$time}";
        if (isset($params['admin_id']) && $params['admin_id']) {
            $sql .= " and a.id in (select clue_id from sq_track_records where clue_type = 2 and user_id = {$params['admin_id']} and cancel_time = 0)";
        }
        if (isset($params['name']) && $params['name']) {
            $sql .= " and a.name like '%{$params['name']}%'";
        }
        if (isset($params['platform']) && $params['platform']) {
            $sql .= " and a.platform = {$params['platform']}";
        }
        $res = Db::connect('mysql')->query($sql);
        $data = [
            'total_count' => 0,
            'normal_count' => 0,
            'normal_rate' => 0,
            'abnormal_count' => 0,
            'abnormal_rate' => 0,
        ];
        if (!isset($res[0])) {
            return $data;
        }

        $data['total_count'] = $res[0]['total_count'] ?? 0;
        $data['normal_count'] = $res[0]['normal_count'] ?? 0;
        $data['abnormal_count'] = $data['total_count'] - $data['normal_count'];
        if ($data['total_count'] > 0) {
            $data['normal_rate'] = round($data['normal_count'] / $data['total_count'] * 100, 2);
            $data['abnormal_rate'] = round($data['abnormal_count'] / $data['total_count'] * 100, 2);
        }
        return $data;
    }

    public function createTemp($params)
    {
        $temp = OperateRuleTemp::create([
            'title' => $params['title'],
        ]);

        (new OperateIndexRule())->saveAll([
            [
                'title' => '收入',
                'days' => 3,
                'threshold' => 10,
                'status' => 1,
                'type' => 5,
                'temp_id' => $temp->id,
            ],
            [
                'title' => '营业额',
                'days' => 3,
                'threshold' => 10,
                'status' => 1,
                'type' => 1,
                'temp_id' => $temp->id,
            ],
            [
                'title' => '有效订单',
                'days' => 3,
                'threshold' => 10,
                'status' => 1,
                'type' => 2,
                'temp_id' => $temp->id,
            ],
            [
                'title' => '下单转化率',
                'days' => 3,
                'threshold' => 10,
                'status' => 1,
                'type' => 3,
                'temp_id' => $temp->id,
            ],
            [
                'title' => '中差评数',
                'days' => 1,
                'threshold' => 2,
                'status' => 1,
                'type' => 4,
                'temp_id' => $temp->id,
            ],
        ]);

    }

    public function updateTemp($params)
    {
        $temp = OperateRuleTemp::where("id", $params['id'])->find();
        if (!$temp) {
            throw new Exception("更新模版失败");
        }

        OperateRuleTemp::where("id", $params['id'])->update([
            'title' => $params['title'],
        ]);
//
//        OperateShopRuleRels::where("temp_id", $params['id'])->delete();
//        $list = [];
//        foreach ($params['shop_ids'] as $v) {
//            $list[] = [
//                'shop_id' => $v,
//                'temp_id' => $temp->id,
//            ];
//        }
//        (new OperateShopRuleRels())->saveAll($list);

    }

    public function updateTempRule($params)
    {
        $rule = OperateIndexRule::where('id', $params['id'])->find();
        if (!$rule) {
            throw new Exception("更新模版失败");
        }
        OperateIndexRule::where('id', $params['id'])->update([
            'days' => $params['days'],
            'threshold' => $params['threshold'],
            'status' => $params['status'],
        ]);
    }


    public function deleteTemp($params)
    {
        $temp = OperateRuleTemp::where("id", $params['id'])->find();
        if (!$temp) {
            throw new Exception("删除模版失败");
        }
        OperateShopRuleRels::where("temp_id", $temp->id)->delete();
        OperateIndexRule::where("temp_id", $temp->id)->delete();
        OperateRuleTemp::where("id", $params['id'])->delete();
    }


    public function operateRevenue($start, $end, $platform, $storeName, $adminId)
    {
        $day = (strtotime($end) - strtotime($start)) / 86400 + 1; // 获取时间跨度的间隔天数

        // 获取环比的上一周期开始日期
        $preStart = date("Y-m-d", strtotime($start) - $day * 86400);
        if (substr($start, 8) == "01") {
            $date = new \DateTime($start);
            $date->modify('last day of this month');
            if ($date->format("Y-m-d") == $end) {
                $preStart = date("Y-m", strtotime($start) - 1) . "-01";
            }
        }
        $res = [
            "statis" => [
                ["text" => "收入", "key" => "revenue", "this" => 0, "last" => 0, "avg" => 0, "rate" => 0],
                ["text" => "营业额", "key" => "sales", "this" => 0, "last" => 0, "avg" => 0, "rate" => 0],
                ["text" => "有效订单", "key" => "valid_order_count", "this" => 0, "last" => 0, "avg" => 0, "rate" => 0],
                ["text" => "曝光人数", "key" => "exposure_count", "this" => 0, "last" => 0, "avg" => 0, "rate" => 0],
                ["text" => "进店转化率", "key" => "enter_store_conversion_rate", "this" => 0, "last" => 0, "avg" => 0, "rate" => 0],
                ["text" => "下单转化率", "key" => "order_conversion_rate", "this" => 0, "last" => 0, "avg" => 0, "rate" => 0],
            ],
            "this" => [],
            "last" => [],
        ];
        $query = StorePerformance::whereBetween('day_dimension', [$preStart, $end]);
        if (!empty($platform) && $platform > 0) {
            $query->where("platform", $platform);
        }
        if (!empty($storeName) && $storeName != "") {
            $query->where("store_name", "like", "%" . $storeName . "%");
        }
        if (!empty($adminId) && $adminId > 0) {
            $sqcrmDb = config("database.connections.mysql.database");
            $query->whereRaw("store_id in (select store_id from $sqcrmDb.sq_operate_shops where id in (select clue_id from $sqcrmDb.sq_track_records where clue_type = 2 and user_id = $adminId and cancel_time = 0 and status = 3))");
        }
        $static = $query->field('day_dimension day, sum(revenue) sum_revenue, sum(sales) sum_sales, sum(valid_order_count) sum_valid_order_count, sum(exposure_count) sum_exposure_count, sum(order_user_count) sum_order_user_count, sum(clk_uv) sum_clk_uv')->group('day_dimension')->select()->toArray();
        $dataMap = [];
        $thisClkUv = 0;
        $lastClkUv = 0;
        $thisExposureCount = 0;
        $lastExposureCount = 0;
        $thisOrderCount = 0;
        $lastOrderCount = 0;
        foreach ($static as $s) {
            $key = "last";
            $enterStoreConversionRate = 0;
            $orderConversionRate = 0;
            $clkUv = $s["sum_clk_uv"];
            $exposureCount = $s["sum_exposure_count"];
            $orderUserCount = $s["sum_order_user_count"];
            if (substr($s["day"], 0, 10) >= $start) {
                $key = "this";
                $thisClkUv += $clkUv;
                $thisExposureCount += $exposureCount;
                $thisOrderCount += $orderUserCount;
            } else {
                $lastClkUv += $clkUv;
                $lastExposureCount += $exposureCount;
                $lastOrderCount += $orderUserCount;
            }

            if ($exposureCount > 0) {
                $enterStoreConversionRate = round($clkUv / $exposureCount * 100, 2);
            }
            if ($clkUv > 0) {
                $orderConversionRate = round($orderUserCount / $clkUv * 100, 2);
            }
            $res["statis"][0][$key] += $s["sum_revenue"] ?? 0;
            $res["statis"][1][$key] += $s["sum_sales"] ?? 0;
            $res["statis"][2][$key] += $s["sum_valid_order_count"] ?? 0;
            $res["statis"][3][$key] += $s["sum_exposure_count"] ?? 0;
            $dataMap[$s['day']] = [
                'sum_revenue' => $s["sum_revenue"],
                'sum_sales' => $s["sum_sales"],
                'sum_valid_order_count' => $s["sum_valid_order_count"],
                'sum_exposure_count' => $s["sum_exposure_count"],
                'enter_store_conversion_rate' => $enterStoreConversionRate,
                'order_conversion_rate' => $orderConversionRate,
            ];
        }


        if ($thisExposureCount > 0) {
            $enterStoreConversionRate = round($thisClkUv / $thisExposureCount * 100, 2);
            $res["statis"][4]['this'] = $enterStoreConversionRate;
        }
        if ($thisClkUv > 0) {
            $orderConversionRate = round($thisOrderCount / $thisClkUv * 100, 2);
            $res["statis"][5]['this'] = $orderConversionRate;
        }

        if ($lastExposureCount > 0) {
            $enterStoreConversionRate = round($lastClkUv / $lastExposureCount * 100, 2);
            $res["statis"][4]['last'] = $enterStoreConversionRate;
        }
        if ($lastClkUv > 0) {
            $orderConversionRate = round($lastOrderCount / $lastClkUv * 100, 2);
            $res["statis"][5]['last'] = $orderConversionRate;
        }
        // 计算环比
        $res = $this->calculateRate($res, $day);
        $res["statis"][4]['this'] .= '%';
        $res["statis"][4]['last'] .= '%';
        $res["statis"][5]['this'] .= '%';
        $res["statis"][5]['last'] .= '%';
        for ($i = 0; ; $i++) {
            $ttDay = date("Y-m-d", strtotime($start) + $i * 86400);
            if ($ttDay > $end) break;
            $item = $dataMap[$ttDay] ?? [];
            $res["this"][] = ["time" => $ttDay, "revenue" => $item["sum_revenue"] ?? 0, "sales" => $item["sum_sales"] ?? 0, "valid_order_count" => $item['sum_valid_order_count'] ?? 0, "exposure_count" => $item["sum_exposure_count"] ?? 0, "enter_store_conversion_rate" => $item['enter_store_conversion_rate'] ?? 0, "order_conversion_rate" => $item['order_conversion_rate'] ?? 0];
        }
        for ($i = 0; ; $i++) {
            $ttDay = date("Y-m-d", strtotime($preStart) + $i * 86400);
            if ($ttDay >= $start) break;
            $item = $dataMap[$ttDay] ?? [];
            $res["last"][] = ["time" => $ttDay, "revenue" => $item["sum_revenue"] ?? 0, "sales" => $item["sum_sales"] ?? 0, "valid_order_count" => $item['sum_valid_order_count'] ?? 0, "exposure_count" => $item["sum_exposure_count"] ?? 0, "enter_store_conversion_rate" => $item['enter_store_conversion_rate'] ?? 0, "order_conversion_rate" => $item['order_conversion_rate'] ?? 0];
        }
        return $res;
    }

    private function calculateRate($res, $day)
    {
        foreach ($res["statis"] as $k => $v) {
            if ($v["this"] > 0) {
                if ($v["last"] > 0) {
                    $res["statis"][$k]["rate"] = round(($v["this"] - $v["last"]) / $v["last"] * 100, 2);
                } else { // 涨100%
                    $res["statis"][$k]["rate"] = 100;
                }
                $res["statis"][$k]["avg"] = intval(floor($v["this"] / $day));
            } else {
                if ($v["last"] > 0) { // 降100%
                    $res["statis"][$k]["rate"] = -100;
                }
            }
        }
        return $res;
    }

    public function recoveryStat(array $params)
    {
        $query = OperateShops::where('status', '>=', 2);
        if (isset($params['platform']) && $params['platform']) {
            $query->where('platform', $params['platform']);
        }
        if (isset($params['name']) && $params['name']) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }
        if (isset($params['admin_id']) && $params['admin_id']) {
            $query->whereRaw("id in (select clue_id from sq_track_records where clue_type = 2 and user_id = {$params['admin_id']} and cancel_time = 0)");
        }
        $list = $query->field('count(1) total_count, count(if(recovery_time > 0, 1, null)) recovery_count')->select()->toArray();
        $totalCount = $list[0]['total_count'] ?? 0;
        $recoveryCount = $list[0]['recovery_count'] ?? 0;
        $lossCount = $totalCount - $recoveryCount;
        $recoveryRate = 0;
        $lossRate = 0;
        if ($totalCount > 0) {
            $recoveryRate = round($recoveryCount / $totalCount * 100, 2);
            $lossRate = round($lossCount / $totalCount * 100, 2);
        }
        return [
            'total_count' => $totalCount,
            'recovery_count' => $recoveryCount,
            'recovery_rate' => $recoveryRate,
            'loss_count' => $lossCount,
            'loss_rate' => $lossRate,
        ];
    }


}