<script setup lang="ts">
import { useDark, useWindowSize, useThrottleFn } from "@vueuse/core";
import zhCn from "element-plus/lib/locale/lang/zh-cn";
import useAppStore from "./stores/modules/app";
import useSettingStore from "./stores/modules/setting";
import { apiMessageRead, apiMessageList } from "@/api/app";
import { ElMessage } from "element-plus";
import { ScreenEnum } from "./enums/appEnums";
const appStore = useAppStore();
const settingStore = useSettingStore();
const elConfig = {
  zIndex: 3000,
  locale: zhCn,
};
const isDark = useDark();
onMounted(async () => {
  //设置主题色
  settingStore.setTheme(isDark.value);
  // 获取配置
  const data: any = await appStore.getConfig();
  // 设置网站logo
  let favicon: HTMLLinkElement = document.querySelector('link[rel="icon"]')!;
  if (favicon) {
    favicon.href = data.web_favicon;
    return;
  }
  favicon = document.createElement("link");
  favicon.rel = "icon";
  favicon.href = data.web_favicon;
  document.head.appendChild(favicon);
});

const { width } = useWindowSize();
watch(
  width,
  useThrottleFn((value) => {
    if (value > ScreenEnum.SM) {
      appStore.setMobile(false);
      appStore.toggleCollapsed(false);
    } else {
      appStore.setMobile(true);
      appStore.toggleCollapsed(true);
    }
    if (value < ScreenEnum.MD) {
      appStore.toggleCollapsed(true);
    }
  }),
  {
    immediate: true,
  }
);

let timer: any = null;
const clearTime = () => {
  if (!timer) return;
  clearInterval(timer);
  timer = null;
};
const getMessage = async () => {
  try {
    const { lists } = await apiMessageList({ message_type: 3, is_read: 0, platform: 1 });
    const list = lists || [];
    if (list.length) {
      clearTime();
      ElMessage({
        type: "warning",
        duration: 3000,
        message: list[0].content,
        onClose: async () => {
          await apiMessageRead({ id: list[0].id });
          refreshMessage();
        },
      });
    }
  } catch (_) {}
};

const refreshMessage = () => {
  clearTime();
  getMessage();
  timer = setInterval(getMessage, 60000);
};

refreshMessage();
</script>

<template>
  <el-config-provider :locale="elConfig.locale" :z-index="elConfig.zIndex">
    <router-view />
  </el-config-provider>
</template>

<style></style>
