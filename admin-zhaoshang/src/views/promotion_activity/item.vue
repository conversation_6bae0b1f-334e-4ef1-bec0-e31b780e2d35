<template>
  <div>
    <el-card class="!border-none mb-4" shadow="never" v-if="queryParams.type == 5">
      <el-form class="mb-[-16px]" :model="queryParams" inline>
        <el-form-item label="推广时间">
          <el-date-picker
              v-model="queryParams.day_range"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="onDayRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
          <export-data
              class="ml-2.5"
              :fetch-fun="apiActivityItemLists"
              :params="queryParams"
              :page-size="pager.size"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="!border-none" v-loading="pager.loading" shadow="never">
      <div class="mt-4">
        <el-table :data="pager.lists">
          <template v-if="queryParams.type == 5">
            <el-table-column label="密令名称" align="center" prop="name" />
            <el-table-column label="账号姓名" align="center" prop="user_name" />
            <el-table-column label="页面访问次数" align="center" prop="sum_click_pv" />
            <el-table-column label="页面访问人数" align="center" prop="sum_click_uv" />
            <el-table-column label="详情页访问次数" align="center" prop="sum_pv" />
            <el-table-column label="详情页访问人数" align="center" prop="sum_uv" />
            <el-table-column label="上游订单数" align="center" prop="sum_order_num" v-if="hasPermission(['promotion.index/pwd_statis'])" />
            <el-table-column label="扣量订单数" align="center" prop="order_deduct_num" v-if="hasPermission(['promotion.index/pwd_statis'])" />
            <el-table-column label="结算订单数" align="center" prop="order_valid_num" />
          </template>
          <template v-else>
            <el-table-column label="归属账号" prop="user_name"/>
            <el-table-column label="归属角色" prop="user_type"/>
            <el-table-column label="扫码用户ID" prop="out_user_id"/>
            <el-table-column label="扫码用户昵称" prop="nickname"/>
            <el-table-column label="关注时间" prop="third_in_time"/>
            <el-table-column label="取关时间" prop="third_out_time"/>
            <el-table-column label="新老用户" prop="third_new">
              <template #default="{ row }">
                <el-tag v-if="row.third_new == 1">新用户</el-tag>
                <el-tag v-else-if="row.third_new == 2">激活用户</el-tag>
                <el-tag v-else>老用户</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="领权益卡" prop="card_time"/>
            <el-table-column label="首单数" prop="order_new_num"/>
            <el-table-column label="订单数" prop="order_num"/>
          </template>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists"/>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup name="promotionActivityItemLists">
import {usePaging} from '@/hooks/usePaging'
import {apiActivityItemLists} from '@/api/promotion/activity'
import {timeFormat} from '@/utils/util'
import feedback from '@/utils/feedback'
import {hasPermission} from "@/permission";

const route = useRoute()

const queryParams = reactive({
  activity_id: route.query.activity_id,
  type: route.query.type,
  qrcode_id: route.query.qrcode_id,
  day_range: [],
  st: "",
  et: "",
})

const onDayRangeChange = () => {
  if (queryParams.day_range.length !== 2) return;
  queryParams.st = queryParams.day_range[0];
  queryParams.et = queryParams.day_range[1];
};

// 分页相关
const {pager, getLists, resetParams, resetPage} = usePaging({
  fetchFun: apiActivityItemLists,
  params: queryParams
})
getLists()
</script>