<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form class="mb-[-16px]" :model="queryParams" inline>
                <el-form-item label="员工" prop="employee_id">
                    <el-select
                        class="w-[280px]"
                        v-model="queryParams.employee_id"
                        clearable
                        filterable
                        placeholder="请选择员工"
                    >
                        <el-option label="全部" value=""></el-option>
                        <el-option
                            v-for="(item, index) in employeeList"
                            :key="index"
                            :label="item.label"
                            :value="parseInt(item.value)"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="报名日期" prop="start_time">
                    <el-date-picker
                        v-model="queryParams.start_time"
                        type="date"
                        placeholder="请选择报名日期"
                        format="YYYY/MM/DD"
                        value-format="YYYY-MM-DD"
                    />
                </el-form-item>
                <el-form-item label="店铺编码" prop="shop_code">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.shop_code"
                        clearable
                        placeholder="请输入店铺编码"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
            <div class="flex flex-wrap">
                <el-button
                    v-perms="['elm_shop_code_employee_rels.elm_shop_code_employee_rels/add']"
                    type="primary"
                    @click="handleAdd"
                >
                    <template #icon>
                        <icon name="el-icon-Plus" />
                    </template>
                    新增
                </el-button>
                <el-button
                    v-perms="['elm_shop_code_employee_rels.elm_shop_code_employee_rels/delete']"
                    :disabled="!selectData.length"
                    @click="handleDelete(selectData)"
                >
                    删除
                </el-button>

                <upload
                    style="margin-left: 10px"
                    type="shop_employee_rel_file"
                    @success="onUploadSuccess"
                    :show-progress="true"
                >
                    <el-button
                        type="primary"
                        v-perms="['elm_shop_code_employee_rels.elm_shop_code_employee_rels/add']"
                        >店铺绑定关系导入</el-button
                    >
                </upload>

                <el-button
                    v-perms="['elm_shop_code_employee_rels.elm_shop_code_employee_rels/add']"
                    style="margin-left: 10px"
                    type="primary"
                    @click="handleConfirm()"
                >
                    <template>
                        <icon name="el-icon-Plus" />
                    </template>
                    动态目标更新
                </el-button>
            </div>

            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="员工" prop="employee_id" width="120">
                        <template #default="{ row }">
                            <span>[{{ row.employee_id }}]</span>
                            <span style="margin-left: 5px">{{ row.employee.name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="通过日期" prop="start_time" width="120" />
                    <el-table-column label="活动截止日期" prop="end_time" width="120" />
                    <el-table-column label="标签" prop="tag_id" width="120">
                        <template #default="{ row }">
                            <el-tag type="success" v-if="row.tag_id == 1">新签</el-tag>
                            <el-tag type="info" v-if="row.tag_id == 0">其他</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺编码" prop="shop_code" />
                    <el-table-column label="店铺名称" prop="shop_name" />
                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                v-perms="[
                                    'elm_shop_code_employee_rels.elm_shop_code_employee_rels/edit'
                                ]"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup
            v-if="showEdit"
            ref="editRef"
            :dict-data="dictData"
            @success="getLists"
            @close="showEdit = false"
        />
    </div>
</template>

<script lang="ts" setup name="elmShopCodeEmployeeRelsLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import {
    apiElmShopCodeEmployeeRelsLists,
    apiElmShopCodeEmployeeRelsDelete,
    apiEmployeeOptions
} from '@/api/elm_shop_code_employee_rels'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
// 是否显示编辑框
const showEdit = ref(false)
const employeeList = ref<any[]>([])

// 查询条件
const queryParams = reactive({
    employee_id: '',
    start_time: '',
    end_time: '',
    tag_id: '',
    remark: '',
    shop_code: ''
})

// 选中数据
const selectData = ref<any[]>([])

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('')

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiElmShopCodeEmployeeRelsLists,
    params: queryParams
})

const onUploadSuccess = (file: any) => {
    feedback.msgSuccess('上传成功，读取中，请稍后刷新页面查看')
}

// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiElmShopCodeEmployeeRelsDelete({ id })
    getLists()
}

const handleConfirm = async () => {
    // 获取当前日期
    const today = new Date()

    // 获取昨天的日期
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

    // 格式化日期为"yyyy-mm-dd"
    const formattedDate = yesterday.toISOString().split('T')[0]
    await feedback.confirm(
        '确定需要生成' + formattedDate + '的动态目标么？确保已经上传昨日的绑定记录和店铺记录'
    )
}

// 获取BD数据
const getEmployeeOptions = async () => {
    const data = await apiEmployeeOptions({ page_size: 1000 })
    employeeList.value = data.map((item: any) => ({
        label: item.name,
        value: item.id
    }))
    // options.value.unshift({ label: '公海', value: 0 })
}

getEmployeeOptions()
getLists()
</script>
