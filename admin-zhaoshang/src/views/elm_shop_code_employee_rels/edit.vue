<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="760px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
                <el-form-item label="员工" prop="employee_id">
                    <el-select
                        class="flex-1"
                        v-model="formData.employee_id"
                        clearable
                        filterable
                        placeholder="请选择员工"
                    >
                        <el-option
                            v-for="(item, index) in employeeList"
                            :key="index"
                            :label="item.label"
                            :value="parseInt(item.value)"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺编码" prop="shop_code">
                    <el-input v-model="formData.shop_code" clearable placeholder="请输入店铺编码" />
                </el-form-item>
                <el-form-item label="店铺名称" prop="shop_name">
                    <el-input v-model="formData.shop_name" clearable placeholder="请输入店铺名称" />
                </el-form-item>
                <el-form-item label="通过日期" prop="start_time">
                    <el-date-picker
                        v-model="formData.start_time"
                        type="date"
                        placeholder="请选择通过日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                    />
                </el-form-item>
                <el-form-item label="粉丝标签" prop="tag_id">
                    <el-radio-group v-model="formData.tag_id">
                        <el-radio :label="1">新签</el-radio>
                        <el-radio :label="0">其他</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="elmShopCodeEmployeeRelsEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import {
    apiElmShopCodeEmployeeRelsAdd,
    apiElmShopCodeEmployeeRelsEdit,
    apiElmShopCodeEmployeeRelsDetail,
    apiEmployeeOptions
} from '@/api/elm_shop_code_employee_rels'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')

const defaultTime1 = new Date(2000, 1, 1, 0, 0, 0) // '12:00:00'

// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑招商活动绑定记录' : '新增招商活动绑定记录'
})

const employeeList = ref<any[]>([])

// 表单数据
const formData = reactive({
    id: '',
    employee_id: '',
    start_time: '',
    tag_id: 1,
    remark: '',
    shop_code: '',
    shop_name: ''
})

// 表单验证
const formRules = reactive<any>({
    employee_id: [
        {
            required: true,
            message: '请选择员工ID',
            trigger: ['blur']
        }
    ],
    start_time: [
        {
            required: true,
            message: '请选择报名时间',
            trigger: ['blur']
        }
    ],
    shop_code: [
        {
            required: true,
            message: '请输入店铺编码',
            trigger: ['blur']
        }
    ],
    shop_name: [
        {
            required: true,
            message: '请输入店铺名称',
            trigger: ['blur']
        }
    ]
})

// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiElmShopCodeEmployeeRelsDetail({
        id: row.id
    })
    setFormData(data)
}

// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData }
    mode.value == 'edit'
        ? await apiElmShopCodeEmployeeRelsEdit(data)
        : await apiElmShopCodeEmployeeRelsAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
    getEmployeeOptions()
    if (type == 'add') {
        const now = new Date() // 获取当前日期时间
        const year = now.getFullYear() // 获取年份
        const month = ('0' + (now.getMonth() + 1)).slice(-2) // 获取月份，需要在前面补零
        const day = ('0' + now.getDate()).slice(-2) // 获取日期，需要在前面补零

        const dateString = year + '-' + month + '-' + day // 拼接日期字符串
        formData.start_time = dateString
    }
}

// 获取BD数据
const getEmployeeOptions = async () => {
    const data = await apiEmployeeOptions({ page_size: 1000 })
    employeeList.value = data.map((item: any) => ({
        label: item.name,
        value: item.id
    }))
    // options.value.unshift({ label: '公海', value: 0 })
}

// 关闭回调
const handleClose = () => {
    emit('close')
}

defineExpose({
    open,
    setFormData,
    getDetail
})
</script>
