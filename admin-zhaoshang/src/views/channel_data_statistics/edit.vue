<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
                <el-form-item label="" prop="channel_name">
                    <el-input v-model="formData.channel_name" clearable placeholder="请输入" />
                </el-form-item>
                <el-form-item label="" prop="date_time">
                    <el-input v-model="formData.date_time" clearable placeholder="请输入" />
                </el-form-item>
                <el-form-item label="" prop="channel_id">
                    <el-input v-model="formData.channel_id" clearable placeholder="请输入" />
                </el-form-item>
                <el-form-item label="1公众号 2个号" prop="channel_type">
                    <el-input
                        v-model="formData.channel_type"
                        clearable
                        placeholder="请输入1公众号 2个号"
                    />
                </el-form-item>
                <el-form-item label="0自然流量 1自营 2外部" prop="platform">
                    <el-input
                        v-model="formData.platform"
                        clearable
                        placeholder="请输入0自然流量 1自营 2外部"
                    />
                </el-form-item>
                <el-form-item label="关注数量" prop="follow_num">
                    <el-input
                        v-model="formData.follow_num"
                        clearable
                        placeholder="请输入关注数量"
                    />
                </el-form-item>
                <el-form-item label="总共人数" prop="total_follow_num">
                    <el-input
                        v-model="formData.total_follow_num"
                        clearable
                        placeholder="请输入总共人数"
                    />
                </el-form-item>
                <el-form-item label="取关人数" prop="unfollow_num">
                    <el-input
                        v-model="formData.unfollow_num"
                        clearable
                        placeholder="请输入取关人数"
                    />
                </el-form-item>
                <el-form-item label="微信好友数量" prop="wx_friend_num">
                    <el-input
                        v-model="formData.wx_friend_num"
                        clearable
                        placeholder="请输入微信好友数量"
                    />
                </el-form-item>
                <el-form-item label="删除好友数量" prop="wx_unfriend_num">
                    <el-input
                        v-model="formData.wx_unfriend_num"
                        clearable
                        placeholder="请输入删除好友数量"
                    />
                </el-form-item>
                <el-form-item label="总共好友数量" prop="wx_total_friend_num">
                    <el-input
                        v-model="formData.wx_total_friend_num"
                        clearable
                        placeholder="请输入总共好友数量"
                    />
                </el-form-item>
                <el-form-item label="神犬用户数" prop="user_num">
                    <el-input
                        v-model="formData.user_num"
                        clearable
                        placeholder="请输入神犬用户数"
                    />
                </el-form-item>
                <el-form-item label="" prop="created_at">
                    <el-input v-model="formData.created_at" clearable placeholder="请输入" />
                </el-form-item>
                <el-form-item label="" prop="updated_at">
                    <el-input v-model="formData.updated_at" clearable placeholder="请输入" />
                </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="channelDataStatisticsEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import {
    apiChannelDataStatisticsAdd,
    apiChannelDataStatisticsEdit,
    apiChannelDataStatisticsDetail
} from '@/api/channel_data_statistics'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')

// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑渠道数据表' : '新增渠道数据表'
})

// 表单数据
const formData = reactive({
    id: '',
    channel_name: '',
    date_time: '',
    channel_id: '',
    channel_type: '',
    platform: '',
    follow_num: '',
    total_follow_num: '',
    unfollow_num: '',
    wx_friend_num: '',
    wx_unfriend_num: '',
    wx_total_friend_num: '',
    user_num: '',
    created_at: '',
    updated_at: ''
})

// 表单验证
const formRules = reactive<any>({
    channel_name: [
        {
            required: true,
            message: '请输入',
            trigger: ['blur']
        }
    ],
    channel_id: [
        {
            required: true,
            message: '请输入',
            trigger: ['blur']
        }
    ],
    channel_type: [
        {
            required: true,
            message: '请输入1公众号 2个号',
            trigger: ['blur']
        }
    ],
    platform: [
        {
            required: true,
            message: '请输入0自然流量 1自营 2外部',
            trigger: ['blur']
        }
    ],
    follow_num: [
        {
            required: true,
            message: '请输入关注数量',
            trigger: ['blur']
        }
    ],
    total_follow_num: [
        {
            required: true,
            message: '请输入总共人数',
            trigger: ['blur']
        }
    ],
    unfollow_num: [
        {
            required: true,
            message: '请输入取关人数',
            trigger: ['blur']
        }
    ],
    wx_friend_num: [
        {
            required: true,
            message: '请输入微信好友数量',
            trigger: ['blur']
        }
    ],
    wx_unfriend_num: [
        {
            required: true,
            message: '请输入删除好友数量',
            trigger: ['blur']
        }
    ],
    wx_total_friend_num: [
        {
            required: true,
            message: '请输入总共好友数量',
            trigger: ['blur']
        }
    ],
    user_num: [
        {
            required: true,
            message: '请输入神犬用户数',
            trigger: ['blur']
        }
    ]
})

// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiChannelDataStatisticsDetail({
        id: row.id
    })
    setFormData(data)
}

// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData }
    mode.value == 'edit'
        ? await apiChannelDataStatisticsEdit(data)
        : await apiChannelDataStatisticsAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}

defineExpose({
    open,
    setFormData,
    getDetail
})
</script>
