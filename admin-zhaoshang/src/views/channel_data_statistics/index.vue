<template>
    <div>
        <el-tabs type="border-card" @tab-change="tabChanage">
            <el-tab-pane label="公众号">
                <el-card class="!border-none mb-4" shadow="never">
                    <el-form class="mb-[-16px]" :model="queryParams" inline>
                        <el-form-item label="时间范围" prop="date_time">
                            <el-date-picker
                                v-model="queryParams.timerange"
                                type="daterange"
                                unlink-panels
                                range-separator="To"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                :disabled-date="disabledDate"
                                :shortcuts="shortcuts"
                            />
                        </el-form-item>
                        <el-row>
                            <el-form-item label="类型" prop="platform">
                                <el-radio-group
                                    v-model="queryParams.platform"
                                    @change="changePlatform"
                                >
                                    <el-radio-button :label="3">所有</el-radio-button>
                                    <el-radio-button :label="1">自研</el-radio-button>
                                    <el-radio-button :label="2">西皮士</el-radio-button>
                                    <el-radio-button :label="0">自然量</el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-row>
                        <el-form-item
                            label="分类"
                            prop="category_id"
                            v-if="queryParams.platform == 1 || queryParams.platform == 2"
                        >
                            <el-cascader
                                v-model="queryParams.category_id"
                                :options="optionsData.options?.category_options"
                                :props="{ multiple: true }"
                                placeholder="请选择分类"
                                size="default"
                            />
                        </el-form-item>
                        <el-form-item v-if="queryParams.platform == 1 || queryParams.platform == 2">
                            <el-checkbox v-model="queryParams.groupable">分码</el-checkbox>
                        </el-form-item>
                        <el-row>
                            <el-form-item
                                label="渠道"
                                prop="channel_id"
                                v-if="queryParams.platform === 1"
                                key="channel_id"
                            >
                                <el-select
                                    v-model="queryParams.channel_id"
                                    placeholder="请选择渠道"
                                    filterable
                                    clearable
                                    multiple
                                >
                                    <el-option
                                        v-for="item in channelOption"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                /></el-select>
                            </el-form-item>
                            <el-form-item
                                label="渠道"
                                prop="channel_name"
                                v-if="queryParams.platform === 2"
                                key="channel_name"
                            >
                                <el-select
                                    v-model="queryParams.channel_name"
                                    placeholder="请选择渠道"
                                    filterable
                                    clearable
                                    multiple
                                >
                                    <el-option
                                        v-for="item in outChannel"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                /></el-select>
                            </el-form-item>
                        </el-row>
                        <el-form-item>
                            <el-button type="primary" @click="resetPage">查询</el-button>
                            <el-button @click="resetParams">重置</el-button>
                            <el-button @click="exportPage">导出</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                <el-card class="!border-none" v-loading="pager.loading" shadow="never">
                    <div class="mt-4">
                        <el-table :data="renderData.list">
                            <el-table-column
                                label="渠道"
                                prop="channel"
                                width="200"
                                fixed="left"
                                v-if="queryParams.groupable"
                            />
                            <el-table-column
                                label="日期"
                                prop="follow_time"
                                width="200"
                                fixed="left"
                            />
                            <el-table-column
                                label="新增粉丝"
                                prop="total_follow_count"
                                sortable
                                width="110"
                                fixed="left"
                            />
                            <el-table-column
                                label="T0取关"
                                prop="t0_unfollow_num"
                                width="120"
                                fixed="left"
                            >
                                <template #default="{ row }">
                                    取关数 {{ row.t0_unfollow_num }} <br />
                                    取关率
                                    {{
                                        row.total_follow_count > 0
                                            ? (
                                                  (row.t0_unfollow_num / row.total_follow_count) *
                                                  100
                                              ).toFixed(1)
                                            : 0
                                    }}%
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="取关粉丝"
                                prop="unfollow_num"
                                width="80"
                                fixed="left"
                            />
                            <el-table-column
                                label="净增数"
                                prop="follow_num"
                                width="80"
                                fixed="left"
                            />
                            <el-table-column label="t0个号数" prop="t0_add_wx_num" width="120">
                                <template #default="{ row }">
                                    添加数 {{ row.t0_add_wx_num }} <br />
                                    添加率
                                    {{
                                        row.total_follow_count > 0
                                            ? (
                                                  (row.t0_add_wx_num / row.total_follow_count) *
                                                  100
                                              ).toFixed(1)
                                            : 0
                                    }}%
                                </template>
                            </el-table-column>
                            <el-table-column label="加个号数" prop="add_wx_num" width="80" />
                            <el-table-column label="删个号数" prop="delete_wx_num" width="80" />
                            <el-table-column label="t0注册数" prop="t0_register_num" width="120">
                                <template #default="{ row }">
                                    注册数 {{ row.t0_register_num }} <br />
                                    注册率
                                    {{
                                        row.total_follow_count > 0
                                            ? (
                                                  (row.t0_register_num / row.total_follow_count) *
                                                  100
                                              ).toFixed(1)
                                            : 0
                                    }}%
                                </template>
                            </el-table-column>
                            <el-table-column label="注册数" prop="register_num" width="80" />
                            <el-table-column
                                label="今日活跃数"
                                prop="current_day_active_user_num"
                                width="100"
                            />
                            <el-table-column label="整体回收" prop="total" width="120">
                                <template #default="{ row }">
                                    订单 {{ row.total_order_count }} <br />
                                    佣金 {{ row.total_order_commission }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                v-for="(col, colIndex) in renderData.colums"
                                :key="colIndex"
                                :label="col.column_title"
                                width="150"
                            >
                                <template #default="{ row }">
                                    <el-popover placement="top-start" width="500" trigger="hover">
                                        <template #reference>
                                            <div v-if="row.red_package_details[colIndex]">
                                                领红包数:
                                                {{
                                                    row.red_package_details[colIndex]
                                                        ?.click_unique_count
                                                }}
                                                <br />
                                                领红包率:
                                                {{
                                                    row.total_follow_count > 0
                                                        ? (
                                                              (row.red_package_details[colIndex]
                                                                  ?.click_unique_count /
                                                                  row.total_follow_count) *
                                                              100
                                                          ).toFixed(1)
                                                        : 0
                                                }}%
                                                <br />
                                                订单： {{ row.details[colIndex]?.order_count }}
                                                <br />
                                                佣金：
                                                {{ row.details[colIndex]?.order_commission }} <br />
                                                单产：
                                                {{
                                                row.total_follow_count > 0
                                                    ? (
                                                        (row.details[colIndex]?.order_commission || 0) /
                                                        row.total_follow_count
                                                    ).toFixed(3)
                                                    : 0
                                                }}
                                            </div>
                                        </template>
                                        <template #default>
                                            <el-descriptions
                                                title="订单明细数据"
                                                width="700px"
                                                :column="4"
                                            >
                                                <el-descriptions-item
                                                    label="社群订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]?.wx_group_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item
                                                    label="个号订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]?.wx_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item
                                                    label="公众号订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]?.gzh_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item
                                                    label="其他订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]
                                                            ?.other_channel_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item label="爆红包订单">
                                                    {{ row.details[colIndex]?.bhb_order_count }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="立减订单">{{
                                                    row.details[colIndex]?.bwc_order_count
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="赏金订单">{{
                                                    row.details[colIndex]?.sj_order_count
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="其他订单">{{
                                                    row.details[colIndex]?.other_order_count
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="爆红包佣金">
                                                    {{ row.details[colIndex]?.bhb_commission }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="立减佣金">{{
                                                    row.details[colIndex]?.bwc_commission
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="赏金佣金">{{
                                                    row.details[colIndex]?.sj_commission
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="其他佣金">{{
                                                    row.details[colIndex]?.other_commission
                                                }}</el-descriptions-item>
                                            </el-descriptions>
                                        </template>
                                    </el-popover>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-card>
            </el-tab-pane>
            <el-tab-pane label="个号">
                <el-card class="!border-none mb-4" shadow="never">
                    <el-form class="mb-[-16px]" :model="queryParams" inline>
                        <el-form-item label="时间范围" prop="date_time">
                            <el-date-picker
                                v-model="queryParams.timerange"
                                type="daterange"
                                unlink-panels
                                range-separator="To"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                :disabled-date="disabledDate"
                                :shortcuts="shortcuts"
                            />
                        </el-form-item>
                        <el-row>
                            <el-form-item label="分类" prop="category_id">
                                <el-cascader
                                    v-model="queryParams.category_id"
                                    :options="optionsData.options?.category_options"
                                    :props="{ multiple: true }"
                                    placeholder="请选择分类"
                                    size="default"
                                />
                            </el-form-item>
                            <el-form-item v-if="queryParams.category_id">
                                <el-checkbox v-model="queryParams.groupable">分码</el-checkbox>
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-form-item label="渠道" prop="channel_id">
                                <el-select
                                    v-model="queryParams.channel_id"
                                    placeholder="请选择渠道"
                                    filterable
                                    clearable
                                    multiple
                                >
                                    <el-option
                                        v-for="item in optionsData.options?.wx_channel_options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                /></el-select>
                            </el-form-item>
                        </el-row>
                        <el-form-item>
                            <el-button type="primary" @click="resetPage">查询</el-button>
                            <el-button @click="resetParams">重置</el-button>
                            <el-button @click="exportPage">导出</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                <el-card class="!border-none" v-loading="pager.loading" shadow="never">
                    <div class="mt-4">
                        <el-table :data="renderData.list">
                            <el-table-column
                                label="渠道"
                                prop="channel"
                                width="200"
                                fixed="left"
                                v-if="queryParams.groupable"
                            />
                            <el-table-column
                                label="日期"
                                prop="follow_time"
                                width="200"
                                fixed="left"
                            />
                            <el-table-column
                                label="新增粉丝"
                                sortable
                                prop="total_follow_count"
                                width="110"
                                fixed="left"
                            />
                            <el-table-column
                                label="t0删个号数"
                                prop="t0_delete_wx_num"
                                width="120"
                                fixed="left"
                            >
                                <template #default="{ row }">
                                    删除数 {{ row.t0_delete_wx_num }} <br />
                                    删除率
                                    {{
                                        row.total_follow_count > 0
                                            ? (
                                                  (row.t0_delete_wx_num / row.total_follow_count) *
                                                  100
                                              ).toFixed(1)
                                            : 0
                                    }}%
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="删个号数"
                                prop="delete_wx_num"
                                width="80"
                                fixed="left"
                            />
                            <el-table-column
                                label="净增数"
                                prop="add_wx_num"
                                width="80"
                                fixed="left"
                            />
                            <el-table-column label="净关注公众号数" prop="follow_num" width="80" />
                            <el-table-column label="取关公众号数" prop="unfollow_num" width="80" />
                            <el-table-column label="t0注册数" prop="t0_register_num" width="120">
                                <template #default="{ row }">
                                    注册数 {{ row.t0_register_num }} <br />
                                    注册率
                                    {{
                                        row.total_follow_count > 0
                                            ? (
                                                  (row.t0_register_num / row.total_follow_count) *
                                                  100
                                              ).toFixed(1)
                                            : 0
                                    }}%
                                </template>
                            </el-table-column>
                            <el-table-column label="注册数" prop="register_num" width="80" />
                            <el-table-column
                                label="今日活跃数"
                                prop="current_day_active_user_num"
                                width="100"
                            />
                            <el-table-column label="整体回收" prop="total" width="120">
                              <template #default="{ row }">
                                订单 {{ row.total_order_count }} <br />
                                佣金 {{ row.total_order_commission }}
                              </template>
                            </el-table-column>
                            <el-table-column
                                v-for="(col, colIndex) in renderData.colums"
                                :key="colIndex"
                                :label="col.column_title"
                                width="150"
                            >
                                <template #default="{ row }">
                                    <el-popover placement="top-start" width="700" trigger="hover">
                                        <template #reference>
                                            <div v-if="row.red_package_details[colIndex]">
                                                领红包数:
                                                {{
                                                    row.red_package_details[colIndex]
                                                        ?.click_unique_count
                                                }}
                                                <br />
                                                领红包率:
                                                {{
                                                    row.total_follow_count > 0
                                                        ? (
                                                              (row.red_package_details[colIndex]
                                                                  ?.click_unique_count /
                                                                  row.total_follow_count) *
                                                              100
                                                          ).toFixed(1)
                                                        : 0
                                                }}%
                                                <br />
                                                订单： {{ row.details[colIndex]?.order_count }}
                                                <br />
                                                佣金：
                                                {{ row.details[colIndex]?.order_commission }} <br />
                                                单产：
                                                {{
                                                row.total_follow_count > 0
                                                    ? (
                                                        (row.details[colIndex]?.order_commission || 0) /
                                                        row.total_follow_count
                                                    ).toFixed(3)
                                                    : 0
                                                }}
                                            </div>
                                        </template>
                                        <template #default>
                                            <el-descriptions
                                                title="订单明细数据"
                                                width="700px"
                                                :column="4"
                                            >
                                                <el-descriptions-item
                                                    label="社群订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]?.wx_group_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item
                                                    label="个号订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]?.wx_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item
                                                    label="公众号订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]?.gzh_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item
                                                    label="其他订单"
                                                    width="150px"
                                                    >{{
                                                        row.details[colIndex]
                                                            ?.other_channel_order_count
                                                    }}</el-descriptions-item
                                                >
                                                <el-descriptions-item label="爆红包订单">
                                                    {{ row.details[colIndex]?.bhb_order_count }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="立减订单">{{
                                                    row.details[colIndex]?.bwc_order_count
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="赏金订单">{{
                                                    row.details[colIndex]?.sj_order_count
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="其他订单">{{
                                                    row.details[colIndex]?.other_order_count
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="爆红包佣金">
                                                    {{ row.details[colIndex]?.bhb_commission }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="立减佣金">{{
                                                    row.details[colIndex]?.bwc_commission
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="赏金佣金">{{
                                                    row.details[colIndex]?.sj_commission
                                                }}</el-descriptions-item>
                                                <el-descriptions-item label="其他佣金">{{
                                                    row.details[colIndex]?.other_commission
                                                }}</el-descriptions-item>
                                            </el-descriptions>
                                        </template>
                                    </el-popover>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="flex mt-4 justify-end">
                        <pagination v-model="pager" @change="getLists" />
                    </div>
                </el-card>
            </el-tab-pane>
            <el-tab-pane label="群">
              <el-card class="!border-none mb-4" shadow="never">
                <el-form class="mb-[-16px]" :model="queryParams" inline>
                  <el-form-item label="时间范围" prop="date_time">
                    <el-date-picker
                        v-model="queryParams.timerange"
                        type="daterange"
                        unlink-panels
                        range-separator="To"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY/MM/DD"
                        value-format="YYYY-MM-DD"
                        :disabled-date="disabledDate"
                        :shortcuts="shortcuts"
                    />
                  </el-form-item>
                  <el-form-item label="渠道" prop="channel_id">
                    <el-select
                        class="w-[280px]"
                        v-model="queryParams.channel_id"
                        placeholder="请选择渠道"
                        allow-create
                        filterable
                        clearable
                        multiple
                    >
                      <el-option
                          v-for="item in optionsData.options?.wx_group_options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                      /></el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                    <el-button @click="exportPage">导出</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
              <el-card class="!border-none" v-loading="pager.loading" shadow="never">
                <div class="mt-4">
                  <el-table :data="renderData.list">
                    <el-table-column
                        label="日期"
                        prop="follow_time"
                        width="200"
                        fixed="left"
                    />
                    <el-table-column
                        label="进群数"
                        sortable
                        prop="total_count"
                        width="110"
                        fixed="left"
                    />
                    <el-table-column
                        label="退群数"
                        prop="leave_count"
                        width="80"
                        fixed="left"
                    />
                    <el-table-column
                        label="净增数"
                        prop="join_count"
                        width="80"
                        fixed="left"
                    />
                    <el-table-column label="沉睡数" prop="sleep_count" width="80" />
                    <el-table-column label="新人数" prop="new_count" width="80" />
                    <el-table-column label="注册数" prop="register_num" width="80" />
                    <el-table-column label="t0注册数" prop="t0_register_num" width="120">
                      <template #default="{ row }">
                        注册数 {{ row.t0_register_num }} <br />
                        注册率
                        {{
                          row.total_count > 0
                              ? (
                                  (row.t0_register_num / row.total_count) *
                                  100
                              ).toFixed(1)
                              : 0
                        }}%
                      </template>
                    </el-table-column>
                    <el-table-column label="整体回收" prop="total" width="120">
                      <template #default="{ row }">
                        订单 {{ row.total_order_count }} <br />
                        佣金 {{ row.total_order_commission }}
                      </template>
                    </el-table-column>
                    <el-table-column
                        v-for="(col, colIndex) in renderData.colums"
                        :key="colIndex"
                        :label="col.column_title"
                        width="150"
                    >
                      <template #default="{ row }">
                        <el-popover placement="top-start" width="700" trigger="hover">
                          <template #reference>
                            <div v-if="row.red_package_details[colIndex]">
                              领红包数:
                              {{
                                row.red_package_details[colIndex]
                                    ?.click_unique_count
                              }}
                              <br />
                              领红包率:
                              {{
                                row.total_count > 0
                                    ? (
                                        (row.red_package_details[colIndex]
                                                ?.click_unique_count /
                                            row.total_count) *
                                        100
                                    ).toFixed(1)
                                    : 0
                              }}%
                              <br />
                              订单： {{ row.details[colIndex]?.order_count }}
                              <br />
                              佣金：
                              {{ row.details[colIndex]?.order_commission }} <br />
                              单产：
                              {{
                                row.total_count > 0
                                ? (
                                        (row.details[colIndex]?.order_commission || 0) /
                                    row.total_count
                                ).toFixed(3)
                                    : 0
                              }}
                            </div>
                          </template>
                          <template #default>
                            <el-descriptions
                                title="订单明细数据"
                                width="700px"
                                :column="4"
                            >
                              <el-descriptions-item
                                  label="社群订单"
                                  width="150px"
                              >{{
                                  row.details[colIndex]?.wx_group_order_count
                                }}</el-descriptions-item
                              >
                              <el-descriptions-item
                                  label="个号订单"
                                  width="150px"
                              >{{
                                  row.details[colIndex]?.wx_order_count
                                }}</el-descriptions-item
                              >
                              <el-descriptions-item
                                  label="公众号订单"
                                  width="150px"
                              >{{
                                  row.details[colIndex]?.gzh_order_count
                                }}</el-descriptions-item
                              >
                              <el-descriptions-item
                                  label="其他订单"
                                  width="150px"
                              >{{
                                  row.details[colIndex]
                                      ?.other_channel_order_count
                                }}</el-descriptions-item
                              >
                              <el-descriptions-item label="爆红包订单">
                                {{ row.details[colIndex]?.bhb_order_count }}
                              </el-descriptions-item>
                              <el-descriptions-item label="立减订单">{{
                                  row.details[colIndex]?.bwc_order_count
                                }}</el-descriptions-item>
                              <el-descriptions-item label="赏金订单">{{
                                  row.details[colIndex]?.sj_order_count
                                }}</el-descriptions-item>
                              <el-descriptions-item label="其他订单">{{
                                  row.details[colIndex]?.other_order_count
                                }}</el-descriptions-item>
                              <el-descriptions-item label="爆红包佣金">
                                {{ row.details[colIndex]?.bhb_commission }}
                              </el-descriptions-item>
                              <el-descriptions-item label="立减佣金">{{
                                  row.details[colIndex]?.bwc_commission
                                }}</el-descriptions-item>
                              <el-descriptions-item label="赏金佣金">{{
                                  row.details[colIndex]?.sj_commission
                                }}</el-descriptions-item>
                              <el-descriptions-item label="其他佣金">{{
                                  row.details[colIndex]?.other_commission
                                }}</el-descriptions-item>
                            </el-descriptions>
                          </template>
                        </el-popover>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div class="flex mt-4 justify-end">
                  <pagination v-model="pager" @change="getLists" />
                </div>
              </el-card>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="ts" setup name="channelDataStatisticsLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictOptions } from '@/hooks/useDictOptions'
import {
    apiChannelDataStatisticsLists,
    apiOutChannelOption,
    apiChannelDataStatisticsOtherList,
    apiChannelDataExport
} from '@/api/channel_data_statistics'
import { apiActivityQrcodeLists } from '@/api/promotion/activity'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import { computed } from 'vue'

// 查询条件
const queryParams = reactive({
    channel_name: '',
    date_time: '',
    channel_id: '',
    channel_type: '',
    platform: 3,
    timerange: '',
    category_id: '',
    type: 1,
    groupable: false
})

const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
}

const shortcuts = [
    {
        text: '近一周',
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            return [start, end]
        }
    },
    {
        text: '近一月',
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            return [start, end]
        }
    }
]

const channelOption = ref<any[]>([])
const outChannel = ref<any[]>([])

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiChannelDataStatisticsLists,
    params: queryParams
})

const renderData = computed(() => {
    const list: Record<string, any>[] = pager.lists || []
    const listSorted = list
        .slice()
        .sort((a, b) => (b.details?.length || 0) - (a.details?.length || 0))
    const colums = listSorted[0]?.details || []
    return { list, colums }
})

// 添加
const getChannelOptions = async () => {
    const res = await apiActivityQrcodeLists({ page_size: 1000 })
    channelOption.value = res.lists
}

const getOutChannel = async () => {
    const res = await apiOutChannelOption({ page_size: 1000 })
    outChannel.value = res
}

const { optionsData } = useDictOptions<{
    options: any
}>({
    options: {
        api: apiChannelDataStatisticsOtherList
    }
})

const changePlatform = async (value) => {
    if (value == 1) {
        queryParams.channel_name = ''
        queryParams.category_id = ''
        queryParams.groupable = false
    }
    if (value == 2) {
        queryParams.channel_id = ''
        queryParams.category_id = ''
        queryParams.groupable = false
    }
    if (value == 3) {
        queryParams.channel_name = ''
        queryParams.channel_id = ''
        queryParams.category_id = ''
        queryParams.groupable = false
    }
    getLists()
}

const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
}

const tabChanage = (index: any) => {
    queryParams.type = parseInt(index) + 1
    getLists()
}

const exportPage = async () => {
    const fileUrl = await apiChannelDataExport(queryParams)
    console.log(fileUrl)
    window.open(fileUrl.url, '_blank')
}

getLists()
getChannelOptions()
getOutChannel()
const end = new Date()
const start = new Date()
start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
queryParams.timerange = [formatDate(start), formatDate(end)]
</script>
