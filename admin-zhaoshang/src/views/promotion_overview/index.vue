<script setup lang="ts">
import { reactive, nextTick, onMounted, onUnmounted } from "vue";
import VCharts from "vue-echarts";
import { timeFormat, mergeConcurrent } from "@/utils/util";
import { usePaging } from "@/hooks/usePaging";
import { apiOverView, apiOverViewStatis } from "@/api/promotion/overview";
import TabRole from "./components/tab-role.vue";
import TabUser from "./components/tab-user.vue";
import TabChannel from "./components/tab-channel.vue";
import TabOrder from "./components/tab-order.vue";
import TabPwd from "./components/tab-pwd.vue";
import { apiRoleLists } from "@/api/promotion/role";

const timeSeleBtn = [
  { name: "实时", type: "real" },
  { name: "昨天", type: "yesterday" },
  { name: "近7天", type: "7day" },
  { name: "近30天", type: "30day" },
  { name: "自然月", type: "month" },
  { name: "自定义", type: "custom" },
];
const state = reactive({
  time: {
    type: "real",
    showMonth: false,
    showCustom: false,
    month: "",
    custom: [],
    t: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
  },
  showChart: true,
  chartOption: {
    grid: {
      left: "52px",
      right: "52px",
      bottom: "20px",
    },
    xAxis: {
      nameLocation: "center",
      type: "category",
      data: [],
    },
    yAxis: { type: "value" },
    legend: { data: [] },
    tooltip: { trigger: "axis" },
    series: [],
  },
  active: 1,
  params: {
    start: timeFormat(new Date().getTime()),
    end: timeFormat(new Date().getTime()),
  },
  dataList: [],
  dataType: "",
  list1: [],
  list2: [],
  refresh: {
    time: "-",
    status: "off", // off on
    interval: 30000,
  },
});
const queryParams = reactive({
  st: state.params.start,
  et: state.params.end,
  mode: state.active,
  role_id: "",
});

// 分页相关
const { pager, getLists } = usePaging({
  fetchFun: apiOverViewStatis,
  params: queryParams,
});

// 获取指定日期的月份结束时间
const getMonthEnd = (date: Date) => {
  const year = date.getFullYear();
  const month = date.getMonth();
  const nextYear = month === 11 ? year + 1 : year;
  const nextMonth = month === 11 ? 0 : month + 1;
  return new Date(nextYear, nextMonth, 0);
};
const formatTime = (current: Date, type: string) => {
  let day = 1;
  const dayTimes = 1000 * 60 * 60 * 24;
  if (type === "7day") day = 7;
  else if (type === "30day") day = 30;
  const t = new Date(current.getTime() - dayTimes * day);
  if (type === "yesterday") {
    const t1 = timeFormat(t.setHours(0, 0, 0, 0));
    const t2 = timeFormat(t.setHours(23, 59, 59, 999));
    queryParams.st = t1;
    queryParams.et = t2;
    state.params = { start: t1, end: t2 };
    return `${t1}~${t2}`;
  }
  const start = timeFormat(t.setHours(0, 0, 0, 0));
  const end = timeFormat(current.getTime() - dayTimes);
  state.params = { start, end };
  queryParams.st = start;
  queryParams.et = end;
  return `${start}~${end}`;
};
// 选择时间
const seleTime = (type: string) => {
  if (type === state.time.type) return;
  state.time.month = "";
  state.time.custom = [];
  state.time.type = type;
  state.time.showCustom = false;
  state.time.showMonth = false;
  if (type === "custom") return (state.time.showCustom = true);
  if (type === "month") return (state.time.showMonth = true);
  const current = new Date();
  if (type === "real") {
    state.time.t = timeFormat(current.getTime(), "yyyy-mm-dd hh:MM:ss");
    state.params = {
      start: timeFormat(current.getTime()),
      end: timeFormat(current.getTime()),
    };
    queryParams.st = timeFormat(current.getTime());
    queryParams.et = timeFormat(current.getTime());
  } else state.time.t = formatTime(current, type);
  init();
};

// 格式话折线图
const formatChart = () => {
  state.showChart = false;
  const data = [];
  let series = [
    { type: "line", data: [] },
    { type: "line", data: [] },
  ];
  if (["real", "yesterday"].includes(state.time.type)) {
    if (state.time.type === "real") {
      state.chartOption.legend.data = ["今天", "昨天"];
      series[0].name = "今天";
      series[1].name = "昨天";
    } else {
      state.chartOption.legend.data = ["昨天", "前天"];
      series[0].name = "昨天";
      series[1].name = "前天";
    }
    state.list2.forEach((item) => {
      series[1].data.push(item[state.dataType]);
    });
  } else {
    state.chartOption.legend.data = [];
    series.splice(1, 1);
  }
  state.list1.forEach((item) => {
    data.push(item.time);
    series[0].data.push(item[state.dataType]);
  });
  state.chartOption.series = series;
  state.chartOption.xAxis.data = data;
  nextTick(() => (state.showChart = true));
};

const getData = async () => {
  try {
    const data = await apiOverView(state.params);
    state.dataList = (data.statis || []).map((item) => ({
      ...item,
      type: item.rate >= 0 ? "plus" : "minus",
    }));
    if (data.statis && data.statis.length) state.dataType = data.statis[0].key;
    state.list1 = data.this;
    state.list2 = data.last;
    formatChart();
  } catch (_) {}
};

// 选择统计数据类型
const seleType = (type: string) => {
  state.dataType = type;
  formatChart();
};

// 月份选择框禁用
const monthDisabledDate = (date: Date) => {
  const current = new Date().getTime();
  const dayTimes = 1000 * 60 * 60 * 24;
  const t = current - dayTimes * 31;
  if (t < date.getTime()) return true;
  else return false;
};
// 自定义时间禁用
const disabledDate = (date: Date) => {
  const current = new Date().getTime() - 1000 * 60 * 60 * 24;
  if (date.getTime() < current) return false;
  else return true;
};

const onMonthChange = () => {
  if (!state.time.month) return;
  const start = timeFormat(state.time.month.getTime());
  const end = timeFormat(getMonthEnd(state.time.month).getTime());
  state.params = { start, end };
  queryParams.st = start;
  queryParams.et = end;
  state.time.t = `${start}~${end}`;
  init();
};
const onCustomChange = () => {
  if (state.time.custom.length !== 2) return;
  const start = timeFormat(state.time.custom[0].getTime());
  const end = timeFormat(state.time.custom[1].getTime());
  state.params = { start, end };
  queryParams.st = start;
  queryParams.et = end;
  state.time.t = `${start}~${end}`;
  init();
};

const getList = mergeConcurrent(() => getLists());

// 切换tabs
const tabChange = () => {
  queryParams.mode = state.active;
  if (state.active != 5) {
    queryParams.role_id = "";
  }
  let page = pager.page;
  let lists = pager.lists;
  lists = [];
  page = 1;
  Object.assign(pager, { page, lists });
  getList();
};

const init = () => {
  getData();
  getList();
};

const refreshFu = () => {
  state.refresh.time = timeFormat(new Date().getTime(), "y-m-d h:M:s");
  init();
};
let timer;
const onRefresh = () => {
  // if(!isPositiveInteger(state.refresh.interval)) return ElMessage.warning('请输入正整数')
  state.refresh.status = "on";
  refreshFu();
  timer = setInterval(refreshFu, state.refresh.interval);
};

const roleOptions = ref<any[]>([]);
const getRoleList = () => {
  apiRoleLists({ page_no: 1, page_size: 1000, type: "4,5,6" }).then(
    (res: any) => {
      roleOptions.value = res.lists;
    }
  );
};

onMounted(() => {
  init();
  getRoleList();
});
onUnmounted(() => {
  if (timer) clearInterval(timer);
});
</script>

<template>
  <div class="flex items-center h-8">
    <div class="flex items-center mr-4">
      <el-icon size="14"><Odometer /></el-icon>
      <p class="ml-1">
        {{ state.time.type === "real" ? "更新" : "统计" }}时间：{{
          state.time.t
        }}
      </p>
    </div>
    <el-button
      v-for="item in timeSeleBtn"
      :key="item.type"
      class="!ml-0"
      size="small"
      :type="item.type === state.time.type ? 'primary' : ''"
      :text="item.type !== state.time.type"
      @click="seleTime(item.type)"
      >{{ item.name }}</el-button
    >
    <div class="ml-2">
      <el-date-picker
        v-if="state.time.showMonth"
        v-model="state.time.month"
        type="month"
        :disabled-date="monthDisabledDate"
        @change="onMonthChange"
      />
      <el-date-picker
        v-if="state.time.showCustom"
        v-model="state.time.custom"
        type="daterange"
        :default-time="[
          new Date(new Date().setHours(0, 0, 0, 0)),
          new Date(new Date().setHours(23, 59, 59, 999)),
        ]"
        :disabled-date="disabledDate"
        @change="onCustomChange"
      />
    </div>
  </div>

  <el-card class="!border-none mt-4" shadow="never">
    <div class="flex">
      <el-form inline size="small">
        <el-form-item
          style="--el-text-color-regular: #999"
          label="上次刷新时间:"
        >
          <span style="color: #999">{{ state.refresh.time }}</span>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="state.refresh.status === 'on'"
            @click="onRefresh"
            >{{
              state.refresh.status === "on" ? "自动刷新中..." : "自动刷新"
            }}</el-button
          >
        </el-form-item>
        <el-form-item label="间隔(ms):">
          <el-input-number
            :min="10000"
            :precision="0"
            :step="1000"
            v-model="state.refresh.interval"
            @change="onRefresh"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="flex items-center">
      <div
        v-for="(item, i) in state.dataList"
        :key="i"
        class="po__data"
        :class="{ po__datas: state.dataType === item.key }"
        @click="seleType(item.key)"
      >
        <p style="color: rgba(0, 0, 0, 0.45); font-size: 14px">
          {{ item.text }}
        </p>
        <p style="font-size: 30px">
          {{ item.this }}<template v-if="item.text.includes('率')">%</template>
        </p>
        <el-divider class="!mt-0 !mb-2" />
        <div class="po__dp" :class="item.type">
          环比&nbsp;<span
            :style="{ color: item.type === 'plus' ? '#f5222d' : '#52c41a' }"
            >{{ Math.abs(item.rate) }}%</span
          >
        </div>
      </div>
    </div>

    <template v-for="item in state.dataList" :key="item.key">
      <div v-if="item.key === state.dataType" class="mt-4">
        均值：{{ item.avg }}
      </div>
    </template>

    <VCharts
      v-if="state.showChart"
      style="height: 293px; width: 100%; margin-top: 33px"
      :autoresize="true"
      :option="state.chartOption"
    />
  </el-card>

  <el-card class="!border-none mt-4" shadow="never">
    <el-tabs v-model="state.active" @tab-change="tabChange">
      <el-tab-pane label="推广角色分析" :name="1">
        <TabRole :pager="pager" :params="queryParams" />
        <div class="flex mt-4 justify-end">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="推广用户分析" :name="2">
        <TabUser :pager="pager" :params="queryParams" />
        <div class="flex mt-4 justify-end">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="推广渠道分析" :name="3">
        <TabChannel :pager="pager" :params="queryParams" />
      </el-tab-pane>
      <el-tab-pane label="推广订单分析" :name="4">
        <TabOrder :pager="pager" :params="queryParams" />
        <div class="flex mt-4 justify-end">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="推广密令分析" :name="5">
        <TabPwd
          :pager="pager"
          :params="queryParams"
          :role-options="roleOptions"
          @getlist="getList"
        />
        <div class="flex mt-4 justify-end">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<style lang="scss">
.po {
  &__data {
    cursor: pointer;
    border: 1px solid #e8e8e8;
    padding: 20px 24px 10px;
    margin-left: 16px;
    border-radius: 10px;
    width: 180px;
    &:first-child {
      margin-left: 0;
    }
  }
  &__datas {
    border-color: #0681ff;
  }
  &__dp {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    position: relative;
    display: inline-block;
    &.minus {
      &::after {
        content: " ";
        position: absolute;
        height: 0 !important;
        width: 0 !important;
        border: 6px solid;
        border-color: transparent;
        border-top-color: #52c41a;
        top: 7px;
        right: -16px;
      }
    }
    &.plus {
      &::after {
        content: " ";
        position: absolute;
        height: 0 !important;
        width: 0 !important;
        border: 6px solid;
        border-color: transparent;
        border-bottom-color: #f5222d;
        top: 1px;
        right: -16px;
      }
    }
  }
}
</style>
