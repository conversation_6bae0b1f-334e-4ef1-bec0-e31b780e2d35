<script setup lang="ts">
import {
  apiMerchantCallRecordUpdate,
  apiMerchantCallRecords,
} from "@/api/members";
import { reqMerchantRules } from "@/api/merchant/overview";
import { useRoute } from "vue-router";
import { getUserList, getBdmList } from "@/api/consumer";
import { timeFormat } from "@/utils/util";
import { ElMessage } from "element-plus";
import { ref, reactive } from "vue";

const { query } = useRoute();
const bdmOptions = ref<Record<string, any>[]>([]);
const getBdmOptions = async () => {
  const data = await getBdmList({
    page: 1,
    page_size: 1000,
  });
  bdmOptions.value = data.lists;
};
getBdmOptions();
const bdOptions = ref<Record<string, any>[]>([]);

// 获取BD数据
const getBdOptions = async () => {
  const data = await getUserList({ page_size: 1000 });
  bdOptions.value = data.lists.map((item: any) => ({
    label: item.real_name ? item.real_name : item.nickname,
    value: item.id,
  }));
};
getBdOptions();

const createForm = () => ({
  page_no: 1,
  page_size: 20,
  order_id: query.order || "",
  shop_keyword: query.shop_keyword || "",
  bdm_id: "",
  bd_id: "",
  tags: "",
  tag_type: undefined as undefined | number,
  business_type: undefined as undefined | number,
  channel: undefined as undefined | number,
  start_time: query.order
    ? ""
    : `${timeFormat(new Date().getTime() - 2592000000)} 00:00:00`,
  end_time: query.order ? "" : `${timeFormat(new Date().getTime())} 23:59:59`,
});
const form = reactive(createForm());
const formTime = ref<any>([
  `${timeFormat(new Date().getTime() - 2592000000)}`,
  `${timeFormat(new Date().getTime())}`,
]);

const state = reactive<any>({
  data: { callNumber: "--", callDuration: "--", exceptionNumber: "--" },
  list: [],
  total: 0,
  loading: false,

  audioPop: false,
  audioItem: {},
  audioSubmiting: false,

  compliance: [],
  violation: [],

  // 质检
  selectQuality: [],
  quality: {
    show: false,
    compliance: [],
    violation: [],
  },
  // 详情
  detail: {
    show: false,
    activeName: "call",
    data: {},
  },
});

const qualityOptions = computed(() => {
  return [
    {
      label: "合规",
      value: 1,
      children: state.compliance.map((item: any) => ({
        label: item,
        value: item,
      })),
    },
    {
      label: "违规",
      value: 2,
      children: state.violation.map((item: any) => ({
        label: item,
        value: item,
      })),
    },
  ];
});

watch(
  () => query.order,
  (val) => {
    if (val) formTime.value = [];
  },
  { immediate: true }
);

watch(
  () => state.selectQuality,
  (val) => {
    if (val?.length) {
      form.tag_type = val[0];
      if (val.label === 1) form.tags = "";
      else form.tags = val[1];
    } else {
      form.tags = "";
      form.tag_type = undefined;
    }
  }
);

const getData = async () => {
  try {
    const data = await reqMerchantRules();
    state.compliance = data.call_asr_keyword.compliance || [];
    state.violation = data.call_asr_keyword.violation || [];
  } catch (_) {}
};
getData();

watch(formTime, (val) => {
  if (val && val.length === 2) {
    form.start_time = val[0] + " 00:00:00";
    form.end_time = val[1] + " 23:59:59";
  } else {
    form.start_time = "";
    form.end_time = "";
  }
});

const getList = async () => {
  try {
    const res = await apiMerchantCallRecords(form);
    state.list = res.lists;
    state.total = res.count;
  } catch (_) {}
};
const reGetList = () => {
  form.page_no = 1;
  getList();
};
const reset = () => {
  formTime.value = [
    `${timeFormat(new Date().getTime() - 2592000000)}`,
    `${timeFormat(new Date().getTime())}`,
  ];
  state.selectQuality = [];
  Object.assign(form, createForm());
  form.order_id = "";
  getList();
};

getList();

// 通话质检item Class
const getQualityItemClass = (item: any) => {
  if (item.compliance_tag && item.compliance_tag.split(",").length > 3)
    return "mcr__s";
  if (item.violation_tag && item.violation_tag.split(",").length > 3)
    return "mcr__s";
  return "";
};

const getQuality = (item: any, type: "compliance" | "violation") => {
  if (item[`${type}_tag`]) {
    const tags = item[`${type}_tag`].split(",");
    return tags.slice(0, 3).join(",");
  } else return "无";
};

const openQuality = (item: any) => {
  if (!getQualityItemClass(item)) return;
  state.quality.compliance = item.compliance_tag
    ? item.compliance_tag.split(",")
    : [];
  state.quality.violation = item.violation_tag
    ? item.violation_tag.split(",")
    : [];
  state.quality.show = true;
};

const onAudioUpload = (res: any) => {
  state.audioItem.audio_url = res.data.uri;
  state.audioItem.audioName = res.data.name;
};
const submitAudio = async () => {
  if (!state.audioItem.audio_url) return ElMessage.error("请上传录音文件");
  if (state.audioSubmiting) return;
  state.audioSubmiting = true;
  try {
    await apiMerchantCallRecordUpdate({
      id: state.audioItem.id,
      field: "audio_url",
      value: state.audioItem.audio_url,
    });
    state.audioPop = false;
    getList();
    ElMessage.success("提交成功");
  } catch (_) {}
  state.audioSubmiting = false;
};

const highlightKeywords = (
  text: string,
  keywords: string[],
  color: string
): string => {
  if (!text || !keywords || keywords.length === 0) return text;
  const escapedKeywords = keywords.map((keyword) =>
    keyword.replace(/[.*+?^${}()|[\]\\/]/g, "$&")
  );
  const regex = new RegExp(`(${escapedKeywords.join("|")})`, "g");
  return text.replace(regex, `<span style="color: ${color};">$1</span>`);
};

// 查看详情
const getDetail = (row: any) => {
  state.detail.show = true;
  state.detail.activeName = "call";
  if (row.channel === "销帮") {
    row.txt = row.asr_txt;
    if (row.violation_tag) {
      row.violation_tags = row.violation_tag.split(",");
      row.txt = highlightKeywords(row.txt, row.violation_tags, "red");
    }
    if (row.compliance_tag) {
      row.compliance_tags = row.compliance_tag.split(",");
      row.txt = highlightKeywords(
        row.txt,
        row.compliance_tags,
        "var(--el-color-primary)"
      );
    }
  }

  state.detail.data = row;
};
</script>

<template>
  <div>
    <el-card shadow="hover">
      <el-form inline @submit.prevent>
        <el-form-item label="记录编号:">
          <el-input
            v-model="form.order_id"
            placeholder="请输入"
            clearable
            @keyup.enter="reGetList"
          />
        </el-form-item>
        <el-form-item label="跟进招商:">
          <el-select v-model="form.bd_id" filterable clearable>
            <el-option
              v-for="(item, index) in bdOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="招商主管:">
          <el-select v-model="form.bdm_id" clearable>
            <el-option
              v-for="(item, index) in bdmOptions"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺信息:">
          <el-input
            v-model="form.shop_keyword"
            placeholder="请输入名称/联系方式"
            clearable
            @keyup.enter="reGetList"
          />
        </el-form-item>
        <el-form-item label="通话质检:" class="w-[244px]">
          <el-cascader
            v-model="state.selectQuality"
            :options="qualityOptions"
            :props="{ checkStrictly: true }"
            clearable
          />
        </el-form-item>
        <el-form-item label="营销产品" prop="business_type">
          <el-select v-model="form.business_type" clearable>
            <el-option label="叠红包" :value="1" />
            <el-option label="大盘招商" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="外呼服务" prop="channel">
          <el-select v-model="form.channel" clearable>
            <el-option label="赢可云" :value="2" />
            <el-option label="销帮" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="通话时间:">
          <el-date-picker
            v-model="formTime"
            type="daterange"
            range-separator="～"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="reGetList">查询</el-button>
          <el-button @click="reset">重置</el-button>
          <export-data
            class="ml-2.5"
            :fetch-fun="apiMerchantCallRecords"
            :params="form"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <!-- 列表 -->
    <el-card class="my-4" shadow="never">
      <el-table size="large" v-loading="state.loading" :data="state.list">
        <el-table-column label="记录编号" prop="order_id" min-width="90">
          <template #default="{ row }">
            <p>
              {{ row.order_id || "-" }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="跟进招商" prop="bd_name" min-width="90" />
        <el-table-column label="店铺名称" prop="shop_name" min-width="150">
          <template #default="{ row }">{{ row.shop_name || "--" }}</template>
        </el-table-column>
        <el-table-column label="店铺城市" prop="city" min-width="90" />
        <el-table-column label="联系电话" prop="callee" min-width="120" />
        <el-table-column label="外呼服务" min-width="90" prop="channel" />
        <el-table-column label="营销产品" min-width="90">
          <template #default="{ row }">{{ row.tags.join(",") }}</template>
        </el-table-column>
        <el-table-column label="主叫被叫" min-width="90" prop="mode" />
        <el-table-column label="通话时间" min-width="90">
          <template #default="{ row }">
            <p>
              {{
                row.start_time
                  ? timeFormat(row.start_time, "yyyy-mm-dd hh:MM:ss")
                  : "-"
              }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="通话质检" width="220">
          <template #default="{ row }">
            <div @click="openQuality(row)" :class="getQualityItemClass(row)">
              <div>合规：{{ getQuality(row, "compliance") }}</div>
              <div>违规：{{ getQuality(row, "violation") }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button type="primary" text link @click="getDetail(row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="form.page_no"
          v-model:page-size="form.page_size"
          :page-sizes="[15, 20, 30, 50, 100, 250]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </el-card>

    <!-- 详情 -->
    <el-drawer v-model="state.detail.show" title="通话详情" :size="1000">
      <el-tabs v-model="state.detail.activeName">
        <el-tab-pane label="通话信息" name="call">
          <el-descriptions>
            <el-descriptions-item label="招商专员">{{
              state.detail.data.bd_name
            }}</el-descriptions-item>
            <el-descriptions-item label="店铺名称">{{
              state.detail.data.shop_name
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              state.detail.data.callee
            }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="通话明细" name="audio">
          <div v-if="state.detail.show" class="flex items-center mb-4">
            通话录音：
            <p v-if="!state.detail.data.audio_url">-</p>
            <audio
              v-else
              style="width: 260px; transform: scale(0.8) translateX(-10%)"
              controls
            >
              <source :src="state.detail.data.audio_url" type="audio/mpeg" />
              您的浏览器不支持该音频格式。
            </audio>
          </div>
          <template v-if="state.detail.data.channel === '销帮'">
            <div class="flex items-center mb-4">
              文本内容：
              <div v-html="state.detail.data.txt"></div>
            </div>
            <div class="flex items-center mb-4">
              合规内容：
              <template v-if="state.detail.data.compliance_tags">
                <el-tag
                  v-for="(t, i) in state.detail.data.compliance_tags"
                  :key="i"
                  type="primary"
                  class="mr-2"
                  >{{ t }}</el-tag
                >
              </template>
              <div v-else>-</div>
            </div>
            <div class="flex items-center mb-4">
              违规内容：
              <template v-if="state.detail.data.violation_tags">
                <el-tag
                  v-for="(t, i) in state.detail.data.violation_tags"
                  :key="i"
                  type="danger"
                  class="mr-2"
                  >{{ t }}</el-tag
                >
              </template>
              <div v-else>-</div>
            </div>
          </template>
          <el-table v-else :data="state.detail.data.asr_txt">
            <el-table-column label="角色">
              <template #default="{ row }">
                {{ row.speakerId ? "招商专员" : "商家老板" }}
              </template>
            </el-table-column>
            <el-table-column label="识别文本" prop="text" />
            <el-table-column label="合规关键词">
              <template #default="{ row }">
                {{ row.compliance.length ? row.compliance.join(",") : "--" }}
              </template>
            </el-table-column>
            <el-table-column label="违规关键词">
              <template #default="{ row }">
                {{ row.violation.length ? row.violation.join(",") : "--" }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    <!-- 合规/违规 -->
    <el-dialog v-model="state.quality.show" title="合规/违规" :width="800">
      <template v-if="state.quality.compliance.length">
        <div class="mb-2">合规</div>
        <div class="flex flex-wrap">
          <div
            class="mcr__t"
            v-for="(item, index) in state.quality.compliance"
            :key="index"
          >
            {{ item }}
          </div>
        </div>
      </template>
      <template v-if="state.quality.violation.length">
        <div class="mt-4 mb-2">违规</div>
        <div class="flex flex-wrap">
          <div
            class="mcr__t"
            v-for="(item, index) in state.quality.violation"
            :key="index"
          >
            {{ item }}
          </div>
        </div>
      </template>
    </el-dialog>
    <!-- 提交录音 -->
    <el-dialog v-model="state.audioPop" title="提交录音" :width="500">
      <upload
        type="video"
        accept="audio/mpeg"
        :show-progress="true"
        drag
        @success="onAudioUpload"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将录音文件拖拽至此 或 <em>点击上传</em>
        </div>
      </upload>
      <p v-if="state.audioItem.audio_url" class="mt-2">
        已上传：{{ state.audioItem.audioName }}
      </p>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :loading="state.audioSubmiting"
            @click="state.audioPop = false"
            >取消</el-button
          >
          <el-button
            type="primary"
            :loading="state.audioSubmiting"
            @click="submitAudio"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.mcr {
  &__s {
    cursor: pointer;
    color: var(--el-color-primary);
  }
  &__t {
    width: 33.33%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    border: 1px solid #e6e6e6;
    margin-top: -1px;
    margin-left: -1px;
  }
}
</style>
