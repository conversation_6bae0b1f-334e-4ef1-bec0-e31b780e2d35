<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" inline>
                <el-form-item label="分类名" prop="name">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.name"
                        clearable
                        placeholder="请输入分类名称"
                    />
                </el-form-item>
                <el-form-item label="上级" prop="parent_id">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.parent_id"
                        clearable
                        placeholder="请选择上级"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getLists">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" shadow="never">
            <div>
                <el-button
                    v-perms="['channel_code_category.channel_code_category/add']"
                    type="primary"
                    @click="handleAdd()"
                >
                    <template #icon>
                        <icon name="el-icon-Plus" />
                    </template>
                    新增
                </el-button>
                <el-button @click="handleExpand"> 展开/折叠 </el-button>
            </div>
            <div class="mt-4">
                <el-table
                    v-loading="loading"
                    ref="tableRef"
                    class="mt-4"
                    size="large"
                    :data="lists"
                    row-key="id"
                    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                >
                    <el-table-column label="名称" prop="name" show-overflow-tooltip />
                    <el-table-column label="备注" prop="remark" show-overflow-tooltip />
                    <el-table-column label="操作" width="160" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                v-perms="['channel_code_category.channel_code_category/add']"
                                type="primary"
                                link
                                @click="handleAdd(row.id)"
                            >
                                新增
                            </el-button>
                            <el-button
                                v-perms="['channel_code_category.channel_code_category/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-perms="['channel_code_category.channel_code_category/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-card>
        <edit-popup
            v-if="showEdit"
            ref="editRef"
            :dict-data="dictData"
            @success="getLists"
            @close="showEdit = false"
        />
        <el-dialog v-model="dialogTableVisible" title="Shipping address" width="800">
            <el-table :data="gridData">
                <el-table-column property="date" label="Date" width="150" />
                <el-table-column property="name" label="Name" width="200" />
                <el-table-column property="address" label="Address" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="channelCodeCategoryLists">
import { timeFormat } from '@/utils/util'
import { useDictData } from '@/hooks/useDictOptions'
import {
    apiChannelCodeCategoryLists,
    apiChannelCodeCategoryDelete
} from '@/api/channel_code_category'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'
import type { ElTable, FormInstance } from 'element-plus'

const tableRef = shallowRef<InstanceType<typeof ElTable>>()
const formRef = shallowRef<FormInstance>()
const editRef = shallowRef<InstanceType<typeof EditPopup>>()
let isExpand = false

// 是否显示编辑框
const showEdit = ref(false)
const dialogTableVisible = ref(false)
const loading = ref(false)
const lists = ref<any[]>([])

const relatedCategoryId = ref(0)
// 选中数据
const selectData = ref<any[]>([])

// 查询条件
const queryParams = reactive({
    name: '',
    parent_id: ''
})

const resetParams = () => {
    formRef.value?.resetFields()
    getLists()
}

const getLists = async () => {
    loading.value = true
    try {
        const data = await apiChannelCodeCategoryLists(queryParams)
        lists.value = data.lists
        loading.value = false
    } catch (error) {
        loading.value = false
    }
}

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('')

// 添加
const handleAdd = async (id?: number) => {
    showEdit.value = true
    await nextTick()
    if (id) {
        editRef.value?.setFormData({
            parent_id: id
        })
    }
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiChannelCodeCategoryDelete({ id })
    getLists()
}

const handleExpand = () => {
    isExpand = !isExpand
    toggleExpand(lists.value, isExpand)
}

const toggleExpand = (children: any[], unfold = true) => {
    for (const key in children) {
        tableRef.value?.toggleRowExpansion(children[key], unfold)
        if (children[key].children) {
            toggleExpand(children[key].children!, unfold)
        }
    }
}

getLists()
</script>
