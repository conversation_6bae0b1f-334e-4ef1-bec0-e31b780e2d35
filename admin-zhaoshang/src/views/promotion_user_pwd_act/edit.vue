<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
              <!-- 角色选择框 -->
              <el-form-item label="账户" prop="user_id">
                <el-select
                    v-model="formData.user_id"
                    class="flex-1"
                    multiple
                    filterable
                    placeholder="请选择账户"
                    clearable
                >
                  <el-option
                      v-for="(item, index) in optionsData.user"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="promotionUserPwdActEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiPromotionUserPwdActAdd } from '@/api/promotion/user_pwd_act'
import { apiUserAll } from '@/api/promotion/user'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
import {useDictOptions} from "@/hooks/useDictOptions";
import {roleAll} from "@/api/perms/role";
import {jobsAll} from "@/api/org/post";
import {deptAll} from "@/api/org/department";
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')

const { optionsData } = useDictOptions<{
  user: any[]
}>({
  user: {
    api: apiUserAll
  },
})
// 弹窗标题
const popupTitle = computed(() => {
    return "新增用户口令活动"
})
const route = useRoute()
// 表单数据
const formData = reactive({
    act_id: route.query.act_id ? Number(route.query.act_id) : 0,
    user_id: [],
})


// 表单验证
const formRules = reactive<any>({

})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
    
    
}



// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData,  }
    mode.value == await apiPromotionUserPwdActAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}



defineExpose({
    open,
    setFormData
})
</script>
