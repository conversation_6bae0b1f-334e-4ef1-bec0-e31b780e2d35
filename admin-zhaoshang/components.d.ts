// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AppLink: typeof import('./src/components/app-link/index.vue')['default']
    ColorPicker: typeof import('./src/components/color-picker/index.vue')['default']
    DaterangePicker: typeof import('./src/components/daterange-picker/index.vue')['default']
    DelWrap: typeof import('./src/components/del-wrap/index.vue')['default']
    DictValue: typeof import('./src/components/dict-value/index.vue')['default']
    Editor: typeof import('./src/components/editor/index.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPageHeader: typeof import('element-plus/es')['ElPageHeader']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExportData: typeof import('./src/components/export-data/index.vue')['default']
    FooterBtns: typeof import('./src/components/footer-btns/index.vue')['default']
    Icon: typeof import('./src/components/icon/index.vue')['default']
    IconPicker: typeof import('./src/components/icon/picker.vue')['default']
    IconSvgIcon: typeof import('./src/components/icon/svg-icon.vue')['default']
    ImageContain: typeof import('./src/components/image-contain/index.vue')['default']
    Link: typeof import('./src/components/link/index.vue')['default']
    LinkCustomLink: typeof import('./src/components/link/custom-link.vue')['default']
    LinkPicker: typeof import('./src/components/link/picker.vue')['default']
    LinkShopPages: typeof import('./src/components/link/shop-pages.vue')['default']
    Loading: typeof import('element-plus/es')['ElLoadingDirective']
    Material: typeof import('./src/components/material/index.vue')['default']
    MaterialFile: typeof import('./src/components/material/file.vue')['default']
    MaterialPicker: typeof import('./src/components/material/picker.vue')['default']
    MaterialPreview: typeof import('./src/components/material/preview.vue')['default']
    OverflowTooltip: typeof import('./src/components/overflow-tooltip/index.vue')['default']
    Pagination: typeof import('./src/components/pagination/index.vue')['default']
    PopoverInput: typeof import('./src/components/popover-input/index.vue')['default']
    Popup: typeof import('./src/components/popup/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Upload: typeof import('./src/components/upload/index.vue')['default']
    VideoPlayer: typeof import('./src/components/video-player/index.vue')['default']
  }
}

export {}
