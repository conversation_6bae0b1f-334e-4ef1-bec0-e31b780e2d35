# Docker 部署指南

本项目支持Docker一键部署，无需本地安装PHP环境。

## 🚀 快速开始

### 前置要求
- Docker
- Docker Compose
- Make (可选，用于快捷命令)

### 使用Makefile快速启动

```bash
# 查看所有可用命令
make help

# 启动所有服务
make up

# 查看服务状态
make status

# 查看日志
make logs
```

### 手动启动
```bash
cd docker
docker-compose -f docker-compose-arm64.yml up -d
```

## 📋 Makefile命令说明

### 基础操作
- `make up` - 启动所有服务
- `make down` - 停止所有服务
- `make restart` - 重启所有服务
- `make build` - 构建并启动服务
- `make rebuild` - 重新构建所有镜像并启动

### 日志查看
- `make logs` - 查看所有服务日志
- `make logs-php` - 查看PHP服务日志
- `make logs-nginx` - 查看Nginx服务日志
- `make logs-mysql` - 查看MySQL服务日志
- `make logs-redis` - 查看Redis服务日志

### 状态监控
- `make status` - 查看服务状态
- `make ps` - 查看运行中的容器

### 容器操作
- `make php-shell` - 进入PHP容器shell
- `make mysql-shell` - 进入MySQL容器shell
- `make redis-shell` - 进入Redis容器shell

### 维护操作
- `make clean` - 清理停止的容器和未使用的镜像
- `make clean-all` - 清理所有相关资源(谨慎使用)
- `make backup-db` - 备份数据库

### 开发调试
- `make test-api` - 测试API接口
- `make restart-php` - 快速重启PHP服务
- `make restart-nginx` - 快速重启Nginx服务
- `make info` - 显示项目信息

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **前端管理界面**: http://localhost:8080
- **API接口**: http://localhost:8080/adminapi
- **MySQL**: localhost:3306 (用户名: root, 密码: root)
- **Redis**: localhost:6379

## 🔧 服务配置

### 端口映射
- Nginx: 8080 -> 80
- PHP-FPM: 9000 -> 9000
- MySQL: 3306 -> 3306
- Redis: 6379 -> 6379

### 数据持久化
- MySQL数据: `docker/data/mysql8.0/lib`
- Redis数据: `docker/data/redis`

## 🐛 常见问题

### 1. 端口被占用
如果8080端口被占用，可以修改 `docker/docker-compose-arm64.yml` 中的端口映射：
```yaml
ports:
  - "8081:80"  # 改为8081端口
```

### 2. 权限问题
如果遇到文件权限问题：
```bash
make php-shell
chown -R www-data:www-data /likeadmin_php/server
```

### 3. API返回HTML页面
确保Nginx配置正确：
```bash
make restart-nginx
```

### 4. 数据库连接失败
检查MySQL服务状态：
```bash
make status
make logs-mysql
```

### 5. 内存不足
如果构建过程中出现内存不足，可以增加Docker的内存限制。

## 🔄 更新部署

### 更新代码后重新部署
```bash
# 重新构建并启动
make rebuild

# 或者分步执行
make down
make build
```

### 仅重启特定服务
```bash
# 重启PHP服务
make restart-php

# 重启Nginx服务
make restart-nginx
```

## ⚙️ .env配置文件管理

### 修改.env文件后的操作步骤

当您修改了 `server/.env` 文件的配置后，需要重启相关服务才能使配置生效：

```bash
# 方法1: 重新加载配置(推荐)
make reload-php

# 方法2: 重启所有服务
make restart

# 方法3: 仅重启PHP服务
make restart-php
```

### 数据库配置管理

#### 查看当前数据库配置
```bash
make show-db-config
```

#### 切换到Docker本地数据库
```bash
# 自动备份原配置并切换到本地数据库
make use-local-db
```

#### 恢复远程数据库配置
```bash
# 恢复到远程数据库配置
make use-remote-db
```

### 常见配置修改场景

#### 1. 修改数据库连接
```bash
# 编辑.env文件
vim server/.env

# 修改以下配置
[DATABASE]
HOSTNAME = "your-db-host"
DATABASE = "your-db-name"
USERNAME = "your-username"
PASSWORD = "your-password"

# 重新加载配置
make reload-php
```

#### 2. 修改Redis配置
```bash
# 编辑.env文件
vim server/.env

# 修改Redis配置
[REDIS]
HOSTNAME = "likeadmin-redis"  # Docker容器名
PASSWORD = ""
PORT = "6379"

# 重新加载配置
make reload-php
```

#### 3. 修改应用配置
```bash
# 编辑.env文件
vim server/.env

# 修改应用配置
[APP]
APP_DEBUG = FALSE  # 生产环境设为FALSE
DEFAULT_TIMEZONE = "Asia/Shanghai"

# 重新加载配置
make reload-php
```

### 配置文件备份与恢复

#### 自动备份
使用 `make use-local-db` 命令时会自动备份原配置文件：
```bash
# 备份文件格式: .env.backup.YYYYMMDD_HHMMSS
server/.env.backup.20241216_143022
```

#### 手动备份
```bash
# 手动备份当前配置
cp server/.env server/.env.backup.$(date +%Y%m%d_%H%M%S)
```

#### 恢复配置
```bash
# 从备份恢复
cp server/.env.backup.20241216_143022 server/.env
make reload-php
```

## 💾 数据备份与恢复

### 备份数据库
```bash
# 自动备份到backups目录
make backup-db

# 手动备份
docker exec likeadmin-mysql mysqldump -uroot -proot --all-databases > backup.sql
```

### 恢复数据库
```bash
# 进入MySQL容器
make mysql-shell

# 或者直接执行
docker exec -i likeadmin-mysql mysql -uroot -proot < backup.sql
```

## 🔍 日志调试

### 查看实时日志
```bash
# 查看所有服务日志
make logs

# 查看特定服务日志
make logs-php
make logs-nginx
make logs-mysql
```

### PHP错误调试
```bash
# 进入PHP容器查看错误日志
make php-shell
tail -f /var/log/php_errors.log
```

## 🏗️ 自定义配置

### 修改PHP配置
编辑 `docker/config/php/php.ini` 文件，然后重启PHP服务：
```bash
make restart-php
```

### 修改Nginx配置
编辑 `docker/config/nginx/conf.d/default.conf` 文件，然后重启Nginx服务：
```bash
make restart-nginx
```

### 修改MySQL配置
编辑 `docker/config/mysql/mysqld.cnf` 文件，然后重启MySQL服务：
```bash
cd docker && docker-compose -f docker-compose-arm64.yml restart mysql
```

## 📊 性能监控

### 查看容器资源使用情况
```bash
# 查看所有容器状态
docker stats

# 查看特定容器
docker stats likeadmin-php likeadmin-mysql
```

### 查看容器详细信息
```bash
docker inspect likeadmin-php
```

## 🚀 生产环境部署

### 环境变量配置
在生产环境中，建议通过环境变量配置敏感信息：

```bash
# 创建.env文件
cp .env.example .env

# 编辑配置
vim .env
```

### 安全配置
1. 修改默认密码
2. 配置防火墙规则
3. 启用HTTPS
4. 定期备份数据

### 性能优化
1. 启用OPcache
2. 配置Redis缓存
3. 优化MySQL配置
4. 使用CDN加速静态资源

这个Docker部署方案让您可以快速启动项目，无需复杂的环境配置！
