<?php
// 测试代理商订单统计导出功能

require_once 'server/vendor/autoload.php';

use app\common\command\HandleExportTask;
use app\common\model\file_task\ExportTask;

// 模拟测试参数
$testParam = [
    'act_ids' => [1, 2, 3],
    'scene' => 1,
    'status' => 1,
    'settle_type' => 1,
    'start' => '2024-01-01',
    'end' => '2024-01-31'
];

// 创建测试任务
$task = new ExportTask();
$task->business_type = 12;
$task->param = $testParam;
$task->status = 0;
$task->save();

echo "测试任务已创建，ID: " . $task->id . "\n";
echo "请运行命令: php think file_task -t 12\n";
