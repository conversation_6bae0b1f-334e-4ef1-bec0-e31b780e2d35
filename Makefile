# 神泉CRM项目 Docker Compose 管理
# 使用方法: make [命令]

# 默认使用ARM64架构的docker-compose文件
DOCKER_COMPOSE_FILE = docker/docker-compose-arm64.yml
DOCKER_DIR = docker

# 默认目标
.DEFAULT_GOAL := help

# 显示帮助信息
.PHONY: help
help:
	@echo "神泉CRM项目 Docker 管理命令:"
	@echo ""
	@echo "  make up          - 启动所有服务"
	@echo "  make down        - 停止所有服务"
	@echo "  make restart     - 重启所有服务"
	@echo "  make build       - 构建并启动服务"
	@echo "  make rebuild     - 重新构建所有镜像并启动"
	@echo ""
	@echo "  make logs        - 查看所有服务日志"
	@echo "  make logs-php    - 查看PHP服务日志"
	@echo "  make logs-nginx  - 查看Nginx服务日志"
	@echo "  make logs-mysql  - 查看MySQL服务日志"
	@echo "  make logs-redis  - 查看Redis服务日志"
	@echo ""
	@echo "  make status      - 查看服务状态"
	@echo "  make ps          - 查看运行中的容器"
	@echo ""
	@echo "  make php-shell   - 进入PHP容器shell"
	@echo "  make mysql-shell - 进入MySQL容器shell"
	@echo "  make redis-shell - 进入Redis容器shell"
	@echo ""
	@echo "  make clean       - 清理停止的容器和未使用的镜像"
	@echo "  make clean-all   - 清理所有相关资源(谨慎使用)"
	@echo ""
	@echo "  make test-api    - 测试API接口"
	@echo "  make backup-db   - 备份数据库"
	@echo ""
	@echo "  make reload-php   - 重新加载php"
	@echo "  make use-local-db    - 切换到Docker本地数据库"
	@echo "  make use-remote-db   - 恢复远程数据库配置"
	@echo "  make show-db-config  - 显示当前数据库配置"

# 启动所有服务
.PHONY: up
up:
	@echo "🚀 启动所有Docker服务..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml up -d
	@echo "✅ 服务启动完成!"
	@echo "📱 前端访问: http://localhost:8080"
	@echo "🔧 API接口: http://localhost:8080/adminapi"

# 停止所有服务
.PHONY: down
down:
	@echo "🛑 停止所有Docker服务..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml down
	@echo "✅ 服务已停止!"

# 重启所有服务
.PHONY: restart
restart:
	@echo "🔄 重启所有Docker服务..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml restart
	@echo "✅ 服务重启完成!"

# 构建并启动服务
.PHONY: build
build:
	@echo "🔨 构建并启动Docker服务..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml up -d --build
	@echo "✅ 构建并启动完成!"

# 重新构建所有镜像
.PHONY: rebuild
rebuild:
	@echo "🔨 重新构建所有镜像..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml build --no-cache
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml up -d
	@echo "✅ 重新构建完成!"

# 查看所有服务日志
.PHONY: logs
logs:
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml logs -f

# 查看PHP服务日志
.PHONY: logs-php
logs-php:
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml logs -f php

# 查看Nginx服务日志
.PHONY: logs-nginx
logs-nginx:
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml logs -f nginx

# 查看MySQL服务日志
.PHONY: logs-mysql
logs-mysql:
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml logs -f mysql

# 查看Redis服务日志
.PHONY: logs-redis
logs-redis:
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml logs -f redis

# 查看服务状态
.PHONY: status
status:
	@echo "📊 Docker服务状态:"
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml ps

# 查看运行中的容器
.PHONY: ps
ps:
	@echo "📋 运行中的容器:"
	docker ps --filter "name=likeadmin"

# 进入PHP容器shell
.PHONY: php-shell
php-shell:
	@echo "🐘 进入PHP容器..."
	docker exec -it likeadmin-php /bin/sh

# 进入MySQL容器shell
.PHONY: mysql-shell
mysql-shell:
	@echo "🗄️ 进入MySQL容器..."
	docker exec -it likeadmin-mysql mysql -uroot -proot

# 进入Redis容器shell
.PHONY: redis-shell
redis-shell:
	@echo "📦 进入Redis容器..."
	docker exec -it likeadmin-redis redis-cli

# 清理停止的容器和未使用的镜像
.PHONY: clean
clean:
	@echo "🧹 清理Docker资源..."
	docker container prune -f
	docker image prune -f
	@echo "✅ 清理完成!"

# 清理所有相关资源(谨慎使用)
.PHONY: clean-all
clean-all:
	@echo "⚠️  警告: 这将删除所有相关的Docker资源!"
	@read -p "确定要继续吗? [y/N] " confirm && [ "$$confirm" = "y" ]
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml down -v --rmi all
	docker system prune -f
	@echo "✅ 所有资源已清理!"

# 测试API接口
.PHONY: test-api
test-api:
	@echo "🧪 测试API接口..."
	@echo "测试基础API:"
	@curl -s -X GET "http://localhost:8080/adminapi/config/getConfig" -H "Content-Type: application/json" | head -1
	@echo ""
	@echo "测试销售成本模版API:"
	@curl -s -X GET "http://localhost:8080/adminapi/operate.sales_cost_template/lists" -H "Content-Type: application/json"
	@echo ""
	@echo "✅ API测试完成!"

# 备份数据库
.PHONY: backup-db
backup-db:
	@echo "💾 备份数据库..."
	@mkdir -p backups
	docker exec likeadmin-mysql mysqldump -uroot -proot --all-databases > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ 数据库备份完成! 文件保存在 backups/ 目录"

# 快速重启PHP服务(开发时常用)
.PHONY: restart-php
restart-php:
	@echo "🔄 重启PHP服务..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml restart php
	@echo "✅ PHP服务重启完成!"

# 快速重启Nginx服务
.PHONY: restart-nginx
restart-nginx:
	@echo "🔄 重启Nginx服务..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml restart nginx
	@echo "✅ Nginx服务重启完成!"

# 显示项目信息
.PHONY: info
info:
	@echo "📋 神泉CRM项目信息:"
	@echo "  项目目录: $(PWD)"
	@echo "  Docker配置: $(DOCKER_COMPOSE_FILE)"
	@echo "  前端地址: http://localhost:8080"
	@echo "  API地址: http://localhost:8080/adminapi"
	@echo "  MySQL端口: 3306"
	@echo "  Redis端口: 6379"

# 重新加载配置(修改.env文件后使用)
.PHONY: reload-php
reload-php:
	@echo "🔄 重新加载配置文件..."
	@echo "重启PHP服务以加载新的.env配置..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml restart php
	@echo "✅ 配置重新加载完成!"
	@echo "💡 如果修改了数据库配置，建议运行: make restart"

# 切换到Docker本地数据库配置
.PHONY: use-local-db
use-local-db:
	@echo "🔄 切换到Docker本地数据库配置..."
	@cp server/.env server/.env.backup.$(shell date +%Y%m%d_%H%M%S)
	@echo "✅ 已备份原配置文件"
	@sed -i '' 's/HOSTNAME = ".*"/HOSTNAME = "likeadmin-mysql"/' server/.env
	@sed -i '' 's/DATABASE = ".*"/DATABASE = "localhost_likeadmin"/' server/.env
	@sed -i '' 's/USERNAME = ".*"/USERNAME = "root"/' server/.env
	@sed -i '' 's/PASSWORD = ".*"/PASSWORD = "root"/' server/.env
	@echo "✅ 已切换到本地数据库配置"
	@echo "🔄 重启服务以应用新配置..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml restart php
	@echo "✅ 配置切换完成!"

# 恢复远程数据库配置
.PHONY: use-remote-db
use-remote-db:
	@echo "🔄 恢复远程数据库配置..."
	@if [ -f server/.env.backup.* ]; then \
		cp server/.env.backup.* server/.env; \
		echo "✅ 已恢复远程数据库配置"; \
	else \
		echo "❌ 未找到备份文件，请手动修改.env文件"; \
	fi
	@echo "🔄 重启服务以应用新配置..."
	cd $(DOCKER_DIR) && docker-compose -f docker-compose-arm64.yml restart php
	@echo "✅ 配置恢复完成!"

# 显示当前数据库配置
.PHONY: show-db-config
show-db-config:
	@echo "📋 当前主数据库配置:"
	@echo "主数据库配置 [DATABASE]:"
	@sed -n '/^\[DATABASE\]/,/^\[/p' server/.env | grep -E "^(HOSTNAME|DATABASE|USERNAME|PASSWORD)" | head -4 || echo "未找到数据库配置"
