//菜单主题类型
export enum ThemeEnum {
  LIGHT = "light",
  DARK = "dark",
}

// 菜单类型
export enum MenuEnum {
  CATALOGUE = "M",
  MENU = "C",
  BUTTON = "A",
}

// 屏幕
export enum ScreenEnum {
  SM = 640,
  MD = 768,
  LG = 1024,
  XL = 1280,
  "2XL" = 1536,
}

// 客户端类型
export enum ClientEnum {
  MP_WEIXIN = 1, // 微信-小程序
  OA_WEIXIN = 2, // 微信-公众号
  H5 = 3, // H5
  PC = 4, // PC
  IOS = 5, //苹果
  ANDROID = 6, //安卓
  ADMIN = 7, //后台
}

export const ClientMap = {
  [ClientEnum.MP_WEIXIN]: "微信小程序",
  [ClientEnum.OA_WEIXIN]: "微信公众号",
  [ClientEnum.H5]: "手机H5",
  [ClientEnum.PC]: "电脑PC",
  [ClientEnum.IOS]: "苹果APP",
  [ClientEnum.ANDROID]: "安卓APP",
  [ClientEnum.ADMIN]: "管理后台",
};

export const MerchantCityOptions = [
  // {label: '全国', value: '全国'},
  { label: "长沙", value: "长沙" },
  { label: "上海", value: "上海" },
  { label: "杭州", value: "杭州" },
  { label: "宁波", value: "宁波" },
  { label: "福州", value: "福州" },
];

export const ShopCityOptions = [
  { label: "上海", value: "上海" },
  { label: "杭州", value: "杭州" },
  { label: "宁波", value: "宁波" },
  { label: "福州", value: "福州" },
  { label: "武汉", value: "武汉" },
];

// 联系情况
export const MerchantContactStatusOptions = [
  { label: "老板高意向度", value: 1 },
  { label: "老板无意向度", value: 2 },
  { label: "老板目前在忙", value: 3 },
  { label: "老板态度恶劣", value: 10 },
  { label: "老板知道神犬", value: 12 },
  { label: "店铺倒闭不开", value: 6 },
  { label: "不是老板", value: 5 },
  { label: "开口挂断", value: 4 },
  { label: "无人接听", value: 11 },
  { label: "拒接", value: 13 },
  { label: "关机", value: 7 },
  { label: "无效号码", value: 8 },
  { label: "无法接通", value: 14 },
  { label: "风险账户", value: 15 },
  { label: "呼叫上限", value: 16 },
  { label: "其他", value: 0 },
];

// 客诉工单类型
export const CustomerTicketsType = [
  { label: "活动扣费太多", value: 1 },
  { label: "未知收费规则", value: 2 },
];

// 客诉工单紧急程度
export const CustomerTicketsUrgent = [
  { label: "高", value: 3 },
  { label: "中", value: 2 },
  { label: "低", value: 1 },
];

// 店铺等级
export const ShopLevelOptions = [
  { label: "S级", value: 1, desc: "头部店铺" },
  { label: "A级", value: 2, desc: "优质店铺" },
  { label: "B级", value: 3, desc: "潜力店铺" },
  { label: "C级", value: 4, desc: "基础店铺" },
];

// 商圈等级
export const BusinessLevelOptions = [
  { label: "一级", value: 1, desc: "头部商圈" },
  { label: "二级", value: 2, desc: "优质商圈" },
  { label: "三级", value: 3, desc: "潜力商圈" },
  { label: "四级", value: 4, desc: "基础商圈" },
];

// 店铺来源
export const MerchantSourceOptions = [
  { label: "大数据爬取", value: 1 },
  { label: "联盟商机池", value: 2 },
  { label: "招商宝录入", value: 3 },
  { label: "其他", value: 4 },
];

// 平台图标
export const PlatformIcon: any = {
  2: "https://img.dac6.cn/users/0/0/baaf0979e48ecef79ee918308f16e178.png",
  4: "https://img.dac6.cn/users/0/0/40938dd00221c4ea24db50e5330fb084.png",
};
