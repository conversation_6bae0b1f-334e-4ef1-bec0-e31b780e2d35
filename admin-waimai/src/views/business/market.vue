<script setup lang="ts">
import { reactive, nextTick } from "vue";
import VCharts from "vue-echarts";
import { adminLists } from "@/api/perms/admin";
import { apiBusinessMarket } from "@/api/business";
import Search from "./../components/overview/search.vue";
import OverviewData from "./../components/overview/data.vue";

const keysTxt: any = {
  revenue: { title: "收入", desc: "统计时间内，所有有效订单商家实际收入" },
  sales: {
    title: "营业额",
    desc: "统计时间内，所有有效订单的商品原价、打包费、自配送费总额",
  },
  valid_order_count: {
    title: "有效订单",
    desc: "统计时间内，所有有效订单数，不包含取消订单与全部退款订单",
  },
  exposure_count: {
    title: "曝光人数",
    desc: "统计时间内，店铺在各个渠道被曝光的人数之和，对顾客进行去重",
  },
  enter_store_conversion_rate: {
    title: "进店转化率",
    desc: "统计时间内，进店人数占曝光人数的比例",
  },
  order_conversion_rate: {
    title: "下单转化率",
    desc: "统计时间内，下单人数占进店人数的比例",
  },
};
const createQuery = () => ({
  platform: "",
  store_name: "",
  admin_id: "",
});
const queryParams = reactive({ ...createQuery(), start: "", end: "" });
const state = reactive({
  loading: false,
  userList: [] as any[],
  dataList: [],
  dataType: "",

  list: [] as any[],
  showChart: true,
  chartOption: {
    grid: {
      left: "52px",
      right: "52px",
      bottom: "20px",
    },
    xAxis: {
      nameLocation: "center",
      type: "category",
      data: [],
    },
    yAxis: { type: "value" },
    legend: { data: [] },
    tooltip: { trigger: "axis" },
    series: [],
  },
});

const userList = async () => {
  try {
    const { lists } = await adminLists({ page_size: 10000, page_no: 1 });
    state.userList = lists || [];
  } catch (_) {}
};

const getPageData = async () => {
  state.loading = true;
  try {
    const data = await apiBusinessMarket(queryParams);
    state.dataList = (data.statis || []).map((item: any) => ({
      ...item,
      type: item.rate >= 0 ? "plus" : "minus",
    }));
    if (data.statis && data.statis.length) {
      state.dataType = data.statis[0].key;
    } else {
      state.dataType = "";
    }
    state.list = data.this || [];
    formatChart();
  } catch (_) {}
  state.loading = false;
};

const onReset = () => {
  Object.assign(queryParams, createQuery());
  getPageData();
};

const onSearch = ({ params }: any) => {
  Object.assign(queryParams, params);
  getPageData();
};

const onTypeChange = (type: string) => {
  state.dataType = type;
  formatChart();
};

const formatChart = () => {
  state.showChart = false;
  const data: any = [];
  let series = [{ type: "line", data: [] }];
  state.chartOption.legend.data = [];
  series.splice(1, 1);
  state.list.forEach((item: any) => {
    data.push(item.time);
    (series[0].data as any).push(item[state.dataType || "total"]);
  });
  (state.chartOption as any).series = series;
  state.chartOption.xAxis.data = data;
  nextTick(() => (state.showChart = true));
};

userList();
</script>

<template>
  <el-card class="!border-none" shadow="never">
    <el-form :model="queryParams" inline>
      <el-form-item>
        <el-radio-group v-model="queryParams.platform" @change="getPageData">
          <el-radio-button label="">全部平台</el-radio-button>
          <el-radio-button label="1">饿了么</el-radio-button>
          <el-radio-button label="2">美团</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="店铺信息" prop="store_name">
        <el-input placeholder="请输入名称" v-model="queryParams.store_name" />
      </el-form-item>
      <el-form-item label="归属运营" prop="admin_id">
        <el-select filterable clearable v-model="queryParams.admin_id">
          <el-option
            v-for="item in state.userList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getPageData">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>

  <el-card v-loading="state.loading" class="!border-none mt-4" shadow="never">
    <Search :show-day="false" @change="onSearch" />
    <OverviewData
      class="my-2"
      :list="state.dataList"
      :data-type="state.dataType"
      :show-chain="false"
      chain
      @change="onTypeChange"
    />
    <div v-if="state.dataType" class="flex items-center">
      <div class="mr-4" style="font-weight: 700">
        {{ keysTxt[state.dataType].title }}
      </div>
      <div>{{ keysTxt[state.dataType].desc }}</div>
    </div>

    <VCharts
      v-if="state.showChart"
      style="height: 293px; width: 100%; margin-top: 33px"
      :autoresize="true"
      :option="state.chartOption"
    />
  </el-card>
</template>
