<script setup lang="ts">
import { reactive } from "vue";
import { adminLists } from "@/api/perms/admin";
import { apiBusinessShop } from "@/api/business";
import { usePaging } from "@/hooks/usePaging";
import Search from "./../components/overview/search.vue";

const createQuery = () => ({
  platform: "",
  admin_id: "",
  name: "",
});

const queryParams = reactive({
  ...createQuery(),
  start_time: "",
  end_time: "",
  order: "",
});
const state = reactive({
  loading: false,
  userList: [] as any[],
});

// 分页相关
const { pager, getLists, resetPage } = usePaging({
  fetchFun: apiBusinessShop,
  params: queryParams,
});

const userList = async () => {
  try {
    const { lists } = await adminLists({ page_size: 10000, page_no: 1 });
    state.userList = lists || [];
  } catch (_) {}
};

const onSearch = ({ params }: any) => {
  Object.assign(queryParams, {
    start_time: params.start,
    end_time: params.end,
  });
  getLists();
};

const sortChange = (data: any) => {
  if (data.order === "ascending") {
    queryParams.order = `${data.prop} asc`;
  } else if (data.order === "descending") {
    queryParams.order = `${data.prop} desc`;
  } else {
    queryParams.order = "";
  }
  getLists();
};

const onReset = () => {
  Object.assign(queryParams, createQuery());
  getLists();
};

const setColor = (val: number) => {
  const c = val > 0 ? "red" : val < 0 ? "green" : "";
  return `color: ${c}`;
};

userList();
</script>

<template>
  <el-card class="!border-none" shadow="never">
    <el-form :model="queryParams" inline>
      <el-form-item>
        <el-radio-group v-model="queryParams.platform" @change="getLists">
          <el-radio-button label="">全部平台</el-radio-button>
          <el-radio-button label="2">饿了么</el-radio-button>
          <el-radio-button label="4">美团</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="店铺信息" prop="store_name">
        <el-input placeholder="请输入名称" v-model="queryParams.name" />
      </el-form-item>
      <el-form-item label="归属运营" prop="admin_id">
        <el-select filterable clearable v-model="queryParams.admin_id">
          <el-option
            v-for="item in state.userList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="resetPage">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>

  <el-card class="!border-none mt-4" shadow="never">
    <Search class="mb-4" :show-day="false" @change="onSearch" />

    <el-table
      v-loading="pager.loading"
      :data="pager.lists"
      @sort-change="sortChange"
    >
      <el-table-column label="店铺名称" width="230">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-image
              :src="
                row.platform === 2
                  ? 'https://img.dac6.cn/users/0/0/baaf0979e48ecef79ee918308f16e178.png'
                  : 'https://img.dac6.cn/users/0/0/40938dd00221c4ea24db50e5330fb084.png'
              "
              fit="cover"
              style="width: 30px; height: 30px"
            />
            <div class="ml-2 flex-1">
              <el-tooltip :content="row.name" placement="top">
                <div class="line-clamp-2">
                  {{ row.name }}
                </div>
              </el-tooltip>
              <div>
                编码：{{
                  row.store_id && row.store_id != 0 ? row.store_id : "无"
                }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="收入"
        prop="sum_revenue"
        sortable="custom"
        min-width="110"
      >
        <template #default="{ row }">
          {{ row.sum_revenue }}
          <div
            class="flex items-center"
            :style="setColor(row.comparison_revenue)"
          >
            <el-icon v-if="row.comparison_revenue > 0"><Top /></el-icon>
            <el-icon v-else-if="row.comparison_revenue < 0"><Bottom /></el-icon>
            {{ row.comparison_revenue }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        min-width="110"
        label="营业额"
        prop="sum_sales"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.sum_sales }}
          <div
            class="flex items-center"
            :style="setColor(row.comparison_sales)"
          >
            <el-icon v-if="row.comparison_sales > 0"><Top /></el-icon>
            <el-icon v-else-if="row.comparison_sales < 0"><Bottom /></el-icon>
            {{ row.comparison_sales }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="有效订单"
        prop="sum_valid_order_count"
        min-width="110"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.sum_valid_order_count }}
          <div
            class="flex items-center"
            :style="setColor(row.comparison_valid_order_count)"
          >
            <el-icon v-if="row.comparison_valid_order_count > 0"
              ><Top
            /></el-icon>
            <el-icon v-else-if="row.comparison_valid_order_count < 0"
              ><Bottom
            /></el-icon>
            {{ row.comparison_valid_order_count }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="曝光人数"
        prop="sum_exposure_count"
        min-width="110"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.sum_exposure_count }}
          <div
            class="flex items-center"
            :style="setColor(row.comparison_exposure_count)"
          >
            <el-icon v-if="row.comparison_exposure_count > 0"><Top /></el-icon>
            <el-icon v-else-if="row.comparison_exposure_count < 0"
              ><Bottom
            /></el-icon>
            {{ row.comparison_exposure_count }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="进店转化率"
        prop="sum_enter_store_conversion_rate"
        min-width="120"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.sum_exposure_count }}%
          <div
            class="flex items-center"
            :style="setColor(row.comparison_enter_store_conversion_rate)"
          >
            <el-icon v-if="row.comparison_enter_store_conversion_rate > 0"
              ><Top
            /></el-icon>
            <el-icon v-else-if="row.comparison_enter_store_conversion_rate < 0"
              ><Bottom
            /></el-icon>
            {{ row.comparison_enter_store_conversion_rate }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="进店人数"
        prop="sum_clk_uv"
        min-width="110"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.comparison_clk_uv }}
          <div
            class="flex items-center"
            :style="setColor(row.comparison_clk_uv)"
          >
            <el-icon v-if="row.comparison_clk_uv > 0"><Top /></el-icon>
            <el-icon v-else-if="row.comparison_clk_uv < 0"><Bottom /></el-icon>
            {{ row.comparison_clk_uv }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="下单转化率"
        prop="sum_order_conversion_rate"
        min-width="120"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.comparison_order_conversion_rate }}%
          <div
            class="flex items-center"
            :style="setColor(row.comparison_order_conversion_rate)"
          >
            <el-icon v-if="row.comparison_order_conversion_rate > 0"
              ><Top
            /></el-icon>
            <el-icon v-else-if="row.comparison_order_conversion_rate < 0"
              ><Bottom
            /></el-icon>
            {{ row.comparison_order_conversion_rate }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="下单人数"
        prop="sum_order_user_count"
        min-width="110"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.comparison_order_user_count }}
          <div
            class="flex items-center"
            :style="setColor(row.comparison_order_user_count)"
          >
            <el-icon v-if="row.comparison_order_user_count > 0"
              ><Top
            /></el-icon>
            <el-icon v-else-if="row.comparison_order_user_count < 0"
              ><Bottom
            /></el-icon>
            {{ row.comparison_order_user_count }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="代运营天数"
        min-width="120"
        prop="day"
        sortable="custom"
      />
      <el-table-column label="归属运营" prop="admin_name" />
    </el-table>
    <div class="flex mt-4 justify-end">
      <pagination v-model="pager" @change="getLists" />
    </div>
  </el-card>
</template>
