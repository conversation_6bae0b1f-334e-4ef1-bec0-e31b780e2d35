<script lang="ts" setup name="workbench">
import { reactive, watch } from "vue";
import { useRouter } from "vue-router";
import VCharts from "vue-echarts";
import feedback from "@/utils/feedback";
import { unitNumber, roundNumber, timeFormat } from "@/utils/util";
import useUserStore from "@/stores/modules/user";
import { reqMerchantOverview } from "@/api/merchant/overview";
import {
  getHomeData,
  apiYunyingRanking,
  apiRuditChart,
  apiMessageRead,
  apiMessageList,
} from "@/api/app";

const visitWaimaiKey = "visitWaimaiKey";
const userStore = useUserStore();
const router = useRouter();
const rankingTime = [
  // { name: "今日", value: 0 },
  { name: "昨日", value: 1 },
  { name: "本周", value: 2 },
  { name: "本月", value: 4 },
];
const signboardSele = [
  { name: "按店铺", value: 1 },
  { name: "按收益", value: 2 },
];

const state = reactive({
  dataLoading: false,
  messageLoading: false,
  ruditLoading: false,
  // 数据
  data: [
    {
      name: "新增外卖店铺数",
      unit: "个",
      value: 0,
      icon: "https://img.dac6.cn/users/0/0/480b5a219a36bde73f84b58f280938b0.png",
    },
    {
      name: "流失外卖店铺数",
      unit: "个",
      value: 0,
      icon: "https://img.dac6.cn/users/0/0/0097460c86c7193750de0ce49f7cf491.png",
    },
    {
      name: "合作外卖店铺数",
      unit: "个",
      value: 0,
      icon: "https://img.dac6.cn/users/0/0/0e80e2e49db5ef094a345f22386dc128.png",
    },
    {
      name: "外卖店铺收益额",
      unit: "元",
      value: 0,
      icon: "https://img.dac6.cn/users/0/0/848b0fd7e05c9b895485f87ce1918a1b.png",
    },
  ],
  signboardData: {},
  signboard: {
    loading: false,
    active: 1,
    option: {
      grid: {
        left: "0",
        right: "0",
        bottom: "0",
        top: "10px",
        containLabel: true,
      },
      color: [] as any[],
      tooltip: { trigger: "axis" },
      xAxis: {
        data: [] as any[],
        axisLabel: {
          fontSize: 12,
          color: "#86909C",
        },
        axisLine: {
          lineStyle: { color: "#E5E8EF" },
        },
      },
      yAxis: {
        type: "value",
        axisLine: {
          show: true,
          lineStyle: { color: "#E5E8EF" },
        },
        axisLabel: {
          fontSize: 12,
          color: "#86909C",
        },
      },
      series: [] as any[],
    },
  },
  // 快捷入口
  shortcut: [
    {
      name: "招商概览",
      icon: "https://img.dac6.cn/users/0/0/7f5cf9e4ab64d0acf175b8bd1aa8a1bb.png",
      url: "/attract/overview",
    },
    {
      name: "店铺列表",
      icon: "https://img.dac6.cn/users/0/0/e1977316757099d76c4d7e2dc3438544.png",
      url: "/operation/shop",
    },
    {
      name: "巡店监控",
      icon: "https://img.dac6.cn/users/0/0/4c4a13b6903e5aefe576ab8c85fee30f.png",
      url: "/monitor/tour",
    },
  ],
  // 最近访问
  visit: (window.localStorage.getItem(visitWaimaiKey)
    ? JSON.parse(window.localStorage.getItem(visitWaimaiKey)!)
    : []) as any[],
  // 成交排名
  ranking: {
    loading: false,
    query: {
      sub_type: rankingTime[0].value,
      st: timeFormat(Date.now() - 86400000),
      et: timeFormat(Date.now() - 86400000),
    },
    list: [],
  },
  // 活动审核数据
  reviewTotal: 0,
  reviewOption: {
    legend: {
      orient: "horizontal",
      bottom: "bottom",
      itemWidth: 10,
      itemHeight: 10,
      icon: "circle",
    },
    series: [
      {
        name: "状态",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        labelLine: { length: 5 },
        label: {
          position: "outside",
          distance: 20,
          formatter: (a: any) => {
            if (a.value) {
              const num = roundNumber((a.value / a.data.total) * 100);
              if (num) return `${num}%`;
              else return "<0.01%";
            }
            return "0%";
          },
          textStyle: { fontSize: 12, color: "#4E5969" },
        },
        itemStyle: { borderWidth: 2, borderColor: "#fff" },
        data: [] as any[],
      },
    ],
  },
  message: [] as any[],
});

// 获取数据
const getData = async () => {
  try {
    const data = await getHomeData();
    const arr = [
      "add_operate_shop_count",
      "loss_operate_shop_count",
      "operate_shop_count",
      "operate_this_month_income",
    ];
    state.data.forEach((item: any, index: number) => {
      if (data[arr[index]] >= 1000000)
        item.value = roundNumber(data[arr[index]] / 10000) + "w";
      else
        item.value = unitNumber(data[arr[index]], { lang: "en", decimal: 2 });
    });
  } catch (_) {}
};
// 成交排名
const getRanking = async () => {
  state.ranking.loading = true;
  try {
    const data = await apiYunyingRanking(state.ranking.query);
    state.ranking.list = (data || []).slice(0, 6);
  } catch (_) {}
  state.ranking.loading = false;
};
// 数据看板
const getSignboard = async () => {
  state.signboard.loading = true;
  try {
    const data = await reqMerchantOverview({
      type: 7,
      sub_type: state.signboard.active,
      start: timeFormat(Date.now() - 86400000 * 7),
      end: timeFormat(Date.now() - 86400000),
    });
    state.signboardData = data;
  } catch (_) {}
  state.signboard.loading = false;
};
// 活动审核数据
const getRuditChart = async () => {
  state.ruditLoading = true;
  try {
    const data = await apiRuditChart();
    state.reviewTotal = data.total_count;
    let arr = [
      {
        name: "有收益店铺数",
        value: data.income_count,
        total: state.reviewTotal,
        itemStyle: { color: "#42FBB4" },
      },
      {
        name: "无收益店铺数",
        value: data.not_income_count,
        total: state.reviewTotal,
        itemStyle: { color: "#DE21FF" },
      },
    ];
    arr = arr.sort((a: any, b: any) => b.value - a.value);
    state.reviewOption.series[0].data = arr;
  } catch (_) {}
  state.ruditLoading = false;
};

const getFormattedDate = (date = new Date()) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 消息中心
const getMessageList = async () => {
  state.messageLoading = true;
  try {
    const data = await apiMessageList({ platform: 2 });
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const t = getFormattedDate();
    const t1 = getFormattedDate(yesterday);
    state.message = (data.lists || [])
      .map((item: any) => {
        let txt = timeFormat(new Date(item.date_time).getTime(), "mm月dd日");
        if (t === item.date_time) txt = "今日";
        else if (t1 === item.date_time) txt = "昨日";
        if (item.message_type === 4) {
          item.title = "店铺分异常";
          item.color = "#0fc6c2";
          item.bg = "#e8fffb";
          item.content = `店铺分异常，${txt}${item.content}个店铺出现异常`;
          item.link = { url: "/monitor/score" };
        } else if (item.message_type === 5) {
          item.title = "商品异常";
          item.color = "#229bff";
          item.bg = "#e4f3ff";
          item.content = `商品异常，${txt}${item.content}个店铺出现异常`;
          item.link = { url: "/monitor/goods" };
        } else {
          item.title = "巡店异常";
          item.color = "#8942FB";
          item.bg = "#F5EEFF";
          item.content = `巡店异常，${txt}${item.content}个店铺出现异常`;
          item.link = { url: "/monitor/tour" };
        }
        return item;
      })
      .slice(0, 10);
  } catch (_) {}
  state.messageLoading = false;
};

const setRankingTime = (val: number) => {
  const today = new Date();
  let st, et;
  switch (val) {
    case 0: // 今日
      st = et = timeFormat(today.getTime());
      break;
    case 1: // 昨日
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      st = et = timeFormat(yesterday.getTime());
      break;
    case 2: // 本周
      // 计算本周一日期
      const firstDayOfWeek = new Date(today);
      const day = today.getDay() || 7; // 将周日的0转为7
      firstDayOfWeek.setDate(today.getDate() - day + 1);
      // 计算本周日日期
      const lastDayOfWeek = new Date(firstDayOfWeek);
      lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);
      st = timeFormat(firstDayOfWeek.getTime());
      et = timeFormat(lastDayOfWeek.getTime());
      break;
    case 4: // 本月
      // 计算本月第一天日期
      const firstDayOfMonth = new Date(
        today.getFullYear(),
        today.getMonth(),
        1
      );
      // 计算本月最后一天日期
      const lastDayOfMonth = new Date(
        today.getFullYear(),
        today.getMonth() + 1,
        0
      );
      st = timeFormat(firstDayOfMonth.getTime());
      et = timeFormat(lastDayOfMonth.getTime());
      break;
    default:
      st = et = timeFormat(today.getTime());
  }
  state.ranking.query.st = st;
  state.ranking.query.et = et;
};

// 切换时间
const onChangeTime = (val: number) => {
  if (state.ranking.query.sub_type === val) return;
  state.ranking.query.sub_type = val;
  setRankingTime(val);
  getRanking();
};

const onShortcut = (item: any) => {
  state.visit.unshift(item);
  state.visit = [
    ...new Set(state.visit.map((item) => JSON.stringify(item))),
  ].map((str) => JSON.parse(str));
  window.localStorage.setItem(visitWaimaiKey, JSON.stringify(state.visit));
  toNav(item.url);
};
const onMessage = async (item: any) => {
  try {
    await apiMessageRead({ id: item.id });
    getMessageList();
    if (item.link) toNav(item.link.url, item.link.query);
  } catch (_) {}
};

const allRoute: string[] = [];
let route = "";
let oneRoute = "";
const allRouterUrl = (list: any[], level: number) => {
  if (list && list.length > 0) {
    list.forEach((item: any) => {
      if (level === 1) {
        route = item.path;
        oneRoute = item.path;
      } else route += `/${item.path}`;
      if (item.children && item.children.length) {
        allRouterUrl(item.children, level + 1);
      } else {
        allRoute.push(route);
        route = oneRoute;
      }
    });
  }
};

// 跳转
const toNav = (path: string, query?: any) => {
  allRouterUrl(userStore.routes, 1);
  if (!allRoute.includes(path)) return feedback.msgWarning("暂无权限");
  router.push({ path, query });
};

const formatCharts = (val: any) => {
  const series = [{ data: [] as any[] }];
  state.signboard.option.xAxis.data = [] as any[];
  const key = state.signboard.active === 1 ? "total_count" : "total_income";
  val.this.forEach((item: any) => {
    state.signboard.option.xAxis.data.push(item.time);
    series[0].data.push(item[key]);
  });
  state.signboard.option.series = series.map((series) => ({
    type: "line",
    data: series.data,
    smooth: true,
    symbolSize: 8,
    lineStyle: { width: 3 },
  }));
  if (state.signboard.active === 1) state.signboard.option.color = ["#1EE7FF"];
  else state.signboard.option.color = ["#F77234"];
};

// 看板切换
const onChangeSignboard = async (val: number) => {
  if (state.signboard.active === val) return;
  state.signboard.active = val;
  await getSignboard();
  formatCharts(state.signboardData);
};

watch(
  () => state.signboardData,
  (val: any) => {
    formatCharts(val);
  },
  { deep: true }
);

const getDataHome = async () => {
  state.dataLoading = true;
  await Promise.all([getData(), getSignboard()]);
  state.dataLoading = false;
};

getRanking();
getDataHome();
getRuditChart();
getMessageList();
</script>

<template>
  <div class="flex wb">
    <div class="flex-[3]">
      <div v-loading="state.dataLoading" class="white mb-[16px]">
        <div
          class="px-[20px] py-[40px] flex justify-between mb-[20px]"
          style="border-bottom: 1px solid #f2f3f5"
        >
          <div
            v-for="(item, index) in state.data"
            :key="index"
            class="flex items-center justify-center w-[25%] wb__d"
          >
            <el-image :src="item.icon" style="width: 54px; height: 54px" />
            <div class="ml-[12px] flex flex-col justify-between">
              <div class="text-[12px]" style="color: #1d2129">
                {{ item.name }}
              </div>
              <div class="text-[12px]" style="color: #1d2129">
                <span class="text-[24px] font-bold mr-[8px]">{{
                  item.value
                }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <!-- 数据看板 -->
        <div class="p-[20px] pt-0">
          <div class="flex items-center justify-between mb-[16px]">
            <div
              class="flex text-[16px] font-medium items-baseline"
              style="color: #1d2129"
            >
              数据看板<span class="font-normal text-[12px]">（近7日）</span>
            </div>
            <div
              class="p-[3px] rounded-[2px] flex items-center"
              style="background-color: #f2f3f5"
            >
              <div
                v-for="(item, index) in signboardSele"
                :key="index"
                class="w-[66px] h-[26px] rounded-[2px] text-[14px] flex justify-center cursor-pointer items-center"
                style="color: #4e5969"
                :class="{ wb__sele: state.signboard.active === item.value }"
                @click="onChangeSignboard(item.value)"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
          <VCharts
            v-loading="state.signboard.loading"
            style="height: 300px; width: 100%"
            :autoresize="true"
            :option="state.signboard.option"
          />
        </div>
      </div>
      <div class="flex">
        <!-- 运营排名 -->
        <div class="white p-5 min-w-[460px] mr-[20px] flex-[1.2]">
          <div class="flex items-center justify-between">
            <div class="text-[16px] font-medium">运营排名</div>
          </div>
          <div
            class="rounded-[2px] p-[3px] my-[16px] w-fit flex items-center"
            style="background-color: #f2f3f5"
          >
            <div
              v-for="(item, index) in rankingTime"
              :key="index"
              class="text-[12px] w-[48px] h-[18px] rounded-[2px] flex justify-center cursor-pointer"
              style="color: #4e5969"
              :class="{ wb__sele: state.ranking.query.sub_type === item.value }"
              @click="onChangeTime(item.value)"
            >
              {{ item.name }}
            </div>
          </div>
          <el-table
            :data="state.ranking.list"
            v-loading="state.ranking.loading"
          >
            <el-table-column label="排名" min-width="52px" align="center">
              <template #default="{ $index }">{{ $index + 1 }}</template>
            </el-table-column>
            <el-table-column
              label="运营姓名"
              prop="admin_name"
              min-width="83px"
              align="center"
            />
            <el-table-column
              label="店铺数量"
              prop="operate_count"
              min-width="90px"
              align="center"
            />
            <el-table-column
              label="合作店铺收益"
              prop="income_amount"
              min-width="110px"
              align="center"
            />
            <el-table-column
              label="累计店铺收益"
              prop="total_amount"
              min-width="110px"
              align="center"
            />
          </el-table>
        </div>
        <!-- 运营概览 -->
        <div
          v-loading="state.ruditLoading"
          class="white p-5 min-w-[400px] h-fit flex-1"
        >
          <div class="flex items-center justify-between mb-[16px]">
            <div class="text-[16px] font-medium">
              运营概览<span class="text-[12px]" style="color: #4e5969"
                >（昨日）</span
              >
            </div>
            <div
              class="text-[14px] cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="toNav('/operation/overview')"
            >
              查看更多
            </div>
          </div>
          <div class="relative">
            <VCharts
              style="height: 344px; width: 100%"
              :autoresize="true"
              :option="state.reviewOption"
            />
            <div
              class="absolute top-[50%] left-[50%] flex items-center justify-center flex-col transform -translate-x-[50%] -translate-y-[50%]"
            >
              <div class="text-[14px]" style="color: #4e5969">全部</div>
              <div class="text-[16px] font-medium" style="color: #1d2129">
                {{ state.reviewTotal }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="ml-[20px] min-w-[280px] flex-1">
      <div class="white p-[20px]">
        <!-- 快捷入口 -->
        <div class="text-[16px] font-medium mb-[16px]" style="color: #1d2129">
          快捷入口
        </div>
        <div class="flex flex-wrap">
          <div
            v-for="(item, index) in state.shortcut"
            :key="index"
            class="w-[33.33%] text-center py-[12px] cursor-pointer"
            @click="onShortcut(item)"
          >
            <el-image :src="item.icon" style="width: 36px; height: 36px" />
            <div class="text-[12px]" style="color: #1d2129">
              {{ item.name }}
            </div>
          </div>
        </div>
        <!-- 最近访问 -->
        <div
          v-if="state.visit.length"
          class="pt-[20px] mt-[8px]"
          style="border-top: 1px solid #e5e6eb"
        >
          <div class="text-[16px] font-medium mb-[16px]" style="color: #1d2129">
            最近访问
          </div>
          <div class="flex flex-wrap">
            <div
              v-for="(item, index) in state.visit"
              :key="index"
              class="w-[33.33%] text-center py-[12px] cursor-pointer"
              @click="toNav(item.url)"
            >
              <el-image :src="item.icon" style="width: 36px; height: 36px" />
              <div class="text-[12px]" style="color: #1d2129">
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 消息中心 -->
      <div v-loading="state.messageLoading" class="white mt-[16px] p-[20px]">
        <div class="text-[16px] font-medium" style="color: #1d2129">
          消息中心
        </div>
        <div
          v-for="(item, index) in state.message"
          :key="index"
          class="mt-[16px] flex justify-between cursor-pointer"
          @click="onMessage(item)"
        >
          <div
            class="rounded-[2px] text-[12px] px-[4px] h-[20px] font-medium flex items-center"
            :style="{ color: item.color, background: item.bg }"
          >
            {{ item.title }}
          </div>
          <div
            class="text-[13px] ml-[4px] mr-[20px] flex-1"
            style="color: #4e5969"
          >
            {{ item.content }}
          </div>
          <div
            v-if="item.is_read === 0"
            class="w-[8px] h-[8px] rounded-[50%] mt-[6px]"
            style="background-color: #fb3e44"
          ></div>
        </div>
        <div
          v-if="!state.message.length"
          class="flex flex-col items-center justify-center pt-[36px] pb-[20px]"
        >
          <el-image
            src="https://img.dac6.cn/users/0/0/ca38c8238f38d182f4312462d8f16557.png"
            style="width: 116px; height: 100px"
          />
          <div class="text-[12px] mt-[16px]" style="color: #86909c">
            空空如也...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.white {
  color: #1d2129;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}
.wb {
  :deep(.el-table__inner-wrapper) {
    &::before {
      background-color: transparent;
    }
  }
  &__sele {
    background-color: #fff;
    color: var(--el-color-primary) !important;
  }
  &__d {
    border-right: 1px solid #f2f3f5;
    &:last-child {
      border-right-width: 0;
    }
  }
}
</style>
