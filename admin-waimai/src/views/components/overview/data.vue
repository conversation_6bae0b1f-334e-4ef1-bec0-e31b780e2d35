<script setup lang="ts">
import { unitNumber } from "@/utils/util";

const emit = defineEmits(["change"]);
defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  dataType: String,
  showTotal: <PERSON>olean,
  longItem: <PERSON>olean,
  // 环比和同比只能一个为true
  chain: <PERSON><PERSON>an,
  showChain: {
    type: Boolean,
    default: true,
  },
});

// 切换
const seleType = (type: string) => {
  emit("change", type);
};
</script>

<template>
  <div class="flex flex-wrap items-center">
    <div
      v-for="(item, i) in list as any[]"
      :key="i"
      :class="{ po__datas: dataType === item.key }"
      class="po__data"
      :style="{ minWidth: longItem ? '232px' : '171px' }"
      @click="seleType(item.key)"
    >
      <p style="color: rgba(0, 0, 0, 0.45); font-size: 14px">{{ item.text }}</p>
      <p style="font-size: 30px">
        {{ typeof item.this === "number" ? unitNumber(item.this) : item.this }}
      </p>
      <p v-if="showTotal" style="font-size: 18">
        总数：{{ unitNumber(item.total) }}
      </p>
      <el-divider v-if="showChain || chain" class="!mt-0 !mb-2" />
      <div v-if="showChain" class="po__dp" :class="item.type">
        环比&nbsp;<span
          :style="{ color: item.type === 'plus' ? '#f5222d' : '#52c41a' }"
          >{{ Math.abs(item.rate) }}%</span
        >
      </div>
      <template v-else-if="chain">
        <span v-if="item.rate === 0">同比持平</span>
        <div v-else class="po__dp" :class="item.type">
          同比&nbsp;
          <span :style="{ color: item.type === 'plus' ? '#f5222d' : '#52c41a' }"
            >{{ Math.abs(item.rate) }}%</span
          >
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.po {
  &__data {
    cursor: pointer;
    border: 1px solid #e8e8e8;
    padding: 20px 24px 10px;
    margin: 0 16px 8px 0;
    border-radius: 10px;
    min-width: 171px;
    &:last-child {
      margin-right: 0;
    }
  }
  &__datas {
    border-color: #0681ff;
  }
  &__dp {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    position: relative;
    display: inline-block;
    &.minus {
      &::after {
        content: " ";
        position: absolute;
        height: 0 !important;
        width: 0 !important;
        border: 6px solid;
        border-color: transparent;
        border-top-color: #52c41a;
        top: 7px;
        right: -16px;
      }
    }
    &.plus {
      &::after {
        content: " ";
        position: absolute;
        height: 0 !important;
        width: 0 !important;
        border: 6px solid;
        border-color: transparent;
        border-bottom-color: #f5222d;
        top: 1px;
        right: -16px;
      }
    }
  }
}
</style>
