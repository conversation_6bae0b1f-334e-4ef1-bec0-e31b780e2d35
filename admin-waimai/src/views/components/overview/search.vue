<script setup lang="ts">
import { reactive, computed, watch } from "vue";
import { timeFormat } from "@/utils/util";

const props = defineProps({
  dimen: { type: String, default: "store" },
  showDay: { type: Boolean, default: true },
  showYesterday: { type: Boolean, default: true },
  dimenList: { type: Array, default: () => [] },
  timeType: { type: String, default: "" },
});
const emit = defineEmits(["change"]);

const timeSeleBtn = computed(() => {
  let arr = [
    { name: "今天", type: "day" },
    { name: "昨天", type: "yesterday" },
    { name: "本周", type: "week" },
    { name: "上周", type: "lastWeek" },
    { name: "本月", type: "month" },
    { name: "上月", type: "lastMonth" },
    { name: "自定义", type: "custom" },
  ];
  if (!props.showYesterday) {
    const index = arr.findIndex((item) => item.type === "yesterday");
    arr.splice(index, 1);
  }
  if (!props.showDay) arr.shift();
  return arr;
});

const timeType = computed(() => {
  if (props.timeType) return props.timeType;
  let type = "day";
  if (!props.showDay && props.showYesterday) type = "yesterday";
  if (!props.showDay && !props.showYesterday) type = "week";
  return type;
});

const state = reactive({
  time: {
    type: timeType.value,
    showCustom: false,
    custom: [] as Date[],
    t: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
  },
  params: {},
});

// 获取昨天时间
const getYesterDay = (current: Date) => {
  const dayTimes = 1000 * 60 * 60 * 24;
  const t = new Date(current.getTime() - dayTimes);
  const t1 = timeFormat(t.setHours(0, 0, 0, 0));
  const t2 = timeFormat(t.setHours(23, 59, 59, 999));
  state.params = { start: t1, end: t2 };
  return `${t1}~${t2}`;
};
// 获取本周或上周时间
const getWeekTime = (type: "week" | "lastWeek", current: Date) => {
  const week = current.getDay();
  const day =
    type === "week" ? (week === 0 ? 6 : week - 1) : week === 0 ? 13 : week + 6;
  const weekStart = new Date(current.getTime() - 1000 * 60 * 60 * 24 * day);
  const weekEnd = new Date(weekStart.getTime() + 1000 * 60 * 60 * 24 * 6);
  state.params = {
    start: timeFormat(weekStart.setHours(0, 0, 0, 0)),
    end: timeFormat(weekEnd.setHours(23, 59, 59, 999)),
  };
  return `${timeFormat(weekStart.getTime())}~${timeFormat(weekEnd.getTime())}`;
};
// 获取本月或上月时间
const getMonthTime = (type: string, current: Date) => {
  const year = current.getFullYear();
  const month = current.getMonth();
  let monthStart, monthEnd;
  if (type === "lastMonth") {
    monthStart = new Date(year, month - 1, 1);
    monthEnd = new Date(year, month, 0);
  } else {
    monthStart = new Date(year, month, 1);
    monthEnd = new Date(year, month + 1, 0);
  }
  state.params = {
    start: timeFormat(monthStart.setHours(0, 0, 0, 0)),
    end: timeFormat(monthEnd.setHours(23, 59, 59, 999)),
  };
  return `${timeFormat(monthStart.getTime())}~${timeFormat(
    monthEnd.getTime()
  )}`;
};
// 格式化时间
const formatTime = (current: Date, type: string) => {
  let t = "";
  switch (type) {
    case "yesterday":
      t = getYesterDay(current);
      break;
    case "week":
    case "lastWeek":
      t = getWeekTime(type, current);
      break;
    default:
      t = getMonthTime(type, current);
  }
  return t;
};

// 时间筛选
const seleTime = (type: string, force?: boolean) => {
  if (type === state.time.type && !force) return;
  state.time.custom = [];
  state.time.type = type;
  state.time.showCustom = false;
  if (type === "custom") return (state.time.showCustom = true);
  const current = new Date();
  if (type === "day") {
    state.time.t = timeFormat(current.getTime(), "yyyy-mm-dd hh:MM:ss");
    state.params = {
      start: timeFormat(current.getTime()),
      end: timeFormat(current.getTime()),
    };
  } else state.time.t = formatTime(current, type);
  emit("change", { params: state.params, type });
};

watch(
  () => state.time.type,
  (val: string) => seleTime(val, true),
  { immediate: true }
);

// 自定义时间禁用
const disabledDate = (date: Date) => {
  const current = new Date().getTime();
  if (date.getTime() < current) return false;
  else return true;
};

const onCustomChange = () => {
  if (state.time.custom.length !== 2) return;
  const start = timeFormat(state.time.custom[0].getTime());
  const end = timeFormat(state.time.custom[1].getTime());
  state.params = { start, end };
  state.time.t = `${start}~${end}`;
  emit("change", { params: state.params, type: "custom" });
};

const onDimen = (item: any) => {
  emit("change", { dimen: { type: item.type, value: item.value } });
};
</script>

<template>
  <div class="flex items-center h-8">
    <div class="flex items-center">
      <div class="flex items-center mr-2" style="font-size: 12px">
        <el-icon><Odometer /></el-icon>
        <p class="ml-1">统计时间：{{ state.time.t }}</p>
      </div>
      <el-button
        v-for="item in timeSeleBtn"
        :key="item.type"
        class="!ml-0"
        size="small"
        :type="item.type === state.time.type ? 'primary' : ''"
        :text="item.type !== state.time.type"
        @click="seleTime(item.type)"
        >{{ item.name }}</el-button
      >
      <el-date-picker
        v-if="state.time.showCustom"
        v-model="state.time.custom"
        class="ml-2"
        style="width: 220px"
        type="daterange"
        :default-time="[
          new Date(new Date().setHours(0, 0, 0, 0)),
          new Date(new Date().setHours(23, 59, 59, 999)),
        ]"
        :disabled-date="disabledDate"
        @change="onCustomChange"
      />
    </div>
    <div v-if="dimenList.length" class="flex items-center ml-2">
      <p class="mr-2" style="font-size: 12px">统计维度：</p>
      <el-button
        v-for="item in dimenList as any[]"
        :key="item.type"
        size="small"
        class="!ml-0"
        :type="item.type === dimen ? 'primary' : ''"
        :text="item.type !== dimen"
        @click="onDimen(item)"
        >{{ item.name }}</el-button
      >
    </div>
  </div>
</template>
