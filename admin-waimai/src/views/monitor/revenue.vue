<script setup lang="ts">
import { reactive } from "vue";
import { type CheckboxValueType } from "element-plus";
import { PlatformIcon } from "@/enums/appEnums";
import { usePaging } from "@/hooks/usePaging";
import { adminLists } from "@/api/perms/admin";
import {
  apiAgencyControlRevenueData,
  apiAgencyControlRevenue,
  apiAgencyControlRevenueConfig,
  apiAgencyControlRevenueConfigAdd,
  apiAgencyControlRevenueConfigEdit,
  apiAgencyControlRevenueConfigDelete,
} from "@/api/agency";
import feedback from "@/utils/feedback";

const createDialogForm = () => ({
  template_name: "",
  merchant_fixed_rate_meituan: undefined as undefined | number,
  merchant_fixed_rate_ele: undefined as undefined | number,
  merchant_daily_fee_meituan: undefined as undefined | number,
  merchant_daily_fee_ele: undefined as undefined | number,
  merchant_upfront_single: undefined as undefined | number,
  merchant_upfront_dual: undefined as undefined | number,
  operation_fixed_rate_meituan: undefined as undefined | number,
  operation_fixed_rate_ele: undefined as undefined | number,
  operation_daily_fee_meituan: undefined as undefined | number,
  operation_daily_fee_ele: undefined as undefined | number,
  operation_upfront_single: undefined as undefined | number,
  operation_upfront_dual: undefined as undefined | number,
  effective_start: "",
  effective_end: "",
  is_active: 1,
});
const tabs = [
  { lable: "回本监控", value: 1 },
  { lable: "监控设置", value: 2 },
];
const state = reactive({
  active: 1,
  userList: [] as any[],
  abnormal: false,
  data: [
    { title: "亏损店铺数", value: 0, symbol: "家", key: "loss_count" },
    { title: "亏损店铺占比", value: 0, symbol: "%", key: "loss_rate" },
    { title: "回本店铺数", value: 0, symbol: "家", key: "recovery_count" },
    { title: "回本店铺占比", value: 0, symbol: "%", key: "recovery_rate" },
  ],
  time: undefined as any,
});
const createQuery = () => ({
  platform: "",
  name: "",
  admin_id: "",
})
const queryParams = reactive({
  ...createQuery(),
  is_recovery: "",
});
const dialog = reactive({
  show: false,
  loading: false,
  title: "添加销售成本模版",
  form: createDialogForm(),
});
const inputNumber = {
  min: 0,
  precision: 2,
  controls: false,
};

// 分页相关
const { pager, getLists, resetPage, fn } = usePaging({
  fetchFun: apiAgencyControlRevenue,
  params: queryParams,
});

const getData = async () => {
  try {
    const data = await apiAgencyControlRevenueData(queryParams);
    state.data.forEach((item: any) => {
      item.value = data[item.key];
    });
  } catch (_) {}
};

const getPageData = () => {
  if (state.active === 2) fn.value = apiAgencyControlRevenueConfig;
  else {
    fn.value = apiAgencyControlRevenue;
    getData();
  }
  getLists();
};

const userList = async () => {
  try {
    const { lists } = await adminLists({ page_size: 10000, page_no: 1 });
    state.userList = lists || [];
  } catch (_) {}
};

const onAbnormalChange = (val: CheckboxValueType) => {
  queryParams.is_recovery = val ? "0" : "";
  onSearch();
};

const onSearch = () => {
  resetPage();
  getData();
};

const onReset = () => {
  Object.assign(queryParams, createQuery());
  getPageData()
};

// 新增编辑
const onHandle = (row?: any) => {
  if (row) {
    dialog.form = { ...row };
    const keys = Object.keys(dialog.form);
    (keys as Array<keyof typeof dialog.form>).forEach((key) => {
      if (key.includes("merchant_") || key.includes("operation_")) {
        (dialog.form[key] as any) = Number(dialog.form[key]);
      }
    });
    state.time = [dialog.form.effective_start, dialog.form.effective_end];
    dialog.title = "编辑销售成本模版";
  } else {
    dialog.form = createDialogForm();
    dialog.title = "新增销售成本模版";
  }
  dialog.loading = false;
  dialog.show = true;
};

// 删除
const handleDelete = async (row: any) => {
  await feedback.confirm("确定删除该模版吗？");
  feedback.loading("删除中...");
  try {
    await apiAgencyControlRevenueConfigDelete({ id: row.id });
    getPageData();
  } catch (_) {}
  feedback.closeLoading();
};

const onSubmit = async () => {
  if (!dialog.form.template_name) return feedback.msgWarning("请输入模版名称");
  if (state.time && state.time.length !== 2)
    return feedback.msgWarning("请选择生效时间");
  const keys = Object.keys(dialog.form);
  let msg = "";
  (keys as Array<keyof typeof dialog.form>).forEach((key) => {
    if (
      (key.includes("merchant_") || key.includes("operation_")) &&
      typeof dialog.form[key] !== "number"
    ) {
      msg = "请输入成本政策金额";
    }
  });
  if (msg) return feedback.msgWarning(msg);
  dialog.loading = true;
  try {
    dialog.form.effective_start = state.time[0] + " 00:00:00";
    dialog.form.effective_end = state.time[1] + " 23:59:59";
    let reqUrl = apiAgencyControlRevenueConfigAdd;
    if ((dialog.form as any).id) reqUrl = apiAgencyControlRevenueConfigEdit;
    await reqUrl(dialog.form);
    getPageData();
    dialog.show = false;
  } catch (_) {}
  dialog.loading = false;
};

getPageData();
userList();
</script>

<template>
  <!-- tabs -->
  <el-card class="!border-none" shadow="never">
    <el-tabs v-model="state.active" @tab-change="getPageData">
      <el-tab-pane
        v-for="item in tabs"
        :key="item.value"
        :label="item.lable"
        :name="item.value"
      />
    </el-tabs>
  </el-card>

  <!-- 监控数据 -->
  <template v-if="state.active === 1">
    <!-- 筛选 -->
    <el-card class="!border-none mt-4" shadow="never">
      <el-form class="mb-[-16px]" :model="queryParams" inline>
        <el-form-item prop="platform">
          <el-radio-group v-model="queryParams.platform" @change="getPageData">
            <el-radio-button label="">全部平台</el-radio-button>
            <el-radio-button label="2">饿了么</el-radio-button>
            <el-radio-button label="4">美团</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="店铺信息" prop="name">
          <el-input placeholder="请输入名称" v-model="queryParams.name" />
        </el-form-item>
        <el-form-item label="归属运营" prop="admin_id">
          <el-select filterable clearable v-model="queryParams.admin_id">
            <el-option
              v-for="item in state.userList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
          <export-data
            class="ml-2.5"
            :fetch-fun="apiAgencyControlRevenue"
            :params="queryParams"
            :page-size="pager.size"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 信息 -->
    <el-card class="!border-none mt-4" shadow="never">
      <div class="mb-2 flex items-center acs__i">
        <div
          v-for="(item, index) in state.data"
          :key="index"
          class="w-[25%] text-center"
        >
          <div class="text-[24px]">{{ item.title }}</div>
          <div class="text-[20px]">
            {{ item.value }}<span class="text-[10px]">{{ item.symbol }}</span>
          </div>
        </div>
      </div>
      <el-checkbox
        v-model="state.abnormal"
        label="仅展示亏损店铺"
        @change="onAbnormalChange"
      />
      <!-- 表格 -->
      <el-table :data="pager.lists" v-loading="pager.loading">
        <el-table-column label="店铺信息" min-width="200px">
          <template #default="{ row }">
            <div class="flex items-center">
              <el-image
                :src="PlatformIcon[row.platform]"
                class="w-[30px] h-[30px]"
              />
              <div class="ml-2 flex-1">
                {{ row.name }}
                <div>编码：{{ row.store_id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="店铺状态" prop="status_text" />
        <el-table-column label="合作模式" prop="mode_text" />
        <el-table-column label="是否回本" prop="is_recovery" />
        <el-table-column label="店铺收益" prop="income" />
        <el-table-column label="销售成本" prop="cost" />
        <el-table-column label="运营利润">
          <template #default="{ row }">
            <div
              :style="{
                color:
                  row.profit > 0
                    ? 'red'
                    : row.profit < 0
                    ? 'green'
                    : '',
              }"
            >
              {{ row.profit }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否新店" prop="is_new" />
        <el-table-column label="回本天数" prop="recovery_day" />
        <el-table-column label="代运营天数" prop="day" width="100px" />
        <el-table-column label="归属运营" prop="admin_name" />
      </el-table>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
  </template>
  <!-- 监控设置 -->
  <el-card v-else class="!border-none mt-4" shadow="never">
    <el-button
      v-perms="['agency.control.revenue.add']"
      type="primary"
      class="mb-4"
      @click="onHandle()"
      >添加销售成本模版</el-button
    >
    <el-table :data="pager.lists" v-loading="pager.loading">
      <el-table-column label="模版名称" prop="template_name" />
      <el-table-column label="面向招商的销售成本" min-width="200px">
        <template #default="{ row }">
          <div>
            固定抽点：美团{{ row.merchant_fixed_rate_meituan }}元，饿了么{{
              row.merchant_fixed_rate_ele
            }}元。
          </div>
          <div>
            按日收费，美团{{ row.merchant_daily_fee_meituan }}元，饿了么{{
              row.merchant_daily_fee_ele
            }}元。
          </div>
          <div>前置收费，单平台{{ row.merchant_upfront_single }}元。</div>
          <div>前置收费，双平台{{ row.merchant_upfront_dual }}元。</div>
        </template>
      </el-table-column>
      <el-table-column label="面向运营的销售成本" min-width="200px">
        <template #default="{ row }">
          <div>
            固定抽点：美团{{ row.operation_fixed_rate_meituan }}元，饿了么{{
              row.operation_fixed_rate_ele
            }}元。
          </div>
          <div>
            按日收费，美团{{ row.operation_daily_fee_meituan }}元，饿了么{{
              row.operation_daily_fee_ele
            }}元。
          </div>
          <div>前置收费，单平台{{ row.operation_upfront_single }}元。</div>
          <div>前置收费，双平台{{ row.operation_upfront_dual }}元。</div>
        </template>
      </el-table-column>
      <el-table-column label="生效时间" min-width="180px">
        <template #default="{ row }">
          {{ row.effective_start }} ~ {{ row.effective_end }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="created_at" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button
            v-perms="['agency.control.revenue.edit']"
            type="primary"
            link
            @click="onHandle(row)"
            >编辑</el-button
          >
          <el-button
            v-perms="['agency.control.revenue.del']"
            type="primary"
            link
            @click="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="flex mt-4 justify-end">
      <pagination v-model="pager" @change="getLists" />
    </div>
  </el-card>

  <el-dialog v-model="dialog.show" :title="dialog.title" width="770px">
    <el-form :model="dialog.form">
      <el-form-item label="模版名称：">
        <el-input
          v-model="dialog.form.template_name"
          placeholder="请输入模版名称"
          show-word-limit
          maxlength="20"
          class="w-[300px]"
        />
      </el-form-item>
      <el-form-item label="面向招商：">
        <el-card class="!border-none" shadow="always">
          <el-form-item label="固定抽点：">
            <div class="flex">
              美团&nbsp;&nbsp;&nbsp;&nbsp;<el-input-number
                v-model="dialog.form.merchant_fixed_rate_meituan"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元，饿了么&nbsp;<el-input-number
                v-model="dialog.form.merchant_fixed_rate_ele"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元
            </div>
          </el-form-item>
          <el-form-item label="按日收费：" class="mt-4">
            <div class="flex">
              美团&nbsp;&nbsp;&nbsp;&nbsp;<el-input-number
                v-model="dialog.form.merchant_daily_fee_meituan"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元，饿了么&nbsp;<el-input-number
                v-model="dialog.form.merchant_daily_fee_ele"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元
            </div>
          </el-form-item>
          <el-form-item label="前置收费：" class="mt-4">
            单平台&nbsp;<el-input-number
              v-bind="inputNumber"
              v-model="dialog.form.merchant_upfront_single"
              :disabled="!!(dialog.form as any).id"
            />&nbsp;元
          </el-form-item>
          <el-form-item label="前置收费：" class="mt-4">
            双平台&nbsp;<el-input-number
              v-bind="inputNumber"
              v-model="dialog.form.merchant_upfront_dual"
              :disabled="!!(dialog.form as any).id"
            />&nbsp;元
          </el-form-item>
        </el-card>
      </el-form-item>
      <el-form-item label="面向运营：">
        <el-card class="!border-none" shadow="always">
          <el-form-item label="固定抽点：">
            <div class="flex">
              美团&nbsp;&nbsp;&nbsp;&nbsp;<el-input-number
                v-model="dialog.form.operation_fixed_rate_meituan"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元，饿了么&nbsp;<el-input-number
                v-model="dialog.form.operation_fixed_rate_ele"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元
            </div>
          </el-form-item>
          <el-form-item label="按日收费：" class="mt-4">
            <div class="flex">
              美团&nbsp;&nbsp;&nbsp;&nbsp;<el-input-number
                v-model="dialog.form.operation_daily_fee_meituan"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元，饿了么&nbsp;<el-input-number
                v-model="dialog.form.operation_daily_fee_ele"
                v-bind="inputNumber"
                :disabled="!!(dialog.form as any).id"
              />&nbsp;元
            </div>
          </el-form-item>
          <el-form-item label="前置收费：" class="mt-4">
            单平台&nbsp;<el-input-number
              v-bind="inputNumber"
              v-model="dialog.form.operation_upfront_single"
              :disabled="!!(dialog.form as any).id"
            />&nbsp;元
          </el-form-item>
          <el-form-item label="前置收费：" class="mt-4">
            双平台&nbsp;<el-input-number
              v-bind="inputNumber"
              v-model="dialog.form.operation_upfront_dual"
              :disabled="!!(dialog.form as any).id"
            />&nbsp;元
          </el-form-item>
        </el-card>
      </el-form-item>
      <el-form-item label="生效时间：" class="w-[440px]">
        <el-date-picker
          v-model="state.time"
          type="daterange"
          value-format="YYYY-MM-DD"
          :disabled="!!(dialog.form as any).id"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button :loading="dialog.loading" type="primary" @click="onSubmit"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin: 0;
}
.acs {
  &__i {
    background: #f8f8f8;
    padding: 20px;
    border-radius: 4px;
  }
}
</style>
