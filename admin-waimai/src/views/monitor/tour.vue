<script setup lang="ts">
import { reactive } from "vue";
import { usePaging } from "@/hooks/usePaging";
import { adminLists } from "@/api/perms/admin";
import {
  apiAgencyControlTourData,
  apiAgencyControlTourShop,
  apiAgencyControlTourTimp,
  apiAgencyControlTourTimpCreate,
  apiAgencyControlTourTimpUpdate,
  apiAgencyControlTourTimpDelete,
  apiAgencyControlTourTimpDeatil,
  apiAgencyControlTourTimpRule,
} from "@/api/agency";
import { timeFormat } from "@/utils/util";
import feedback from "@/utils/feedback";

const tabs = [
  { lable: "巡店监控", value: 1 },
  { lable: "监控设置", value: 2 },
];
const createQuery = () => ({
  platform: "",
  admin_id: "",
  name: "",
});
const state = reactive({
  active: 1,
  timeActive: 1,
  yesterday: timeFormat(Date.now() - 86400000, "yyyy-mm-dd"),
  abnormal: false,
  userList: [] as any[],
  data: [
    { title: "异常店铺数", value: 0, symbol: "家", key: "abnormal_count" },
    { title: "异常店铺占比", value: 0, symbol: "%", key: "abnormal_rate" },
    { title: "正常店铺数", value: 0, symbol: "家", key: "normal_count" },
    { title: "正常店铺占比", value: 0, symbol: "%", key: "normal_rate" },
  ],
  edit: {
    show: false,
    id: 0,
    loading: false,
    list: [] as any[],
    btnLoading: false,
    form: {} as Record<string, any>,
    showRule: false,
  },
});
const queryParams = reactive({
  ...createQuery(),
  date_time: state.yesterday,
  abnormal: "",
  order: "",
});

// 分页相关
const { pager, getLists, resetPage, fn } = usePaging({
  fetchFun: apiAgencyControlTourShop,
  params: queryParams,
});

const userList = async () => {
  try {
    const { lists } = await adminLists({ page_no: 1, page_size: 10000 });
    state.userList = lists || [];
  } catch (_) {}
};

const getData = async () => {
  try {
    const data = await apiAgencyControlTourData(queryParams);
    state.data.forEach((item: any) => {
      item.value = data[item.key];
    });
  } catch (_) {}
};

const getPageData = () => {
  if (state.active === 2) fn.value = apiAgencyControlTourTimp;
  else {
    fn.value = apiAgencyControlTourShop;
    getData();
  }
  getLists();
};

const onAbnormalChange = (val: boolean) => {
  queryParams.abnormal = val ? "1" : "";
  getPageData();
};

const onChangeTime = (val: number) => {
  if (val === 1) {
    queryParams.date_time = state.yesterday;
    getPageData();
  }
};

const onReset = () => {
  Object.assign(queryParams, createQuery());
  getPageData();
};

// 创建模版
const createTimp = async (data: any) => {
  try {
    await apiAgencyControlTourTimpCreate(data);
    feedback.msgSuccess("创建成功");
    getPageData();
  } catch (_) {}
};

const updateTimp = async (data: any) => {
  try {
    await apiAgencyControlTourTimpUpdate(data);
    feedback.msgSuccess("修改成功");
    getPageData();
  } catch (_) {}
};

// 添加模版
const handleAdd = (row?: any) => {
  feedback.prompt("", "添加巡店模版", {
    inputValue: row ? row.title : "",
    inputValidator: (value: string) => {
      if (!value) return "请输入巡店模版";
      else if (value.length > 20) return "巡店模版名称最多20个字符";
      return true;
    },
    beforeClose: async (action: string, instance: any, done: any) => {
      if (action === "confirm") {
        instance.confirmButtonLoading = true;
        try {
          if (row) await updateTimp({ ...row, title: instance.inputValue });
          else await createTimp({ title: instance.inputValue });
        } catch (_) {}
        instance.confirmButtonLoading = false;
        done();
      } else done();
    },
  });
};

// 删除巡店模版
const deleteItem = async (id: number) => {
  await feedback.confirm("您确定要删除吗？");
  try {
    feedback.loading("删除中...");
    await apiAgencyControlTourTimpDelete({ id });
    feedback.msgSuccess("删除成功");
    getPageData();
  } catch (_) {}
  feedback.closeLoading();
};

const getRuleDeatil = async () => {
  try {
    const { lists } = await apiAgencyControlTourTimpDeatil({
      temp_id: state.edit.id,
    });
    state.edit.list = (lists || []).map((item: any) => ({
      ...item,
      value: item.type === 4 ? item.threshold : `${item.threshold}%`,
      rule: item.type === 4 ? "当日数值大于" : `连续${item.days}天下降超过`,
    }));
  } catch (_) {}
};

const onEdit = async (row: any) => {
  state.edit = {
    show: true,
    id: row.id,
    list: [],
    form: {},
    loading: true,
    btnLoading: false,
    showRule: false,
  };
  await getRuleDeatil();
  state.edit.loading = false;
};

const handleEdit = (row: any) => {
  state.edit.form = { ...row };
  state.edit.showRule = true;
  state.edit.btnLoading = false;
};

const onSubmit = async (item?: any) => {
  state.edit.btnLoading = true;
  if (item) item.loading = true;

  try {
    await apiAgencyControlTourTimpRule(item || state.edit.form);
    if (!item) {
      feedback.msgSuccess("修改成功");
      state.edit.showRule = false;
    }
    getRuleDeatil();
    getPageData();
  } catch (_) {
    console.log(_);
  }
  if (item) item.loading = false;
  state.edit.btnLoading = false;
};

userList();
getPageData();
</script>

<template>
  <!-- tabs -->
  <el-card class="!border-none" shadow="never">
    <el-tabs v-model="state.active" @tab-change="getPageData">
      <el-tab-pane
        v-for="item in tabs"
        :key="item.value"
        :label="item.lable"
        :name="item.value"
      />
    </el-tabs>
  </el-card>

  <!-- 店铺分监控 -->
  <template v-if="state.active === 1">
    <!-- 筛选 -->
    <el-card class="!border-none mt-4" shadow="never">
      <el-form class="mb-[-16px]" :model="queryParams" inline>
        <el-form-item prop="platform">
          <el-radio-group v-model="queryParams.platform" @change="getPageData">
            <el-radio-button label="">全部平台</el-radio-button>
            <el-radio-button label="2">饿了么</el-radio-button>
            <el-radio-button label="4">美团</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="店铺信息" prop="name">
          <el-input placeholder="请输入名称" v-model="queryParams.name" />
        </el-form-item>
        <el-form-item label="归属运营" prop="admin_id">
          <el-select filterable clearable v-model="queryParams.admin_id">
            <el-option
              v-for="item in state.userList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 信息 -->
    <el-card class="!border-none mt-4" shadow="never">
      <div class="flex items-center">
        <el-radio-group v-model="state.timeActive" @change="onChangeTime">
          <el-radio-button :label="1">昨日</el-radio-button>
          <el-radio-button :label="2">自定义</el-radio-button>
        </el-radio-group>
        <div class="ml-5">
          <div v-if="state.timeActive === 1">{{ state.yesterday }}</div>
          <el-date-picker
            v-else
            :clearable="false"
            type="date"
            value-format="YYYY-MM-DD"
            v-model="queryParams.date_time"
            @change="getPageData"
          />
        </div>
      </div>
      <div class="mt-3 mb-2 flex items-center acs__i">
        <div
          v-for="(item, index) in state.data"
          :key="index"
          class="w-[25%] text-center"
        >
          <div class="text-[24px]">{{ item.title }}</div>
          <div class="text-[20px]">
            {{ item.value }}<span class="text-[10px]">{{ item.symbol }}</span>
          </div>
        </div>
      </div>
      <el-checkbox
        v-model="state.abnormal"
        label="仅展示异常店铺"
        @change="onAbnormalChange"
      />
      <div v-loading="pager.loading">
        <el-table :data="pager.lists">
          <el-table-column label="店铺信息">
            <template #default="{ row }">
              <div class="flex items-center">
                <el-image
                  :src="
                    row.platform === 2
                      ? 'https://img.dac6.cn/users/0/0/baaf0979e48ecef79ee918308f16e178.png'
                      : 'https://img.dac6.cn/users/0/0/40938dd00221c4ea24db50e5330fb084.png'
                  "
                  style="width: 30px; height: 30px"
                />
                <div class="ml-2 flex-1">
                  {{ row.name }}
                  <div>编码：{{ row.store_id }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="异常指标数量"
            sortable="custom"
            prop="anomaly_index_count"
          />
          <el-table-column label="异常指标内容" width="200px">
            <template #default="{ row }">
              <template
                v-if="row.anomaly_index_info && row.anomaly_index_info.length"
              >
                <div
                  v-for="(item, index) in row.anomaly_index_info"
                  :key="index"
                >
                  {{ item }}
                </div>
              </template>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="代运营天数" prop="day" />
          <el-table-column label="归属运营" prop="admin_name" />
          <el-table-column label="签约时间">
            <template #default="{ row }">{{
              timeFormat(row.start_time, "yyyy/mm/dd")
            }}</template>
          </el-table-column>
        </el-table>
        <div class="flex mt-4 justify-end">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </div>
    </el-card>
  </template>

  <!-- 监控设置 -->
  <template v-else>
    <el-card class="!border-none mt-4" shadow="never">
      <el-button
        v-perms="['agency.control.tour.temp.create']"
        type="primary"
        class="mb-3"
        @click="handleAdd()"
        >添加监控模版</el-button
      >
      <el-table :data="pager.lists" v-loading="pager.loading">
        <el-table-column label="模版名称">
          <template #default="{ row }">
            <div class="flex items-center cursor-pointer">
              {{ row.title
              }}<el-icon
                v-perms="['agency.control.tour.temp.update']"
                class="ml-2"
                @click="handleAdd(row)"
                ><Edit
              /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="异常指标项目" prop="rules" />
        <el-table-column label="适用店铺数量" prop="shop_count" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button
              v-perms="['agency.control.tour.temp.rule']"
              type="primary"
              link
              @click="onEdit(row)"
              >编辑</el-button
            >
            <el-button
              v-perms="['agency.control.tour.temp.delete']"
              type="primary"
              link
              @click="deleteItem(row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
  </template>

  <!-- 编辑规则列表 -->
  <el-drawer v-model="state.edit.show" title="编辑巡店模版" size="800px">
    <el-table v-loading="state.edit.loading" :data="state.edit.list">
      <el-table-column label="异常指标项目" prop="title" />
      <el-table-column label="异常规则" prop="rule" />
      <el-table-column label="异常阀值" prop="value" />
      <el-table-column label="启用状态">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            :loading="row.loading"
            @change="onSubmit(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="state.edit.show = false">取消</el-button>
      <el-button type="primary" @click="state.edit.show = false"
        >确定</el-button
      >
    </template>
  </el-drawer>
  <!-- 编辑规则 -->
  <el-drawer v-model="state.edit.showRule" title="编辑异常项目" size="800px">
    <div>
      当<span style="color: red">【{{ state.edit.form.title }}】</span
      >满足下面条件时，触发预警
    </div>
    <el-card class="mt-4" shadow="never">
      <el-form :model="state.edit.form">
        <template v-if="state.edit.form.type === 4">
          <el-form-item label="中差评对比阀值：">
            当日数值，大于<el-input-number
              v-model="state.edit.form.threshold"
              class="ml-2"
              :min="0"
              :precision="0"
              :controls="false"
            />
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="取值方式：">
            连续N天
            <el-select
              v-model="state.edit.form.days"
              placeholder="请选择"
              class="ml-2"
            >
              <el-option label="3天" :value="3" />
              <el-option label="7天" :value="7" />
            </el-select>
          </el-form-item>
          <el-form-item label="对比阀值：">
            下降超过，对比指标：<el-input-number
              v-model="state.edit.form.threshold"
              :min="0"
              :max="99"
              class="ml-2"
              :precision="2"
              :controls="false"
            />%
          </el-form-item>
        </template>
      </el-form>
    </el-card>
    <template #footer>
      <el-button @click="state.edit.showRule = false">取消</el-button>
      <el-button
        :loading="state.edit.btnLoading"
        type="primary"
        @click="onSubmit()"
        >确定</el-button
      >
    </template>
  </el-drawer>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin: 0;
}
.acs {
  &__i {
    background: #f8f8f8;
    padding: 20px;
    border-radius: 4px;
  }
}
</style>
