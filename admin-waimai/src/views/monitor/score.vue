<script setup lang="ts">
import { reactive } from "vue";
import { usePaging } from "@/hooks/usePaging";
import { adminLists } from "@/api/perms/admin";
import {
  apiAgencyControlShop,
  apiAgencyControlShopData,
  apiAgencyControlShopConfig,
  apiAgencyControlShopConfigSave,
} from "@/api/agency";
import { timeFormat } from "@/utils/util";
import feedback from "@/utils/feedback";

const createQuery = () => ({
  platform: "",
  admin_id: "",
  name: "",
});
const state = reactive({
  active: 1,
  timeActive: 1,
  yesterday: timeFormat(Date.now() - 86400000, "yyyy-mm-dd"),
  abnormal: false,
  userList: [] as any[],
  data: [
    { title: "异常店铺数", value: 0, symbol: "家", key: "abnormal_count" },
    { title: "异常店铺占比", value: 0, symbol: "%", key: "abnormal_rate" },
    { title: "正常店铺数", value: 0, symbol: "家", key: "normal_count" },
    { title: "正常店铺占比", value: 0, symbol: "%", key: "normal_rate" },
  ],

  configLoading: false,
  config: {} as Record<string, any>,
});
const queryParams = reactive({
  ...createQuery(),
  date_time: state.yesterday,
  abnormal: "",
  order: "",
});

// 分页相关
const { pager, getLists, resetPage } = usePaging({
  fetchFun: apiAgencyControlShop,
  params: queryParams,
});

const userList = async () => {
  try {
    const { lists } = await adminLists({ page_size: 10000, page_no: 1 });
    state.userList = lists || [];
  } catch (_) {}
};

const getData = async () => {
  try {
    const data = await apiAgencyControlShopData(queryParams);
    state.data.forEach((item: any) => {
      item.value = data[item.key];
    });
  } catch (_) {}
};

const getPageData = () => {
  getData();
  getLists();
};

const onAbnormalChange = (val: boolean) => {
  queryParams.abnormal = val ? "1" : "";
  getPageData();
};

const onChangeTime = (val: number) => {
  if (val === 1) {
    queryParams.date_time = state.yesterday;
    getPageData();
  }
};

const sortChange = (data: any) => {
  if (data.order === "ascending") {
    queryParams.order = `${data.prop} asc`;
  } else if (data.order === "descending") {
    queryParams.order = `${data.prop} desc`;
  } else {
    queryParams.order = "";
  }
  getPageData();
};

const onReset = () => {
  Object.assign(queryParams, createQuery());
  getPageData();
};

const getConfig = async () => {
  try {
    const data = await apiAgencyControlShopConfig();
    Object.keys(data).forEach((key: string) => {
      state.config[key] = Number(data[key]);
    });
  } catch (_) {}
};

const onSubmit = async () => {
  let msg = "";
  Object.keys(state.config).forEach((key: string) => {
    if (typeof state.config[key] !== "number") msg = "请填写正确的店铺分";
  });
  if (msg) return feedback.msgWarning(msg);
  state.configLoading = true;
  try {
    await apiAgencyControlShopConfigSave(state.config);
    feedback.msgSuccess("保存成功");
    getConfig();
  } catch (_) {}
  state.configLoading = false;
};

userList();
getPageData();
getConfig();
</script>

<template>
  <!-- tabs -->
  <el-card class="!border-none" shadow="never">
    <el-tabs v-model="state.active">
      <el-tab-pane label="店铺分监控" :name="1" />
      <el-tab-pane label="监控设置" :name="2" />
    </el-tabs>
  </el-card>

  <!-- 店铺分监控 -->
  <template v-if="state.active === 1">
    <!-- 筛选 -->
    <el-card class="!border-none mt-4" shadow="never">
      <el-form class="mb-[-16px]" :model="queryParams" inline>
        <el-form-item prop="platform">
          <el-radio-group v-model="queryParams.platform" @change="getPageData">
            <el-radio-button label="">全部平台</el-radio-button>
            <el-radio-button label="2">饿了么</el-radio-button>
            <el-radio-button label="4">美团</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="店铺信息" prop="name">
          <el-input placeholder="请输入名称" v-model="queryParams.name" />
        </el-form-item>
        <el-form-item label="归属运营" prop="admin_id">
          <el-select filterable clearable v-model="queryParams.admin_id">
            <el-option
              v-for="item in state.userList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 信息 -->
    <el-card class="!border-none mt-4" shadow="never">
      <div class="flex items-center">
        <el-radio-group v-model="state.timeActive" @change="onChangeTime">
          <el-radio-button :label="1">昨日</el-radio-button>
          <el-radio-button :label="2">自定义</el-radio-button>
        </el-radio-group>
        <div class="ml-5">
          <div v-if="state.timeActive === 1">{{ state.yesterday }}</div>
          <el-date-picker
            v-else
            type="date"
            :clearable="false"
            value-format="YYYY-MM-DD"
            v-model="queryParams.date_time"
            @change="getPageData"
          />
        </div>
      </div>
      <div class="mt-3 mb-2 flex items-center acs__i">
        <div
          v-for="(item, index) in state.data"
          :key="index"
          class="w-[25%] text-center"
        >
          <div class="text-[24px]">{{ item.title }}</div>
          <div class="text-[20px]">
            {{ item.value }}<span class="text-[10px]">{{ item.symbol }}</span>
          </div>
        </div>
      </div>
      <el-checkbox
        v-model="state.abnormal"
        label="仅展示异常店铺"
        @change="onAbnormalChange"
      />

      <div v-loading="pager.loading">
        <el-table :data="pager.lists" @sort-change="sortChange">
          <el-table-column label="店铺信息">
            <template #default="{ row }">
              <div class="flex items-center">
                <el-image
                  :src="
                    row.platform === 2
                      ? 'https://img.dac6.cn/users/0/0/baaf0979e48ecef79ee918308f16e178.png'
                      : 'https://img.dac6.cn/users/0/0/40938dd00221c4ea24db50e5330fb084.png'
                  "
                  style="width: 30px; height: 30px"
                />
                <div class="ml-2 flex-1">
                  {{ row.name }}
                  <div>ID：{{ row.store_id }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="店铺分"
            sortable="custom"
            prop="store_score"
          />
          <el-table-column label="店铺分变化">
            <template #default="{ row }">
              <div
                class="flex items-center"
                :style="{
                  color:
                    row.comparison_store_score > 0
                      ? 'red'
                      : row.comparison_store_score < 0
                      ? 'green'
                      : '',
                }"
              >
                {{ Math.abs(row.comparison_store_score) }}
                <el-icon v-if="row.comparison_store_score > 0"><Top /></el-icon>
                <el-icon v-else-if="row.comparison_store_score < 0"
                  ><Bottom
                /></el-icon>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="代运营天数" prop="day" />
          <el-table-column label="归属运营" prop="admin_name" />
          <el-table-column label="签约时间">
            <template #default="{ row }">{{
              timeFormat(row.start_time, "yyyy/mm/dd")
            }}</template>
          </el-table-column>
        </el-table>
        <div class="flex mt-4 justify-end">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </div>
    </el-card>
  </template>

  <!-- 监控设置 -->
  <template v-else>
    <el-card class="!border-none mt-4" shadow="never">
      <el-card shadow="never" header="美团店铺分监控">
        <div class="flex items-center">
          店铺分不可低于<el-input-number
            v-model="state.config.mt"
            :min="0"
            :precision="2"
            :controls="false"
            class="mx-1"
          />分
        </div>
      </el-card>
      <el-card class="mt-4" shadow="never" header="饿了么店铺分监控">
        店铺分不可低于<el-input-number
          v-model="state.config.elm"
          :min="0"
          :precision="2"
          :controls="false"
          class="mx-1"
        />分
      </el-card>
      <el-button
        v-perms="['agency.control.shop.save']"
        type="primary"
        class="mt-4"
        :loading="state.configLoading"
        @click="onSubmit"
        >保存</el-button
      >
    </el-card>
  </template>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin: 0;
}
.acs {
  &__i {
    background: #f8f8f8;
    padding: 20px;
    border-radius: 4px;
  }
}
</style>
