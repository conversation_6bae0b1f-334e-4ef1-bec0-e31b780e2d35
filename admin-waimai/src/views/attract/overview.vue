<script setup lang="ts">
import { reactive, onMounted, nextTick } from "vue";
import VCharts from "vue-echarts";
import { usePaging } from "@/hooks/usePaging";
import {
  reqMerchantOverview,
  reqMerchantStatis,
} from "@/api/merchant/overview";
import { timeFormat } from "@/utils/util";
import OverviewSearch from "./../components/overview/search.vue";
import OverviewData from "../components/overview/data.vue";
import PageList from "./components/page-list.vue";

const dimenList = [
  { name: "按店铺", type: "clue", value: 1 },
  // { name: "按收益", type: "earn", value: 2 },
];
const state = reactive({
  showChart: true,
  chartOption: {
    grid: {
      left: "52px",
      right: "52px",
      bottom: "20px",
    },
    xAxis: {
      nameLocation: "center",
      type: "category",
      data: [],
    },
    yAxis: { type: "value" },
    legend: { data: [] },
    tooltip: { trigger: "axis" },
    series: [],
  },
  dimen: "clue",
  params: {
    type: 7,
    sub_type: 1,
    start: timeFormat(new Date().getTime()),
    end: timeFormat(new Date().getTime()),
  },
  dataList: [],
  dataType: "",
  list1: [],
  list: [],
});

const queryParams = reactive({
  type: state.params.type,
  st: state.params.start,
  et: state.params.end,
  sub_type: state.params.sub_type,
  mode: 1,
});

const tabPane = computed(() => {
  if (queryParams.sub_type === 1) {
    return [
      { label: "招商账号分析", value: 1 },
      { label: "运营账号分析", value: 2 },
      { label: "店铺收益分析", value: 3 },
    ];
  } else {
    return [
      { label: "平台运营分析", value: 1 },
      { label: "运营账号分析", value: 2 },
      { label: "合作模式分析", value: 3 },
    ];
  }
});

const { pager, getLists } = usePaging({
  fetchFun: reqMerchantStatis,
  params: queryParams,
  size: 100,
});

const getData = async () => {
  try {
    const data = await reqMerchantOverview(state.params);
    state.dataList = (data.statis || []).map((item: any) => ({
      ...item,
      type: item.rate >= 0 ? "plus" : "minus",
    }));
    if (data.statis && data.statis.length) {
      state.dataType = data.statis[0].key;
    } else {
      state.dataType = "";
    }
    state.list1 = data.this;
    formatChart();
  } catch (_) {}
};

// 格式话折线图
const formatChart = () => {
  state.showChart = false;
  const data: any = [];
  let series = [{ type: "line", data: [] }];
  state.chartOption.legend.data = [];
  series.splice(1, 1);
  state.list1.forEach((item: any) => {
    data.push(item.time);
    (series[0].data as any).push(item[state.dataType || "total"]);
  });
  (state.chartOption as any).series = series;
  state.chartOption.xAxis.data = data;
  nextTick(() => (state.showChart = true));
};

const onChange = (item: any) => {
  if (item.dimen) {
    queryParams.mode = 1;
    state.dimen = item.dimen.type;
    state.params.sub_type = item.dimen.value;
  }
  if (item.params) {
    state.params = {
      ...state.params,
      ...item.params,
    };
  }
  Object.assign(queryParams, {
    type: state.params.type,
    sub_type: state.params.sub_type,
    st: state.params.start,
    et: state.params.end,
  });
  init();
};

const onTypeChange = (type: string) => {
  state.dataType = type;
  formatChart();
};

const onModeChange = () => {
  Object.assign(pager, { page: 1, lists: [] });
  getLists();
};

const init = () => {
  getData();
  getLists();
};

// onMounted(() => init());
</script>

<template>
  <OverviewSearch
    :dimen-list="dimenList"
    :dimen="state.dimen"
    time-type="week"
    @change="onChange"
  />
  <el-card class="!border-none mt-4" shadow="never">
    <OverviewData
      :list="state.dataList"
      :data-type="state.dataType"
      @change="onTypeChange"
    />

    <VCharts
      v-if="state.showChart"
      style="height: 293px; width: 100%; margin-top: 33px"
      :autoresize="true"
      :option="state.chartOption"
    />
  </el-card>

  <el-card class="!border-none mt-4" shadow="never">
    <el-tabs v-model="queryParams.mode" @tab-change="onModeChange">
      <el-tab-pane
        v-for="(item, i) in tabPane"
        :key="i"
        :label="item.label"
        :name="item.value"
      />
    </el-tabs>
    <PageList
      :type="state.dimen"
      :mode="queryParams.mode"
      :pager="pager"
      :params="queryParams"
      @getlist="getLists"
    />
  </el-card>
</template>
