<script setup lang="ts">
import feedback from "@/utils/feedback";
import { usePaging } from "@/hooks/usePaging";
import { getUserList } from "@/api/consumer";
import { adminLists } from "@/api/perms/admin";
import { apiMerchantCateLists } from "@/api/merchant/brand";
import { apiAgencyControlTourTimp } from "@/api/agency";
import {
  apiBehalfList,
  apiBehalfCounts,
  apiBehalfAdd,
  apiBehalfEdit,
  apiBehalfUnbind,
  apiBehalfBind,
} from "@/api/merchant/behalf";
import { ShopCityOptions } from "@/enums/appEnums";

const termination = [
  "老店翻新",
  "店铺长期停业",
  "店铺已经下线",
  "店铺没有收益",
  "店铺已经倒闭",
  "老板已经转行",
  "老板不配合，要求解约",
  "数据有提升，老板无理由解约",
  "数据无提升，老板已要求解约",
];
const mode = [
  { name: "固定抽点", value: 1 },
  { name: "按日收费", value: 2 },
  { name: "前置收费+固定抽点", value: 3 },
  { name: "前置收费+按日收费", value: 4 },
];
const tabOptions = ref<Record<string, any>[]>([
  { label: "合作中", value: 2, count: "" },
  { label: "未签约", value: 1, count: "" },
  { label: "已解约", value: 3, count: "" },
  { label: "已到期", value: 4, count: "" },
]);
const platform = [
  { name: "单平台", value: 1 },
  { name: "双平台", value: 2 },
];
const platformName = [
  { name: "饿了么", value: 2 },
  { name: "美团", value: 4 },
];
const eat = [
  { name: "有", value: 1 },
  { name: "无", value: 0 },
];
const noYes = [
  { name: "是", value: 1 },
  { name: "否", value: 0 },
];
const tabs = [
  { name: "基本信息", value: "data" },
  { name: "代运营信息", value: "behalf" },
];
const issues = [
  { name: "活动配置问题", value: 1 },
  { name: "门店推广问题", value: 2 },
  { name: "门店线下问题", value: 3 },
  { name: "商家配合问题", value: 4 },
  { name: "菜单产品问题", value: 5 },
  { name: "大额订单问题", value: 6 },
  { name: "门店曝光率低", value: 7 },
  { name: "门店进店率低", value: 8 },
  { name: "下单转化率低", value: 9 },
  { name: "门店复购率低", value: 10 },
  { name: "门店已经歇业", value: 11 },
];
const level = [
  { name: "A", value: "A" },
  { name: "B", value: "B" },
  { name: "C", value: "C" },
  { name: "D", value: "D" },
];

const queryParams = reactive({
  platform: "",
  keyword: "",
  status: 2,
  category_id: undefined,
  is_new: undefined,
  is_brand: undefined,
  user_id: undefined,
  operate_id: undefined,
  city: "",
  time_type: 1,
  start_time: "",
  end_time: "",
  level_tier: "",
  is_scaling: undefined,
  has_promotion: undefined,
  has_bwc: undefined,
  main_issues: "",
  user_type: undefined,
  mode: undefined,
});
const state = reactive({
  formTime: null,
  userList: [] as { id: number; name: string }[],
  behalf: [] as any[],
  categoryList: [] as any[],
  platform_type: 1,
  mtUser: undefined,
  elmUser: undefined,

  item: {} as any,
  showClaim: false,
  showDetail: false,
  showEdit: false,
  editTitle: "新增",

  activeName: "data",
  mainIssues: [],
  operation: [] as any[],
  seleOpt: [],
  showContract: false,
  timpList: [] as any[],
});

const operation = computed(() => {
  return [
    { name: "招商", id: 1, children: state.userList },
    { name: "运营", id: 2, children: state.behalf },
  ];
});

const fee = computed(() => {
  let num = 0;
  if (state.item.mode === 2) {
    if (state.item.platform_info.length > 1) num = 40;
    else {
      if (state.item.platform_info[0].platform === 2) num = 15;
      else if (state.item.platform_info[0].platform === 4) num = 25;
    }
  } else if ([3, 4].includes(state.item.mode)) {
    if (state.item.platform_info.length === 1) num = 299;
    else num = 499;
  }
  return num;
});
const fee1 = computed(() => {
  let num = 0;
  if (state.item.mode === 4) {
    if (state.item.platform_info.length > 1) num = 40;
    else {
      if (state.item.platform_info[0].platform === 2) num = 15;
      else if (state.item.platform_info[0].platform === 4) num = 25;
    }
  }
  return num;
});

// 巡店模版
const getTourTimp = async () => {
  try {
    const { lists } = await apiAgencyControlTourTimp({
      page_no: 1,
      page_size: 1000,
    });
    state.timpList = lists || [];
  } catch (_) {}
};

// 店铺分类
const getCategoryList = async () => {
  try {
    const { lists } = await apiMerchantCateLists({
      page_no: 1,
      page_size: 2000,
      type: 2,
    });
    state.categoryList = lists || [];
  } catch (_) {}
};
// 招商
const getUserData = async (role_id: number) => {
  try {
    const { lists } = await getUserList({
      page_size: 10000,
      is_disable: 0,
      role_id,
      city: "长沙",
    });
    state.userList = (lists || []).map((item: any) => ({
      id: item.id,
      name: item.real_name,
    }));
  } catch (_) {}
};
// 代运营
const getPerUser = async () => {
  try {
    const { lists } = await adminLists({
      page_no: 1,
      page_size: 10000,
      role_id: 21,
    });
    state.behalf = lists || [];
  } catch (_) {}
};

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: apiBehalfList,
  params: queryParams,
});

watch(
  pager,
  (val: any) => {
    const i = tabOptions.value.findIndex((a) => a.value === queryParams.status);
    if (i === -1 || val.loading || tabOptions.value[i].count === val.count)
      return;
    tabOptions.value[i].count = val.count;
  },
  { immediate: true }
);

const getCounts = async () => {
  try {
    const { expire_count, not_sign_count, termination_count, sign_count } =
      await apiBehalfCounts(queryParams);
    const arr = [sign_count, not_sign_count, termination_count, expire_count];
    for (let i = 0; i < arr.length; i++) {
      if (tabOptions.value[i].value !== queryParams.status) {
        tabOptions.value[i].count = arr[i];
      }
    }
  } catch (_) {}
};
// 查询
const onSearch = () => {
  queryParams.main_issues = state.mainIssues.join();
  resetPage();
  getCounts();
};
// 重置
const onReset = async () => {
  state.formTime = null;
  state.seleOpt = [];
  await nextTick();
  state.mainIssues = [];
  queryParams.main_issues = "";
  queryParams.user_type = undefined;
  queryParams.user_id = undefined;
  resetParams();
  getCounts();
};
// tab切换
const handleTabChange = async () => {
  await nextTick();
  resetPage();
};
// 解约
const openContract = (item: any) => {
  state.item = { ...item };
  state.showContract = true;
};
const onContract = async () => {
  if (!state.item.reason) return feedback.msgWarning("请选择解约原因");
  feedback.loading("解约中");
  try {
    await apiBehalfUnbind({ id: state.item.id, reason: state.item.reason });
    feedback.msgSuccess("解约成功");
    state.showContract = false;
    onSearch();
  } catch (_) {}
  feedback.closeLoading();
};
// 认领
const onClaim = async () => {
  if (!state.mtUser && !state.elmUser)
    return feedback.msgWarning("请选择分配运营");
  feedback.loading("认领中");
  try {
    const query = { id: state.item?.id, platform_info: [] as any[] };
    if (state.mtUser)
      query.platform_info.push({ user_id: state.mtUser, platform: 4 });
    if (state.elmUser)
      query.platform_info.push({ user_id: state.elmUser, platform: 2 });
    await apiBehalfBind(query);
    onSearch();
    feedback.msgSuccess("认领成功");
    state.showClaim = false;
  } catch (_) {}
  feedback.closeLoading();
};
const openClaim = (item: any) => {
  if (item.platform_text.includes("饿了么")) item.isElm = true;
  if (item.platform_text.includes("美团")) item.isMt = true;
  if (!item.isElm && !item.isMt) return feedback.msgWarning("没有签约平台");
  state.mtUser = undefined;
  state.elmUser = undefined;
  state.item = item;
  state.showClaim = true;
};
// 关闭认领弹框
const closeClaim = () => {
  state.item = null;
  state.showClaim = false;
};
// 查看详情
const openDetail = (item: any) => {
  state.item = {
    ...item,
    main_issues: item.main_issues || [],
  };
  state.showDetail = true;
};

const onChange = (val: 1 | 2) => {
  if (val === 1) {
    state.item.platform_info = [{ platform: "", operate_id: "" }];
  } else {
    state.item.platform_info = [
      { platform: 2, operate_id: "" },
      { platform: 4, operate_id: "" },
    ];
  }
};

// 新增编辑
const onEdit = (item: any) => {
  if (typeof item === "number") {
    state.editTitle = "新增";
    state.operation = [];
    state.platform_type = 1;
    state.item = {
      platform_info: [{ platform: "", operate_id: "" }],
      mode: 1,
      commission_rate: 5,
    };
  } else {
    let platform_info = [
      {
        platform: item.platform_text === "美团" ? 4 : 2,
        operate_id: item.operate_id,
      },
    ];
    state.operation = [[item.user_type, item.user_id]];
    item = JSON.parse(JSON.stringify(item));
    state.editTitle = "编辑";
    state.platform_type = 1;
    const obj: Record<string, number> = { 是: 1, 否: 0 };
    const obj1: Record<string, number> = { 有: 1, 无: 0 };
    item.start_time = item.start_time.split(" ")[0];
    item.end_time = item.end_time.split(" ")[0];
    item.is_brand = obj[item.is_brand];
    item.is_charge = obj1[item.is_charge];
    item.is_new = obj[item.is_new];

    if (item.has_bwc === -1) delete item.has_bwc;
    if (item.has_promotion === -1) delete item.has_promotion;
    if (item.is_scaling === -1) delete item.is_scaling;
    state.item = { ...item, platform_info };
  }
  state.showEdit = true;
};

const onModeChange = (val: number) => {
  if ([2, 4].includes(val)) state.item.commission_rate = 0;
  else state.item.commission_rate = 5;
  if ([3, 4].includes(val)) {
    if (state.item.platform_info.length > 1) {
      state.item.fee = 499;
    } else {
      state.item.fee = 299;
    }
  } else {
    state.item.fee = 0;
  }
};

const onSubmit = async () => {
  if (state.item.fee < 0) return feedback.msgWarning("前置收费不能小于0");
  const platform_info: any[] = [];
  let msg = "";
  state.item.platform_info.forEach((a: any) => {
    if (!a.operate_id) msg = "请选择归属运营";
    else if (a.platform && a.operate_id) platform_info.push(a);
  });
  const lng = state.item.platform_info.length;
  if (
    state.operation.length !== lng ||
    state.operation.some((a: any) => a.length !== 2)
  ) {
    msg = "请选择归属招商";
  }
  if (msg) return feedback.msgWarning(msg);
  if (lng !== platform_info.length)
    return feedback.msgWarning("请完成平台信息");
  feedback.loading("保存中");
  try {
    state.item.platform_info = platform_info;
    if (state.item.start_time)
      state.item.start_time = `${state.item.start_time.split(" ")[0]} 00:00:00`;
    if (state.item.end_time)
      state.item.end_time = `${state.item.end_time.split(" ")[0]} 23:59:59`;
    const form: any = [];
    state.item.platform_info.forEach((item: any, index: number) => {
      let amount = fee.value;
      if (lng > 1) {
        if ([3, 4].includes(state.item.mode)) {
          amount = state.item.fee / 2;
        } else if (state.item.mode === 2) {
          if (item.platform === 2) amount = 15;
          else amount = 25;
        }
      } else if ([3, 4].includes(state.item.mode)) {
        amount = state.item.fee;
      }
      item = {
        ...state.item,
        ...item,
        user_type: state.operation[index][0],
        user_id: state.operation[index][1],
        fee: amount,
      };
      delete item.platform_info;
      form.push(item);
    });
    let query = { platform_info: form };
    let url = apiBehalfAdd;
    if (state.item.id) {
      url = apiBehalfEdit;
      query = { ...query.platform_info[0] };
    }
    await url(query);
    feedback.msgSuccess("操作成功");
    getLists();
    getCounts();
    state.showEdit = false;
  } catch (_) {
    console.log(_);
  }
  feedback.closeLoading();
};

watch(
  () => state.formTime,
  (val: any) => {
    if (val?.length === 2) {
      queryParams.start_time = val[0] + " 00:00:00";
      queryParams.end_time = val[1] + " 23:59:59";
    } else {
      queryParams.start_time = "";
      queryParams.end_time = "";
    }
  }
);

watch(
  () => state.seleOpt,
  (val: any) => {
    if (val && val.length === 2) {
      queryParams.user_type = val[0];
      queryParams.user_id = val[1];
    } else {
      queryParams.user_id = undefined;
      queryParams.user_type = undefined;
    }
  }
);

getLists();
getCounts();
getUserData(1);
getPerUser();
getCategoryList();
getTourTimp();
</script>

<template>
  <el-card class="!border-none mb-4" shadow="hover">
    <el-form inline :model="queryParams" @submit.prevent>
      <el-form-item label="店铺信息:" prop="keyword">
        <el-input
          placeholder="请输入名称/联系方式"
          v-model="queryParams.keyword"
          style="width: 194px"
        />
      </el-form-item>
      <el-form-item label="店铺城市:" prop="city">
        <el-select v-model="queryParams.city" clearable>
          <el-option
            v-for="item in ShopCityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
          <el-option value="湖州" label="湖州" />
        </el-select>
      </el-form-item>
      <el-form-item label="店铺分类:" prop="category_id">
        <el-select v-model="queryParams.category_id" clearable>
          <el-option
            v-for="item in state.categoryList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否新店:" prop="is_new">
        <el-select v-model="queryParams.is_new" clearable>
          <el-option
            v-for="item in noYes"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否品牌:" prop="is_brand">
        <el-select v-model="queryParams.is_brand" clearable>
          <el-option
            v-for="item in noYes"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="归属招商:">
        <el-cascader
          v-model="state.seleOpt"
          :options="operation"
          :props="{ value: 'id', label: 'name' }"
          clearable
        />
      </el-form-item>
      <el-form-item label="归属运营:" prop="operate_id">
        <el-select v-model="queryParams.operate_id" clearable>
          <el-option
            v-for="item in state.behalf"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分层等级:" prop="level_tier">
        <el-select v-model="queryParams.level_tier" clearable>
          <el-option
            v-for="item in level"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否起量:" prop="is_scaling">
        <el-select v-model="queryParams.is_scaling" clearable>
          <el-option
            v-for="item in noYes"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="有无推广:" prop="has_promotion">
        <el-select v-model="queryParams.has_promotion" clearable>
          <el-option
            v-for="item in eat"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推霸王餐:" prop="has_bwc">
        <el-select v-model="queryParams.has_bwc" clearable>
          <el-option
            v-for="item in eat"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主要问题:">
        <el-select v-model="state.mainIssues" multiple clearable collapse-tags>
          <el-option
            v-for="item in issues"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合作模式:" prop="mode">
        <el-select v-model="queryParams.mode" clearable>
          <el-option
            v-for="item in mode"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <div class="inline-block">
        <el-form-item label="时间类型:">
          <el-select v-model="queryParams.time_type" class="w-[100px]">
            <el-option label="签约时间" :value="1" />
            <el-option label="到期时间" :value="2" />
            <el-option label="录入时间" :value="3" />
            <el-option label="更新时间" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="state.formTime"
            style="width: 300px"
            type="daterange"
            unlink-panels
            range-separator="～"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
      </div>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
        <export-data
          class="ml-2.5"
          :fetch-fun="apiBehalfList"
          :params="queryParams"
          :page-size="pager.size"
        />
      </el-form-item>
    </el-form>
  </el-card>

  <el-card class="!border-none" v-loading="pager.loading" shadow="never">
    <el-button
      v-perms="['agency.edit.button']"
      type="primary"
      style="margin-bottom: 4px"
      @click="onEdit(1)"
      >新增代运营店铺</el-button
    >
    <el-tabs v-model="queryParams.status" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="(tab, tabIndex) in tabOptions"
        :key="tabIndex"
        :label="`${tab.label}（${tab.count}）`"
        :name="tab.value"
      />
    </el-tabs>

    <el-radio-group
      v-model="queryParams.platform"
      size="large"
      class="mb-4"
      @change="onSearch"
    >
      <el-radio-button label="">全部</el-radio-button>
      <el-radio-button
        v-for="item in platformName"
        :key="item.value"
        :label="item.value"
        >{{ item.name }}</el-radio-button
      >
    </el-radio-group>

    <el-table :data="pager.lists">
      <el-table-column label="店铺名称" width="230">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-image
              :src="
                row.platform_text === '饿了么'
                  ? 'https://img.dac6.cn/users/0/0/baaf0979e48ecef79ee918308f16e178.png'
                  : 'https://img.dac6.cn/users/0/0/40938dd00221c4ea24db50e5330fb084.png'
              "
              fit="cover"
              style="width: 30px; height: 30px"
            />
            <div class="ml-2 flex-1">
              <el-tooltip :content="row.name" placement="top">
                <div
                  class="line-clamp-2"
                  @click="openDetail(row)"
                  style="cursor: pointer; color: var(--el-color-primary)"
                >
                  {{ row.name }}
                </div>
              </el-tooltip>
              <div>
                编码：{{
                  row.store_id && row.store_id != 0 ? row.store_id : "无"
                }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="店铺分类" prop="category_name" />
      <el-table-column label="联系方式" prop="tel" />
      <el-table-column label="分层等级" prop="level_tier" />
      <el-table-column label="平台名称" prop="platform_text" />
      <template v-if="![3, 4].includes(queryParams.status)">
        <el-table-column label="是否新店" prop="is_new" />
        <el-table-column label="是否品牌" prop="is_brand" />
        <el-table-column label="有无推广">
          <template #default="{ row }">{{
            row.has_promotion === -1
              ? ""
              : row.has_promotion === 0
              ? "无"
              : "有"
          }}</template>
        </el-table-column>
      </template>

      <el-table-column label="归属招商" prop="submit_user" />
      <el-table-column
        v-if="queryParams.status > 1"
        label="归属运营"
        prop="operate_name"
      />
      <el-table-column label="合作模式" prop="mode_text" />
      <el-table-column label="收费金额">
        <template #default="{ row }">
          <div v-if="row.mode === 2">{{ row.fee }}元/天</div>
          <div v-else-if="row.mode === 4">
            {{ row.fee }}，{{ row.platform_text === "饿了么" ? 15 : 25 }}元/天
          </div>
          <div v-else>{{ row.fee }}</div>
        </template>
      </el-table-column>
      <el-table-column label="抽佣比例">
        <template #default="{ row }">{{ row.commission_rate }}%</template>
      </el-table-column>
      <el-table-column v-if="queryParams.status === 1" label="录入时间">
        <template #default="{ row }">{{
          row.create_time.split(" ")[0]
        }}</template>
      </el-table-column>
      <template v-else-if="queryParams.status === 2">
        <el-table-column label="签约时间">
          <template #default="{ row }">{{
            row.start_time.split(" ")[0]
          }}</template>
        </el-table-column>
        <el-table-column label="到期时间">
          <template #default="{ row }">{{
            row.end_time.split(" ")[0]
          }}</template>
        </el-table-column>
      </template>
      <template v-else-if="queryParams.status === 3">
        <el-table-column label="解约原因" prop="reason" />
        <el-table-column label="解约时间">
          <template #default="{ row }">{{
            row.termination_time.split(" ")[0]
          }}</template>
        </el-table-column>
      </template>
      <el-table-column v-else label="到期时间">
        <template #default="{ row }">{{ row.end_time.split(" ")[0] }}</template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="queryParams.status === 1"
            v-perms="['agency.claim.button']"
            type="primary"
            link
            @click="openClaim(row)"
            >认领</el-button
          >
          <el-button
            v-perms="['agency.edit.button']"
            type="primary"
            link
            @click="onEdit(row)"
            >{{
              queryParams.status === 3
                ? "重签"
                : queryParams.status === 4
                ? "续签"
                : "编辑"
            }}</el-button
          >
          <el-button
            v-if="queryParams.status === 2"
            v-perms="['agency.unbind.button']"
            type="primary"
            link
            @click="openContract(row)"
            >解约</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="flex mt-4 justify-end">
      <pagination v-model="pager" @change="getLists" />
    </div>
  </el-card>
  <!-- 解约 -->
  <el-dialog v-model="state.showContract" width="20%">
    <div>确定解约‘{{ state.item.name }}’吗？</div>
    <el-select
      v-model="state.item.reason"
      style="width: 100%; margin-top: 10px"
    >
      <el-option
        v-for="item in termination"
        :key="item"
        :value="item"
        :label="item"
      />
    </el-select>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.showContract = false">取消</el-button>
        <el-button type="primary" @click="onContract">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 认领 -->
  <el-dialog
    v-model="state.showClaim"
    :title="state.item?.name"
    width="30%"
    @close="closeClaim"
  >
    <el-form label-position="right" label-width="auto">
      <el-form-item v-if="state.item?.isMt" label="美团店铺分配至">
        <el-select v-model="state.mtUser">
          <el-option
            v-for="item in state.behalf"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="state.item?.isElm" label="饿了么店铺分配至">
        <el-select v-model="state.elmUser">
          <el-option
            v-for="item in state.behalf"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.showClaim = false">取消</el-button>
        <el-button type="primary" @click="onClaim">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 详情 -->
  <el-drawer v-model="state.showDetail" size="70%">
    <template #header>
      <div class="flex items-center">
        <el-image
          :src="
            state.item?.platform_text === '饿了么'
              ? 'https://img.dac6.cn/users/0/0/baaf0979e48ecef79ee918308f16e178.png'
              : 'https://img.dac6.cn/users/0/0/40938dd00221c4ea24db50e5330fb084.png'
          "
          fit="cover"
          style="width: 30px; height: 30px"
        />
        <div class="ml-2">{{ state.item?.name }}</div>
      </div>
    </template>
    <el-tabs v-model="state.activeName">
      <el-tab-pane
        v-for="item in tabs"
        :key="item.value"
        :name="item.value"
        :label="item.name"
      />
    </el-tabs>
    <el-descriptions v-if="state.activeName === 'data'">
      <el-descriptions-item label="店铺名称：">{{
        state.item?.name
      }}</el-descriptions-item>
      <el-descriptions-item label="店铺分类：">{{
        state.item?.category_name
      }}</el-descriptions-item>
      <el-descriptions-item label="联系方式：">{{
        state.item?.tel
      }}</el-descriptions-item>
      <el-descriptions-item label="店铺城市：">{{
        state.item?.address
      }}</el-descriptions-item>
      <el-descriptions-item label="分层等级：" span="2">{{
        state.item?.level_tier
      }}</el-descriptions-item>
      <el-descriptions-item label="是否新店：">{{
        state.item?.is_new
      }}</el-descriptions-item>
      <el-descriptions-item label="是否品牌：" span="2">{{
        state.item?.is_brand
      }}</el-descriptions-item>
      <el-descriptions-item label="归属招商：">{{
        state.item?.submit_user
      }}</el-descriptions-item>
      <el-descriptions-item label="录入时间：">{{
        state.item?.create_time
      }}</el-descriptions-item>
      <el-descriptions-item label="更新时间：">{{
        state.item?.update_time
      }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions v-else>
      <el-descriptions-item label="合作状态：">{{
        state.item?.status_text
      }}</el-descriptions-item>
      <el-descriptions-item label="归属运营：">{{
        state.item?.operate_name
      }}</el-descriptions-item>
      <el-descriptions-item label="运营天数：" span="1"
        >{{ state.item?.day }}天</el-descriptions-item
      >
      <el-descriptions-item label="签约时间：">{{
        state.item?.start_time || "无"
      }}</el-descriptions-item>
      <el-descriptions-item label="到期时间：">{{
        state.item?.end_time || "无"
      }}</el-descriptions-item>
      <el-descriptions-item label="解约时间：">{{
        state.item?.termination_time || "无"
      }}</el-descriptions-item>
      <el-descriptions-item label="合作模式：">{{
        state.item?.mode_text || "无"
      }}</el-descriptions-item>
      <el-descriptions-item label="收费金额："
        >{{ state.item?.fee }}元</el-descriptions-item
      >
      <el-descriptions-item label="抽佣比例："
        >{{ state.item?.commission_rate }}%</el-descriptions-item
      >
      <el-descriptions-item label="是否起量：">{{
        state.item?.is_scaling === -1
          ? ""
          : state.item?.is_scaling === 0
          ? "否"
          : "是"
      }}</el-descriptions-item>
      <el-descriptions-item label="有无推广：">{{
        state.item?.has_promotion === -1
          ? ""
          : state.item?.has_promotion === 0
          ? "无"
          : "有"
      }}</el-descriptions-item>
      <el-descriptions-item label="推霸王餐：">{{
        state.item?.has_bwc === -1
          ? ""
          : state.item?.has_bwc === 0
          ? "无"
          : "有"
      }}</el-descriptions-item>
      <el-descriptions-item label="主要问题：" span="3">
        <template v-if="state.item?.main_issues.length">
          <template v-for="(item, index) in issues" :key="index">
            <el-tag
              v-if="state.item?.main_issues.includes(item.value)"
              class="mr-1"
            >
              {{ item.name }}</el-tag
            >
          </template>
        </template>
        <template v-else>无</template>
      </el-descriptions-item>
      <el-descriptions-item label="备注：" span="3">{{
        state.item?.remark
      }}</el-descriptions-item>
    </el-descriptions>
  </el-drawer>
  <!-- 新增编辑 -->
  <el-dialog v-model="state.showEdit" :title="state.editTitle" width="1050px">
    <el-form
      class="behalf-form"
      inline
      label-position="right"
      label-width="auto"
    >
      <el-card header="基本信息">
        <el-form-item label="店铺名称：">
          <el-input class="w-[200px]" v-model="state.item.name" />
        </el-form-item>
        <el-form-item label="店铺编码：">
          <el-input class="w-[200px]" v-model="state.item.store_id" />
        </el-form-item>
        <el-form-item label="店铺分类：">
          <el-select class="w-[200px]" v-model="state.item.category_id">
            <el-option
              v-for="item in state.categoryList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <div class="flex">
          <el-form-item label="联系方式：">
            <el-input class="w-[200px]" v-model="state.item.tel" />
          </el-form-item>
          <el-form-item label="店铺城市：">
            <el-select class="w-[200px]" v-model="state.item.address">
              <el-option
                v-for="item in ShopCityOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
              <el-option value="湖州" label="湖州" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否新店：">
            <el-select class="w-[200px]" v-model="state.item.is_new">
              <el-option
                v-for="item in noYes"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="是否品牌：">
            <el-select class="w-[200px]" v-model="state.item.is_brand">
              <el-option
                v-for="item in noYes"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-card>
      <el-card header="平台信息" class="mt-4">
        <el-form-item v-if="!state.item.id" label="合作平台：">
          <el-radio-group v-model="state.platform_type" @change="onChange">
            <el-radio
              v-for="item in platform"
              :key="item.value"
              :label="item.value"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <div v-for="(item, i) in state.item.platform_info" :key="item">
          <el-form-item label="平台名称：">
            <el-select
              class="w-[200px]"
              v-model="state.item.platform_info[i].platform"
              :disabled="state.item.id || state.platform_type === 2"
            >
              <el-option
                v-for="item in platformName"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="归属招商：">
            <el-cascader
              class="w-[200px]"
              v-model="state.operation[i]"
              :options="operation"
              :props="{ value: 'id', label: 'name' }"
            />
          </el-form-item>
          <el-form-item label="归属运营：">
            <el-select
              v-model="state.item.platform_info[i].operate_id"
              class="w-[200px]"
            >
              <el-option
                v-for="item in state.behalf"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-card>
      <el-card header="代运营信息" class="mt-4">
        <div class="flex">
          <el-form-item label="签约时间：">
            <el-date-picker
              style="width: 200px"
              type="date"
              v-model="state.item.start_time"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="到期时间：">
            <el-date-picker
              style="width: 200px"
              type="date"
              v-model="state.item.end_time"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="合作模式：">
            <el-select
              class="w-[200px]"
              v-model="state.item.mode"
              @change="onModeChange"
            >
              <el-option
                v-for="item in mode"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="[2, 3, 4].includes(state.item.mode)"
            :label="`${state.item.mode === 4 ? '前置收费' : '收费金额'}：`"
          >
            <div
              v-if="[3, 4].includes(state.item.mode)"
              class="flex"
              :min="0"
              step-strictly
              :precision="0"
            >
              <el-input-number v-model="state.item.fee" />
              <div class="ml-2">元</div>
            </div>
            <el-input
              v-else
              class="w-[200px]"
              v-model="fee"
              disabled
              type="number"
            >
              <template #append>{{
                state.item.mode === 2 ? "元/天" : "元"
              }}</template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="state.item.mode === 4">
            <el-input class="w-[200px]" v-model="fee1" disabled>
              <template #append>元/天</template>
            </el-input>
          </el-form-item>
          <el-form-item
            v-if="[1, 3].includes(state.item.mode)"
            label="抽佣比例："
          >
            <el-select class="w-[200px]" v-model="state.item.commission_rate">
              <el-option :value="3" label="3%" />
              <el-option :value="4" label="4%" />
              <el-option :value="5" label="5%" />
              <el-option :value="6" label="6%" />
              <el-option :value="7" label="7%" />
              <el-option :value="8" label="8%" />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="分层等级：">
          <el-select class="w-[200px]" v-model="state.item.level_tier">
            <el-option
              v-for="item in level"
              :key="item.value"
              :value="item.value"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否起量：">
          <el-select class="w-[200px]" v-model="state.item.is_scaling">
            <el-option
              v-for="item in noYes"
              :key="item.value"
              :value="item.value"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="有无推广：">
          <el-select class="w-[200px]" v-model="state.item.has_promotion">
            <el-option
              v-for="item in eat"
              :key="item.value"
              :value="item.value"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <div class="flex">
          <el-form-item label="推霸王餐：">
            <el-select class="w-[200px]" v-model="state.item.has_bwc">
              <el-option
                v-for="item in eat"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="主要问题：">
            <el-select
              class="w-[200px]"
              v-model="state.item.main_issues"
              multiple
            >
              <el-option
                v-for="item in issues"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="巡店模版：">
          <el-select v-model="state.item.temp_id" filterable>
            <el-option
              v-for="item in state.timpList"
              :key="item.id"
              :value="item.id"
              :label="item.title"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%">
          <el-input v-model="state.item.remark" />
        </el-form-item>
      </el-card>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.showEdit = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style>
.behalf-form .el-input__wrapper {
  width: 100%;
}
</style>
