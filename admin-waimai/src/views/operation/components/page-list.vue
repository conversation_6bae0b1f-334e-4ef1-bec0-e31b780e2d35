<script setup lang="ts">
import feedback from "@/utils/feedback";
import { reqMerchantStatis, reqOperateUpload } from "@/api/merchant/overview";

const emit = defineEmits(["getlist"]);
defineProps({
  type: {
    type: String,
    default: "clue",
  },
  mode: Number,
  pager: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object,
    default: () => ({}),
  },
});
const state = reactive({
  type: 1,
  url: "",
  show: false,
});

const getLists = () => emit("getlist");

const onOpen = () => {
  state.type = 1;
  state.url = "";
  state.show = true;
};

// 上传成功
const onSuccess = ({ data }: any) => {
  state.url = data.uri;
  feedback.msgSuccess("上传成功");
};
// 上传失败
const onError = () => feedback.msgWarning("上传失败");
// 上传
const onSubmit = async () => {
  if (!state.url) return feedback.msgWarning("请先上传文件");
  try {
    await reqOperateUpload({ platform: state.type, url: state.url });
    state.show = false;
  } catch (_) {}
};
</script>

<template>
  <div class="flex justify-end">
    <el-button v-if="type === 'earn'" class="mr-4" @click="onOpen"
      >导入</el-button
    >
    <export-data
      :params="params"
      :fetch-fun="reqMerchantStatis"
      :page-size="pager.size"
      style="margin-bottom: 15px"
    />
  </div>
  <!-- 上传弹框 -->
  <el-dialog v-model="state.show" title="上传文件" width="400px">
    <el-form label-width="auto" label-position="right">
      <el-form-item label="文件类型">
        <el-select v-model="state.type">
          <el-option label="饿了么账单" :value="1" />
          <el-option label="美团外卖账单" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="文件路径">
        <upload
          accept=".xls,.xlsx"
          type="upload"
          :data="{ type: 30 }"
          :multiple="false"
          @success="onSuccess"
          @error="onError"
        >
          <el-button type="primary" :disabled="!!state.url">{{
            state.url ? "上传成功" : "上传"
          }}</el-button>
        </upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="state.show = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-dialog>
  <el-table :data="pager.lists" v-loading="pager.loading" show-summary>
    <template v-if="type === 'clue'">
      <template v-if="mode === 3">
        <el-table-column prop="name" label="店铺名称" align="center" />
        <el-table-column prop="platform_text" label="平台" align="center" />
        <el-table-column prop="day" label="代运营天数" align="center" />
        <el-table-column prop="day_amount" label="按日收费" align="center" />
        <el-table-column prop="front_amount" label="前置收费" align="center" />
        <el-table-column prop="draw_some_amount" label="固定抽点" align="center" />
        <el-table-column prop="total_amount" label="累计收益" align="center" />
        <el-table-column prop="status_text" label="合作状态" align="center" />
        <el-table-column prop="user_name" label="归属招商" align="center" />
        <el-table-column prop="operate_name" label="归属运营" align="center" />
      </template>
      <template v-else>
        <el-table-column prop="user_name" label="账号姓名" align="center" />
        <el-table-column
          prop="cooperate_count"
          label="合作店铺数"
          align="center"
        />
        <el-table-column prop="elm_count" label="饿了么店铺数" align="center" />
        <el-table-column prop="mt_count" label="美团店铺数" align="center" />
      </template>
    </template>
    <template v-else>
      <template v-if="params.mode === 3">
        <el-table-column label="合作模式" align="center">
          <template #default="{ row }">
            {{
              row.mode === 1
                ? "固定抽点"
                : row.mode === 2
                ? "按日收费"
                : "前置收费"
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sum_settlement_amount"
          label="累计收益金额"
          align="center"
        />
        <el-table-column
          prop="elm_sum_settlement_amount"
          label="饿了么收益金额"
          align="center"
        />
        <el-table-column
          prop="mt_sum_settlement_amount"
          label="美团收益金额"
          align="center"
        />
      </template>
      <template v-else>
        <el-table-column
          v-if="params.mode === 1"
          label="平台名称"
          align="center"
        >
          <template #default="{ row }">{{
            row.platform === 1 ? "饿了么" : "美团"
          }}</template>
        </el-table-column>
        <el-table-column
          v-if="params.mode === 2"
          prop="user_name"
          label="账号姓名"
          align="center"
        />
        <el-table-column
          prop="shop_count"
          label="收益店铺数量"
          align="center"
        />
        <el-table-column
          prop="sum_settlement_amount"
          label="累计抽点收益"
          align="center"
        />
        <el-table-column
          prop="this_avg_income"
          label="单店平均收益"
          align="center"
        />
      </template>
    </template>
  </el-table>
  <!-- <div class="flex mt-4 justify-end">
    <pagination v-model="pager" @change="getLists" />
  </div> -->
</template>
