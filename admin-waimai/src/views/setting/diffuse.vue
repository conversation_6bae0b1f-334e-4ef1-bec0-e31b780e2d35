<script setup lang="ts">
import { reactive } from "vue";
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import {
  reqDiffuseList,
  reqDiffuseDelete,
  reqDiffuseAdd,
  reqDiffuseEdit,
  reqDiffuseData,
} from "@/api/merchant/diffuse";

const typeOptions = [
  { label: "看店宣传", value: 1 },
  { label: "案例宣传", value: 2 },
  { label: "合作宣传", value: 3 },
  { label: "产品宣传", value: 4 },
];
const createForm = () => ({
  title: "",
  img: "",
  content: "",
  type: undefined,
});
const queryParams = reactive({ type: undefined });
const state = reactive({
  share: {
    show: false,
    list: [],
  },
  data: {
    show: false,
    title: "新增宣传内容",
    form: createForm(),
  },
});

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: reqDiffuseList,
  params: queryParams,
});

// 删除
const onDelete = async (row: any) => {
  await feedback.confirm("确定删除该宣传吗？");
  feedback.loading("删除中...");
  try {
    await reqDiffuseDelete({ id: row.id });
    getLists();
    feedback.msgSuccess("删除成功");
  } catch (_) {}
  feedback.closeLoading();
};

// 操作
const onHandle = (type: number, row?: any) => {
  if (type === 1) {
    state.data.title = "新增宣传内容";
    state.data.form = createForm();
  } else {
    state.data.title = "编辑宣传内容";
    state.data.form = { ...row };
  }
  state.data.show = true;
};
// 提交
const onSubmit = async () => {
  if (!state.data.form.content) return feedback.msgError("请输入宣传文案");
  if (!state.data.form.type) return feedback.msgError("请选择宣传类型");
  feedback.loading("提交中...");
  try {
    let url = reqDiffuseAdd;
    if ((state.data.form as any).id) url = reqDiffuseEdit;
    await url(state.data.form);
    getLists();
    state.data.show = false;
    feedback.msgSuccess("提交成功");
  } catch (_) {}
  feedback.closeLoading();
};

// 上传成功
const onSuccess = ({ data }: any) => {
  state.data.form.img = data.uri;
};

// 查看分享信息
const getShareData = async (promotion_id: number) => {
  try {
    const { lists } = await reqDiffuseData({
      promotion_id,
      type: 5,
      page_no: 1,
      page_size: 1000,
    });
    state.share.list = lists || [];
    state.share.show = true;
  } catch (_) {}
};

getLists();
</script>

<template>
  <el-card class="!border-none mb-4" shadow="never">
    <el-form inline :model="queryParams" @submit.prevent>
      <el-form-item label="宣传类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择宣传类型"
          clearable
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="resetPage">查询</el-button>
        <el-button @click="resetParams">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>

  <el-card class="!border-none" shadow="never">
    <el-button
      v-perms="['agency.diffuse.add.button']"
      type="primary"
      class="mb-4"
      @click="onHandle(1)"
      >新增宣传</el-button
    >
    <el-table :data="pager.lists" v-loading="pager.loading">
      <el-table-column label="宣传内容" prop="content" />
      <el-table-column label="宣传海报">
        <template #default="{ row }">
          <el-image
            v-if="row.img"
            :src="row.img"
            fit="cover"
            :preview-src-list="[row.img]"
            preview-teleported
            style="width: 50px; height: 50px"
          />
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column label="宣传类型">
        <template #default="{ row }">
          {{ typeOptions.find((item) => item.value === row.type)?.label }}
        </template>
      </el-table-column>
      <el-table-column
        v-perms="['agency.diffuse.share.button']"
        label="分享次数"
      >
        <template #default="{ row }">
          <div
            class="cursor-pointer"
            style="color: var(--el-color-primary)"
            @click="getShareData(row.id)"
          >
            {{ row.pv }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="create_time" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button
            v-perms="['agency.diffuse.edit.button']"
            type="primary"
            text
            link
            @click="onHandle(2, row)"
            >编辑</el-button
          >
          <el-button
            v-perms="['agency.diffuse.delete.button']"
            type="danger"
            text
            link
            @click="onDelete(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="flex mt-4 justify-end">
      <pagination v-model="pager" @change="getLists" />
    </div>
  </el-card>

  <el-dialog v-model="state.data.show" :title="state.data.title" width="800px">
    <el-form :model="state.data.form" label-width="auto">
      <el-form-item label="宣传文案">
        <el-input
          v-model="state.data.form.content"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 6 }"
          maxlength="300"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="宣传图片">
        <div v-if="state.data.form.img" class="flex items-center">
          {{ state.data.form.img
          }}<el-icon
            @click="state.data.form.img = ''"
            class="ml-2 cursor-pointer"
            ><Delete
          /></el-icon>
        </div>
        <upload v-else :multiple="false" @success="onSuccess">
          <div style="color: var(--el-color-primary)">上传</div>
        </upload>
      </el-form-item>
      <el-form-item label="宣传类型">
        <el-select v-model="state.data.form.type" placeholder="请选择宣传类型">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="onSubmit">提交</el-button>
      <el-button @click="state.data.show = false">取消</el-button>
    </template>
  </el-dialog>

  <!-- 分享信息 -->
  <el-dialog v-model="state.share.show" title="分享详情">
    <el-table :data="state.share.list" max-height="50vh">
      <el-table-column label="招商姓名" prop="user_name" />
      <el-table-column label="分享次数" prop="number" />
    </el-table>
  </el-dialog>
</template>
