<script setup lang="ts">
import { reactive } from "vue";
import {
  reqMerchantGetDispos,
  reqMerchantSetDispos,
} from "@/api/merchant/disposition";
import { reqDiffuseData } from "@/api/merchant/diffuse";

const img =
  "https://img.dac6.cn/users/0/0/a7ba5a4cdbb05b87503dadef57ac3575.png";
const createForm = () => ({
  framework: "",
  framework_clicks: 0,
  ad_img: "",
  policy: "",
  policy_clicks: 0,
  merchant_policy: "",
  merchant_policy_clicks: 0,
  case: [] as string[],
  case_clicks: 0,
});
const state = reactive({
  form: createForm(),
  image: "",
  loading: false,
  dialog: {
    show: false,
    list: [],
  },
});

const getData = async () => {
  try {
    const data = await reqMerchantGetDispos();
    if (data.framework) state.image = img;
    state.form = data || createForm();
  } catch (_) {}
};

const onSuccessFils = ({ data }: any) => {
  state.form.framework = data.uri;
  state.image = img;
};

const onClose = () => {
  state.form.framework = "";
  state.image = "";
};

const onSuccessImage = (
  key: "ad_img" | "policy" | "merchant_policy",
  { data }: any
) => {
  state.form[key] = data.uri;
};

const onSuccessCase = (index: number, { data }: any) => {
  state.form.case[index] = data.uri;
};

const onCloseCase = (index: any) => (state.form.case[index] = "");
const onPreviewCase = (index: any) => {
  const dom = document.getElementsByClassName("image")[index];
  (dom.firstChild as any).click();
};

const onSubmit = async () => {
  state.loading = true;
  const arr = state.form.case.filter((a: string) => !!a);
  try {
    await reqMerchantSetDispos({ ...state.form, case: arr });
    getData();
  } catch (_) {}
  state.loading = false;
};

const adImg = ref();
const policy = ref();
const merchantPolicy = ref();
const onPreview = (dom: any) => {
  if (dom.$el) dom.$el.firstChild.click();
  else window.open(state.form.framework, "_blank");
};

// 打开分享详情
const openDialog = async (type: number) => {
  try {
    const { lists } = await reqDiffuseData({
      type: type,
      page_size: 1000,
      page_no: 1,
    });
    state.dialog.show = true;
    state.dialog.list = lists || [];
  } catch (_) {}
};

getData();
</script>

<template>
  <el-card header="首页广告">
    <div
      v-if="state.form.ad_img"
      class="gd__box"
      style="width: 171px; height: 100px"
    >
      <el-image
        :src="state.form.ad_img"
        preview-teleported
        :preview-src-list="[state.form.ad_img]"
        style="width: 171px; height: 100px"
        ref="adImg"
      />
      <div class="gd__mask">
        <el-icon
          color="#fff"
          size="20px"
          style="cursor: pointer"
          @click="state.form.ad_img = ''"
          ><Delete
        /></el-icon>
        <el-icon
          color="#fff"
          size="20px"
          class="ml-4"
          style="cursor: pointer"
          @click="onPreview(adImg)"
          ><ZoomIn
        /></el-icon>
      </div>
    </div>
    <upload
      v-else
      :multiple="false"
      accept="image/*"
      @success="onSuccessImage('ad_img', $event)"
    >
      <div class="gd__box" style="width: 171px; height: 100px">
        <p>+添加广告</p>
      </div>
    </upload>
    <div class="mt-2">图片配置建议尺寸：171*100px</div>
  </el-card>
  <el-card class="mt-4">
    <template #header>
      <div class="flex items-center">
        服务框架<span
          class="cursor-pointer"
          style="color: var(--el-color-primary)"
          @click="openDialog(1)"
          >(点击分享的累计次数：{{ state.form.framework_clicks }})</span
        >
      </div>
    </template>
    <div
      v-if="state.form.framework"
      class="gd__box"
      style="width: 100px; height: 100px"
    >
      <el-image :src="state.image" style="width: 100%; height: 100%" />
      <div class="gd__mask">
        <el-icon
          color="#fff"
          size="20px"
          style="cursor: pointer"
          @click="onClose"
          ><Delete
        /></el-icon>
        <el-icon
          color="#fff"
          size="20px"
          class="ml-4"
          style="cursor: pointer"
          @click="onPreview"
          ><ZoomIn
        /></el-icon>
      </div>
    </div>
    <upload
      v-else
      :multiple="false"
      type="upload"
      accept=".pdf"
      :data="{ type: 30 }"
      @success="onSuccessFils"
    >
      <div class="gd__box" style="width: 100px; height: 100px">
        <p>+添加文件</p>
      </div>
    </upload>
  </el-card>
  <el-card class="mt-4">
    <template #header>
      <div class="flex items-center">
        服务案例<span
          class="cursor-pointer"
          style="color: var(--el-color-primary)"
          @click="openDialog(2)"
          >(点击分享的累计次数：{{ state.form.case_clicks }})</span
        >
      </div>
    </template>
    <div class="flex">
      <template v-for="(item, index) in state.form.case" :key="index">
        <div v-if="item" class="gd__box" style="width: 117px; height: 174px">
          <el-image
            :src="item"
            class="image"
            preview-teleported
            :initial-index="index"
            :preview-src-list="state.form.case"
            style="width: 117px; height: 174px"
          />
          <div class="gd__mask">
            <el-icon
              color="#fff"
              size="20px"
              style="cursor: pointer"
              @click="onCloseCase(index)"
              ><Delete
            /></el-icon>
            <el-icon
              color="#fff"
              size="20px"
              class="ml-4"
              style="cursor: pointer"
              @click="onPreviewCase(index)"
              ><ZoomIn
            /></el-icon>
          </div>
        </div>
        <upload
          v-else
          :multiple="false"
          accept="image/*"
          @success="onSuccessCase(index, $event)"
          class="ml-2"
        >
          <div class="gd__box" style="width: 117px; height: 174px">
            <p>+添加图片</p>
          </div>
        </upload>
      </template>
      <upload
        v-if="state.form.case.length <= 10"
        :multiple="false"
        accept="image/*"
        @success="onSuccessCase(state.form.case.length, $event)"
        class="ml-2"
      >
        <div class="gd__box" style="width: 117px; height: 174px">
          <p>+添加图片</p>
        </div>
      </upload>
    </div>
    <div class="mt-2">图片配置建议尺寸：351*520px</div>
  </el-card>
  <el-card class="mt-4">
    <template #header>
      <div class="flex items-center">
        招商政策<span
          class="cursor-pointer"
          style="color: var(--el-color-primary)"
          @click="openDialog(3)"
          >(点击分享的累计次数：{{ state.form.policy_clicks }})</span
        >
      </div>
    </template>

    <div
      v-if="state.form.policy"
      class="gd__box"
      style="width: 117px; height: 200px"
    >
      <el-image
        :src="state.form.policy"
        preview-teleported
        :preview-src-list="[state.form.policy]"
        style="width: 117px; height: 200px"
        ref="policy"
      />
      <div class="gd__mask">
        <el-icon
          color="#fff"
          size="20px"
          style="cursor: pointer"
          @click="state.form.policy = ''"
          ><Delete
        /></el-icon>
        <el-icon
          color="#fff"
          size="20px"
          class="ml-4"
          style="cursor: pointer"
          @click="onPreview(policy)"
          ><ZoomIn
        /></el-icon>
      </div>
    </div>
    <upload
      v-else
      :multiple="false"
      accept="image/*"
      @success="onSuccessImage('policy', $event)"
    >
      <div class="gd__box" style="width: 117px; height: 200px">
        <p>+添加图片</p>
      </div>
    </upload>
    <div class="mt-2">图片配置建议尺寸：宽度351px，高度不限</div>
  </el-card>
  <el-card class="mt-4">
    <template #header>
      <div class="flex items-center">
        商家政策<span
          class="cursor-pointer"
          style="color: var(--el-color-primary)"
          @click="openDialog(4)"
          >(点击分享的累计次数：{{ state.form.merchant_policy_clicks }})</span
        >
      </div>
    </template>
    <div
      v-if="state.form.merchant_policy"
      class="gd__box"
      style="width: 117px; height: 200px"
    >
      <el-image
        :src="state.form.merchant_policy"
        ref="merchantPolicy"
        preview-teleported
        :preview-src-list="[state.form.merchant_policy]"
        style="width: 117px; height: 200px"
      />
      <div class="gd__mask">
        <el-icon
          color="#fff"
          size="20px"
          style="cursor: pointer"
          @click="state.form.merchant_policy = ''"
          ><Delete
        /></el-icon>
        <el-icon
          color="#fff"
          size="20px"
          class="ml-4"
          style="cursor: pointer"
          @click="onPreview(merchantPolicy)"
          ><ZoomIn
        /></el-icon>
      </div>
    </div>
    <upload
      v-else
      :multiple="false"
      accept="image/*"
      @success="onSuccessImage('merchant_policy', $event)"
    >
      <div class="gd__box" style="width: 117px; height: 200px">
        <p>+添加图片</p>
      </div>
    </upload>
    <div class="mt-2">图片配置建议尺寸：宽度351px，高度不限</div>
  </el-card>
  <el-button
    type="primary"
    class="mt-6"
    :loading="state.loading"
    @click="onSubmit"
    >保存</el-button
  >

  <el-dialog v-model="state.dialog.show" title="分享详情">
    <el-table :data="state.dialog.list" max-height="50vh">
      <el-table-column label="招商姓名" prop="user_name" />
      <el-table-column label="分享次数" prop="number" />
    </el-table>
  </el-dialog>
</template>

<style lang="scss" scoped>
.gd__box {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border: 1px dashed #ddd;
  :hover {
    opacity: 1;
  }
}
.gd__mask {
  opacity: 0;
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
