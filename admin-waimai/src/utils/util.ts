import { isObject } from "@vue/shared";
import { cloneDeep } from "lodash";

/**
 * @description 添加单位
 * @param {String | Number} value 值 100
 * @param {String} unit 单位 px em rem
 */
export const addUnit = (value: string | number, unit = "px") => {
  return !Object.is(Number(value), NaN) ? `${value}${unit}` : value;
};

/**
 * @description 添加单位
 * @param {unknown} value
 * @return {Boolean}
 */
export const isEmpty = (value: unknown) => {
  return value == null && typeof value == "undefined";
};

/**
 * @description 树转数组，队列实现广度优先遍历
 * @param {Array} data  数据
 * @param {Object} props `{ children: 'children' }`
 */

export const treeToArray = (data: any[], props = { children: "children" }) => {
  data = cloneDeep(data);
  const { children } = props;
  const newData = [];
  const queue: any[] = [];
  data.forEach((child: any) => queue.push(child));
  while (queue.length) {
    const item: any = queue.shift();
    if (item[children]) {
      item[children].forEach((child: any) => queue.push(child));
      delete item[children];
    }
    newData.push(item);
  }
  return newData;
};

/**
 * @description 数组转
 * @param {Array} data  数据
 * @param {Object} props `{ parent: 'pid', children: 'children' }`
 */

export const arrayToTree = (
  data: any[],
  props = { id: "id", parentId: "pid", children: "children" }
) => {
  data = cloneDeep(data);
  const { id, parentId, children } = props;
  const result: any[] = [];
  const map = new Map();
  data.forEach((item) => {
    map.set(item[id], item);
    const parent = map.get(item[parentId]);
    if (parent) {
      parent[children] = parent[children] ?? [];
      parent[children].push(item);
    } else {
      result.push(item);
    }
  });
  return result;
};

/**
 * @description 获取正确的路经
 * @param {String} path  数据
 */
export function getNormalPath(path: string) {
  if (path.length === 0 || !path || path == "undefined") {
    return path;
  }
  const newPath = path.replace("//", "/");
  const length = newPath.length;
  if (newPath[length - 1] === "/") {
    return newPath.slice(0, length - 1);
  }
  return newPath;
}

/**
 * @description对象格式化为Query语法
 * @param { Object } params
 * @return {string} Query语法
 */
export function objectToQuery(params: Record<string, any>): string {
  let query = "";
  for (const props of Object.keys(params)) {
    const value = params[props];
    const part = encodeURIComponent(props) + "=";
    if (!isEmpty(value)) {
      if (isObject(value)) {
        for (const key of Object.keys(value)) {
          if (!isEmpty(value[key])) {
            const params = props + "[" + key + "]";
            const subPart = encodeURIComponent(params) + "=";
            query += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        query += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return query.slice(0, -1);
}

/**
 * @description 时间格式化
 * @param dateTime { number } 时间戳
 * @param fmt { string } 时间格式
 * @return { string }
 */
// yyyy:mm:dd|yyyy:mm|yyyy年mm月dd日|yyyy年mm月dd日 hh时MM分等,可自定义组合
export const timeFormat = (dateTime: number, fmt = "yyyy-mm-dd") => {
  // 如果为null,则格式化当前时间
  if (!dateTime) {
    dateTime = Number(new Date());
  }
  // 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
  if (dateTime.toString().length == 10) {
    dateTime *= 1000;
  }
  const date = new Date(dateTime);
  let ret;
  const opt: any = {
    "y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "h+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "s+": date.getSeconds().toString(), // 秒
  };
  for (const k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  return fmt;
};

/**
 * @description 获取不重复的id
 * @param length { Number } id的长度
 * @return { String } id
 */
export const getNonDuplicateID = (length = 8) => {
  let idStr = Date.now().toString(36);
  idStr += Math.random().toString(36).substring(3, length);
  return idStr;
};

/**
 * 合并异步任务的并发，即“相等”的异步任务并发情况下仅执行一次
 * @param func 异步任务函数
 * @description 好像不需要处理this了，用箭头函数
 */
export function mergeConcurrent<T extends any[]>(
  func: (...args: T) => Promise<unknown>
) {
  const resolverList: { resolve: Function; reject: Function }[] = [];
  let progress = false;

  return (...args: T) => {
    return new Promise(async (resolve, reject) => {
      resolverList.push({ resolve, reject });
      if (progress) return;

      progress = true;
      try {
        const ret = await func(...args);
        resolverList.forEach((li) => li.resolve(ret));
      } catch (e) {
        resolverList.forEach((li) => li.reject(e));
      }
      resolverList.length = 0;
      progress = false;
    });
  };
}

/**
 * 四舍五入，Number.toFixed 是四舍六入五取偶
 * @param num     [待处理数字]
 * @param decimal [需要保留的小数位]
 * @param opt     {floor: 处理精度、舍入时是否向下取“整” 默认false}
 */
export const roundNumber = (
  num: number | string,
  decimal = 2,
  opt?: { floor?: boolean }
): number => {
  const n = Number(num);
  if (isNaN(n)) {
    return 0;
  }
  const { floor } = opt || {};
  const p1 = Math.pow(10, decimal + 1);
  const p2 = Math.pow(10, decimal);
  return (!floor ? Math.round((n * p1) / 10) : Math.floor((n * p1) / 10)) / p2;
};

/**
 * 把数字转成带单位的字符串：99->"99"，99999->"9.99万"
 * @param n 数值
 * @param opt { plusSign: 加单位后是否在末尾补加号; lang: zh|es 默认zh中文单位; decimal: 小数位数 默认lang=zh时为2，否则为1; floor: 是否向下取整 默认true }
 */
export const unitNumber = (
  n: number,
  opt?: { lang?: string; plusSign?: boolean; decimal?: number; floor?: boolean }
) => {
  const { lang = "zh", plusSign = false, decimal, floor = true } = opt || {};
  const piece = lang === "zh" ? 10000 : 1000;
  const theDecimal =
    typeof decimal === "number" ? decimal : lang === "zh" ? 2 : 1;
  if (n < piece) {
    if (plusSign) {
      if (n > 1000) return `${Math.floor(n / 1000) * 1000}+`;
      if (n > 100) return `${Math.floor(n / 100) * 100}+`;
    }
    return roundNumber(n, theDecimal, { floor }) + "";
  }
  let val: number | string;
  if (lang === "zh") {
    const units = ["", "万", "亿", "万亿"];
    const i = Math.floor(Math.log(n) / Math.log(piece));
    val = roundNumber(n / Math.pow(piece, i), theDecimal, { floor }) + units[i];
  } else {
    if (n >= 1000000)
      val = roundNumber(n / 1000000, theDecimal, { floor }) + "m";
    else if (n >= 10000)
      val = roundNumber(n / 10000, theDecimal, { floor }) + "w";
    else val = roundNumber(n / 1000, theDecimal, { floor }) + "k";
  }
  return `${val}${!plusSign ? "" : "+"}`;
};

/**
 * 防抖函数
 */
export function debounce(fn: Function, wait = 200, immediate = false) {
	let timer: ReturnType<typeof setTimeout> | null

	return function (this: unknown, ...args: any) {
		if (timer) clearTimeout(timer)
		if (immediate) {
			timer = null
			return fn.apply(this, args)
		}
		timer = setTimeout(() => fn.apply(this, args), wait)
	}
}
