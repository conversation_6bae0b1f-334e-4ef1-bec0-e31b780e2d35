/**
 * 权限控制
 */

import NProgress from "nprogress";
import router, { findFirstValidRoute } from "./router";
import "nprogress/nprogress.css";
import { isExternal } from "./utils/validate";
import useUserStore from "./stores/modules/user";
import { INDEX_ROUTE, INDEX_ROUTE_NAME } from "./router/routes";
import { PageEnum } from "./enums/pageEnum";
import useTabsStore from "./stores/modules/multipleTabs";
import { clearAuthInfo } from "./utils/auth";
import config from "./config";
import cache from "@/utils/cache";
import { TOKEN_KEY, TOKEN_APP_ID } from "@/enums/cacheEnums";

// NProgress配置
NProgress.configure({ showSpinner: false });

const loginPath = PageEnum.LOGIN;
const defaultPath = PageEnum.INDEX;
// 免登录白名单
const whiteList: string[] = [PageEnum.LOGIN, PageEnum.ERROR_403];
// 为了token失效后跳到授权中心能再丝滑回来，需要应用ID带过去
const setAppId = (token: string) => {
  if (!token) return;
  try {
    const appId = JSON.parse(window.atob(token.split(".")[1]))?.Info?.App;
    if (!appId) return;
    cache.set(TOKEN_APP_ID, appId);
  } catch (e) {}
};

router.beforeEach(async (to, from, next) => {
  // 开始 Progress Bar
  NProgress.start();
  document.title = to.meta.title ?? config.title;

  const toQuery = { ...to.query };
  if (toQuery.token) {
    cache.set(TOKEN_KEY, toQuery.token);
    setAppId(toQuery.token as string);
    delete toQuery.token;
    // next({ path: to.path, query: toQuery, replace: true })
    location.replace(router.resolve({ path: to.path, query: toQuery }).href);
    return false;
  }

  const userStore = useUserStore();
  const tabsStore = useTabsStore();
  if (whiteList.includes(to.path)) {
    // 在免登录白名单，直接进入
    next();
  } else if (userStore.token) {
    // 获取用户信息
    const hasGetUserInfo = Object.keys(userStore.userInfo).length !== 0;
    if (hasGetUserInfo) {
      if (to.path === loginPath) {
        next({ path: defaultPath });
      } else {
        next();
      }
    } else {
      try {
        await userStore.getUserInfo();
        const routes = userStore.routes;
        // 找到第一个有效路由
        const routeName = findFirstValidRoute(routes);
        // 没有有效路由跳转到403页面
        if (!routeName) {
          clearAuthInfo();
          next(PageEnum.ERROR_403);
          return;
        }
        tabsStore.setRouteName(routeName!);
        INDEX_ROUTE.redirect = { name: routeName };

        // 动态添加index路由
        router.addRoute(INDEX_ROUTE);
        routes.forEach((route: any) => {
          // https 则不插入
          if (isExternal(route.path)) {
            return;
          }
          if (!route.children) {
            router.addRoute(INDEX_ROUTE_NAME, route);
            return;
          }
          // 动态添加可访问路由表
          router.addRoute(route);
        });
        next({ ...to, replace: true });
      } catch (err) {
        clearAuthInfo();
        next({ path: loginPath, query: { redirect: to.fullPath } });
      }
    }
  } else {
    if (to.meta.auth === false) {
      next();
    } else {
      next({ path: loginPath, query: { redirect: to.fullPath } });
    }
  }

  if (to.path === loginPath) {
    if (userStore.token) return { path: "/", query: to.query };
    const host = /(pdo0|test|:)/.test(location.host)
      ? "https://auth.test.biyingniao.com"
      : "https://auth.biyingniao.com";
    const url = location.pathname.includes(loginPath)
      ? location.href.replace(loginPath, "/")
      : location.href;
    location.replace(
      `${host}/a/auth/login/?redirectFrom=${encodeURIComponent(
        url
      )}&redirectApp=${cache.get(TOKEN_APP_ID) || ""}`
    );
    return false;
  }
});

router.afterEach(() => {
  NProgress.done();
});

export function hasPermission(perms: string[]) {
  const userStore = useUserStore();
  const permissions = userStore.perms;
  const all_permission = "*";
  if (perms.length > 0) {
    return permissions.some((key: string) => {
      return all_permission == key || perms.includes(key);
    });
  }
  return true;
}
