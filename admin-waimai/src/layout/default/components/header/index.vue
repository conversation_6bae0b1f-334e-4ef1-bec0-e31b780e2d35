<template>
  <header class="header">
    <div class="navbar">
      <div class="flex-1 flex">
        <div class="navbar-item">
          <fold />
        </div>
        <div class="navbar-item">
          <refresh />
        </div>
        <div
          class="flex items-center px-2"
          v-if="!isMobile && settingStore.showCrumb"
        >
          <breadcrumb />
        </div>
      </div>
      <div class="flex">
        <!-- <div class="navbar-item">
          <el-button @click="showDownload = true">下载招商宝</el-button>
        </div> -->
        <div class="navbar-item" v-if="!isMobile">
          <full-screen />
        </div>
        <div class="navbar-item">
          <user-drop-down />
        </div>
        <div class="navbar-item">
          <setting />
        </div>
      </div>
    </div>
    <multiple-tabs v-if="settingStore.openMultipleTabs" />
  </header>

  <!-- <el-dialog v-model="showDownload" width="1000px">
    <div style="height: 60vh; overflow-y: auto">
      <el-tabs v-model="downloadTab">
        <el-tab-pane label="下载应用" name="download">
          <div class="flex items-center justify-center">
            <div
              v-for="(item, index) in appList"
              :key="index"
              class="flex flex-col items-center"
              :style="{ marginRight: index === 0 ? '100px' : '' }"
            >
              <el-image :src="item.code" style="width: 200px; height: 200px" />
              <div style="font-size: 12px">
                招商宝{{ item.type }}
              </div>
              <el-button v-copy="item.url" type="primary" text link class="mt-2"
                >复制下载链接</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="安装记录" name="installation">
          <el-table v-loading="pager.loading" :data="pager.lists">
            <el-table-column label="姓名" prop="real_name" />
            <el-table-column label="账号" prop="account" />
            <el-table-column label="招商城市">
              <template #default="{ row }">{{ row.city || "--" }}</template>
            </el-table-column>
            <el-table-column label="版本编号">
              <template #default="{ row }">{{ row.version || "--" }}</template>
            </el-table-column>
            <el-table-column label="最近登录时间">
              <template #default="{ row }">{{
                row.login_time || "--"
              }}</template>
            </el-table-column>
          </el-table>
          <div class="flex justify-center mt-4">
            <pagination v-model="pager" @change="getLists" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog> -->
</template>

<script setup lang="ts">
import { usePaging } from "@/hooks/usePaging";
// import { getUserList } from "@/api/consumer";
import useAppStore from "@/stores/modules/app";
import Fold from "./fold.vue";
import Refresh from "./refresh.vue";
import Breadcrumb from "./breadcrumb.vue";
import FullScreen from "./full-screen.vue";
import UserDropDown from "./user-drop-down.vue";
import Setting from "../setting/index.vue";
import MultipleTabs from "./multiple-tabs.vue";

import useSettingStore from "@/stores/modules/setting";

// const { pager, getLists } = usePaging({
//   fetchFun: getUserList,
//   params: { is_disable: 0, order: "login_time" },
// });

// getLists();

const downloadTab = ref("download");
// const showDownload = ref(false);
const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);
// const appList = computed(() => {
//   return [
//     {
//       // icon: "https://img.dac6.cn/users/0/0/338529d8b6558a430da0fdf251c29576.png",
//       type: "Android",
//       code: appStore.config.app_config.android.qrcode,
//       url: appStore.config.app_config.android.url,
//       version: appStore.config.app_config.android.version,
//     },
//     {
//       // icon: "https://img.dac6.cn/users/0/0/d8085f0af107e45d66e6ba4525922fca.png",
//       type: "IOS",
//       code: appStore.config.app_config.ios.qrcode,
//       url: appStore.config.app_config.ios.url,
//       version: appStore.config.app_config.ios.version,
//     },
//   ];
// });

const settingStore = useSettingStore();
</script>

<style lang="scss">
.navbar {
  height: var(--navbar-height);
  @apply flex px-2 bg-body;
  .navbar-item {
    @apply h-full  flex justify-center items-center  hover:bg-page;
  }
}
</style>
