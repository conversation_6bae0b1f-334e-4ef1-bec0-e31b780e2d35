// @ts-ignore
import request from '@/utils/request'

export function apiCategoryLists(params: any) {
    return request.get({ url: '/adelm.category/lists', params })
}

export function apiCategoryAdd(params: any) {
    return request.post({ url: '/adelm.category/add', params })
}

export function apiCategoryEdit(params: any) {
    return request.post({ url: '/adelm.category/edit', params })
}

export function apiCategoryDelete(params: any) {
    return request.post({ url: '/adelm.category/delete', params })
}

export function apiElmAdzoneLists(params: any) {
    return request.get({ url: '/adelm.category/adzones', params })
}

export function apiElmAdzoneCategoryAssign(params: any) {
    return request.post({ url: '/adelm.category/assign', params })
}