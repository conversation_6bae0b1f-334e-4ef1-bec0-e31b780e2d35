import request from "@/utils/request";

export function apiPwdLists(params: any) {
    return request.get({
        url: '/adelm.pwd/lists',
        params
    })
}

export function apiPwdAdd(params: any) {
    return request.post({
        url: '/adelm.pwd/add',
        params
    })
}

export function apiPwdDelete(params: any) {
    return request.post({
        url: '/adelm.pwd/delete',
        params
    })
}

export function apiActivityLists() {
    return request.get({
        url: '/adelm.pwd/activities'
    })
}
