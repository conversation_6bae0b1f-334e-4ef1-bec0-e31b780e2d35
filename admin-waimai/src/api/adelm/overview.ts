import request from '@/utils/request'

export const apiOverView = (params: any) => {
    return request.get({ url: '/adelm.index/overview', params })
}

export const otherList = (params: any) => {
    return request.get({ url: '/adelm.index/otherList', params })
}

export const dataWatches = (params: any) => {
    return request.get({ url: '/adelm.index/dataWatches', params })
}

export const watchOption = (params: any) => {
    return request.get({ url: '/adelm.index/watches', params })
}