import request from '@/utils/request'

// 团队成员列表
export function apiTeamMemberLists(params: any) {
    return request.get({ url: '/team_member.team_member/lists', params })
}

// 添加团队成员
export function apiTeamMemberAdd(params: any) {
    return request.post({ url: '/team_member.team_member/add', params })
}

// 编辑团队成员
export function apiTeamMemberEdit(params: any) {
    return request.post({ url: '/team_member.team_member/edit', params })
}

// 删除团队成员
export function apiTeamMemberDelete(params: any) {
    return request.post({ url: '/team_member.team_member/delete', params })
}

// 团队成员详情
export function apiTeamMemberDetail(params: any) {
    return request.get({ url: '/team_member.team_member/detail', params })
}