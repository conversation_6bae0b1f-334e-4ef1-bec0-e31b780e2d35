import request from '@/utils/request'

// 任务列表
export function apiCrawlTaskLists(params: any) {
    return request.get({ url: '/crawltask.crawl_task/lists', params })
}

// 添加任务
export function apiCrawlTaskAdd(params: any) {
    return request.post({ url: '/crawltask.crawl_task/add', params })
}

// 编辑任务
export function apiCrawlTaskEdit(params: any) {
    return request.post({ url: '/crawltask.crawl_task/edit', params })
}

// 删除任务
export function apiCrawlTaskDelete(params: any) {
    return request.post({ url: '/crawltask.crawl_task/delete', params })
}

// 任务详情
export function apiCrawlTaskDetail(params: any) {
    return request.get({ url: '/crawltask.crawl_task/detail', params })
}