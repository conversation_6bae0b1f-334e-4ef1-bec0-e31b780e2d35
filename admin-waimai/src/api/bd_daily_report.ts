import request from '@/utils/request'

// 个人考核列表
export function apiBdDailyReportLists(params: any) {
    return request.get({ url: '/sq_bd_daily_report.bd_daily_report/lists', params })
}

// 添加个人考核
export function apiBdDailyReportAdd(params: any) {
    return request.post({ url: '/sq_bd_daily_report.bd_daily_report/add', params })
}

// 编辑个人考核
export function apiBdDailyReportEdit(params: any) {
    return request.post({ url: '/sq_bd_daily_report.bd_daily_report/edit', params })
}

// 删除个人考核
export function apiBdDailyReportDelete(params: any) {
    return request.post({ url: '/sq_bd_daily_report.bd_daily_report/delete', params })
}

// 个人考核详情
export function apiBdDailyReportDetail(params: any) {
    return request.get({ url: '/sq_bd_daily_report.bd_daily_report/detail', params })
}