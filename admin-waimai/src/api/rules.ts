import request from "@/utils/request";

/**
 * 数据规则列表
 * @param params 
 */
export const apiRuleData = (params: any) => {
  return request.get({ url: "/rule.dataRule/lists", params });
};

/**
 * 数据规则新增
 * @param params 
 */
export const apiRuleDataAdd = (params: any) => {
  return request.post({ url: "/rule.dataRule/add", params });
};

/**
 * 数据规则编辑
 * @param params 
 */
export const apiRuleDataEdit = (params: any) => {
  return request.post({ url: "/rule.dataRule/edit", params });
};