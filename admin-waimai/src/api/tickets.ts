import request from '@/utils/request'

/**
 * 创建客诉
 * @param params 
 * @returns 
 */
export const apiCreateTicket  = (params: any) => {
  return request.post({ url: '/shop.complaint/create', params })
}

/**
 * 客诉列表
 * @param params 
 * @returns 
 */
export const apiTicketList = (params: any) => {
  return request.get({ url: '/shop.complaint/lists', params })
}

/**
 * 客诉统计
 * @param params 
 * @returns 
 */
export const apiTicketCounts = (params: any) => {
  return request.get({ url: '/shop.complaint/getStatusList', params })
}

/**
 * 客诉详情
 * @param params 
 * @returns 
 */
export const apiTicketDetail = (params: any) => {
  return request.get({ url: '/shop.complaint/recLists', params })
}

/**
 * 客诉跟进
 * @param params 
 * @returns 
 */
export const apiTicketHeel = (params: any) => {
  return request.post({ url: '/shop.complaint/heel', params })
}

/**
 * 客诉关闭
 * @param params 
 * @returns 
 */
export const apiTicketClose = (params: any) => {
  return request.post({ url: '/shop.complaint/close', params })
}