import request from '@/utils/request'

// 渠道码分类表列表
export function apiChannelCodeCategoryLists(params: any) {
    return request.get({ url: '/channel_code_category.channel_code_category/lists', params })
}

// 添加渠道码分类表
export function apiChannelCodeCategoryAdd(params: any) {
    return request.post({ url: '/channel_code_category.channel_code_category/add', params })
}

// 编辑渠道码分类表
export function apiChannelCodeCategoryEdit(params: any) {
    return request.post({ url: '/channel_code_category.channel_code_category/edit', params })
}

// 删除渠道码分类表
export function apiChannelCodeCategoryDelete(params: any) {
    return request.post({ url: '/channel_code_category.channel_code_category/delete', params })
}

// 渠道码分类表详情
export function apiChannelCodeCategoryDetail(params: any) {
    return request.get({ url: '/channel_code_category.channel_code_category/detail', params })
}