import request from '@/utils/request'

// 招商活动绑定记录列表
export function apiElmShopCodeEmployeeRelsLists(params: any) {
    return request.get({
        url: '/elm_shop_code_employee_rels.elm_shop_code_employee_rels/lists',
        params
    })
}

// 添加招商活动绑定记录
export function apiElmShopCodeEmployeeRelsAdd(params: any) {
    return request.post({
        url: '/elm_shop_code_employee_rels.elm_shop_code_employee_rels/add',
        params
    })
}

// 编辑招商活动绑定记录
export function apiElmShopCodeEmployeeRelsEdit(params: any) {
    return request.post({
        url: '/elm_shop_code_employee_rels.elm_shop_code_employee_rels/edit',
        params
    })
}

// 删除招商活动绑定记录
export function apiElmShopCodeEmployeeRelsDelete(params: any) {
    return request.post({
        url: '/elm_shop_code_employee_rels.elm_shop_code_employee_rels/delete',
        params
    })
}

// 招商活动绑定记录详情
export function apiElmShopCodeEmployeeRelsDetail(params: any) {
    return request.get({
        url: '/elm_shop_code_employee_rels.elm_shop_code_employee_rels/detail',
        params
    })
}

// 获取员工列表数据
export function apiEmployeeOptions(params: any) {
    return request.get({
        url: '/elm_shop_code_employee_rels.elm_shop_code_employee_rels/employee_lists',
        params
    })
}
