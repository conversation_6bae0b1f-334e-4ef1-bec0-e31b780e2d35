import request from "@/utils/request";

// 店铺列表
export function apiShopLists(params: any) {
  return request.get({ url: "/shop.shop/lists", params });
}

// 店铺列表2
export function apiShopListsV2(params: any) {
  return request.get(
    { url: "/shop.shop/clueList", params },
    { ignoreCancelToken: true }
  );
}

// 店铺列表3
export function apiShopListsV3(params: any) {
  return request.post(
    { url: "/shop.shop/clueList", params },
    { ignoreCancelToken: true }
  );
}

// 店铺详情2
export function apiShopDetailV2(params: any) {
  return request.get({ url: "/shop.shop/detail", params });
}

// 店铺线索认领数量
export function apiShopClueCounts(params?: any) {
  return request.get({ url: "/shop.shop/clueCounts", params });
}

// 店铺线索认领数量2
export function apiShopClueCountsV2(params?: any) {
  return request.post({ url: "/shop.shop/clueCounts", params });
}

// 店铺分配
export function apiShopAssign(params: any) {
  return request.post({ url: "/shop.shop/allocation", params });
}

// 店铺批量分配
export function apiShopBatchAssign(params: {
  ids: number[];
  user_id: number;
  cause?: string;
}) {
  return request.post({ url: "/shop.shop/batchAllocation", params });
}

// 添加店铺
export function apiShopAdd(params: any) {
  return request.post({ url: "/shop.shop/add", params });
}

// 编辑店铺
export function apiShopEdit(params: any) {
  return request.post({ url: "/shop.shop/edit", params });
}

// 删除店铺
export function apiShopDelete(params: any) {
  return request.post({ url: "/shop.shop/delete", params });
}

// 店铺详情
export function apiShopDetail(params: any) {
  return request.get({ url: "/shop.shop/detail", params });
}

// 店铺审核列表
export function apiShopReviewLists(params: any) {
  return request.get({ url: "/shop.shop/auditList", params });
}

// 店铺审核各个状态的数量
export function apiShopReviewCounts(params?: any) {
  return request.get({ url: "/shop.shop/auditCounts", params });
}

// 店铺审核列表
export function apiShopReview(params: any) {
  return request.post({ url: "/shop.shop/audit", params });
}

// 店铺活动列表
export function apiShopActivityLists(params: any) {
  return request.get(
    { url: "/shop.shop/clueActList", params },
    { ignoreCancelToken: true }
  );
}

// 店铺活动各个状态的数量
export function apiShopActivityCounts(params?: any) {
  return request.get({ url: "/shop.shop/clueActStateCount", params });
}

/**
 * 商圈列表
 * @returns
 */
export function apiBusinessList() {
  return request.get({ url: "/shop.shop/businessDistrictList" });
}

/**
 * 商圈列表
 * @returns 
 */
export function apiBusinessListV2(params: any) {
  return request.get({url: '/shop.shop/businessDistricts', params})
}

/**
 * 批量店铺审核成功
 * @returns
 */
export function apiBatchAuditAdopt(params: any) {
  return request.post({ url: "/shop.shop/batchAuditAdopt", params });
}

/**
 * 批量店铺审核拒绝
 * @returns
 */
export function apiBatchAuditReject(params: any) {
  return request.post({ url: "/shop.shop/batchAuditReject", params });
}

/**
 * 修改线索名称
 * @param params
 * @returns
 */
export function apiBatchAuditEditName(params: any) {
  return request.post({ url: "/shop.shop/updateShopName", params });
}

/**
 * 修改店铺编码
 * @param params
 * @returns
 */
export function apiBatchAuditEditCode(params: any) {
  return request.post({ url: "/shop.shop/updateShopCode", params });
}

/**
 * 任务列表
 * @param params
 * @returns
 */
export function apiShopTaskList(params: any) {
  return request.get({ url: "/shop.shop/taskList", params });
}

/**
 * 任务列表数量
 * @param params
 * @returns
 */
export function apiShopTaskCounts(params: any) {
  return request.get({ url: "/shop.shop/taskStatus", params });
}

/**
 * 创建任务
 * @param params
 * @returns
 */
export function apiShopTaskAdd(params: any) {
  return request.post({ url: "/shop.shop/createTask", params });
}

/**
 * 任务bd数据看板
 * @param params
 * @returns
 */
export const apiShopBdTaskData = (params: any) => {
  return request.get({ url: "/task.task/bdData", params });
};

/**
 * 任务bd数据列表
 * @param params 
 * @returns 
 */
export const apiShopBdTaskList = (params: any) => {
  return request.get({ url: "/task.task/bdList", params });
};

/**
 * 任务详情
 * @param params
 * @returns
 */
export function apiShopTaskDetails(params: any) {
  return request.get({ url: "/shop.shop/taskDetail", params });
}

/**
 * 任务明细列表
 * @param params
 * @returns
 */
export function apiShopTaskDetail(params: any) {
  return request.get({ url: "/shop.shop/taskInfoList", params });
}

/**
 * 任务明细状态数据
 * @param params
 * @returns
 */
export function apiShopTaskDetailCounts(params: any) {
  return request.get({ url: "/shop.shop/taskInfoStatus", params });
}

/**
 * 查看店铺附近学校
 * @param params
 * @returns
 */
export function apiShopSchools(params: any) {
  return request.get({ url: "/shop.shop/schools", params });
}

// 店铺审核列表-CPS
export function apiShopReviewListsCps(params: any) {
  return request.get({ url: "/shop.shop/cpsAuditList", params });
}

// 店铺审核各个状态的数量-CPS
export function apiShopReviewCountsCps(params?: any) {
  return request.get({ url: "/shop.shop/cpsStatusCount", params });
}

/**
 * 商圈等级列表
 * @param params 
 * @returns 
 */
export function apiShopBusinessAnalyse(params: any) {
  return request.get({ url: "/rule.businessDistrict/lists", params });
}

/**
 * 商圈等级详情
 * @param params 
 * @returns 
 */
export function apiShopBusinessAnalyseDetail(params: any) {
  return request.get({ url: "/rule.businessDistrict/detail", params });
}

/**
 * 商圈等级编辑
 * @param params 
 * @returns 
 */
export function apiShopBusinessAnalyseEdit(params: any) {
  return request.post({ url: "/rule.businessDistrict/edit", params });
}

/**
 * 店铺等级列表
 * @param params 
 * @returns 
 */
export function apiShopAnalyse(params: any) {
  return request.get({ url: "/rule.shopGrade/lists", params });
}

/**
 * 店铺等级详情
 * @param params 
 * @returns 
 */
export function apiShopAnalyseDetail(params: any) {
  return request.get({ url: "/rule.shopGrade/detail", params });
}

/**
 * 店铺等级编辑
 * @param params 
 * @returns 
 */
export function apiShopAnalyseEdit(params: any) {
  return request.post({ url: "/rule.shopGrade/edit", params });
}

/**
 * 点位采集数据
 * @returns 
 */
export function apiCirclePoint(params: any) {
  return request.get({ url: "/shop.shop/circlePoint", params });
}

/**
 * 点位采集数据详情
 * @returns 
 */
export function apiCirclePointDateil(params: any) {
  return request.get({ url: "/shop.shop/pointInfoList", params });
}