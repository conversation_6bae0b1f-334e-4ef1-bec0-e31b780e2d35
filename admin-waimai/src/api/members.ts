import request from '@/utils/request'

// 商家表列表
export function apiMembersLists(params: any) {
    return request.get({ url: '/member.members/lists', params })
}

// 添加商家表
export function apiMembersAdd(params: any) {
    return request.post({ url: '/member.members/add', params })
}

// 编辑商家表
export function apiMembersEdit(params: any) {
    return request.post({ url: '/member.members/edit', params })
}

// 删除商家表
export function apiMembersDelete(params: any) {
    return request.post({ url: '/member.members/delete', params })
}

// 商家表详情
export function apiMembersDetail(params: any) {
    return request.get({ url: '/member.members/detail', params })
}

// 招商电话外呼记录列表
export function apiMerchantCallRecords(params?: { order_id?: string; callee?: string; start_time?: number; end_time?: number; bdm_id?: string; bd_name?: string;clue_id?: number }) {
    return request.get({ url: '/call.call_record/lists', params },{ignoreCancelToken: true})
}

// 招商电话外呼记录-更新
export function apiMerchantCallRecordUpdate(data: { id: string; field: string; value: string }) {
    return request.post({ url: '/call.call_record/update', data })
}

// 招商外呼-沟通小记
export function apiMerchantCallNotes(params: { shop_name?: string; user_id?: string; tel?: string; start_time?: string; end_time?: string;clue_id?: number }) {
    return request.get({ url: '/shop.shop/noteList', params }, { ignoreCancelToken: true })
}