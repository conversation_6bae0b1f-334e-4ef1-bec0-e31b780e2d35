import request from '@/utils/request'

// 客户列表
export function apiCustomerLists(params: any) {
    return request.get({ url: '/customer.customer/lists', params })
}

// 添加客户
export function apiCustomerAdd(params: any) {
    return request.post({ url: '/customer.customer/add', params })
}

// 编辑客户
export function apiCustomerEdit(params: any) {
    return request.post({ url: '/customer.customer/edit', params })
}

// 删除客户
export function apiCustomerDelete(params: any) {
    return request.post({ url: '/customer.customer/delete', params })
}

// 客户详情
export function apiCustomerDetail(params: any) {
    return request.get({ url: '/customer.customer/detail', params })
}

export function apiCustomerMatch(params: any) {
    return request.get({ url: '/customer.customer/match', params })
}
