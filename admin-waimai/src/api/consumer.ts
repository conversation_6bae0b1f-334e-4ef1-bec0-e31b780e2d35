import request from '@/utils/request'

// 用户列表
export function getUserList(params: any) {
    return request.get({ url: '/user.user/lists', params }, { ignoreCancelToken: true })
}

// BDM列表
export function getBdmList(params: any) {
    return request.get({ url: '/user.employee/bdmLists', params })
}

// 钉钉群列表
export function getDingGroupList(params: any) {
    return request.get({ url: '/ding.ding/list', params })
}

// 用户详情
export function getUserDetail(params: any) {
    return request.get({ url: '/user.user/detail', params })
}

// 用户编辑
export function userEdit(params: any) {
    return request.post({ url: '/user.user/edit', params })
}

// 用户编辑
export function adjustMoney(params: any) {
    return request.post({ url: '/user.user/adjustMoney', params })
}

// 添加用户
export function userAdd(params: any) {
    return request.post({ url: '/user.user/add', params })
}
