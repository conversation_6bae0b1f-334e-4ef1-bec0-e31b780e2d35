import request from "@/utils/request";

export function apiBrandLists(params: any) {
  return request.get({
    url: "/merchant.brand/lists",
    params,
  });
}

/**
 * 品牌数量
 * @param params
 * @returns
 */
export function apiBrandClueCounts(params: any) {
  return request.get({
    url: "/merchant.brand/statusCounts",
    params,
  });
}

/**
 * 分类列表
 * @param params -{ type: 1-品牌 2-店铺 }
 */
export function apiMerchantCateLists(params?: {
  page_no?: number;
  page_size?: number;
  type?: number;
}) {
  return request.get({ url: "/merchant.category/lists", params });
}

// 添加招商活动绑定记录
export function apiBrandAdd(params: any) {
  return request.post({
    url: "/merchant.brand/add",
    params,
  });
}

// 编辑招商活动绑定记录
export function apiBrandEdit(params: any, options?: { showMsg?: boolean }) {
  return request.post(
    {
      url: "/merchant.brand/edit",
      params,
    },
    options
  );
}

// 删除招商活动绑定记录
export function apiBrandDelete(params: any) {
  return request.post({
    url: "/merchant.brand/delete",
    params,
  });
}

export function apiBrandClaimLists(params: any) {
  return request.get({
    url: "/merchant.brand/claims",
    params,
  });
}

export function apiBrandClaimRelease(params: any) {
  return request.post({
    url: "/merchant.brand/release",
    params,
  });
}

export function apiBrandConfigGet() {
  return request.get({
    url: "/merchant.brand/setting",
  });
}

// 添加招商活动绑定记录
export function apiBrandConfigSet(params: any) {
  return request.post({
    url: "/merchant.brand/setting",
    params,
  });
}

// 品牌认领、归属招商明细
export function reqBrandMerchantData(params: {
  clue_id: string | number;
  status: number;
}) {
  return request.get({
    url: "/merchant.brand/trackList",
    params,
  });
}
// 品牌认领、归属招商数量
export function reqBrandMerchantCounts(params: { clue_id: string | number }) {
  return request.get({
    url: "/merchant.brand/trackStatusList",
    params,
  });
}
// 品牌详情
export function reqBrandDetail(params: { id: string | number }) {
  return request.get({
    url: "/merchant.brand/detail",
    params,
  });
}

// 合并品牌
export function reqBrandMerge(params: any) {
  return request.get({ url: "/merchant.brand/merge", params });
}

// 品牌门店认领
export function reqBrandClaim(params: any) {
  return request.post({ url: "/merchant.brand/batchClaim", params });
}

// 品牌门店导入
export function reqBrandImport(params: any) {
  return request.post({ url: "/merchant.brand/import", params });
}

// 品牌平台门店
export function reqBrandPlatformStore(params: any) {
  return request.get({ url: "/merchant.brand/brandStores", params });
}

/**
 * 品牌店铺列表
 * @param params 
 * @returns 
 */
export function reqBrandShop(params: any) {
  return request.get({url: '/merchant.brand/brandShopList', params})
}

/**
 * 品牌提报信息
 */
export function reqBrandSubmitLists(params: any) {
  return request.get({url: '/merchant.brand/brandSubmitLists', params})
}