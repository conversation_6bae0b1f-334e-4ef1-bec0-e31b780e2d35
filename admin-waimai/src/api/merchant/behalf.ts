import request from "@/utils/request";

/**
 * 代运营列表
 * @param params
 * @returns
 */
export const apiBehalfList = (params: any) => {
  return request.get({ url: "/merchant.operate/lists", params });
};

/**
 * 统计数据
 * @param params
 * @returns
 */
export const apiBehalfCounts = (params: any) => {
  return request.get({ url: "/merchant.operate/claimCounts", params });
};

/**
 * 创建代运营
 * @param params
 * @returns
 */
export const apiBehalfAdd = (params: any) => {
  return request.post({ url: "/merchant.operate/add", params });
};

/**
 * 编辑代运营
 * @param params
 * @returns
 */
export const apiBehalfEdit = (params: any) => {
  return request.post({ url: "/merchant.operate/edit", params });
};

/**
 * 解绑
 * @param params
 * @returns
 */
export const apiBehalfUnbind = (params: any) => {
  return request.post({ url: "/merchant.operate/unbind", params });
};

/**
 * 认领
 * @param params
 * @returns
 */
export const apiBehalfBind = (params: any) => {
  return request.post({ url: "/merchant.operate/bind", params });
};
