import request from "@/utils/request";

export function reqMerchantOverview(params: any) {
  return request.get({ url: '/merchant.index/overview', params })
}

export function reqMerchantStatis(params: any) {
  return request.get({ url: '/merchant.index/statis', params })
}

export function reqMerchantDetail(params?: any) {
  return request.get({ url: '/merchant.index/detail', params })
}

// 店铺活动数据面板
export const reqMerchantShop = (params: any) => request.get({ url: '/shop.shop/actShopList', params })
// 店铺活动数据列表
export const reqMerchantShopData = (params: any) => request.get({ url: '/shop.shop/actShopData', params })

// 活动数据列表
export const reqMerchantAct = (params: any) => request.get({ url: '/activity.activity/actLists', params })

// 活动数据看板
export const reqMerchantActData = (params: any) => request.get({ url: '/activity.activity/actData', params })


// 规则
export function reqMerchantRules() {
  return request.get({ url: '/shop.shop/getClueConfig' })
}
export function reqSaveMerchantRules(params: any) {
  return request.post({ url: '/shop.shop/setClueConfig', params })
}

export function reqOperateUpload(params: any) {
  return request.post({url: '/merchant.operate/upload', params})
}