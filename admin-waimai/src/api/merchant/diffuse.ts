import request from "@/utils/request";

// 宣传配置列表
export const reqDiffuseList = (params: any) => {
  return request.get({ url: "/merchant.operate/getPromotionList", params });
};

// 删除宣传配置
export const reqDiffuseDelete = (params: any) => {
  return request.post({ url: "/merchant.operate/deletePromotion", params });
};

// 新增配置
export const reqDiffuseAdd = (params: any) => {
  return request.post({ url: "/merchant.operate/addPromotion", params });
};

// 编辑配置
export const reqDiffuseEdit = (params: any) => {
  return request.post({ url: "/merchant.operate/editPromotion", params });
};

// 数据看板
export const reqDiffuseData = (params: any) => {
  return request.get({ url: "/merchant.operate/getPromotionClickList", params });
};