import request from '@/utils/request'

// 文件任务列表
export function apiFileTaskLists(params: any) {
    return request.get({ url: '/file_task.file_task/lists', params })
}

// 添加文件任务
export function apiFileTaskAdd(params: any) {
    return request.post({ url: '/file_task.file_task/add', params })
}

// 编辑文件任务
export function apiFileTaskEdit(params: any) {
    return request.post({ url: '/file_task.file_task/edit', params })
}

// 删除文件任务
export function apiFileTaskDelete(params: any) {
    return request.post({ url: '/file_task.file_task/delete', params })
}

// 文件任务详情
export function apiFileTaskDetail(params: any) {
    return request.get({ url: '/file_task.file_task/detail', params })
}