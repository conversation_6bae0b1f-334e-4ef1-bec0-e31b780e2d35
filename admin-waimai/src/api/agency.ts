import request from "@/utils/request";

/**
 * 店铺分监控数据
 * @param params
 * @returns
 */
export const apiAgencyControlShopData = (params: any) => {
  return request.get({ url: "/merchant.operate/shopScore", params });
};

/**
 * 店铺分监控
 * @param params
 * @returns
 */
export const apiAgencyControlShop = (params: any) => {
  return request.get({ url: "/merchant.operate/shopScoreLists", params });
};

/**
 * 店铺分监控配置
 * @param params
 * @returns
 */
export const apiAgencyControlShopConfig = () => {
  return request.get({ url: "/merchant.operate/getShopScoreConfig" });
};

/**
 * 设置店铺分监控配置
 * @param params
 * @returns
 */
export const apiAgencyControlShopConfigSave = (params: any) => {
  return request.post({ url: "/merchant.operate/setShopScoreConfig", params });
};

/**
 * 商品监控数据
 * @param params
 * @returns
 */
export const apiAgencyControlGoodsData = (params: any) => {
  return request.get({ url: "/merchant.operate/shopGoods", params });
};

/**
 * 商品监控
 * @param params
 * @returns
 */
export const apiAgencyControlGoods = (params: any) => {
  return request.get({ url: "/merchant.operate/shopGoodsLists", params });
};

/**
 * 商品监控配置
 * @param params
 * @returns
 */
export const apiAgencyControlGoodsConfig = () => {
  return request.get({ url: "/merchant.operate/getShopGoodsConfig" });
};

/**
 * 设置商品监控配置
 * @param params
 * @returns
 */
export const apiAgencyControlGoodsConfigSave = (params: any) => {
  return request.post({ url: "/merchant.operate/setShopGoodsConfig", params });
};

/**
 * 巡店监控数据
 * @param params
 * @returns
 */
export const apiAgencyControlTourData = (params: any) => {
  return request.get({ url: "/merchant.operate/shopAnomalyIndex", params });
};

/**
 * 巡店监控店铺列表
 * @param params 
 * @returns 
 */
export const apiAgencyControlTourShop = (params: any) => {
  return request.get({ url: "/merchant.operate/shopAnomalyIndexLists", params });
};

/**
 * 巡店模版列表
 * @param params 
 * @returns 
 */
export const apiAgencyControlTourTimp = (params: any) => {
  return request.get({ url: "/merchant.operate/getTempLists", params });
};

/**
 * 创建巡店模版
 * @param params 
 * @returns 
 */
export const apiAgencyControlTourTimpCreate = (params: any) => {
  return request.post({ url: "/merchant.operate/createTemp", params });
}

/**
 * 更新巡店模版
 * @param params 
 * @returns 
 */
export const apiAgencyControlTourTimpUpdate = (params: any) => {
  return request.post({ url: "/merchant.operate/updateTemp", params });
}

/**
 * 删除巡店模版
 * @param params 
 * @returns 
 */
export const apiAgencyControlTourTimpDelete = (params: any) => {
  return request.post({ url: "/merchant.operate/deleteTemp", params });
}

/**
 * 巡店模版详情
 * @param params 
 * @returns 
 */
export const apiAgencyControlTourTimpDeatil = (params: any) => {
  return request.get({ url: "/merchant.operate/getTempDetailLists", params });
}

/**
 * 巡店模版规则修改
 * @param params 
 * @returns 
 */
export const apiAgencyControlTourTimpRule = (params: any) => {
  return request.post({ url: "/merchant.operate/updateTempRule", params });
}

/**
 * 回本监控数据
 * @param params 
 * @returns 
 */
export const apiAgencyControlRevenueData = (params: any) => {
  return request.get({ url: "/merchant.operate/recoveryStat", params });
}

/**
 * 回本监控列表
 * @param params 
 * @returns 
 */
export const apiAgencyControlRevenue = (params: any) => {
  return request.get({ url: "/merchant.operate/recoveryLists", params });
}

/**
 * 回本监控配置列表
 * @returns 
 */
export const apiAgencyControlRevenueConfig = (params: any) => {
  return request.get({ url: "/operate.sales_cost_template/lists", params });
}

/**
 * 回本监控配置添加
 * @param params 
 * @returns 
 */
export const apiAgencyControlRevenueConfigAdd = (params: any) => {
  return request.post({ url: "/operate.sales_cost_template/add", params });
}

/**
 * 回本监控配置编辑
 * @param params 
 * @returns 
 */
export const apiAgencyControlRevenueConfigEdit = (params: any) => {
  return request.post({ url: "/operate.sales_cost_template/edit", params });
}

/**
 * 回本监控配置删除
 * @param params 
 * @returns 
 */
export const apiAgencyControlRevenueConfigDelete = (params: any) => {
  return request.post({ url: "/operate.sales_cost_template/delete", params });
}