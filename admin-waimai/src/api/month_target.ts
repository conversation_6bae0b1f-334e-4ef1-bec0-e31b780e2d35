import request from '@/utils/request'

// 月度绩效制定列表
export function apiMonthTargetLists(params: any) {
    return request.get({ url: '/sq_month_target.month_target/lists', params })
}

// 添加月度绩效制定
export function apiMonthTargetAdd(params: any) {
    return request.post({ url: '/sq_month_target.month_target/add', params })
}

// 编辑月度绩效制定
export function apiMonthTargetEdit(params: any) {
    return request.post({ url: '/sq_month_target.month_target/edit', params })
}

// 删除月度绩效制定
export function apiMonthTargetDelete(params: any) {
    return request.post({ url: '/sq_month_target.month_target/delete', params })
}

// 月度绩效制定详情
export function apiMonthTargetDetail(params: any) {
    return request.get({ url: '/sq_month_target.month_target/detail', params })
}

export function apiGetTarget(params: any) {
    return request.get({ url: '/sq_month_target.month_target/gen', params })
}

export function apiSetTarget(params: any) {
    return request.post({ url: '/sq_month_target.month_target/gen', params })
}
