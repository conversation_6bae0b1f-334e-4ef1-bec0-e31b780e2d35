import request from '@/utils/request'

// 渠道数据表列表
export function apiChannelDataStatisticsLists(params: any) {
    return request.get({ url: '/channel_data.channel_data_statistics/lists', params })
}

// 添加渠道数据表
export function apiChannelDataStatisticsAdd(params: any) {
    return request.post({ url: '/channel_data.channel_data_statistics/add', params })
}

export function apiChannelDataStatisticsOtherList() {
    return request.get({ url: '/channel_data.channel_data_statistics/otherlist' })
}

// 编辑渠道数据表
export function apiChannelDataStatisticsEdit(params: any) {
    return request.post({ url: '/channel_data.channel_data_statistics/edit', params })
}

// 删除渠道数据表
export function apiChannelDataStatisticsDelete(params: any) {
    return request.post({ url: '/channel_data.channel_data_statistics/delete', params })
}

// 渠道数据表详情
export function apiChannelDataStatisticsDetail(params: any) {
    return request.get({ url: '/channel_data.channel_data_statistics/detail', params })
}

export function apiOutChannelOption(params: any) {
    return request.get({ url: '/channel_data.channel_data_statistics/outChannel', params })
}

export function apiChannelDataExport(params: any) {
    return request.get({ url: '/channel_data.channel_data_statistics/export', params })
}
