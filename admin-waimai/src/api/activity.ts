import request from "@/utils/request";

// 活动列表
export function apiActivityLists(params: any) {
  return request.get({ url: "/activity.activity/lists", params });
}

// 添加活动
export function apiActivityAdd(params: any) {
  return request.post({ url: "/activity.activity/add", params });
}

// 编辑活动
export function apiActivityEdit(params: any) {
  return request.post({ url: "/activity.activity/edit", params });
}

// 删除活动
export function apiActivityDelete(params: any) {
  return request.post({ url: "/activity.activity/delete", params });
}

// 活动详情
export function apiActivityDetail(params: any) {
  return request.get({ url: "/activity.activity/detail", params });
}

/**
 * 自有活动列表
 * @param params
 * @returns
 */
export function apiActivitySelf(params: any) {
  return request.get({ url: "/shop.shop/rewardList", params });
}

/**
 * 自有活动列表统计
 * @param params
 * @returns
 */
export function apiActivitySelfCounts(params: any) {
  return request.get({ url: "/shop.shop/rewardCounts", params });
}

/**
 * 更新店铺联系电话
 * @param params
 * @returns
 */
export function apiActivitySelfUpdateTel(params: any) {
  return request.post({ url: "/shop.shop/updateTel", params });
}

/**
 * 自报活动作废
 * @param params
 * @returns
 */
export function apiActivitySelfVoid(params: any) {
  return request.post({ url: "/shop.shop/cancelAwardShop", params });
}

/**
 * 导入任务列表
 * @param params
 * @returns
 */
export function apiActivityReport(params: any) {
  return request.get({ url: "/merchant.index/importList", params });
}

/**
 * 导入任务
 * @param params
 * @returns
 */
export function apiActivityReportImport(params: any) {
  return request.post({ url: "/merchant.index/import", params });
}

/**
 * 推广明细
 * @param params
 * @returns
 */
export function apiActivityReports(params: any) {
  return request.get({ url: "/order.order/shopActReports", params });
}

/**
 * 导入店铺数据
 */
export function apiActivityReportData() {
  return request.get({ url: "/merchant.index/importShopData" });
}
