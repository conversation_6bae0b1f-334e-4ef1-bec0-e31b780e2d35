import request from '@/utils/request'

// 团队列表
export function apiTeamLists(params: any) {
    return request.get({ url: '/team.team/lists', params })
}

// 添加团队
export function apiTeamAdd(params: any) {
    return request.post({ url: '/team.team/add', params })
}

// 编辑团队
export function apiTeamEdit(params: any) {
    return request.post({ url: '/team.team/edit', params })
}

// 删除团队
export function apiTeamDelete(params: any) {
    return request.post({ url: '/team.team/delete', params })
}

// 团队详情
export function apiTeamDetail(params: any) {
    return request.get({ url: '/team.team/detail', params })
}