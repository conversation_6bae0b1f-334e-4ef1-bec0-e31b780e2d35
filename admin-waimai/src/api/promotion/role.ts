import request from "@/utils/request";

export function apiRoleLists(params: any) {
    return request.get({
        url: '/promotion.role/lists',
        params
    })
}

// 添加招商活动绑定记录
export function apiRoleAdd(params: any) {
    return request.post({
        url: '/promotion.role/add',
        params
    })
}

// 编辑招商活动绑定记录
export function apiRoleEdit(params: any) {
    return request.post({
        url: '/promotion.role/edit',
        params
    })
}

// 删除招商活动绑定记录
export function apiRoleDelete(params: any) {
    return request.post({
        url: '/promotion.role/delete',
        params
    })
}