import request from "@/utils/request";

export function apiActivityLists(params: any) {
    return request.get({
        url: '/promotion.activity/lists',
        params
    })
}

// 添加招商活动绑定记录
export function apiActivityAdd(params: any) {
    return request.post({
        url: '/promotion.activity/add',
        params
    })
}

// 编辑招商活动绑定记录
export function apiActivityEdit(params: any) {
    return request.post({
        url: '/promotion.activity/edit',
        params
    })
}

// 删除招商活动绑定记录
export function apiActivityDelete(params: any) {
    return request.post({
        url: '/promotion.activity/delete',
        params
    })
}

export function apiActivityQrcodeLists(params: any) {
    return request.get({
        url: '/promotion.qrcode/lists',
        params
    })
}

export function apiActivityQrcodeAssign(params: any) {
    return request.post({
        url: '/promotion.qrcode/assign',
        params
    })
}

export function apiActivityLogLists(params: any) {
    return request.get({
        url: '/promotion.activity/logs',
        params
    })
}

export function apiActivityItemLists(params: any) {
    return request.get({
        url: '/promotion.qrcode/details',
        params
    })
}

export function apiActivityReportLists(params: any) {
    return request.get({
        url: '/promotion.activity/reports',
        params
    })
}

export function apiActivityReportMulUpdate(params: any) {
    return request.post({
        url: '/promotion.activity/updateReportMul',
        params
    })
}

export function apiActivityReportExtraUpdate(params: any) {
    return request.post({
        url: '/promotion.activity/updateExtraReward',
        params
    })
}

export function apiActivityQrcodeDownload(params: any) {
    return request.get({
        url: '/promotion.qrcode/download',
        params
    })
}

export function apiElmPwdAdd(params: any) {
    return request.post({
        url: '/promotion.qrcode/pwdAdd',
        params
    })
}

// 编辑招商活动绑定记录
export function apiElmPwdEdit(params: any) {
    return request.post({
        url: '/promotion.qrcode/pwdEdit',
        params
    })
}