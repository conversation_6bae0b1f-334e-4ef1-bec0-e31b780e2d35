import request from '@/utils/request'

// 用户口令活动明细列表
export function apiPromotionUserPwdActLists(params: any) {
    return request.get({ url: '/promotion.user_pwd_act/lists', params })
}

// 添加用户口令活动明细
export function apiPromotionUserPwdActAdd(params: any) {
    return request.post({ url: '/promotion.user_pwd_act/add', params })
}

//apiPromotionUserPwdActAdd

//apiPromotionUserPwdActCounts
export function apiPromotionUserPwdActCounts(params?: any) {
    return request.post({ url: "/promotion.user_pwd_act/counts", params });
}
