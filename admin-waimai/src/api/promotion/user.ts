import request from "@/utils/request";

export function apiUserLists(params: any) {
    return request.get({
        url: '/promotion.user/lists',
        params
    })
}

//all
export function apiUserAll(params: any) {
    return request.get({
        url: '/promotion.user/all',
        params
    })
}

export function apiUserParentLists(params: any) {
    return request.get({
        url: '/promotion.user/parents',
        params
    })
}

// 添加招商活动绑定记录
export function apiUserAdd(params: any) {
    return request.post({
        url: '/promotion.user/add',
        params
    })
}

// 编辑招商活动绑定记录
export function apiUserEdit(params: any) {
    return request.post({
        url: '/promotion.user/edit',
        params
    })
}

// 删除招商活动绑定记录
export function apiUserDelete(params: any) {
    return request.post({
        url: '/promotion.user/delete',
        params
    })
}

export function apiUserBalanceAdjust(params: any) {
    return request.post({
        url: '/promotion.user/adjustBalance',
        params
    })
}

export function apiUserAccountLogLists(params: any) {
    return request.get({
        url: '/promotion.user/accountLogs',
        params
    })
}

export function apiUserAccountShow(params: any) {
    return request.post({
        url: '/promotion.user/accountShow',
        params
    })
}

export function apiUserTxLists(params: any) {
    return request.get({
        url: '/promotion.withdraw/lists',
        params
    })
}

export function apiUserTxAudit(params: any) {
    return request.post({
        url: '/promotion.withdraw/audit',
        params
    })
}

export function apiPubLogLists(params: any) {
    return request.get({
        url: '/promotion.user/pubLogs',
        params
    })
}

// 添加招商活动绑定记录
export function apiPubLogAdd(params: any) {
    return request.post({
        url: '/promotion.user/addPub',
        params
    })
}

// 编辑招商活动绑定记录
export function apiPubLogEdit(params: any) {
    return request.post({
        url: '/promotion.user/editPub',
        params
    })
}

// 删除招商活动绑定记录
export function apiPubLogDelete(params: any) {
    return request.post({
        url: '/promotion.user/deletePub',
        params
    })
}