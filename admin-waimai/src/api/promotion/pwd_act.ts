import request from '@/utils/request'

// 口令活动列表
export function apiPromotionPwdActLists(params: any) {
    return request.get({ url: '/promotion.pwd_act/lists', params })
}

// 删除口令活动
export function apiPromotionPwdActDelete(params: any) {
    return request.post({ url: '/promotion.pwd_act/delete', params })
}

export function apiPromotionPwdActAdd(params: any) {
    return request.post({ url: '/promotion.pwd_act/add', params })
}

export function apiPromotionPwdActEdit(params: any) {
    return request.post({ url: '/promotion.pwd_act/edit', params })
}
