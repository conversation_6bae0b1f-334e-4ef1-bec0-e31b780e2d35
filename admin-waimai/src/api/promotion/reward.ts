import request from "@/utils/request";

export function apiRewardLists(params: any) {
    return request.get({
        url: '/promotion.reward/lists',
        params
    })
}

export function apiRewardAdd(params: any) {
    return request.post({
        url: '/promotion.reward/add',
        params
    })
}

export function apiRewardEdit(params: any) {
    return request.post({
        url: '/promotion.reward/edit',
        params
    })
}


export function apiRewardOrderLists(params: any) {
    return request.get({
        url: '/promotion.reward_order/lists',
        params
    })
}

export function apiRewardOrderDelivery(params: any) {
    return request.post({
        url: '/promotion.reward_order/delivery',
        params
    })
}

export function apiRewardOrderCancel(params: any) {
    return request.post({
        url: '/promotion.reward_order/cancel',
        params
    })
}