import request from "@/utils/request";

export function apiOrderTypeLists(params: any) {
    return request.get({
        url: '/promotion.order_type/lists',
        params
    })
}

export function apiOrderTypes() {
    return request.get({
        url: '/promotion.order_type/orderTypes'
    })
}

// 添加招商活动绑定记录
export function apiOrderTypeAdd(params: any) {
    return request.post({
        url: '/promotion.order_type/add',
        params
    })
}

// 编辑招商活动绑定记录
export function apiOrderTypeEdit(params: any) {
    return request.post({
        url: '/promotion.order_type/edit',
        params
    })
}

// 删除招商活动绑定记录
export function apiOrderTypeDelete(params: any) {
    return request.post({
        url: '/promotion.order_type/delete',
        params
    })
}