import request from "@/utils/request";

export function apiPlanLists(params: any) {
    return request.get({
        url: '/promotion.plan/lists',
        params
    })
}

// 添加招商活动绑定记录
export function apiPlanAdd(params: any) {
    return request.post({
        url: '/promotion.plan/add',
        params
    })
}

// 编辑招商活动绑定记录
export function apiPlanEdit(params: any) {
    return request.post({
        url: '/promotion.plan/edit',
        params
    })
}

// 删除招商活动绑定记录
export function apiPlanDelete(params: any) {
    return request.post({
        url: '/promotion.plan/delete',
        params
    })
}