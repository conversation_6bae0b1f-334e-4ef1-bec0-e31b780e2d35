import request from "@/utils/request";

// 配置
export function getConfig() {
  return request.get({ url: "/config/getConfig" });
}

// 工作台主页
export function getWorkbench() {
  return request.get({ url: "/workbench/index" });
}

//字典数据
export function getDictData(params: any) {
  return request.get({ url: "/config/dict", params });
}

// 上传视频/音频
export function apiUploadVideo(data: { file: File }) {
  const d = new FormData();
  d.append("file", data.file);
  return request.post({ url: "/upload/video", data: d });
}

/**
 * 城市列表
 * @param params
 * @returns
 */
export function apiCityList(params: any) {
  return request.get({ url: "/merchant.city/lists", params });
}

/**
 * 首页数据
 * @returns
 */
export const getHomeData = () => {
  return request.get({ url: "/merchant.index/operateHome" });
};

/**
 * 首页审核图表
 * @returns
 */
export const apiRuditChart = () => {
  return request.get({ url: "/merchant.index/operatePieChart" });
};

/**
 * 首页消息列表
 * @returns
 */
export const apiMessageList = (params?: any) => {
  return request.get({ url: "/message.message/lists", params });
};

/**
 * 读取消息
 * @param params
 * @returns
 */
export const apiMessageRead = (data: any) => {
  return request.post({ url: "/message.message/read", data });
};

/**
 * 运营排名
 * @returns
 */
export const apiYunyingRanking = (params: any) => {
  return request.get({ url: "/merchant.index/operateRank", params });
};
